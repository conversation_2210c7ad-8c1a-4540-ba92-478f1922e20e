package com.bt.itsms.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.bt.itscore.domain.dto.DeviceDTO;
import com.bt.itscore.domain.dto.IdStringDTO;
import com.bt.itscore.domain.dto.VideoDeviceDTO;

@FeignClient(name = "its-device")
public interface ItsDeviceFeignClient {
	@PostMapping(value = "/device/selectCameraCodeById")
	public VideoDeviceDTO selectCameraCodeById(@RequestBody IdStringDTO dto);

	@PostMapping(value = "/device/selectLightpro")
	public Object selectLightpro(@RequestBody DeviceDTO dto);

	@PostMapping(value = "/device/selectDeviceId")
	public Object selectDeviceId(@RequestBody IdStringDTO dto);
}
