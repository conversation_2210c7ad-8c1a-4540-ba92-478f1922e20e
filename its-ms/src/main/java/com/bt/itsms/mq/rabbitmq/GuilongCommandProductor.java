package com.bt.itsms.mq.rabbitmq;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024年5月17日 上午9:29:51
 * @Description 中交贵隆公司的队列消息
 */
@Component
public class GuilongCommandProductor {
	
	private MessageChannel output;

	public GuilongCommandProductor(@Qualifier(GuilongCommandSource.COMMAND) MessageChannel guilongCommandOutput) {
		this.output = guilongCommandOutput;
	}

	// 生产消息
	public String produce(String msg) {
		this.output.send(MessageBuilder.withPayload(msg).build());
		return "sendTo..." + msg;
	}
	
}
