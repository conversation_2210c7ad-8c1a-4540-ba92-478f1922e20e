package com.bt.itsms.mapper.outfield;

import com.bt.itsms.outfield.entity.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface VdFluxMapper {
    int saveToday(@Param("list") List<VdFlux> list);

    int saveHour(@Param("list") List<VdFlux> list);

    int saveError(@Param("list") List<VdFlux> list);

    int saveHourHistory(@Param("list") List<VdFlux> list);

    int saveHistory(@Param("list") List<VdFluxRecord> list);

    boolean deleteNotToday();

    int hourToHistory();

    int deleteHour();


}

