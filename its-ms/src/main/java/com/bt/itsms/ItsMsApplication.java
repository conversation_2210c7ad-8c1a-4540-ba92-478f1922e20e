package com.bt.itsms;

import java.io.Serializable;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScans;
import org.springframework.context.annotation.FilterType;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.support.spring.FastJsonRedisSerializer;
import com.bt.itscore.monitor.HeartBeatSender;
import com.bt.itsms.mq.rabbitmq.BeihaiCommandSource;
import com.bt.itsms.mq.rabbitmq.CommandSink;
import com.bt.itsms.mq.rabbitmq.CommandSource;
import com.bt.itsms.mq.rabbitmq.DahuaCommandSource;
import com.bt.itsms.mq.rabbitmq.GuilongCommandSource;
import com.bt.itsms.mq.rabbitmq.JkCommandSource;
import com.bt.itsms.mq.rabbitmq.LaibinCommandSource;
import com.bt.itsms.mq.rabbitmq.LingshanCommandSource;
import com.bt.itsms.mq.rabbitmq.LuochengCommandSource;
import com.bt.itsms.mq.rabbitmq.NanningCommandSource;
import com.bt.itsms.mq.rabbitmq.NingmingCommandSource;
import com.bt.itsms.mq.rabbitmq.PowerCommandSource;
import com.bt.itsms.mq.rabbitmq.PowerResultSink;
import com.bt.itsms.mq.rabbitmq.QinzhouCommandSource;
import com.bt.itsms.mq.rabbitmq.XfzCommandSource;
import com.bt.itsms.mq.rabbitmq.YanhaiCommandSource;
import com.bt.itsms.mq.rabbitmq.ZhaopingCommandSource;

@EnableFeignClients
@EnableDiscoveryClient
@EnableScheduling
@SpringBootApplication
//@ComponentScans(value = { @ComponentScan(value = "com.bt.itscore"),
//		@ComponentScan(value = "com.bt.itsms")})
@ComponentScans(value = { @ComponentScan(value = "com.bt.itscore", excludeFilters = @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = {HeartBeatSender.class})),
		@ComponentScan(value = "com.bt.itsms")})
@EnableBinding({CommandSource.class,YanhaiCommandSource.class,LuochengCommandSource.class,DahuaCommandSource.class,ZhaopingCommandSource.class,LingshanCommandSource.class, LaibinCommandSource.class,CommandSink.class
	,PowerCommandSource.class,PowerResultSink.class,XfzCommandSource.class,NingmingCommandSource.class,NanningCommandSource.class,QinzhouCommandSource.class,BeihaiCommandSource.class,JkCommandSource.class,GuilongCommandSource.class})
public class ItsMsApplication {

	public static void main(String[] args) {
		SpringApplication.run(ItsMsApplication.class, args);
	}

    @Bean(name="redisTemplate")
    public RedisTemplate<String, Serializable> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate redisTemplate = new RedisTemplate();
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
        redisTemplate.setConnectionFactory(redisConnectionFactory);
        // 设置value的序列化规则和 key的序列化规则
        redisTemplate.setValueSerializer(stringRedisSerializer);
        redisTemplate.setKeySerializer(stringRedisSerializer);
        redisTemplate.setKeySerializer(stringRedisSerializer);
        redisTemplate.setHashValueSerializer(stringRedisSerializer);
        //value序列化
        redisTemplate.setValueSerializer(new FastJsonRedisSerializer<>(Object.class));
//        redisTemplate.setValueSerializer(stringRedisSerializer);
        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }
	@Bean
	public RestTemplate restTemplate() {
		SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(30000);// 设置连接超时，单位毫秒
        requestFactory.setReadTimeout(30000);  //设置读取超时
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(requestFactory);
        return restTemplate;
	}
}
