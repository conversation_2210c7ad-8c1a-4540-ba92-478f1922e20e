package com.bt.itsms.outfield.service;

import com.bt.itscore.domain.dto.CmsRepertoryDTO;
import com.bt.itscore.domain.dto.CommandDTO;
import com.bt.itscore.domain.dto.FogMessageDTO;
import com.bt.itscore.utils.CmsSecurityUtils;
import com.bt.itscore.utils.ListUtils;
import com.bt.itsms.domain.dto.MsOutfieldSendDTO;
import com.bt.itsms.feign.ItsOutfieldFeignClient;
import com.bt.itsms.outfield.entity.*;
import com.bt.itsms.mapper.outfield.VdFluxMapper;
import com.bt.itsms.mapper.outfield.VdTrafficCarTypeMapper;
import com.bt.itsms.mapper.outfield.WdAlarmMapper;
import com.bt.itsms.mapper.outfield.WdFluxMapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("outfieldService")
public class OutfieldService {
    /**
     * 数据库写入类
     */
    @Autowired
    private VdFluxMapper vdFluxMapper;
    @Autowired
    private VdTrafficCarTypeMapper vdTrafficCarTypeMapper;
    @Autowired
    private WdFluxMapper wdFluxMapper;
    @Autowired
    private WdAlarmMapper wdAlarmMapper;
    @Autowired
    private ItsOutfieldFeignClient itsOutfieldFeignClient;

    @Value("${cms.privateKey}")
    private String cmsPrivateKey;

    Logger logger = LogManager.getLogger(LogManager.ROOT_LOGGER_NAME);

    public boolean saveVdFlux(List<VdFlux> list) {
        boolean flag = true;
        if (list.size() > 0) {
            List<List<VdFlux>> dataGroup = ListUtils.splitList(list, 100);
            for (List<VdFlux> group : dataGroup) {
                //校验合法性
                List<VdFlux> errorList = new ArrayList<>();
                List<VdFlux> rightList = new ArrayList<>();
                for (VdFlux v : group) {
                    //流量和速度不对，数据就全不对了，入错误库
                    if (!isRightVluxData(v)) {
                        errorList.add(v);
                        continue;
                    }
                    //如果是交调站而且日期是未来，无法校正，入错误库
                    else if (v.getVd_type() != null && v.getVd_type().equals("交调站") && isFutureDate(v.getWrite_date())) {
                        errorList.add(v);
                        continue;
                    } else {
                        //其他参数不对可以校正为0
                        //校正跟车百分比
                        if (v.getCarfollowing() == null || v.getCarfollowing() > 1000 || v.getCarfollowing() < 0) {
                            v.setCarfollowing(0.0);
                        }
                        v.setCarfollowing(removeLongThan2bit(v.getCarfollowing()));
                        //校正占有率
                        if (v.getOccupancy() == null || v.getOccupancy() > 200 || v.getOccupancy() < 0) {
                            v.setOccupancy(0.0);
                        }
                        v.setOccupancy(removeLongThan2bit(v.getOccupancy()));
                        //校正车长度
                        if (v.getCarlength() == null || v.getCarlength() > 5000 || v.getCarlength() < 0) {
                            v.setCarlength(0);
                        }
                        //校正车间距
                        if (v.getSpacing() == null || v.getSpacing() > 99999 || v.getSpacing() < 0) {
                            v.setSpacing(0);
                        }
                        //如果是车检器而且日期不对，可以校正为当前日期（车检器无过去数据重传机制）
                        if ((v.getVd_direction() == null || v.getVd_direction() == 0) && v.getVd_type() != null && v.getVd_type().equals("车辆检测器")) {
                            v.setWrite_date(checkAndCorrectDate(v.getWrite_date()));
                        }
                        rightList.add(v);
                    }
                }
                if (errorList.size() > 0) {
                    vdFluxMapper.saveError(errorList);
                }
                if (rightList.size() > 0) {
                    flag &= (vdFluxMapper.saveToday(rightList) > 0);
                }
            }
        }
        return flag;
    }

    public boolean saveTrafficCartype(List<VdTrafficCarType> list) {
        boolean flag = true;
        if (list.size() > 0) {
            List<List<VdTrafficCarType>> dataGroup = ListUtils.splitList(list, 100);
            for (List<VdTrafficCarType> group : dataGroup) {
                //校验合法性
                List<VdTrafficCarType> errorList = new ArrayList<>();
                List<VdTrafficCarType> rightList = new ArrayList<>();
                for (VdTrafficCarType v : group) {
                    //流量和速度不对，数据就全不对了，入错误库
                    if (!isRightTrafficData(v)) {
                        errorList.add(v);
                        continue;
                    }
                    //交调站而且日期是未来，无法校正，入错误库
                    else if (isFutureDate(v.getWrite_date())) {
                        errorList.add(v);
                        continue;
                    } else {
                        rightList.add(v);
                    }
                }
                if (errorList.size() > 0) {
                    vdTrafficCarTypeMapper.batchSaveError(errorList);
                }
                if (rightList.size() > 0) {
                    flag &= (vdTrafficCarTypeMapper.batchSaveToday(rightList) > 0);
                }
            }
        }
        return flag;
    }

    public boolean saveWdFlux(List<WdFlux> list) {
        boolean flag = true;
        if (list.size() > 0) {
            List<List<WdFlux>> dataGroup = ListUtils.splitList(list, 100);
            for (List<WdFlux> group : dataGroup) {
                flag &= (wdFluxMapper.saveToday(group) > 0);
                flag &= (wdFluxMapper.saveHistory(group) > 0);
            }
        }
        return flag;
    }

    public boolean saveWdAlarm(List<WdAlarm> wdAlarms) {
        boolean flag = (wdAlarmMapper.batchSave(wdAlarms) > 0);
        return flag;
    }

    public synchronized boolean saveCmsRt(List<CmsRtRepertoryDTO> cmsRtRepertoryList) {
        //解密
        if (cmsPrivateKey != null && cmsPrivateKey.length() > 0) {
            String keyCopy = cmsPrivateKey + "";
            for (CmsRtRepertoryDTO n : cmsRtRepertoryList) {
                if (n.getMessageBody() != null && n.getMessageBody().length() > 0) {
                    String content = n.getMessageBody();
                    //在这里做SM2解密
                    String decrypt = CmsSecurityUtils.decryptBase64(content, keyCopy);
                    //把解密内容写入content
                    content = decrypt;

                    n.setMessageBody(content);
                }
            }

        }
        MsCmsLog2RtRepertoryDTO dto = new MsCmsLog2RtRepertoryDTO();
        dto.setCmsRtRepertoryDTOList(cmsRtRepertoryList);
        boolean flag = itsOutfieldFeignClient.saveCmsRt(dto);
        return flag;
    }

    public boolean saveStatus(Map<String, Integer> statusMap) {
        boolean flag = itsOutfieldFeignClient.saveStatus(statusMap);
        return flag;
    }


    public synchronized boolean cmsLogPage(MsCmsLog2RtRepertoryDTO entity) {
        //解密
        if (cmsPrivateKey != null && cmsPrivateKey.length() > 0) {

            String keyCopy = cmsPrivateKey + "";
            List<CmsRepertoryDTO> list = entity.getPage() != null ? entity.getPage().getChildren() : new ArrayList<>();
            for (CmsRepertoryDTO n : list) {
                if (n.getMultiBody() == null && n.getMessageBody() != null) {
                    String content = n.getMessageBody();
                    //在这里做SM2解密
                    String decrypt = CmsSecurityUtils.decryptBase64(content, keyCopy);
                    //把解密内容写入content
                    content = decrypt;
                    n.setMessageBody(content);
                }
            }

        }
        boolean flag = itsOutfieldFeignClient.cmsLogPage(entity);
        //写日志的时候同时推送消息
        List<String> idList = new ArrayList<>();
        if (entity.getDeviceId() != null) {
            idList.add(entity.getDeviceId());
        }

        itsOutfieldFeignClient.tunnelPush(idList);
        return flag;
    }

    public boolean fogAddStatus(FogMessageDTO message) {
        boolean flag = itsOutfieldFeignClient.fogAddStatus(message);
        return flag;
    }

    public static boolean isRightVluxData(VdFlux v) {
        //不合法的速度
        if (v.getSpeed() == null || v.getSpeed() > 500 || v.getSpeed() < 0) {
            return false;
        }
        //不合法的流量
        if (v.getFlow() == null || v.getFlow() > 10000 || v.getFlow() < 0) {
            return false;
        }
        return true;
    }

    public static double removeLongThan2bit(double d) {
        String str = String.format("%.2f", d);
        double result = Double.parseDouble(str);
        return result;
    }


    /**
     * @param file
     * @param targetDirPath 存储MultipartFile文件的目标文件夹
     * @return 文件的存储的绝对路径
     */
    public String saveMultipartFile(MultipartFile file, String targetDirPath) {

        File toFile = null;
        if (file.equals("") || file.getSize() <= 0) {
            return null;
        } else {

            /*获取文件原名称*/
            String originalFilename = file.getOriginalFilename();
            /*获取文件格式*/
            String fileFormat = originalFilename.substring(originalFilename.lastIndexOf("."));

            toFile = new File(targetDirPath + File.separator + originalFilename);

            String absolutePath = null;
            try {
                absolutePath = toFile.getCanonicalPath();

                /*判断路径中的文件夹是否存在，如果不存在，先创建文件夹*/
                String dirPath = absolutePath.substring(0, absolutePath.lastIndexOf(File.separator));
                File dir = new File(dirPath);
                if (!dir.exists()) {
                    dir.mkdirs();
                }

                InputStream ins = file.getInputStream();

                inputStreamToFile(ins, toFile);
                ins.close();

            } catch (IOException e) {
                e.printStackTrace();
            }

            return absolutePath;
        }

    }

    //获取流文件
    private static void inputStreamToFile(InputStream ins, File file) {
        try {
            OutputStream os = new FileOutputStream(file);
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            ins.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 删除本地临时文件
     *
     * @param file
     */
    public static void deleteTempFile(File file) {
        if (file != null) {
            File del = new File(file.toURI());
            del.delete();
        }
    }

    //判断字符串是不是正确日期格式
    public static boolean isValidDate(String str) {
        boolean convertSuccess = true;
        // 指定日期格式为四位年/两位月份/两位日期，注意yyyy/MM/dd区分大小写；
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        try {
            // 设置lenient为false. 否则SimpleDateFormat会比较宽松地验证日期，比如2007/02/29会被接受，并转换成2007/03/01
            format.setLenient(false);
            format.parse(str);
        } catch (ParseException e) {
            // e.printStackTrace();
            // 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
            convertSuccess = false;
        }
        return convertSuccess;
    }


    //检查并修正日期
    public static String checkAndCorrectDate(String writeDate) {
        Calendar cal = Calendar.getInstance();
        int min_now = cal.get(Calendar.MINUTE);//分
        int hour_now = cal.get(Calendar.HOUR_OF_DAY);//小时
        int day_now = cal.get(Calendar.DATE);//获取日
        int month_now = cal.get(Calendar.MONTH) + 1;//获取月份
        int year_now = cal.get(Calendar.YEAR);//获取年份
        //获取最近过去的5分钟
        int last5Min = min_now - min_now % 5;
        //若日期不合法的处理
        if (!isValidDate(writeDate)) {
            //日期修正为前5分钟
            writeDate = year_now + "-" + month_now + "-" + day_now + " " + hour_now + ":" + last5Min + ":01";
        } else {
            Calendar cal2 = String2Calendar(writeDate);
            int day = cal2.get(Calendar.DATE);//获取日
            int month = cal2.get(Calendar.MONTH) + 1;//获取月份
            int year = cal2.get(Calendar.YEAR);//获取年份

            if (Math.abs(year_now - year) > 0 || Math.abs(month_now - month) > 0 || Math.abs(day_now - day) > 0) {
                //日期修正为前5分钟
                writeDate = year_now + "-" + month_now + "-" + day_now + " " + hour_now + ":" + last5Min + ":01";
            }

        }
        return writeDate;
    }

    //判断是否是未来日期（交调用）
    public static boolean isFutureDate(String writeDate) {

        if (!isValidDate(writeDate))
            return true;

        Calendar cal = String2Calendar(writeDate);
        int day = cal.get(Calendar.DATE);//获取日
        int month = cal.get(Calendar.MONTH) + 1;//获取月份
        int year = cal.get(Calendar.YEAR);//获取年份

        Calendar now = Calendar.getInstance();
        int day_now = now.get(Calendar.DATE);//获取日
        int month_now = now.get(Calendar.MONTH) + 1;//获取月份
        int year_now = now.get(Calendar.YEAR);//获取年份
        //大于3天的
        if (year - year_now > 0 || month - month_now > 0 || day - day_now > 3) {
            return true;
        }
        return false;
    }

    public static Calendar String2Calendar(String time) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        Date date = null;
        try {
            date = sdf.parse(time);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar;
    }

    public static String getNowTime() {
        Date date = new Date();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String time = df.format(new Date());
        return time;
    }

    public boolean isRightTrafficData(VdTrafficCarType v) {
        //不合法的速度
        if (v.getZx_car_speed() > 500 || v.getZx_car_speed() < 0) {
            return false;
        }
        //不合法的流量
        if (v.getZx_car() > 10000 || v.getZx_car() < 0) {
            return false;
        }
        if (v.getX_truck_speed() > 500 || v.getX_truck_speed() < 0) {
            return false;
        }
        if (v.getX_truck() > 10000 || v.getX_truck() < 0) {
            return false;
        }
        if (v.getZ_truck_speed() > 500 || v.getZ_truck_speed() < 0) {
            return false;
        }
        if (v.getZ_truck() > 10000 || v.getZ_truck() < 0) {
            return false;
        }
        if (v.getD_car_speed() > 500 || v.getD_car_speed() < 0) {
            return false;
        }
        if (v.getD_car() > 10000 || v.getD_car() < 0) {
            return false;
        }
        if (v.getD_truck_speed() > 500 || v.getD_truck_speed() < 0) {
            return false;
        }
        if (v.getD_truck() > 10000 || v.getD_truck() < 0) {
            return false;
        }
        if (v.getTd_truck_speed() > 500 || v.getTd_truck_speed() < 0) {
            return false;
        }
        if (v.getTd_truck() > 10000 || v.getTd_truck() < 0) {
            return false;
        }
        if (v.getJzx_car_speed() > 500 || v.getJzx_car_speed() < 0) {
            return false;
        }
        if (v.getJzx_car() > 10000 || v.getJzx_car() < 0) {
            return false;
        }
        if (v.getTlj_car_speed() > 500 || v.getTlj_car_speed() < 0) {
            return false;
        }
        if (v.getTlj_car() > 10000 || v.getTlj_car() < 0) {
            return false;
        }
        if (v.getMoto_car_speed() > 500 || v.getMoto_car_speed() < 0) {
            return false;
        }
        if (v.getMoto_car() > 10000 || v.getMoto_car() < 0) {
            return false;
        }
        return true;
    }


}
