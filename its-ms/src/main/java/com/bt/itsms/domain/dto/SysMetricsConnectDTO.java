package com.bt.itsms.domain.dto;

/**
 * <AUTHOR>
 * @date 2022年11月10日 下午5:11:12
 * @Description 服务之间连接指标DTO
 */
public class SysMetricsConnectDTO {
	private String serviceName;
	private String serviceIp;
	private String targetServiceName;
	private String targetServiceIp;
	private Integer connectStatus;
	private String sourceName;
	private String sourceTime;
	private String createTime;

	public String getServiceName() {
		return serviceName;
	}

	public void setServiceName(String serviceName) {
		this.serviceName = serviceName;
	}

	public String getServiceIp() {
		return serviceIp;
	}

	public void setServiceIp(String serviceIp) {
		this.serviceIp = serviceIp;
	}

	public String getTargetServiceName() {
		return targetServiceName;
	}

	public void setTargetServiceName(String targetServiceName) {
		this.targetServiceName = targetServiceName;
	}

	public String getTargetServiceIp() {
		return targetServiceIp;
	}

	public void setTargetServiceIp(String targetServiceIp) {
		this.targetServiceIp = targetServiceIp;
	}

	public Integer getConnectStatus() {
		return connectStatus;
	}

	public void setConnectStatus(Integer connectStatus) {
		this.connectStatus = connectStatus;
	}

	public String getSourceName() {
		return sourceName;
	}

	public void setSourceName(String sourceName) {
		this.sourceName = sourceName;
	}

	public String getSourceTime() {
		return sourceTime;
	}

	public void setSourceTime(String sourceTime) {
		this.sourceTime = sourceTime;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

}
