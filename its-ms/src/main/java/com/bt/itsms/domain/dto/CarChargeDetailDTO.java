package com.bt.itsms.domain.dto;

/**
 * 
 * @Description: 管辖路段今日收费站出口收费额
 * @author: NingYiQiang
 * @date: 2021年10月15日 下午2:17:38
 */
public class CarChargeDetailDTO {
	private String id;// ID
	private String tollStationId; // 站代码
	private Double cashFee;// 现金收费，单位：元
	private Double otherFee;//其他收费，单位：元
	private Double totalFee;// 统计日对应收费站总通行费，单位：元
	private Integer totalCount;// 总通行车流量
	private String createTime; // 统计时间
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getTollStationId() {
		return tollStationId;
	}
	public void setTollStationId(String tollStationId) {
		this.tollStationId = tollStationId;
	}
	
	public Double getTotalFee() {
		return totalFee;
	}
	public void setTotalFee(Double totalFee) {
		this.totalFee = totalFee;
	}
	public String getCreateTime() {
		return createTime;
	}
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}
	public Double getCashFee() {
		return cashFee;
	}
	public void setCashFee(Double cashFee) {
		this.cashFee = cashFee;
	}
	public Double getOtherFee() {
		return otherFee;
	}
	public void setOtherFee(Double otherFee) {
		this.otherFee = otherFee;
	}
	public Integer getTotalCount() {
		return totalCount;
	}
	public void setTotalCount(Integer totalCount) {
		this.totalCount = totalCount;
	}

	

}
