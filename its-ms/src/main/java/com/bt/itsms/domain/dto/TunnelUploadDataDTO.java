package com.bt.itsms.domain.dto;

import java.util.ArrayList;
import java.util.List;



public class TunnelUploadDataDTO {
	private String authSecret;// 授权秘钥
	private String timestamp;// 上传数据时间戳
	private String uploadType;// 上传类型
	private List<TunnelDataDTO> dataList = new ArrayList<TunnelDataDTO>(); // 上传数据集合
	public String getUploadType() {
		return uploadType;
	}
	public void setUploadType(String uploadType) {
		this.uploadType = uploadType;
	}
	public List<TunnelDataDTO> getDataList() {
		return dataList;
	}
	public void setDataList(List<TunnelDataDTO> dataList) {
		this.dataList = dataList;
	}
	public String getAuthSecret() {
		return authSecret;
	}
	public void setAuthSecret(String authSecret) {
		this.authSecret = authSecret;
	}
	public String getTimestamp() {
		return timestamp;
	}
	public void setTimestamp(String timestamp) {
		this.timestamp = timestamp;
	}
	
}
