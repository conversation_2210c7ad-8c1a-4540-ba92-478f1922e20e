package com.bt.itsms.domain.dto;

/**
 * <AUTHOR>
 * @date 2022年9月14日 下午2:46:54
 * @Description ITS气象设备下发数据
 */
public class ItsQiXiangDTO {
	private String windSpeed;//风速，单位为m/s，0.09
	private String windDirection;//风向,单位为北偏东{{ windDirection }}度，315
	private String airTemp;//空气温度，单位为度，26.3
	private String airHumid;//空气湿度,单位为%，61.8
	private String pressure;//气压，单位为kPa，100.12
	private String rainFall;//雨量，单位为mm，0.50
	private String intensity;//光照强度,单位为Lux，271w
	private String CCID;//SIM卡序列号，898600B72021C0281992
	private String sourceId;//来源标识，ITS
	private String msgType;//消息类型，weather
	private String id;//设备唯一标识
	private String ctime;//时间
	
	public String getWindSpeed() {
		return windSpeed;
	}
	public void setWindSpeed(String windSpeed) {
		this.windSpeed = windSpeed;
	}
	public String getWindDirection() {
		return windDirection;
	}
	public void setWindDirection(String windDirection) {
		this.windDirection = windDirection;
	}
	public String getAirTemp() {
		return airTemp;
	}
	public void setAirTemp(String airTemp) {
		this.airTemp = airTemp;
	}
	public String getAirHumid() {
		return airHumid;
	}
	public void setAirHumid(String airHumid) {
		this.airHumid = airHumid;
	}
	public String getPressure() {
		return pressure;
	}
	public void setPressure(String pressure) {
		this.pressure = pressure;
	}
	public String getRainFall() {
		return rainFall;
	}
	public void setRainFall(String rainFall) {
		this.rainFall = rainFall;
	}
	public String getIntensity() {
		return intensity;
	}
	public void setIntensity(String intensity) {
		this.intensity = intensity;
	}
	public String getCCID() {
		return CCID;
	}
	public void setCCID(String cCID) {
		CCID = cCID;
	}
	public String getSourceId() {
		return sourceId;
	}
	public void setSourceId(String sourceId) {
		this.sourceId = sourceId;
	}
	public String getMsgType() {
		return msgType;
	}
	public void setMsgType(String msgType) {
		this.msgType = msgType;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getCtime() {
		return ctime;
	}
	public void setCtime(String ctime) {
		this.ctime = ctime;
	}
}
//{
//"did": "ITS_QiXiang_00000001",
//"Utime": "22/09/20,16:22:44+32",
//"content": {
//"windSpeed": "0.14",
//"windDirection": "289",
//"airTemp": "26.9",
//"airHumid": "62.5",
//"pressure": "99.66",
//"rainFall": "0.50",
//"intensity": "225"
//}
//}

