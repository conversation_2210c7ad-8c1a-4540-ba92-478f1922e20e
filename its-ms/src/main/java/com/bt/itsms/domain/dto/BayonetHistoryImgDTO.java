package com.bt.itsms.domain.dto;

import java.util.List;

public class BayonetHistoryImgDTO {
    private String id;
    private Integer entryFlag;
    private List<String> imgUrls;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getEntryFlag() {
        return entryFlag;
    }

    public void setEntryFlag(Integer entryFlag) {
        this.entryFlag = entryFlag;
    }

    public List<String> getImgUrls() {
        return imgUrls;
    }

    public void setImgUrls(List<String> imgUrls) {
        this.imgUrls = imgUrls;
    }
}
