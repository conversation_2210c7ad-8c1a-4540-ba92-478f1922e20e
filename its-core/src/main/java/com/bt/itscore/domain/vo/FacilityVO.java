package com.bt.itscore.domain.vo;

import java.io.Serializable;
import java.util.List;

/**
 * 所有设施的公用属性
 *
 */
public class FacilityVO implements Serializable {

	private static final long serialVersionUID = 1L;
	private String facilityNo;// int(11) default null,
	private String facilityName;// 设施名称 varchar(100) default null,
	private Integer roadNo;// 路段号-关联road表 int(11) default null,
	private Integer facilityTypeNo;// 设施类型no int(11) default null,
	private String milePost;// 桩号 varchar(50) default null,
	private String upFacilityNo;// 上一级设施no int(11) default null, 上一级 设施NO （虚拟道路）
	private Integer mpValue;// 桩号的值 double(10,3) default null,
	private Integer sort;// int(11) default null,
	private String lng;// 经度 float default null,
	private String lat;// 纬度 float default null,
	private String facilityCode;// varchar(100) default null
	private String facilityTypeName;// int(11) default null,
	private String directionNo;//
	private String directionName;//
	private String roadName;
	private String roadAlias;
	private Integer computeSwitch;// 计算经纬度开关，1计算，0不计算
	private Integer upFacilityTypeNo; // 上级设施的typeNo
	private List<FacilityAttachVO> attachs;
	private List<FacilityChargeStationVO> chargeStations;

	public String getFacilityNo() {
		return facilityNo;
	}

	public void setFacilityNo(String facilityNo) {
		this.facilityNo = facilityNo;
	}

	public String getFacilityName() {
		return facilityName;
	}

	public void setFacilityName(String facilityName) {
		this.facilityName = facilityName;
	}

	public Integer getRoadNo() {
		return roadNo;
	}

	public void setRoadNo(Integer roadNo) {
		this.roadNo = roadNo;
	}

	public Integer getFacilityTypeNo() {
		return facilityTypeNo;
	}

	public void setFacilityTypeNo(Integer facilityTypeNo) {
		this.facilityTypeNo = facilityTypeNo;
	}

	public String getMilePost() {
		return milePost;
	}

	public void setMilePost(String milePost) {
		this.milePost = milePost;
	}

	public String getUpFacilityNo() {
		return upFacilityNo;
	}

	public void setUpFacilityNo(String upFacilityNo) {
		this.upFacilityNo = upFacilityNo;
	}

	public Integer getMpValue() {
		return mpValue;
	}

	public void setMpValue(Integer mpValue) {
		this.mpValue = mpValue;
	}

	public Integer getSort() {
		return sort;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}

	public String getLng() {
		return lng;
	}

	public void setLng(String lng) {
		this.lng = lng;
	}

	public String getLat() {
		return lat;
	}

	public void setLat(String lat) {
		this.lat = lat;
	}

	public String getFacilityCode() {
		return facilityCode;
	}

	public void setFacilityCode(String facilityCode) {
		this.facilityCode = facilityCode;
	}

	public String getFacilityTypeName() {
		return facilityTypeName;
	}

	public void setFacilityTypeName(String facilityTypeName) {
		this.facilityTypeName = facilityTypeName;
	}

	public String getDirectionNo() {
		return directionNo;
	}

	public void setDirectionNo(String directionNo) {
		this.directionNo = directionNo;
	}

	public String getDirectionName() {
		return directionName;
	}

	public void setDirectionName(String directionName) {
		this.directionName = directionName;
	}

	public String getRoadName() {
		return roadName;
	}

	public void setRoadName(String roadName) {
		this.roadName = roadName;
	}

	public Integer getComputeSwitch() {
		return computeSwitch;
	}

	public void setComputeSwitch(Integer computeSwitch) {
		this.computeSwitch = computeSwitch;
	}

	public List<FacilityAttachVO> getAttachs() {
		return attachs;
	}

	public void setAttachs(List<FacilityAttachVO> attachs) {
		this.attachs = attachs;
	}

	public Integer getUpFacilityTypeNo() {
		return upFacilityTypeNo;
	}

	public void setUpFacilityTypeNo(Integer upFacilityTypeNo) {
		this.upFacilityTypeNo = upFacilityTypeNo;
	}

	public String getRoadAlias() {
		return roadAlias;
	}

	public void setRoadAlias(String roadAlias) {
		this.roadAlias = roadAlias;
	}

	public List<FacilityChargeStationVO> getChargeStations() {
		return chargeStations;
	}

	public void setChargeStations(List<FacilityChargeStationVO> chargeStations) {
		this.chargeStations = chargeStations;
	}

}
