package com.bt.itspbx.controller;

import java.util.Arrays;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bt.itscore.auth.Login;
import com.bt.itscore.domain.dto.PageDTO;
import com.bt.itscore.domain.vo.PageVO;
import com.bt.itscore.domain.vo.ResponseVO;
import com.bt.itscore.exception.AuthzException;
import com.bt.itscore.utils.ValidUtils;
import com.bt.itspbx.component.CommonCache;
import com.bt.itspbx.domain.dto.DevicePbxDTO;
import com.bt.itspbx.domain.dto.ParamsDTO;
import com.bt.itspbx.domain.dto.TreeDTO;
import com.bt.itspbx.service.DevicePbxService;
import com.bt.itspbx.service.EmergencyTelService;
import com.bt.itspbx.service.NatsDeviceService;
import com.bt.itspbx.service.SoftphoneService;
import com.bt.itspbx.service.TalkBackService;
import com.bt.itspbx.service.TalkCardService;

/**
 * 
 * @Description: 设备管理后台（包括紧急电话、软件电话、亭内对讲）
 * <AUTHOR>
 * @date 2023年10月19日 上午11:17:51
 *
 */

@RestController
@RequestMapping("pbx")
public class DevicePbxController {
	@Autowired
	private DevicePbxService devicePbxService;
	@Autowired
	private SoftphoneService softphoneService; // 软件电话
	@Autowired
	private EmergencyTelService emergencyTelService; // 紧急电话
	@Autowired
	private TalkBackService talkBackService; // 亭内对讲系统
	@Autowired
	private TalkCardService talkCardService; // 发卡机系统
	@Autowired
	private NatsDeviceService natsDeviceService;

	/**
	 * @api {POST} /pbx/selectSoftphoneList 获取当前登录用户注册的软电话信息 /pbx/selectSoftphoneList
	 * @apiDescription 获取当前登录用户注册的软电话信息；创建人：宁艺强，修改人：无
	 * @apiGroup DevicePbxController-包括紧急电话、软件电话、亭内对讲
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam {String} userId 用户id
	 * @apiParamExample {json} Request-Example: 
	 * 		{ 
	 * 			"userId":"d09ef18d-9bd3-4d8e-bc33-3bdcdb47cfdb"
	 * 		}
	 * @apiSuccess (Success 200) {String} deviceTypeNo 12-监控中心软电话 5-隧道内紧急电话
	 * @apiSuccess (Success 200) {String} deviceId 软电话ID
	 * @apiSuccess (Success 200) {String} deviceCode 设备编号（软电话SIP注册账号）
	 * @apiSuccess (Success 200) {String} deviceTypeNo 12-监控中心软电话,5-隧道内紧急电话
	 * @apiSuccess (Success 200) {String} deviceName 软电话名称
	 * @apiSuccess (Success 200) {String} userName 软电话SIP注册账号
	 * @apiSuccess (Success 200) {String} password 软电话SIP注册密码
	 * @apiSuccess (Success 200) {String} ipAddress 软电话SIP注册IP
	 * @apiSuccess (Success 200) {String} sipCode 所属SIP服务节点编号
	 * @apiSuccess (Success 200) {String} sipName sip服务名称
	 * @apiSuccess (Success 200) {Integer} type SIP节点类型 1-路段节点 0-上云节点
	 * @apiSuccess (Success 200) {String} entityId SIP节点唯一标识，通常是绑定IP的MAC地址
	 * @apiSuccess (Success 200) {String} hostName SIP节点主机名称
	 * @apiSuccess (Success 200) {String} publicAddress  SIP节点提供SIP服务的公网访问地址，可以存在多个，以逗号隔开，例如***************:5060,***************:5060
	 * @apiSuccess (Success 200) {String} privateAddress SIP节点提供SIP服务的内网访问地址，可以存在多个，以逗号隔开，例如*************:5060,*************:5060
	 * @apiSuccess (Success 200) {String} hostName SIP节点主机名称
	 * @apiSuccess (Success 200) {String} domainName 内网域名
	 * @apiSuccess (Success 200) {String} port 端口号
	 * @apiSuccess (Success 200) {String} milePost 桩号
	 * @apiSuccess (Success 200) {String} directionNo 方向id
	 * @apiSuccess (Success 200) {String} directionName 方向名称
	 * @apiSuccess (Success 200) {String} facilityNo 所属设施
	 * @apiSuccess (Success 200) {String} facilityName 所属设施名称
	 * @apiSuccess (Success 200) {String} roadNo 所属路段id
	 * @apiSuccess (Success 200) {String} roadName 所属路段名称
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 
	 * 	[
	 * 
	 *		{ 
	 * 			"deviceTypeNo": "12",
	 * 			"deviceId": "1f866219-0c9b-408c-a6b1-a8a82629c480",
	 * 			"deviceCode": "6001",
	 * 			"deviceName": "交科ITS软电话01",
	 * 			"deviceTypeNo": "12",
	 *  		"userName": "6001",
	 *  		"password": "admin",
	 *  		"ipAddress": "************",
	 *  		"sipCode": "00",
	 *  		"sipName": "上云节点-中心节点",
	 *  		"type": 0,
	 *  		"entityId": "10:io:10:10",
	 *  		"hostName": "上云节点",
	 *  		"publicAddress": "*************:5060,*************:5060",
	 *  		"privateAddress": "*************:5060,*************:5060",
	 *  		"port": null
	 *   		"milePost": null,
	 *    		"directionNo": null,
	 *     		"directionName": null,
	 *      	"facilityNo": null,
	 *       	"facilityName": null,
	 *          "roadNo": null,
	 *          "roadName": null
	 *   	}
	 *	]
	 * @apiErrorExample {json} Success-Response: HTTP/1.1 200 OK code=401用户未登录状态 
	 * 		{ 
	 * 			"code: 401 
	 * 		}
	 * @apiSampleRequest /pbx/selectSoftphoneList
	 */
	@Login
	@PostMapping("selectSoftphoneList")
	public Object selectSoftphoneList(@RequestBody ParamsDTO dto, HttpServletRequest request) {
		if (StringUtils.isBlank(dto.getUserId())) {
			dto.setUserId(getUserId(request));
		}
		return softphoneService.selectSoftphoneList(dto);
	}

	/**
	 * @api {POST} /pbx/selectDevicePbx  查询SIP电话详细信息 /pbx/selectDevicePbx
	 * @apiDescription 查询SIP电话详细信息；创建人：宁艺强，修改人：无
	 * @apiGroup DevicePbxController-包括紧急电话、软件电话、亭内对讲
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam {String} userId 用户id
	 * @apiParamExample {json} Request-Example: 
	 * 		{ 
	 * 			"deviceId":"1f866219-0c9b-408c-a6b1-a8a82629c480",
	 * 			"deviceCode":"1310149"
	 * 		}
	 * @apiSuccess (Success 200) {String} deviceTypeNo 12-监控中心软电话 5-隧道内紧急电话
	 * @apiSuccess (Success 200) {String} deviceId 软电话ID
	 * @apiSuccess (Success 200) {String} deviceCode 设备编号（软电话SIP注册账号）
	 * @apiSuccess (Success 200) {String} deviceTypeNo 12-监控中心软电话,5-隧道内紧急电话
	 * @apiSuccess (Success 200) {String} deviceName 软电话名称
	 * @apiSuccess (Success 200) {String} userName 软电话SIP注册账号
	 * @apiSuccess (Success 200) {String} ipAddress 软电话SIP注册IP
	 * @apiSuccess (Success 200) {String} sipCode 所属SIP服务节点编号
	 * @apiSuccess (Success 200) {String} sipName sip服务名称
	 * @apiSuccess (Success 200) {Integer} type SIP节点类型 1-路段节点 0-上云节点
	 * @apiSuccess (Success 200) {String} entityId SIP节点唯一标识，通常是绑定IP的MAC地址
	 * @apiSuccess (Success 200) {String} hostName SIP节点主机名称
	 * @apiSuccess (Success 200) {String} publicAddress  SIP节点提供SIP服务的公网访问地址，可以存在多个，以逗号隔开，例如***************:5060,***************:5060
	 * @apiSuccess (Success 200) {String} privateAddress SIP节点提供SIP服务的内网访问地址，可以存在多个，以逗号隔开，例如*************:5060,*************:5060
	 * @apiSuccess (Success 200) {String} hostName SIP节点主机名称
	 * @apiSuccess (Success 200) {String} domainName 内网域名
	 * @apiSuccess (Success 200) {String} port 端口号
	 * @apiSuccess (Success 200) {String} milePost 桩号
	 * @apiSuccess (Success 200) {String} directionNo 方向id
	 * @apiSuccess (Success 200) {String} directionName 方向名称
	 * @apiSuccess (Success 200) {String} facilityNo 所属设施
	 * @apiSuccess (Success 200) {String} facilityName 所属设施名称
	 * @apiSuccess (Success 200) {String} roadNo 所属路段id
	 * @apiSuccess (Success 200) {String} roadName 所属路段名称
	 * @apiSuccess (Success 200) {Integer} webrtc (1-开启 0-未开启)开启webrtc
	 * @apiSuccess (Success 200) {Integer} iceSupport (1-开启 0-未开启)开启ice
	 * @apiSuccess (Success 200) {String} webUserName 网页配置登录电话用户
	 * @apiSuccess (Success 200) {String} webPassword 网页配置登录电话密码
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 
	 *		{ 
	 * 			"deviceTypeNo": "12",
	 * 			"deviceId": "1f866219-0c9b-408c-a6b1-a8a82629c480",
	 * 			"deviceCode": "6001",
	 * 			"deviceName": "交科ITS软电话01",
	 * 			"deviceTypeNo": "12",
	 *  		"userName": "6001",
	 *  		"password": "admin",
	 *  		"ipAddress": "************",
	 *  		"sipCode": "00",
	 *  		"sipName": "上云节点-中心节点",
	 *  		"type": 0,
	 *  		"entityId": "10:io:10:10",
	 *  		"hostName": "上云节点",
	 *  		"publicAddress": "*************:5060,*************:5060",
	 *  		"privateAddress": "*************:5060,*************:5060",
	 *  		"port": null,
	 *   		"milePost": null,
	 *    		"directionNo": null,
	 *     		"directionName": null,
	 *      	"facilityNo": null,
	 *       	"facilityName": null,
	 *       	"webrtc":0,
	 *      	"iceSupport": 0,
	 *      	"webUserName":null,
	 *      	"webPassword":null,
	 *          "roadNo": null,
	 *          "roadName": null
	 *   	}
	 * @apiErrorExample {json} Success-Response: HTTP/1.1 200 OK code=401用户未登录状态 
	 * 		{ 
	 * 			"code: 401 
	 * 		}
	 * @apiSampleRequest /pbx/selectDevicePbx
	 */
	@Login
	@PostMapping("selectDevicePbx")
	public Object selectDevicePbx(@RequestBody ParamsDTO dto, HttpServletRequest request) {
		if (StringUtils.isBlank(dto.getDeviceId()) && StringUtils.isBlank(dto.getDeviceCode())) {
			return new ResponseVO("查询明细信息的设备deviceId或设备编码不能全为空！", 400);
		}
		return devicePbxService.selectDevicePbx(dto);
	}

	/**
	 * @api {POST} /pbx/selectSoftphone 查询软件电话详细信息 /pbx/selectSoftphone
	 * @apiDescription 查询软件电话详细信息；创建人：宁艺强，修改人：无
	 * @apiGroup DevicePbxController-包括紧急电话、软件电话、亭内对讲
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam {String} userId 用户id
	 * @apiParamExample {json} Request-Example: 
	 * 		{ 
	 * 			"deviceId":"1f866219-0c9b-408c-a6b1-a8a82629c480",
	 * 			"deviceCode":"1310149"
	 * 		}
	 * @apiSuccess (Success 200) {String} deviceTypeNo 12-监控中心软电话 5-隧道内紧急电话
	 * @apiSuccess (Success 200) {String} deviceId 软电话ID
	 * @apiSuccess (Success 200) {String} deviceCode 设备编号（软电话SIP注册账号）
	 * @apiSuccess (Success 200) {String} deviceTypeNo 12-监控中心软电话,5-隧道内紧急电话
	 * @apiSuccess (Success 200) {String} deviceName 软电话名称
	 * @apiSuccess (Success 200) {String} userName 软电话SIP注册账号
	 * @apiSuccess (Success 200) {String} ipAddress 软电话SIP注册IP
	 * @apiSuccess (Success 200) {String} sipCode 所属SIP服务节点编号
	 * @apiSuccess (Success 200) {String} sipName sip服务名称
	 * @apiSuccess (Success 200) {Integer} type SIP节点类型 1-路段节点 0-上云节点
	 * @apiSuccess (Success 200) {String} entityId SIP节点唯一标识，通常是绑定IP的MAC地址
	 * @apiSuccess (Success 200) {String} hostName SIP节点主机名称
	 * @apiSuccess (Success 200) {String} publicAddress  SIP节点提供SIP服务的公网访问地址，可以存在多个，以逗号隔开，例如***************:5060,***************:5060
	 * @apiSuccess (Success 200) {String} privateAddress SIP节点提供SIP服务的内网访问地址，可以存在多个，以逗号隔开，例如*************:5060,*************:5060
	 * @apiSuccess (Success 200) {String} hostName SIP节点主机名称
	 * @apiSuccess (Success 200) {String} domainName 内网域名
	 * @apiSuccess (Success 200) {String} port 端口号
	 * @apiSuccess (Success 200) {String} milePost 桩号
	 * @apiSuccess (Success 200) {String} directionNo 方向id
	 * @apiSuccess (Success 200) {String} directionName 方向名称
	 * @apiSuccess (Success 200) {String} facilityNo 所属设施
	 * @apiSuccess (Success 200) {String} facilityName 所属设施名称
	 * @apiSuccess (Success 200) {String} roadNo 所属路段id
	 * @apiSuccess (Success 200) {String} roadName 所属路段名称
	 * @apiSuccess (Success 200) {Integer} webrtc (1-开启 0-未开启)开启webrtc
	 * @apiSuccess (Success 200) {Integer} iceSupport (1-开启 0-未开启)开启ice
	 * @apiSuccess (Success 200) {String} webUserName 网页配置登录电话用户
	 * @apiSuccess (Success 200) {String} webPassword 网页配置登录电话密码
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 
	 *		{ 
	 * 			"deviceTypeNo": "12",
	 * 			"deviceId": "1f866219-0c9b-408c-a6b1-a8a82629c480",
	 * 			"deviceCode": "6001",
	 * 			"deviceName": "交科ITS软电话01",
	 * 			"deviceTypeNo": "12",
	 *  		"userName": "6001",
	 *  		"password": "admin",
	 *  		"ipAddress": "************",
	 *  		"sipCode": "00",
	 *  		"sipName": "上云节点-中心节点",
	 *  		"type": 0,
	 *  		"entityId": "10:io:10:10",
	 *  		"hostName": "上云节点",
	 *  		"publicAddress": "*************:5060,*************:5060",
	 *  		"privateAddress": "*************:5060,*************:5060",
	 *  		"port": null,
	 *   		"milePost": null,
	 *    		"directionNo": null,
	 *     		"directionName": null,
	 *      	"facilityNo": null,
	 *       	"facilityName": null,
	 *       	"webrtc":0,
	 *      	"iceSupport": 0,
	 *      	"webUserName":null,
	 *      	"webPassword":null,
	 *          "roadNo": null,
	 *          "roadName": null
	 *   	}
	 * @apiErrorExample {json} Success-Response: HTTP/1.1 200 OK code=401用户未登录状态 
	 * 		{ 
	 * 			"code: 401 
	 * 		}
	 * @apiSampleRequest /pbx/selectSoftphone
	 */
	@Login
	@PostMapping("selectSoftphone")
	public Object selectSoftphone(@RequestBody ParamsDTO dto, HttpServletRequest request) {
		if (StringUtils.isBlank(dto.getDeviceId()) && StringUtils.isBlank(dto.getDeviceCode())) {
			return new ResponseVO("查询明细信息的设备deviceId或设备编码不能全为空！", 400);
		}
		dto.setType(3);// 软件电话
		return devicePbxService.selectDevicePbx(dto);
	}

	/**
	 * @api {POST} /pbx/selectEmergencyTel 查询隧道紧急电话详细信息 /pbx/selectEmergencyTel
	 * @apiDescription 查询隧道紧急电话详细信息；创建人：宁艺强，修改人：无
	 * @apiGroup DevicePbxController-包括紧急电话、软件电话、亭内对讲
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam {String} userId 用户id
	 * @apiParamExample {json} Request-Example: 
	 * 		{ 
	 * 			"deviceId":"1f866219-0c9b-408c-a6b1-a8a82629c480",
	 * 			"deviceCode":"1310149"
	 * 		}
	 * @apiSuccess (Success 200) {String} deviceId 隧道紧急电话ID
	 * @apiSuccess (Success 200) {String} deviceCode 紧急电话隧道设备编号(自研SIP对应注册编号)
	 * @apiSuccess (Success 200) {String} otherCode 其他编号，捷赛系统编号
	 * @apiSuccess (Success 200) {String} deviceTypeNo 12-监控中心软电话 5-隧道内紧急电话
	 * @apiSuccess (Success 200) {String} deviceName 紧急电话名称
	 * @apiSuccess (Success 200) {String} deviceShortName 设备简称
	 * @apiSuccess (Success 200) {String} userName SIP系统注册登录账号
	 * @apiSuccess (Success 200) {String} password SIP系统注册登录密码
	 * @apiSuccess (Success 200) {String} ipAddress 紧急电话IP地址
	 * @apiSuccess (Success 200) {String} milePost 桩号
	 * @apiSuccess (Success 200) {String} directionNo 方向id
	 * @apiSuccess (Success 200) {String} directionName 方向名称
	 * @apiSuccess (Success 200) {String} facilityNo 所属设施
	 * @apiSuccess (Success 200) {String} facilityName 所属设施名称
	 * @apiSuccess (Success 200) {String} roadNo 所属路段id
	 * @apiSuccess (Success 200) {String} roadName 所属路段名称
	 * @apiSuccess (Success 200) {String} sipCode 所属SIP服务节点编号
	 * @apiSuccess (Success 200) {String} sipName sip服务名称
	 * @apiSuccess (Success 200) {Integer} type 1-紧急电话，2-亭内对讲
	 * @apiSuccess (Success 200) {String} entityId SIP节点唯一标识，通常是绑定IP的MAC地址
	 * @apiSuccess (Success 200) {String} hostName SIP节点主机名称
	 * @apiSuccess (Success 200) {String} publicAddress  SIP节点提供SIP服务的公网访问地址，可以存在多个，以逗号隔开，例如***************:5060,***************:5060
	 * @apiSuccess (Success 200) {String} privateAddress SIP节点提供SIP服务的内网访问地址，可以存在多个，以逗号隔开，例如*************:5060,*************:5060
	 * @apiSuccess (Success 200) {String} hostName SIP节点主机名称
	 * @apiSuccess (Success 200) {String} domainName 内网域名
	 * @apiSuccess (Success 200) {String} port 端口号
	 * @apiSuccess (Success 200) {Integer} webrtc (1-开启 0-未开启)开启webrtc
	 * @apiSuccess (Success 200) {Integer} iceSupport (1-开启 0-未开启)开启ice
	 * @apiSuccess (Success 200) {String} webUserName 网页配置登录电话用户
	 * @apiSuccess (Success 200) {String} webPassword 网页配置登录电话密码
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 
	 *		{ 
	 * 			"deviceId": "001f3c2a-793b-4b92-aeb9-96a8c3de6da1",
	 * 			"deviceCode": "1310149",
	 * 			"otherCode": "10803",
	 * 			"deviceTypeNo": "5",
	 * 			"deviceName": "隧道外-都安端-右洞-紧急电话01#",
	 * 			"deviceShortName": null,
	 *  		"userName": "admin",
	 *  		"password": "admin",
	 *  		"ipAddress": "************",
	 *  		"milePost": "K458+148",
	 *   		"directionNo": "eb474098-025d-49ad-8d69-b577741283d0",
	 *   		"directionName": "巴马往平果(上行)",
	 *   		"facilityNo": "f0181e83-9858-460b-845c-992d3ecdd97d",
	 *   		"facilityName": "光明山隧道",
	 *   		"roadNo": "216",
	 *   		"roadName": "G7522贵北高速(巴平路)",
	 *  		"sipCode": "13",
	 *  		"sipName": "大化SIP节点",
	 *  		"type": 1,
	 *  		"entityId": "10:io:10:10",
	 *  		"hostName": "大化节点",
	 *  		"publicAddress": "*************:5060,*************:5060",
	 *  		"privateAddress": "*************:5060,*************:5060",
	 *  		"domainName": null,
	 *  		"port": null
	 *   	}
	 * @apiErrorExample {json} Success-Response: HTTP/1.1 200 OK code=401用户未登录状态 
	 * 		{ 
	 * 			"code: 401 
	 * 		}
	 * @apiSampleRequest /pbx/selectEmergencyTel
	 */
	@Login
	@PostMapping("selectEmergencyTel")
	public Object selectEmergencyTel(@RequestBody ParamsDTO dto, HttpServletRequest request) {
		if (StringUtils.isBlank(dto.getDeviceId()) && StringUtils.isBlank(dto.getDeviceCode())) {
			return new ResponseVO("查询明细信息的设备deviceId或设备编码不能全为空！", 400);
		}
		dto.setType(1);// 软件电话
		return devicePbxService.selectDevicePbx(dto);
	}

	/**
	 * @api {POST} /pbx/selectTalkBack 查询亭内对讲详细信息 /pbx/selectTalkBack
	 * @apiDescription 查询亭内对讲详细信息；创建人：宁艺强，修改人：无
	 * @apiGroup DevicePbxController-包括紧急电话、软件电话、亭内对讲
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam {String} userId 用户id
	 * @apiParamExample {json} Request-Example: 
	 * 		{ 
	 * 			"deviceId":"1f866219-0c9b-408c-a6b1-a8a82629c480",
	 * 			"deviceCode":"1310149"
	 * 		}
	 * @apiSuccess (Success 200) {String} deviceId 隧道紧急电话ID
	 * @apiSuccess (Success 200) {String} deviceCode 紧急电话隧道设备编号(自研SIP对应注册编号)
	 * @apiSuccess (Success 200) {String} otherCode 其他编号，捷赛系统编号
	 * @apiSuccess (Success 200) {String} deviceTypeNo 12-监控中心软电话 5-隧道内紧急电话
	 * @apiSuccess (Success 200) {String} deviceName 紧急电话名称
	 * @apiSuccess (Success 200) {String} deviceShortName 设备简称
	 * @apiSuccess (Success 200) {String} userName SIP系统注册登录账号
	 * @apiSuccess (Success 200) {String} password SIP系统注册登录密码
	 * @apiSuccess (Success 200) {String} ipAddress 紧急电话IP地址
	 * @apiSuccess (Success 200) {String} milePost 桩号
	 * @apiSuccess (Success 200) {String} directionNo 方向id
	 * @apiSuccess (Success 200) {String} directionName 方向名称
	 * @apiSuccess (Success 200) {String} facilityNo 所属设施
	 * @apiSuccess (Success 200) {String} facilityName 所属设施名称
	 * @apiSuccess (Success 200) {String} roadNo 所属路段id
	 * @apiSuccess (Success 200) {String} roadName 所属路段名称
	 * @apiSuccess (Success 200) {String} sipCode 所属SIP服务节点编号
	 * @apiSuccess (Success 200) {String} sipName sip服务名称
	 * @apiSuccess (Success 200) {Integer} type 1-紧急电话，2-亭内对讲
	 * @apiSuccess (Success 200) {String} entityId SIP节点唯一标识，通常是绑定IP的MAC地址
	 * @apiSuccess (Success 200) {String} hostName SIP节点主机名称
	 * @apiSuccess (Success 200) {String} publicAddress  SIP节点提供SIP服务的公网访问地址，可以存在多个，以逗号隔开，例如***************:5060,***************:5060
	 * @apiSuccess (Success 200) {String} privateAddress SIP节点提供SIP服务的内网访问地址，可以存在多个，以逗号隔开，例如*************:5060,*************:5060
	 * @apiSuccess (Success 200) {String} hostName SIP节点主机名称
	 * @apiSuccess (Success 200) {String} domainName 内网域名
	 * @apiSuccess (Success 200) {String} port 端口号
	 * @apiSuccess (Success 200) {Integer} webrtc (1-开启 0-未开启)开启webrtc
	 * @apiSuccess (Success 200) {Integer} iceSupport (1-开启 0-未开启)开启ice
	 * @apiSuccess (Success 200) {String} webUserName 网页配置登录电话用户
	 * @apiSuccess (Success 200) {String} webPassword 网页配置登录电话密码
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 
	 *		{ 
	 * 			"deviceId": "001f3c2a-793b-4b92-aeb9-96a8c3de6da1",
	 * 			"deviceCode": "1310149",
	 * 			"otherCode": "10803",
	 * 			"deviceTypeNo": "5",
	 * 			"deviceName": "隧道外-都安端-右洞-紧急电话01#",
	 * 			"deviceShortName": null,
	 *  		"userName": "admin",
	 *  		"password": "admin",
	 *  		"ipAddress": "************",
	 *  		"milePost": "K458+148",
	 *   		"directionNo": "eb474098-025d-49ad-8d69-b577741283d0",
	 *   		"directionName": "巴马往平果(上行)",
	 *   		"facilityNo": "f0181e83-9858-460b-845c-992d3ecdd97d",
	 *   		"facilityName": "光明山隧道",
	 *   		"roadNo": "216",
	 *   		"roadName": "G7522贵北高速(巴平路)",
	 *  		"sipCode": "13",
	 *  		"sipName": "大化SIP节点",
	 *  		"type": 1,
	 *  		"entityId": "10:io:10:10",
	 *  		"hostName": "大化节点",
	 *  		"publicAddress": "*************:5060,*************:5060",
	 *  		"privateAddress": "*************:5060,*************:5060",
	 *  		"domainName": null,
	 *  		"port": null
	 *   	}
	 * @apiErrorExample {json} Success-Response: HTTP/1.1 200 OK code=401用户未登录状态 
	 * 		{ 
	 * 			"code: 401 
	 * 		}
	 * @apiSampleRequest /pbx/selectTalkBack
	 */
	@Login
	@PostMapping("selectTalkBack")
	public Object selectTalkBack(@RequestBody ParamsDTO dto, HttpServletRequest request) {
		if (StringUtils.isBlank(dto.getDeviceId()) && StringUtils.isBlank(dto.getDeviceCode())) {
			return new ResponseVO("查询明细信息的设备deviceId或设备编码不能全为空！", 400);
		}
		dto.setType(2);// 查询亭内对讲详细
		return devicePbxService.selectDevicePbx(dto);
	}

	/**
	 * @描述 查询发卡机明细
	 * @param dto
	 * @param request
	 * @return
	 */
	@Login
	@PostMapping("selectTalkCard")
	public Object selectTalkCard(@RequestBody ParamsDTO dto, HttpServletRequest request) {
		if (StringUtils.isBlank(dto.getDeviceId()) && StringUtils.isBlank(dto.getDeviceCode())) {
			return new ResponseVO("查询明细信息的设备deviceId或设备编码不能全为空！", 400);
		}
		dto.setType(4);// 查询发卡机
		return devicePbxService.selectDevicePbx(dto);
	}

	/**
	 * 获取系统用户id
	 * @param request
	 * @return
	 */
	private String getUserId(HttpServletRequest request) {
		return (String) request.getAttribute("userId");
	}

	/**
	 ***********************************************软件电话管理***************************************************
	 */

	/**
	 * @api {POST} /pbx/pageSoftphone 软件电话分页查询 /pbx/pageSoftphone
	 * @apiDescription 软件电话分页查询；创建人：宁艺强，修改人：无
	 * @apiGroup  DevicePbxController-包括紧急电话、软件电话、亭内对讲
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam {number} page 页码，必须大于0
	 * @apiParam {number} limit 每页的数目，必须大于0
	 * @apiParam (Success 200) {String} deviceCode 
	 * @apiParam (Success 200) {String} deviceName 设备名称
	 * @apiParam (Success 200) {String} facilityNo 所属设施id
	 * @apiParam (Success 200) {String} milePost 桩号
	 * @apiParam (Success 200) {String} sipCode 所属SIP服务节点编号
	 * @apiParamExample {json} Request-Example: 
	 *		{ 
	 * 			"deviceCode":"",
	 *  		"deviceName":"交科ITS软电话04",
	 *  		"facilityNo":"",
	 *  		"milePost":"",
	 *  		"sipCode":""
	 *   	}
	 * @apiSuccess (Success 200) {String} deviceId 设备ID
	 * @apiSuccess (Success 200) {String} deviceCode 设备编号(自研SIP对应注册编号)
	 * @apiSuccess (Success 200) {String} otherCode 其他编号，捷赛系统设备编号
	 * @apiSuccess (Success 200) {String} deviceTypeNo 12-sip电话类型
	 * @apiSuccess (Success 200) {Integer} type 0-软件电话，1-紧急电话，2-亭内对讲
	 * @apiSuccess (Success 200) {String} deviceName 设备名称
	 * @apiSuccess (Success 200) {String} deviceShortName 设备简称
	 * @apiSuccess (Success 200) {String} deviceNameRemark 设备简称
	 * @apiSuccess (Success 200) {String} userName SIP系统注册登录账号
	 * @apiSuccess (Success 200) {String} password SIP系统注册登录密码
	 * @apiSuccess (Success 200) {String} ipAddress IP地址
	 * @apiSuccess (Success 200) {String} directionNo 方向id
	 * @apiSuccess (Success 200) {String} directionName 方向名称
	 * @apiSuccess (Success 200) {String} facilityNo 所属设施id
	 * @apiSuccess (Success 200) {String} facilityName 所属设施名称
	 * @apiSuccess (Success 200) {String} roadNo 所属路段id
	 * @apiSuccess (Success 200) {String} roadName 所属路段名称
	 * @apiSuccess (Success 200) {String} milePost 桩号
	 * @apiSuccess (Success 200) {Double} mpValue 桩号值
	 * @apiSuccess (Success 200) {String} lng 经度
	 * @apiSuccess (Success 200) {String} lat 经度
	 * @apiSuccess (Success 200) {Integer} sourceId 源站id
	 * @apiSuccess (Success 200) {Integer} use 启用状态
	 * @apiSuccess (Success 200) {String} status 状态(0离线 1在线)
	 * @apiSuccess (Success 200) {String} manufacturer 厂家信息
	 * @apiSuccess (Success 200) {Integer} loginFlag 使用标志1-已经登陆 0-未登录
	 * @apiSuccess (Success 200) {String} remark 备注描述	
	 * @apiSuccess (Success 200) {String} sipCode 所属SIP服务节点编号
	 * @apiSuccess (Success 200) {String} sipName sip服务名称
	 * @apiSuccess (Success 200) {String} entityId SIP节点唯一标识，通常是绑定IP的MAC地址
	 * @apiSuccess (Success 200) {String} hostName SIP节点主机名称
	 * @apiSuccess (Success 200) {String} publicAddress  SIP节点提供SIP服务的公网访问地址，可以存在多个，以逗号隔开，例如***************:5060,***************:5060
	 * @apiSuccess (Success 200) {String} privateAddress SIP节点提供SIP服务的内网访问地址，可以存在多个，以逗号隔开，例如*************:5060,*************:5060
	 * @apiSuccess (Success 200) {String} hostName SIP节点主机名称
	 * @apiSuccess (Success 200) {String} domainName 内网域名
	 * @apiSuccess (Success 200) {String} port 端口号
	 * @apiSuccess (Success 200) {Integer} webrtc (1-开启 0-未开启)开启webrtc
	 * @apiSuccess (Success 200) {Integer} iceSupport (1-开启 0-未开启)开启ice
	 * @apiSuccess (Success 200) {String} webUserName 网页配置登录电话用户
	 * @apiSuccess (Success 200) {String} webPassword 网页配置登录电话密码
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 
	 * {	
	 *   "total": 4,
	 * 	 "items": [
	 *		{ 
	 * 			"deviceId": "b536d9c6-4b43-4c2f-9c09-0aed5475a980",
	 * 			"deviceCode": "1300001",
	 * 			"otherCode": "1300001",
	 * 			"deviceTypeNo": 12,
	 *     		"type": 0,
	 * 			"deviceName": "交科ITS软电话04",
	 * 			"deviceShortName": null,
	 * 			"deviceNameRemark": null,
	 *  		"userName": "admin",
	 *  		"password": "admin",
	 *  		"ipAddress": "************",
	 *   		"directionNo": "eb474098-025d-49ad-8d69-b577741283d0",
	 *   		"directionName": "巴马往平果(上行)",
	 *   		"facilityNo": "f0181e83-9858-460b-845c-992d3ecdd97d",
	 *   		"facilityName": "光明山隧道",
	 *   		"roadNo": "216",
	 *   		"roadName": "G7522贵北高速(巴平路)",
	 *   	 	"milePost": "K458+148",
	 *   		"mpValue": null,
	 *   		"lng": null,
	 *   		"lat": null,
	 *   		"sourceId": null,
	 *   		"use": 1,
	 *   		"status": 1,
	 *   		"manufacturer": "软件电话",
	 *   		"loginFlag": null,
	 *   		"remark": "软件电话",
	 *   		"groupCode": null,
	 *  		"sipCode": "10",
	 *  		"sipName": "上云节点-中心节点",
	 *  		"entityId": "10:io:10:10",
	 *  		"hostName": "大化节点",
	 *  		"publicAddress": "*************:5060,*************:5060",
	 *  		"privateAddress": "*************:5060,*************:5060",
	 *  		"domainName": null,
	 *  		"port": null
	 *   	}
	 *	]
	 *}
	 * @apiErrorExample {json} Success-Response: HTTP/1.1 200 OK code=401用户未登录状态 
	 * 		{ 
	 * 			"code: 401 
	 * 		}
	 * @apiSampleRequest /pbx/pageSoftphone
	 */
	@Login
	@PostMapping("pageSoftphone")
	public Object pageSoftphone(@Valid PageDTO pageDTO, @RequestBody DevicePbxDTO dto, HttpServletRequest request) {
		return new PageVO(softphoneService.page(dto, pageDTO));
	}

	/**
	 * @api {POST} /pbx/addSoftphone 新增软件电话记录 /pbx/addSoftphone
	 * @apiDescription 新增软件电话记录；创建人：宁艺强，修改人：无
	 * @apiGroup DevicePbxController-包括紧急电话、软件电话、亭内对讲
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam (Success 200) {String} deviceCode 设备编号(自研SIP对应注册编号)
	 * @apiParam (Success 200) {String} otherCode 其他编号，捷赛系统设备编号
	 * @apiParam (Success 200) {String} deviceName 设备名称
	 * @apiParam (Success 200) {String} deviceShortName 设备简称
	 * @apiParam (Success 200) {String} deviceNameRemark 设备简称
	 * @apiParam (Success 200) {String} userName SIP系统注册登录账号
	 * @apiParam (Success 200) {String} password SIP系统注册登录密码
	 * @apiParam (Success 200) {String} ipAddress IP地址
	 * @apiParam (Success 200) {String} directionNo 方向id
	 * @apiParam (Success 200) {String} facilityNo 所属设施id
	 * @apiParam (Success 200) {String} milePost 桩号
	 * @apiParam (Success 200) {Integer} sourceId 源站id
	 * @apiParam (Success 200) {Integer} sort 设备排序
	 * @apiParam (Success 200) {Integer} use 启用状态
	 * @apiParam (Success 200) {String} status 状态(0离线 1在线)
	 * @apiParam (Success 200) {String} manufacturer 厂家信息
	 * @apiParam (Success 200) {Integer} loginFlag 使用标志1-已经登陆 0-未登录
	 * @apiParam (Success 200) {String} remark 备注描述	
	 * @apiParam (Success 200) {String} sipCode 所属SIP服务节点编号
	 * @apiParam (Success 200) {Integer} webrtc (1-开启 0-未开启)开启webrtc
	 * @apiParam (Success 200) {Integer} iceSupport (1-开启 0-未开启)开启ice
	 * @apiParam (Success 200) {String} webUserName 网页配置登录电话用户
	 * @apiParam (Success 200) {String} webPassword 网页配置登录电话密码
	 * @apiParamExample {json} Request-Example: 
	 *		{ 
	 * 			"deviceCode": "1300001",
	 * 			"otherCode": "1300001",
	 * 			"deviceName": "交科ITS软电话04",
	 * 			"deviceShortName": null,
	 * 			"deviceNameRemark": null,
	 *  		"userName": "admin",
	 *  		"password": "admin",
	 *  		"ipAddress": "************",
	 *   		"directionNo": null,
	 *   		"facilityNo": "f0181e83-9858-460b-845c-992d3ecdd97d",
	 *   	 	"milePost": "K458+148", 		
	 *   		"sourceId": null,
	 *   		"sort": null,
	 *   		"use": 1,
	 *   		"status": 1,
	 *   		"manufacturer": "软件电话",
	 *   		"loginFlag": 0,
	 *   		"remark": "软件电话",
	 *   		"groupCode": 1000,
	 *   		"webrtc": 0,
	 *   		"iceSupport": 1,
	 *  		"sipCode": "18"
	 *   	}
	 * @apiSuccess (Success 200) {Integer} code 编码（ 0-失败、1-成功）
	 * @apiSuccess (Success 200) {String} message 反馈消息
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 
	 * 	[
	 *		{ 
	 * 			"code": 1,
	 * 			"message": "SUCCESS",
	 *   	}
	 *	]
	 * @apiErrorExample {json} Success-Response: HTTP/1.1 200 OK code=401用户未登录状态 
	 * 		{ 
	 * 			"code: 401 
	 * 		}
	 * @apiSampleRequest /pbx/addSoftphone
	 */
	@Login
	@PostMapping("addSoftphone")
	public ResponseVO addSoftphone(@Valid @RequestBody DevicePbxDTO dto, BindingResult result,
			HttpServletRequest request) {
		ValidUtils.error(result);
		dto.setUserId(this.getUserId(request));
		if (StringUtils.isBlank(dto.getMilePost())) {
			dto.setMilePost("K0+000");
		}
		return softphoneService.add(dto);
	}

	/**
	 * @api {POST} /pbx/updateSoftphone 更新软件电话记录 /pbx/updateSoftphone
	 * @apiDescription 更新软件电话记录；创建人：宁艺强，修改人：无
	 * @apiGroup DevicePbxController-包括紧急电话、软件电话、亭内对讲
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam (Success 200) {String} deviceId 设备id
	 * @apiParam (Success 200) {String} deviceCode 设备编号(自研SIP对应注册编号)
	 * @apiParam (Success 200) {String} otherCode 其他编号，捷赛系统设备编号
	 * @apiParam (Success 200) {String} deviceName 设备名称
	 * @apiParam (Success 200) {String} deviceShortName 设备简称
	 * @apiParam (Success 200) {String} deviceNameRemark 设备简称
	 * @apiParam (Success 200) {String} userName SIP系统注册登录账号
	 * @apiParam (Success 200) {String} password SIP系统注册登录密码
	 * @apiParam (Success 200) {String} ipAddress IP地址
	 * @apiParam (Success 200) {String} directionNo 方向id
	 * @apiParam (Success 200) {String} facilityNo 所属设施id
	 * @apiParam (Success 200) {String} milePost 桩号
	 * @apiParam (Success 200) {Integer} sourceId 源站id
	 * @apiParam (Success 200) {Integer} sort 设备排序
	 * @apiParam (Success 200) {Integer} use 启用状态
	 * @apiParam (Success 200) {String} status 状态(0离线 1在线)
	 * @apiParam (Success 200) {String} manufacturer 厂家信息
	 * @apiParam (Success 200) {Integer} loginFlag 使用标志1-已经登陆 0-未登录
	 * @apiParam (Success 200) {String} remark 备注描述	
	 * @apiParam (Success 200) {String} sipCode 所属SIP服务节点编号
	 * @apiParam (Success 200) {Integer} webrtc (1-开启 0-未开启)开启webrtc
	 * @apiParam (Success 200) {Integer} iceSupport (1-开启 0-未开启)开启ice
	 * @apiParam (Success 200) {String} webUserName 网页配置登录电话用户
	 * @apiParam (Success 200) {String} webPassword 网页配置登录电话密码
	 * @apiParamExample {json} Request-Example: 
	 *		{ 
	 * 			"deviceId": "b536d9c6-4b43-4c2f-9c09-0aed5475a980",
	 * 			"deviceCode": "1300001",
	 * 			"otherCode": "1300001",
	 * 			"deviceName": "交科ITS软电话04",
	 * 			"deviceShortName": null,
	 * 			"deviceNameRemark": null,
	 *  		"userName": "admin",
	 *  		"password": "admin",
	 *  		"ipAddress": "************",
	 *   		"directionNo": null,
	 *   		"facilityNo": "f0181e83-9858-460b-845c-992d3ecdd97d",
	 *   	 	"milePost": "K458+148", 		
	 *   		"sourceId": null,
	 *   		"sort": null,
	 *   		"use": 1,
	 *   		"status": 1,
	 *   		"manufacturer": "软件电话",
	 *   		"loginFlag": 0,
	 *   		"remark": "软件电话",
	 *   		"groupCode": 1000,
	 *   		"webrtc": 0,
	 *   		"iceSupport": 1,
	 *  		"sipCode": "18"
	 *   	}
	 * @apiSuccess (Success 200) {Integer} code 编码（ 0-失败、1-成功）
	 * @apiSuccess (Success 200) {String} message 反馈消息
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 
	 * 	[
	 *		{ 
	 * 			"code": 1,
	 * 			"message": "SUCCESS",
	 *   	}
	 *	]
	 * @apiErrorExample {json} Success-Response: HTTP/1.1 200 OK code=401用户未登录状态 
	 * 		{ 
	 * 			"code: 401 
	 * 		}
	 * @apiSampleRequest /pbx/updateSoftphone
	 */
	@Login
	@PostMapping("updateSoftphone")
	public ResponseVO updateSoftphone(@Valid @RequestBody DevicePbxDTO dto, BindingResult result,
			HttpServletRequest request) {
		ValidUtils.error(result);
		if (dto == null || dto.getDeviceId() == null) {
			return new ResponseVO("更新信息不能为空", 400);
		}
		if (StringUtils.isBlank(dto.getMilePost())) {
			dto.setMilePost("K0+000");
		}
		dto.setUserId(this.getUserId(request));
		return softphoneService.update(dto);
	}

	/**
	 * @api {POST} /pbx/deleteSoftphone 删除软件电话信息 /pbx/deleteSoftphone
	 * @apiDescription 删除软件电话信息；创建人：宁艺强，修改人：无
	 * @apiGroup DevicePbxController-包括紧急电话、软件电话、亭内对讲
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam (Success 200) {String} id 通话ID
	 * @apiParamExample {json} Request-Example: 
	 *		{ 
	 * 			"id": "1723061e-5bd9-43f8-b180-8ad1cb5195c1"
	 *   	}
	 * @apiSuccess (Success 200) {Integer} code 编码（ 0-失败、1-成功）
	 * @apiSuccess (Success 200) {String} message 反馈消息
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 
	 * 	[
	 *		{ 
	 * 			"code": 1,
	 * 			"message": "SUCCESS",
	 *   	}
	 *	]
	 * @apiErrorExample {json} Success-Response: HTTP/1.1 200 OK code=401用户未登录状态 
	 * 		{ 
	 * 			"code: 401 
	 * 		}
	 * @apiSampleRequest /pbx/deleteSoftphone
	 */
	@Login
	@PostMapping("deleteSoftphone")
	public ResponseVO deleteSoftphone(@RequestBody DevicePbxDTO dto, HttpServletRequest request) {
		if (dto == null || dto.getId() == null) {
			return new ResponseVO("删除信息不能为空", 400);
		}
		dto.setUserId(this.getUserId(request));
		return new ResponseVO(softphoneService.delete(dto));
	}

	/**
	 * @api {POST} /pbx/batchDeleteSoftphone 批量删除软件电话信息 /pbx/batchDeleteSoftphone
	 * @apiDescription 批量删除软件电话信息；创建人：宁艺强，修改人：无
	 * @apiGroup DevicePbxController-包括紧急电话、软件电话、亭内对讲
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam (Success 200) {String[]} ids 通话ID集合
	 * @apiParamExample {json} Request-Example: 
	 * {	
	 * 	 "ids": ["9c62391c-bfb4-4e54-ab72-22f35bab6abd","9c72391c-bfb4-4e54-ab72-22f35bab6abd"]
	 *}
	 * @apiSuccess (Success 200) {Integer} code 编码（ 0-失败、1-成功）
	 * @apiSuccess (Success 200) {String} message 反馈消息
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 
	 * 	[
	 *		{ 
	 * 			"code": 1,
	 * 			"message": "SUCCESS",
	 *   	}
	 *	]
	 * @apiErrorExample {json} Success-Response: HTTP/1.1 200 OK code=401用户未登录状态 
	 * 		{ 
	 * 			"code: 401 
	 * 		}
	 * @apiSampleRequest /pbx/batchDeleteSoftphone
	 */
	@Login
	@PostMapping("batchDeleteSoftphone")
	public ResponseVO batchDeleteSoftphone(@RequestBody DevicePbxDTO dto, HttpServletRequest request) {
		if (dto == null || CollectionUtils.isEmpty(dto.getIds())) {
			return new ResponseVO("删除信息不能为空", 400);
		}
		dto.setUserId(this.getUserId(request));
		return new ResponseVO(softphoneService.batchDelete(dto));
	}

	/**
	 ***********************************************************隧道紧急电话管理**********************************************************
	 */

	/**
	 * @api {POST} /pbx/pageEmergencyTel 隧道紧急电话查询 /pbx/pageEmergencyTel
	 * @apiDescription 隧道紧急电话查询；创建人：宁艺强，修改人：无
	 * @apiGroup  DevicePbxController-包括紧急电话、软件电话、亭内对讲
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam {number} page 页码，必须大于0
	 * @apiParam {number} limit 每页的数目，必须大于0
	 * @apiParam (Success 200) {String} deviceCode 
	 * @apiParam (Success 200) {String} deviceName 设备名称
	 * @apiParam (Success 200) {String} facilityNo 所属设施id
	 * @apiParam (Success 200) {String} milePost 桩号
	 * @apiParam (Success 200) {String} sipCode 所属SIP服务节点编号
	 * @apiParamExample {json} Request-Example: 
	 *		{ 
	 * 			"deviceCode":"",
	 *  		"deviceName":"交科ITS软电话04",
	 *  		"facilityNo":"",
	 *  		"milePost":"",
	 *  		"sipCode":""
	 *   	}
	 * @apiSuccess (Success 200) {String} deviceId 设备ID
	 * @apiSuccess (Success 200) {String} deviceCode 设备编号(自研SIP对应注册编号)
	 * @apiSuccess (Success 200) {String} otherCode 其他编号，捷赛系统设备编号
	 * @apiSuccess (Success 200) {String} deviceTypeNo 12-sip电话类型
	 * @apiSuccess (Success 200) {Integer} type 0-软件电话，1-紧急电话，2-亭内对讲
	 * @apiSuccess (Success 200) {String} deviceName 设备名称
	 * @apiSuccess (Success 200) {String} deviceShortName 设备简称
	 * @apiSuccess (Success 200) {String} deviceNameRemark 设备简称
	 * @apiSuccess (Success 200) {String} userName SIP系统注册登录账号
	 * @apiSuccess (Success 200) {String} password SIP系统注册登录密码
	 * @apiSuccess (Success 200) {String} ipAddress IP地址
	 * @apiSuccess (Success 200) {String} directionNo 方向id
	 * @apiSuccess (Success 200) {String} directionName 方向名称
	 * @apiSuccess (Success 200) {String} facilityNo 所属设施id
	 * @apiSuccess (Success 200) {String} facilityName 所属设施名称
	 * @apiSuccess (Success 200) {String} roadNo 所属路段id
	 * @apiSuccess (Success 200) {String} roadName 所属路段名称
	 * @apiSuccess (Success 200) {String} milePost 桩号
	 * @apiSuccess (Success 200) {Double} mpValue 桩号值
	 * @apiSuccess (Success 200) {String} lng 经度
	 * @apiSuccess (Success 200) {String} lat 经度
	 * @apiSuccess (Success 200) {Integer} sourceId 源站id
	 * @apiSuccess (Success 200) {Integer} use 启用状态
	 * @apiSuccess (Success 200) {String} status 状态(0离线 1在线)
	 * @apiSuccess (Success 200) {String} manufacturer 厂家信息
	 * @apiSuccess (Success 200) {Integer} loginFlag 使用标志1-已经登陆 0-未登录
	 * @apiSuccess (Success 200) {String} remark 备注描述	
	 * @apiSuccess (Success 200) {String} sipCode 所属SIP服务节点编号
	 * @apiSuccess (Success 200) {String} sipName sip服务名称
	 * @apiSuccess (Success 200) {String} entityId SIP节点唯一标识，通常是绑定IP的MAC地址
	 * @apiSuccess (Success 200) {String} hostName SIP节点主机名称
	 * @apiSuccess (Success 200) {String} publicAddress  SIP节点提供SIP服务的公网访问地址，可以存在多个，以逗号隔开，例如***************:5060,***************:5060
	 * @apiSuccess (Success 200) {String} privateAddress SIP节点提供SIP服务的内网访问地址，可以存在多个，以逗号隔开，例如*************:5060,*************:5060
	 * @apiSuccess (Success 200) {String} hostName SIP节点主机名称
	 * @apiSuccess (Success 200) {String} domainName 内网域名
	 * @apiSuccess (Success 200) {String} port 端口号
	 * @apiSuccess (Success 200) {String} webUserName 网页配置登录电话用户
	 * @apiSuccess (Success 200) {String} webPassword 网页配置登录电话密码
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 
	 * {	
	 *   "total": 4,
	 * 	 "items": [
	 *		{ 
	 * 			"deviceId": "b536d9c6-4b43-4c2f-9c09-0aed5475a980",
	 * 			"deviceCode": "1300001",
	 * 			"otherCode": "1300001",
	 * 			"deviceTypeNo": 12,
	 *     		"type": 0,
	 * 			"deviceName": "交科ITS软电话04",
	 * 			"deviceShortName": null,
	 * 			"deviceNameRemark": null,
	 *  		"userName": "admin",
	 *  		"password": "admin",
	 *  		"ipAddress": "************",
	 *   		"directionNo": "eb474098-025d-49ad-8d69-b577741283d0",
	 *   		"directionName": "巴马往平果(上行)",
	 *   		"facilityNo": "f0181e83-9858-460b-845c-992d3ecdd97d",
	 *   		"facilityName": "光明山隧道",
	 *   		"roadNo": "216",
	 *   		"roadName": "G7522贵北高速(巴平路)",
	 *   	 	"milePost": "K458+148",
	 *   		"mpValue": null,
	 *   		"lng": null,
	 *   		"lat": null,
	 *   		"sourceId": null,
	 *   		"use": 1,
	 *   		"status": 1,
	 *   		"manufacturer": "软件电话",
	 *   		"loginFlag": null,
	 *   		"remark": "软件电话",
	 *   		"groupCode": null,
	 *  		"sipCode": "10",
	 *  		"sipName": "上云节点-中心节点",
	 *  		"entityId": "10:io:10:10",
	 *  		"hostName": "大化节点",
	 *  		"publicAddress": "*************:5060,*************:5060",
	 *  		"privateAddress": "*************:5060,*************:5060",
	 *  		"domainName": null,
	 *  		"port": null
	 *   	}
	 *	]
	 *}
	 * @apiErrorExample {json} Success-Response: HTTP/1.1 200 OK code=401用户未登录状态 
	 * 		{ 
	 * 			"code: 401 
	 * 		}
	 * @apiSampleRequest /pbx/pageEmergencyTel
	 */
	@Login
	@PostMapping("pageEmergencyTel")
	public Object pageEmergencyTel(@Valid PageDTO pageDTO, @RequestBody DevicePbxDTO dto, HttpServletRequest request) {
		return new PageVO(emergencyTelService.page(dto, pageDTO));
	}

	/**
	 * @api {POST} /pbx/addEmergencyTel 新增隧道紧急电话记录 /pbx/addEmergencyTel
	 * @apiDescription 新增隧道紧急电话记录；创建人：宁艺强，修改人：无
	 * @apiGroup DevicePbxController-包括紧急电话、软件电话、亭内对讲
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam (Success 200) {String} deviceCode 设备编号(自研SIP对应注册编号)
	 * @apiParam (Success 200) {String} otherCode 其他编号，捷赛系统设备编号
	 * @apiParam (Success 200) {String} deviceName 设备名称
	 * @apiParam (Success 200) {String} deviceShortName 设备简称
	 * @apiParam (Success 200) {String} deviceNameRemark 设备简称
	 * @apiParam (Success 200) {String} userName SIP系统注册登录账号
	 * @apiParam (Success 200) {String} password SIP系统注册登录密码
	 * @apiParam (Success 200) {String} ipAddress IP地址
	 * @apiParam (Success 200) {String} directionNo 方向id
	 * @apiParam (Success 200) {String} facilityNo 所属设施id
	 * @apiParam (Success 200) {String} milePost 桩号
	 * @apiParam (Success 200) {Integer} sourceId 源站id
	 * @apiParam (Success 200) {Integer} sort 设备排序
	 * @apiParam (Success 200) {Integer} use 启用状态
	 * @apiParam (Success 200) {String} status 状态(0离线 1在线)
	 * @apiParam (Success 200) {String} manufacturer 厂家信息
	 * @apiParam (Success 200) {Integer} loginFlag 使用标志1-已经登陆 0-未登录
	 * @apiParam (Success 200) {String} remark 备注描述	
	 * @apiParam (Success 200) {String} sipCode 所属SIP服务节点编号
	 * @apiParam (Success 200) {Integer} webrtc (1-开启 0-未开启)开启webrtc
	 * @apiParam (Success 200) {Integer} iceSupport (1-开启 0-未开启)开启ice
	 * @apiParam (Success 200) {String} webUserName 网页配置登录电话用户
	 * @apiParam (Success 200) {String} webPassword 网页配置登录电话密码
	 * @apiParamExample {json} Request-Example: 
	 *		{ 
	 * 			"deviceCode": "1300001",
	 * 			"otherCode": "1300001",
	 * 			"deviceName": "交科ITS软电话04",
	 * 			"deviceShortName": null,
	 * 			"deviceNameRemark": null,
	 *  		"userName": "admin",
	 *  		"password": "admin",
	 *  		"ipAddress": "************",
	 *   		"directionNo": null,
	 *   		"facilityNo": "f0181e83-9858-460b-845c-992d3ecdd97d",
	 *   	 	"milePost": "K458+148", 		
	 *   		"sourceId": null,
	 *   		"sort": null,
	 *   		"use": 1,
	 *   		"status": 1,
	 *   		"manufacturer": "软件电话",
	 *   		"loginFlag": 0,
	 *   		"remark": "软件电话",
	 *   		"groupCode": 1000,
	 *   		"webrtc": 0,
	 *   		"iceSupport": 1,
	 *  		"sipCode": "18"
	 *   	}
	 * @apiSuccess (Success 200) {Integer} code 编码（ 0-失败、1-成功）
	 * @apiSuccess (Success 200) {String} message 反馈消息
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 
	 * 	[
	 *		{ 
	 * 			"code": 1,
	 * 			"message": "SUCCESS",
	 *   	}
	 *	]
	 * @apiErrorExample {json} Success-Response: HTTP/1.1 200 OK code=401用户未登录状态 
	 * 		{ 
	 * 			"code: 401 
	 * 		}
	 * @apiSampleRequest /pbx/addEmergencyTel
	 */
	@Login
	@PostMapping("addEmergencyTel")
	public ResponseVO addEmergencyTel(@Valid @RequestBody DevicePbxDTO dto, BindingResult result,
			HttpServletRequest request) {
		ValidUtils.error(result);
		if (StringUtils.isBlank(dto.getDirectionNo())) {
			return new ResponseVO("隧道紧急电话安装方向不能为空", 400);
		}
		if (StringUtils.isBlank(dto.getIpAddress())) {
			return new ResponseVO("隧道紧急电话IP不能为空", 400);
		}
		if (StringUtils.isBlank(dto.getMilePost())) {
			return new ResponseVO("隧道紧急电话桩号不能为空", 400);
		}
		dto.setUserId(this.getUserId(request));
		return emergencyTelService.add(dto);
	}

	/**
	 * @api {POST} /pbx/updateEmergencyTel 更新隧道紧急电话 /pbx/updateEmergencyTel
	 * @apiDescription 更新隧道紧急电话记录；创建人：宁艺强，修改人：无
	 * @apiGroup DevicePbxController-包括紧急电话、软件电话、亭内对讲
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam (Success 200) {String} deviceId 设备id
	 * @apiParam (Success 200) {String} deviceCode 设备编号(自研SIP对应注册编号)
	 * @apiParam (Success 200) {String} otherCode 其他编号，捷赛系统设备编号
	 * @apiParam (Success 200) {String} deviceName 设备名称
	 * @apiParam (Success 200) {String} deviceShortName 设备简称
	 * @apiParam (Success 200) {String} deviceNameRemark 设备简称
	 * @apiParam (Success 200) {String} userName SIP系统注册登录账号
	 * @apiParam (Success 200) {String} password SIP系统注册登录密码
	 * @apiParam (Success 200) {String} ipAddress IP地址
	 * @apiParam (Success 200) {String} directionNo 方向id
	 * @apiParam (Success 200) {String} facilityNo 所属设施id
	 * @apiParam (Success 200) {String} milePost 桩号
	 * @apiParam (Success 200) {Integer} sourceId 源站id
	 * @apiParam (Success 200) {Integer} sort 设备排序
	 * @apiParam (Success 200) {Integer} use 启用状态
	 * @apiParam (Success 200) {String} status 状态(0离线 1在线)
	 * @apiParam (Success 200) {String} manufacturer 厂家信息
	 * @apiParam (Success 200) {Integer} loginFlag 使用标志1-已经登陆 0-未登录
	 * @apiParam (Success 200) {String} remark 备注描述	
	 * @apiParam (Success 200) {String} sipCode 所属SIP服务节点编号
	 * @apiParam (Success 200) {Integer} webrtc (1-开启 0-未开启)开启webrtc
	 * @apiParam (Success 200) {Integer} iceSupport (1-开启 0-未开启)开启ice
	 * @apiParam (Success 200) {String} webUserName 网页配置登录电话用户
	 * @apiParam (Success 200) {String} webPassword 网页配置登录电话密码
	 * @apiParamExample {json} Request-Example: 
	 *		{ 
	 * 			"deviceId": "b536d9c6-4b43-4c2f-9c09-0aed5475a980",
	 * 			"deviceCode": "1300001",
	 * 			"otherCode": "1300001",
	 * 			"deviceName": "交科ITS软电话04",
	 * 			"deviceShortName": null,
	 * 			"deviceNameRemark": null,
	 *  		"userName": "admin",
	 *  		"password": "admin",
	 *  		"ipAddress": "************",
	 *   		"directionNo": null,
	 *   		"facilityNo": "f0181e83-9858-460b-845c-992d3ecdd97d",
	 *   	 	"milePost": "K458+148", 		
	 *   		"sourceId": null,
	 *   		"sort": null,
	 *   		"use": 1,
	 *   		"status": 1,
	 *   		"manufacturer": "软件电话",
	 *   		"loginFlag": 0,
	 *   		"remark": "软件电话",
	 *   		"groupCode": 1000,
	 *   		"webrtc": 1,
	 *   		"iceSupport": 0,
	 *  		"sipCode": "18"
	 *   	}
	 * @apiSuccess (Success 200) {Integer} code 编码（ 0-失败、1-成功）
	 * @apiSuccess (Success 200) {String} message 反馈消息
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 
	 * 	[
	 *		{ 
	 * 			"code": 1,
	 * 			"message": "SUCCESS",
	 *   	}
	 *	]
	 * @apiErrorExample {json} Success-Response: HTTP/1.1 200 OK code=401用户未登录状态 
	 * 		{ 
	 * 			"code: 401 
	 * 		}
	 * @apiSampleRequest /pbx/updateEmergencyTel
	 */
	@Login
	@PostMapping("updateEmergencyTel")
	public ResponseVO updateEmergencyTel(@Valid @RequestBody DevicePbxDTO dto, BindingResult result,
			HttpServletRequest request) {
		ValidUtils.error(result);
		if (dto == null || dto.getDeviceId() == null) {
			return new ResponseVO("更新信息不能为空", 400);
		}
		if (StringUtils.isBlank(dto.getDirectionNo())) {
			return new ResponseVO("隧道紧急电话安装方向不能为空", 400);
		}
		if (StringUtils.isBlank(dto.getIpAddress())) {
			return new ResponseVO("隧道紧急电话IP不能为空", 400);
		}
		dto.setUserId(this.getUserId(request));
		return emergencyTelService.update(dto);
	}

	/**
	 * @api {POST} /pbx/deleteEmergencyTel 删除隧道紧急电话信息 /pbx/deleteEmergencyTel
	 * @apiDescription 删除隧道紧急电话信息；创建人：宁艺强，修改人：无
	 * @apiGroup DevicePbxController-包括紧急电话、软件电话、亭内对讲
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam (Success 200) {String} id 通话ID
	 * @apiParamExample {json} Request-Example: 
	 *		{ 
	 * 			"id": "1723061e-5bd9-43f8-b180-8ad1cb5195c1"
	 *   	}
	 * @apiSuccess (Success 200) {Integer} code 编码（ 0-失败、1-成功）
	 * @apiSuccess (Success 200) {String} message 反馈消息
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 
	 * 	[
	 *		{ 
	 * 			"code": 1,
	 * 			"message": "SUCCESS",
	 *   	}
	 *	]
	 * @apiErrorExample {json} Success-Response: HTTP/1.1 200 OK code=401用户未登录状态 
	 * 		{ 
	 * 			"code: 401 
	 * 		}
	 * @apiSampleRequest /pbx/deleteEmergencyTel
	 */
	@Login
	@PostMapping("deleteEmergencyTel")
	public ResponseVO deleteEmergencyTel(@RequestBody DevicePbxDTO dto, HttpServletRequest request) {
		if (dto == null || dto.getId() == null) {
			return new ResponseVO("删除信息不能为空", 400);
		}
		dto.setUserId(this.getUserId(request));
		return new ResponseVO(emergencyTelService.delete(dto));
	}

	/**
	 * @api {POST} /pbx/batchDeleteEmergencyTel 批量删除隧道紧急电话信息 /pbx/batchDeleteEmergencyTel
	 * @apiDescription 批量删除隧道紧急电话信息；创建人：宁艺强，修改人：无
	 * @apiGroup DevicePbxController-包括紧急电话、软件电话、亭内对讲
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam (Success 200) {String[]} ids 通话ID集合
	 * @apiParamExample {json} Request-Example: 
	 * {	
	 * 	 "ids": ["9c62391c-bfb4-4e54-ab72-22f35bab6abd","9c72391c-bfb4-4e54-ab72-22f35bab6abd"]
	 *}
	 * @apiSuccess (Success 200) {Integer} code 编码（ 0-失败、1-成功）
	 * @apiSuccess (Success 200) {String} message 反馈消息
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 
	 * 	[
	 *		{ 
	 * 			"code": 1,
	 * 			"message": "SUCCESS",
	 *   	}
	 *	]
	 * @apiErrorExample {json} Success-Response: HTTP/1.1 200 OK code=401用户未登录状态 
	 * 		{ 
	 * 			"code: 401 
	 * 		}
	 * @apiSampleRequest /pbx/batchDeleteEmergencyTel
	 */
	@Login
	@PostMapping("batchDeleteEmergencyTel")
	public ResponseVO batchDeleteEmergencyTel(@RequestBody DevicePbxDTO dto, HttpServletRequest request) {
		if (dto == null || CollectionUtils.isEmpty(dto.getIds())) {
			return new ResponseVO("删除信息不能为空", 400);
		}
		dto.setUserId(this.getUserId(request));
		return new ResponseVO(emergencyTelService.batchDelete(dto));
	}

	/**
	 ********************************************亭内对讲管理***********************************************
	 */
	/**
	 * @api {POST} /pbx/pageTalkBack 亭内对讲分页查询 /pbx/pageTalkBack
	 * @apiDescription 亭内对讲分页查询；创建人：宁艺强，修改人：无
	 * @apiGroup  DevicePbxController-包括紧急电话、软件电话、亭内对讲
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam {number} page 页码，必须大于0
	 * @apiParam {number} limit 每页的数目，必须大于0
	 * @apiParam (Success 200) {String} deviceCode 
	 * @apiParam (Success 200) {String} deviceName 设备名称
	 * @apiParam (Success 200) {String} facilityNo 所属设施id
	 * @apiParam (Success 200) {String} milePost 桩号
	 * @apiParam (Success 200) {String} sipCode 所属SIP服务节点编号
	 * @apiParamExample {json} Request-Example: 
	 *		{ 
	 * 			"deviceCode":"",
	 *  		"deviceName":"交科ITS软电话04",
	 *  		"facilityNo":"",
	 *  		"milePost":"",
	 *  		"sipCode":""
	 *   	}
	 * @apiSuccess (Success 200) {String} deviceId 设备ID
	 * @apiSuccess (Success 200) {String} deviceCode 设备编号(自研SIP对应注册编号)
	 * @apiSuccess (Success 200) {String} otherCode 其他编号，捷赛系统设备编号
	 * @apiSuccess (Success 200) {String} deviceTypeNo 12-sip电话类型
	 * @apiSuccess (Success 200) {Integer} type 0-软件电话，1-紧急电话，2-亭内对讲
	 * @apiSuccess (Success 200) {String} deviceName 设备名称
	 * @apiSuccess (Success 200) {String} deviceShortName 设备简称
	 * @apiSuccess (Success 200) {String} deviceNameRemark 设备简称
	 * @apiSuccess (Success 200) {String} userName SIP系统注册登录账号
	 * @apiSuccess (Success 200) {String} password SIP系统注册登录密码
	 * @apiSuccess (Success 200) {String} ipAddress IP地址
	 * @apiSuccess (Success 200) {String} directionNo 方向id
	 * @apiSuccess (Success 200) {String} directionName 方向名称
	 * @apiSuccess (Success 200) {String} facilityNo 所属设施id
	 * @apiSuccess (Success 200) {String} facilityName 所属设施名称
	 * @apiSuccess (Success 200) {String} roadNo 所属路段id
	 * @apiSuccess (Success 200) {String} roadName 所属路段名称
	 * @apiSuccess (Success 200) {String} milePost 桩号
	 * @apiSuccess (Success 200) {Double} mpValue 桩号值
	 * @apiSuccess (Success 200) {String} lng 经度
	 * @apiSuccess (Success 200) {String} lat 经度
	 * @apiSuccess (Success 200) {Integer} sourceId 源站id
	 * @apiSuccess (Success 200) {Integer} use 启用状态
	 * @apiSuccess (Success 200) {String} status 状态(0离线 1在线)
	 * @apiSuccess (Success 200) {String} manufacturer 厂家信息
	 * @apiSuccess (Success 200) {Integer} loginFlag 使用标志1-已经登陆 0-未登录
	 * @apiSuccess (Success 200) {String} remark 备注描述	
	 * @apiSuccess (Success 200) {String} sipCode 所属SIP服务节点编号
	 * @apiSuccess (Success 200) {String} sipName sip服务名称
	 * @apiSuccess (Success 200) {String} entityId SIP节点唯一标识，通常是绑定IP的MAC地址
	 * @apiSuccess (Success 200) {String} hostName SIP节点主机名称
	 * @apiSuccess (Success 200) {String} publicAddress  SIP节点提供SIP服务的公网访问地址，可以存在多个，以逗号隔开，例如***************:5060,***************:5060
	 * @apiSuccess (Success 200) {String} privateAddress SIP节点提供SIP服务的内网访问地址，可以存在多个，以逗号隔开，例如*************:5060,*************:5060
	 * @apiSuccess (Success 200) {String} hostName SIP节点主机名称
	 * @apiSuccess (Success 200) {String} domainName 内网域名
	 * @apiSuccess (Success 200) {String} port 端口号
	 * @apiSuccess (Success 200) {Integer} webrtc (1-开启 0-未开启)开启webrtc
	 * @apiSuccess (Success 200) {Integer} iceSupport (1-开启 0-未开启)开启ice
	 * @apiSuccess (Success 200) {String} webUserName 网页配置登录电话用户
	 * @apiSuccess (Success 200) {String} webPassword 网页配置登录电话密码
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 
	 * {	
	 *   "total": 4,
	 * 	 "items": [
	 *		{ 
	 * 			"deviceId": "b536d9c6-4b43-4c2f-9c09-0aed5475a980",
	 * 			"deviceCode": "1300001",
	 * 			"otherCode": "1300001",
	 * 			"deviceTypeNo": 12,
	 *     		"type": 0,
	 * 			"deviceName": "交科ITS软电话04",
	 * 			"deviceShortName": null,
	 * 			"deviceNameRemark": null,
	 *  		"userName": "admin",
	 *  		"password": "admin",
	 *  		"ipAddress": "************",
	 *   		"directionNo": "eb474098-025d-49ad-8d69-b577741283d0",
	 *   		"directionName": "巴马往平果(上行)",
	 *   		"facilityNo": "f0181e83-9858-460b-845c-992d3ecdd97d",
	 *   		"facilityName": "光明山隧道",
	 *   		"roadNo": "216",
	 *   		"roadName": "G7522贵北高速(巴平路)",
	 *   	 	"milePost": "K458+148",
	 *   		"mpValue": null,
	 *   		"lng": null,
	 *   		"lat": null,
	 *   		"sourceId": null,
	 *   		"use": 1,
	 *   		"status": 1,
	 *   		"manufacturer": "软件电话",
	 *   		"loginFlag": null,
	 *   		"remark": "软件电话",
	 *   		"groupCode": null,
	 *  		"sipCode": "10",
	 *  		"sipName": "上云节点-中心节点",
	 *  		"entityId": "10:io:10:10",
	 *  		"hostName": "大化节点",
	 *  		"publicAddress": "*************:5060,*************:5060",
	 *  		"privateAddress": "*************:5060,*************:5060",
	 *  		"domainName": null,
	 *  		"port": null
	 *   	}
	 *	]
	 *}
	 * @apiErrorExample {json} Success-Response: HTTP/1.1 200 OK code=401用户未登录状态 
	 * 		{ 
	 * 			"code: 401 
	 * 		}
	 * @apiSampleRequest /pbx/pageTalkBack
	 */
	@Login
	@PostMapping("pageTalkBack")
	public Object pageTalkBack(@Valid PageDTO pageDTO, @RequestBody DevicePbxDTO dto, HttpServletRequest request) {
		return new PageVO(talkBackService.page(dto, pageDTO));
	}

	/**
	 * @api {POST} /pbx/addTalkBack 新增亭内对讲设备 /pbx/addTalkBack
	 * @apiDescription 新增亭内对讲设备；创建人：宁艺强，修改人：无
	 * @apiGroup DevicePbxController-包括紧急电话、软件电话、亭内对讲
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam (Success 200) {String} deviceCode 设备编号(自研SIP对应注册编号)
	 * @apiParam (Success 200) {String} otherCode 其他编号，捷赛系统设备编号
	 * @apiParam (Success 200) {String} deviceName 设备名称
	 * @apiParam (Success 200) {String} deviceShortName 设备简称
	 * @apiParam (Success 200) {String} deviceNameRemark 设备简称
	 * @apiParam (Success 200) {String} userName SIP系统注册登录账号
	 * @apiParam (Success 200) {String} password SIP系统注册登录密码
	 * @apiParam (Success 200) {String} ipAddress IP地址
	 * @apiParam (Success 200) {String} directionNo 方向id
	 * @apiParam (Success 200) {String} facilityNo 所属设施id
	 * @apiParam (Success 200) {String} milePost 桩号
	 * @apiParam (Success 200) {Integer} sourceId 源站id
	 * @apiParam (Success 200) {Integer} sort 设备排序
	 * @apiParam (Success 200) {Integer} use 启用状态
	 * @apiParam (Success 200) {String} status 状态(0离线 1在线)
	 * @apiParam (Success 200) {String} manufacturer 厂家信息
	 * @apiParam (Success 200) {Integer} loginFlag 使用标志1-已经登陆 0-未登录
	 * @apiParam (Success 200) {String} remark 备注描述	
	 * @apiParam (Success 200) {String} sipCode 所属SIP服务节点编号
	 * @apiParam (Success 200) {Integer} webrtc (1-开启 0-未开启)开启webrtc
	 * @apiParam (Success 200) {Integer} iceSupport (1-开启 0-未开启)开启ice
	 * @apiParam (Success 200) {String} webUserName 网页配置登录电话用户
	 * @apiParam (Success 200) {String} webPassword 网页配置登录电话密码
	 * @apiParamExample {json} Request-Example: 
	 *		{ 
	 * 			"deviceCode": "1300001",
	 * 			"otherCode": "1300001",
	 * 			"deviceName": "交科ITS软电话04",
	 * 			"deviceShortName": null,
	 * 			"deviceNameRemark": null,
	 *  		"userName": "admin",
	 *  		"password": "admin",
	 *  		"ipAddress": "************",
	 *   		"directionNo": null,
	 *   		"facilityNo": "f0181e83-9858-460b-845c-992d3ecdd97d",
	 *   	 	"milePost": "K458+148", 		
	 *   		"sourceId": null,
	 *   		"sort": null,
	 *   		"use": 1,
	 *   		"status": 1,
	 *   		"manufacturer": "软件电话",
	 *   		"loginFlag": 0,
	 *   		"remark": "软件电话",
	 *   		"groupCode": 1000,
	 *   	 	"webrtc": 1,
	 *   		"iceSupport": 0,
	 *  		"sipCode": "18"
	 *   	}
	 * @apiSuccess (Success 200) {Integer} code 编码（ 0-失败、1-成功）
	 * @apiSuccess (Success 200) {String} message 反馈消息
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 
	 * 	[
	 *		{ 
	 * 			"code": 1,
	 * 			"message": "SUCCESS",
	 *   	}
	 *	]
	 * @apiErrorExample {json} Success-Response: HTTP/1.1 200 OK code=401用户未登录状态 
	 * 		{ 
	 * 			"code: 401 
	 * 		}
	 * @apiSampleRequest /pbx/addTalkBack
	 */
	@Login
	@PostMapping("addTalkBack")
	public ResponseVO addTalkBack(@Valid @RequestBody DevicePbxDTO dto, BindingResult result,
			HttpServletRequest request) {
		ValidUtils.error(result);
		dto.setUserId(this.getUserId(request));
		return talkBackService.add(dto);
	}

	/**
	 * @api {POST} /pbx/updateTalkBack 修改亭内对讲设备 /pbx/updateTalkBack
	 * @apiDescription 修改亭内对讲设备；创建人：宁艺强，修改人：无
	 * @apiGroup DevicePbxController-包括紧急电话、软件电话、亭内对讲
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam (Success 200) {String} deviceId 设备id
	 * @apiParam (Success 200) {String} deviceCode 设备编号(自研SIP对应注册编号)
	 * @apiParam (Success 200) {String} otherCode 其他编号，捷赛系统设备编号
	 * @apiParam (Success 200) {String} deviceName 设备名称
	 * @apiParam (Success 200) {String} deviceShortName 设备简称
	 * @apiParam (Success 200) {String} deviceNameRemark 设备简称
	 * @apiParam (Success 200) {String} userName SIP系统注册登录账号
	 * @apiParam (Success 200) {String} password SIP系统注册登录密码
	 * @apiParam (Success 200) {String} ipAddress IP地址
	 * @apiParam (Success 200) {String} directionNo 方向id
	 * @apiParam (Success 200) {String} facilityNo 所属设施id
	 * @apiParam (Success 200) {String} milePost 桩号
	 * @apiParam (Success 200) {Integer} sourceId 源站id
	 * @apiParam (Success 200) {Integer} sort 设备排序
	 * @apiParam (Success 200) {Integer} use 启用状态
	 * @apiParam (Success 200) {String} status 状态(0离线 1在线)
	 * @apiParam (Success 200) {String} manufacturer 厂家信息
	 * @apiParam (Success 200) {Integer} loginFlag 使用标志1-已经登陆 0-未登录
	 * @apiParam (Success 200) {String} remark 备注描述	
	 * @apiParam (Success 200) {String} sipCode 所属SIP服务节点编号
	 * @apiParam (Success 200) {Integer} webrtc (1-开启 0-未开启)开启webrtc
	 * @apiParam (Success 200) {Integer} iceSupport (1-开启 0-未开启)开启ice
	 * @apiParam (Success 200) {String} webUserName 网页配置登录电话用户
	 * @apiParam (Success 200) {String} webPassword 网页配置登录电话密码
	 * @apiParamExample {json} Request-Example: 
	 *		{ 
	 * 			"deviceId": "b536d9c6-4b43-4c2f-9c09-0aed5475a980",
	 * 			"deviceCode": "1300001",
	 * 			"otherCode": "1300001",
	 * 			"deviceName": "交科ITS软电话04",
	 * 			"deviceShortName": null,
	 * 			"deviceNameRemark": null,
	 *  		"userName": "admin",
	 *  		"password": "admin",
	 *  		"ipAddress": "************",
	 *   		"directionNo": null,
	 *   		"facilityNo": "f0181e83-9858-460b-845c-992d3ecdd97d",
	 *   	 	"milePost": "K458+148", 		
	 *   		"sourceId": null,
	 *   		"sort": null,
	 *   		"use": 1,
	 *   		"status": 1,
	 *   		"manufacturer": "软件电话",
	 *   		"loginFlag": 0,
	 *   		"remark": "软件电话",
	 *   		"groupCode": 1000,
	 *   		"webrtc": 1,
	 *   		"iceSupport": 0,
	 *  		"sipCode": "18"
	 *   	}
	 * @apiSuccess (Success 200) {Integer} code 编码（ 0-失败、1-成功）
	 * @apiSuccess (Success 200) {String} message 反馈消息
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 
	 * 	[
	 *		{ 
	 * 			"code": 1,
	 * 			"message": "SUCCESS",
	 *   	}
	 *	]
	 * @apiErrorExample {json} Success-Response: HTTP/1.1 200 OK code=401用户未登录状态 
	 * 		{ 
	 * 			"code: 401 
	 * 		}
	 * @apiSampleRequest /pbx/updateTalkBack
	 */
	@Login
	@PostMapping("updateTalkBack")
	public ResponseVO updateTalkBack(@Valid @RequestBody DevicePbxDTO dto, BindingResult result,
			HttpServletRequest request) {
		ValidUtils.error(result);
		if (dto == null || dto.getDeviceId() == null) {
			return new ResponseVO("更新信息不能为空", 400);
		}
		dto.setUserId(this.getUserId(request));
		return talkBackService.update(dto);
	}

	/**
	 * @api {POST} /pbx/deleteTalkBack 删除亭内对讲设备 /pbx/deleteTalkBack
	 * @apiDescription 删除亭内对讲设备；创建人：宁艺强，修改人：无
	 * @apiGroup DevicePbxController-包括紧急电话、软件电话、亭内对讲
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam (Success 200) {String} id 通话ID
	 * @apiParamExample {json} Request-Example: 
	 *		{ 
	 * 			"id": "1723061e-5bd9-43f8-b180-8ad1cb5195c1"
	 *   	}
	 * @apiSuccess (Success 200) {Integer} code 编码（ 0-失败、1-成功）
	 * @apiSuccess (Success 200) {String} message 反馈消息
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 
	 * 	[
	 *		{ 
	 * 			"code": 1,
	 * 			"message": "SUCCESS",
	 *   	}
	 *	]
	 * @apiErrorExample {json} Success-Response: HTTP/1.1 200 OK code=401用户未登录状态 
	 * 		{ 
	 * 			"code: 401 
	 * 		}
	 * @apiSampleRequest /pbx/deleteTalkBack
	 */
	@Login
	@PostMapping("deleteTalkBack")
	public ResponseVO deleteTalkBack(@RequestBody DevicePbxDTO dto, HttpServletRequest request) {
		if (dto == null || dto.getId() == null) {
			return new ResponseVO("删除信息不能为空", 400);
		}
		dto.setUserId(this.getUserId(request));
		return new ResponseVO(talkBackService.delete(dto));
	}

	/**
	 * @api {POST} /pbx/batchDeleteTalkBack 批量删除亭内对讲设备 /pbx/batchDeleteTalkBack
	 * @apiDescription 批量删除亭内对讲设备；创建人：宁艺强，修改人：无
	 * @apiGroup DevicePbxController-包括紧急电话、软件电话、亭内对讲
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam (Success 200) {String[]} ids 通话ID集合
	 * @apiParamExample {json} Request-Example: 
	 * {	
	 * 	 "ids": ["9c62391c-bfb4-4e54-ab72-22f35bab6abd","9c72391c-bfb4-4e54-ab72-22f35bab6abd"]
	 *}
	 * @apiSuccess (Success 200) {Integer} code 编码（ 0-失败、1-成功）
	 * @apiSuccess (Success 200) {String} message 反馈消息
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 
	 * 	[
	 *		{ 
	 * 			"code": 1,
	 * 			"message": "SUCCESS",
	 *   	}
	 *	]
	 * @apiErrorExample {json} Success-Response: HTTP/1.1 200 OK code=401用户未登录状态 
	 * 		{ 
	 * 			"code: 401 
	 * 		}
	 * @apiSampleRequest /pbx/batchDeleteTalkBack
	 */
	@Login
	@PostMapping("batchDeleteTalkBack")
	public ResponseVO batchDeleteTalkBack(@RequestBody DevicePbxDTO dto, HttpServletRequest request) {
		if (dto == null || CollectionUtils.isEmpty(dto.getIds())) {
			return new ResponseVO("删除信息不能为空", 400);
		}
		dto.setUserId(this.getUserId(request));
		return new ResponseVO(talkBackService.batchDelete(dto));
	}

	/**
	 ********************************************发卡机管理***********************************************
	 */

	/**
	 * @描述 分页查询发卡机设备
	 * @param pageDTO
	 * @param dto
	 * @param request
	 * @return
	 */
	@Login
	@PostMapping("pageTalkCard")
	public Object pageTalkCard(@Valid PageDTO pageDTO, @RequestBody DevicePbxDTO dto, HttpServletRequest request) {
		return new PageVO(talkCardService.page(dto, pageDTO));
	}

	/**
	 * 
	 * @param dto
	 * @param result
	 * @param request
	 * @return
	 */
	@Login
	@PostMapping("addTalkCard")
	public ResponseVO addTalkCard(@Valid @RequestBody DevicePbxDTO dto, BindingResult result,
			HttpServletRequest request) {
		ValidUtils.error(result);
		dto.setUserId(this.getUserId(request));
		return talkCardService.add(dto);
	}

	@Login
	@PostMapping("updateTalkCard")
	public ResponseVO updateTalkCard(@Valid @RequestBody DevicePbxDTO dto, BindingResult result,
			HttpServletRequest request) {
		ValidUtils.error(result);
		if (dto == null || dto.getDeviceId() == null) {
			return new ResponseVO("更新信息不能为空", 400);
		}
		dto.setUserId(this.getUserId(request));
		return talkCardService.update(dto);
	}

	@Login
	@PostMapping("deleteTalkCard")
	public ResponseVO deleteTalkCard(@RequestBody DevicePbxDTO dto, HttpServletRequest request) {
		if (dto == null || dto.getId() == null) {
			return new ResponseVO("删除信息不能为空", 400);
		}
		dto.setUserId(this.getUserId(request));
		return new ResponseVO(talkCardService.delete(dto));
	}

	@Login
	@PostMapping("batchDeleteTalkCard")
	public ResponseVO batchDeleteTalkCard(@RequestBody DevicePbxDTO dto, HttpServletRequest request) {
		if (dto == null || CollectionUtils.isEmpty(dto.getIds())) {
			return new ResponseVO("删除信息不能为空", 400);
		}
		dto.setUserId(this.getUserId(request));
		return new ResponseVO(talkCardService.batchDelete(dto));
	}

	/**
	 * @api {POST} /pbx/mainTree 主页电话树形菜单 /pbx/mainTree
	 * @apiDescription 主页电话树形菜单；创建人：宁艺强，修改人：无
	 * @apiGroup  DevicePbxController-包括紧急电话、软件电话、亭内对讲
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam (Success 200) {Integer} groupType groupType不能为空，1按公司分类；2按路段分类，默认为1
	 * @apiParam (Success 200) {String} keywords 查询设备关键子，模糊查询(设备名称、编码、IP,设施名称、路段名称、方向名称、公司名称)
	 * @apiParamExample {json} Request-Example: 
	 *		{ 
	 *  		"groupType":1,
	 *  		"keywords":""
	 *   	}
	 * @apiSuccess (Success 200) {String} id 节点id
	 * @apiSuccess (Success 200) {String} parentId 上级节点id
	 * @apiSuccess (Success 200) {String} name 节点名称
	 * @apiSuccess (Success 200) {Integer} type 设备类型：0-软件电话，1-紧急电话，2-亭内对讲
	 * @apiSuccess (Success 200) {String} online 在线数量
	 * @apiSuccess (Success 200) {String} offline offline
	 * @apiSuccess (Success 200) {String} status 单个设备状态(0离线 1在线)
	 * @apiSuccess (Success 200) {List[]} children 节点子集
	 * @apiSuccess (Success 200) {String} level 节点级别 1-公司（组织机构）2-路段3-设施（隧道、监控中心、收费站）4-路段方向5-设备
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 	
	 * 	  [
	 *		{ 
	 * 			"id": "orgId_d9bffff7-4f6c-4768-94e8-2491ed592453",
	 * 			"parentId": "",
	 * 			"name": "大化高速公路运营管理中心",
	 * 			"online": 0,
	 *     		"offline": 2,
	 * 			"status": null,
	 * 			"type": 0,
	 * 			"level": 1,
	 * 			"children": [{
	 *  			"id": "deviceId_044be8ff-25b1-4b10-a804-391527d743da",
	 *  			"parentId": "facilityNo_6cdf398a-fc80-4343-aca4-f8db56ee2e26",
	 *  			"name": "22",
	 *   			"online": null,
	 *   			"offline": null,
	 *   			"status": 0,
	 *  			"type": 0,
	 *  			"level": 5,
	 *  			"children": null
	 *  		}]
	 *   	}
	 *	]
	 * @apiErrorExample {json} Success-Response: HTTP/1.1 200 OK code=401用户未登录状态 
	 * 		{ 
	 * 			"code: 401 
	 * 		}
	 * @apiSampleRequest /pbx/mainTree
	 */
	@Login
	@PostMapping("mainTree")
	public Object mainTree(@RequestBody TreeDTO dto, BindingResult result, HttpServletRequest request) {
		ValidUtils.error(result);
		// 首次请求，先拉取设备状态
		if (StringUtils.isBlank(CommonCache.GET_DEVICE_MAP.get("deviceStatus"))) {
			natsDeviceService.syncAllDeviceStatus();
		}
		String role = (String) request.getAttribute("role");
		if (role == null || role.length() < 1) {
			throw new AuthzException("没有角色权限");
		}
		Integer groupType = dto.getGroupType();
		if (groupType == null) {
			dto.setGroupType(1);
		}
		dto.setUserId(getUserId(request));
		dto.setRoleIds(Arrays.asList(role.split(";")));
		return devicePbxService.mainTree(dto);
	}

	/**
	 * @api {POST} /pbx/selectList 查询SIP电话设备 /pbx/selectList
	 * @apiDescription 查询SIP电话设备；创建人：宁艺强，修改人：无
	 * @apiGroup  DevicePbxController-包括紧急电话、软件电话、亭内对讲
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam {number} page 页码，必须大于0
	 * @apiParam {number} limit 每页的数目，必须大于0
	 * @apiParam (Success 200) {String} facilityNo 所属设施id
	 * @apiParam (Success 200) {String} directionNo 路段方向
	 * @apiParam (Success 200) {Integer} type 0-软电话，1-紧急电话，2-亭内对讲
	 * @apiParamExample {json} Request-Example: 
	 *		{ 
	 * 			"facilityNo":"",
	 *  		"directionNo":"交科ITS软电话04",
	 *  		"type":0
	 *   	}
	 * @apiSuccess (Success 200) {String} deviceId 设备ID
	 * @apiSuccess (Success 200) {String} deviceCode 设备编号(自研SIP对应注册编号)
	 * @apiSuccess (Success 200) {String} otherCode 其他编号，捷赛系统设备编号
	 * @apiSuccess (Success 200) {String} deviceTypeNo 12-sip电话类型
	 * @apiSuccess (Success 200) {Integer} type 0-软件电话，1-紧急电话，2-亭内对讲
	 * @apiSuccess (Success 200) {String} deviceName 设备名称
	 * @apiSuccess (Success 200) {String} deviceShortName 设备简称
	 * @apiSuccess (Success 200) {String} deviceNameRemark 设备简称
	 * @apiSuccess (Success 200) {String} userName SIP系统注册登录账号
	 * @apiSuccess (Success 200) {String} password SIP系统注册登录密码
	 * @apiSuccess (Success 200) {String} ipAddress IP地址
	 * @apiSuccess (Success 200) {String} directionNo 方向id
	 * @apiSuccess (Success 200) {String} directionName 方向名称
	 * @apiSuccess (Success 200) {String} facilityNo 所属设施id
	 * @apiSuccess (Success 200) {String} facilityName 所属设施名称
	 * @apiSuccess (Success 200) {String} roadNo 所属路段id
	 * @apiSuccess (Success 200) {String} roadName 所属路段名称
	 * @apiSuccess (Success 200) {String} milePost 桩号
	 * @apiSuccess (Success 200) {Double} mpValue 桩号值
	 * @apiSuccess (Success 200) {String} lng 经度
	 * @apiSuccess (Success 200) {String} lat 经度
	 * @apiSuccess (Success 200) {Integer} sourceId 源站id
	 * @apiSuccess (Success 200) {Integer} use 启用状态
	 * @apiSuccess (Success 200) {String} status 状态(0离线 1在线)
	 * @apiSuccess (Success 200) {String} manufacturer 厂家信息
	 * @apiSuccess (Success 200) {Integer} loginFlag 使用标志1-已经登陆 0-未登录
	 * @apiSuccess (Success 200) {String} remark 备注描述	
	 * @apiSuccess (Success 200) {String} sipCode 所属SIP服务节点编号
	 * @apiSuccess (Success 200) {String} sipName sip服务名称
	 * @apiSuccess (Success 200) {String} entityId SIP节点唯一标识，通常是绑定IP的MAC地址
	 * @apiSuccess (Success 200) {String} hostName SIP节点主机名称
	 * @apiSuccess (Success 200) {String} publicAddress  SIP节点提供SIP服务的公网访问地址，可以存在多个，以逗号隔开，例如***************:5060,***************:5060
	 * @apiSuccess (Success 200) {String} privateAddress SIP节点提供SIP服务的内网访问地址，可以存在多个，以逗号隔开，例如*************:5060,*************:5060
	 * @apiSuccess (Success 200) {String} hostName SIP节点主机名称
	 * @apiSuccess (Success 200) {String} domainName 内网域名
	 * @apiSuccess (Success 200) {String} port 端口号
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 
	 * {	
	 *   "total": 4,
	 * 	 "items": [
	 *		{ 
	 * 			"deviceId": "b536d9c6-4b43-4c2f-9c09-0aed5475a980",
	 * 			"deviceCode": "1300001",
	 * 			"otherCode": "1300001",
	 * 			"deviceTypeNo": 12,
	 *     		"type": 0,
	 * 			"deviceName": "交科ITS软电话04",
	 * 			"deviceShortName": null,
	 * 			"deviceNameRemark": null,
	 *  		"userName": "admin",
	 *  		"password": "admin",
	 *  		"ipAddress": "************",
	 *   		"directionNo": "eb474098-025d-49ad-8d69-b577741283d0",
	 *   		"directionName": "巴马往平果(上行)",
	 *   		"facilityNo": "f0181e83-9858-460b-845c-992d3ecdd97d",
	 *   		"facilityName": "光明山隧道",
	 *   		"roadNo": "216",
	 *   		"roadName": "G7522贵北高速(巴平路)",
	 *   	 	"milePost": "K458+148",
	 *   		"mpValue": null,
	 *   		"lng": null,
	 *   		"lat": null,
	 *   		"sourceId": null,
	 *   		"use": 1,
	 *   		"status": 1,
	 *   		"manufacturer": "软件电话",
	 *   		"loginFlag": null,
	 *   		"remark": "软件电话",
	 *   		"groupCode": null,
	 *  		"sipCode": "10",
	 *  		"sipName": "上云节点-中心节点",
	 *  		"entityId": "10:io:10:10",
	 *  		"hostName": "大化节点",
	 *  		"publicAddress": "*************:5060,*************:5060",
	 *  		"privateAddress": "*************:5060,*************:5060",
	 *  		"domainName": null,
	 *  		"port": null
	 *   	}
	 *	]
	 *}
	 * @apiErrorExample {json} Success-Response: HTTP/1.1 200 OK code=401用户未登录状态 
	 * 		{ 
	 * 			"code: 401 
	 * 		}
	 * @apiSampleRequest /pbx/selectList
	 */
	@Login
	@PostMapping("selectList")
	public Object selectList(@Valid PageDTO pageDTO, @RequestBody DevicePbxDTO dto, HttpServletRequest request) {
		if (StringUtils.isBlank(dto.getFacilityNo())) {
			return new ResponseVO("facilityNo查询条件不能为空！", 400);
		}
		return devicePbxService.selectList(dto);
	}

	/**
	 * @api {POST} /pbx/pageDeviceList 主页分页查询IP电话设备 /pbx/pageDeviceList
	 * @apiDescription 主页分页查询IP电话设备；创建人：宁艺强，修改人：无
	 * @apiGroup  DevicePbxController-包括紧急电话、软件电话、亭内对讲
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam {number} page 页码，必须大于0
	 * @apiParam {number} limit 每页的数目，必须大于0
	 * @apiParam (Success 200) {String} orgId 所属公司
	 * @apiParam (Success 200) {String} roadNo 所属路段
	 * @apiParam (Success 200) {String} facilityNo 所属设施
	 * @apiParam (Success 200) {String} directionNo 路段方向
	 * @apiParam (Success 200) {String} keywords 关键字，进行模糊搜索（设备名称、设备编码、IP地址、设施名称、公司名称、路段名称、方向名称）
	 * @apiParamExample {json} Request-Example: 
	 *		{ 
	 * 			"orgId":"",
	 * 			"roadNo":"216",
	 * 			"keywords":"",
	 * 			"facilityNo": "f0181e83-9858-460b-845c-992d3ecdd97d",
	 *  		"directionNo":""eb474098-025d-49ad-8d69-b577741283d0",
	 *   	}
	 * @apiSuccess (Success 200) {String} deviceId 设备ID
	 * @apiSuccess (Success 200) {String} deviceCode 设备编号(自研SIP对应注册编号)
	 * @apiSuccess (Success 200) {String} deviceTypeNo 12-sip电话类型
	 * @apiSuccess (Success 200) {Integer} type 0-软件电话，1-紧急电话，2-亭内对讲
	 * @apiSuccess (Success 200) {String} deviceName 设备名称
	 * @apiSuccess (Success 200) {String} userName SIP系统注册登录账号
	 * @apiSuccess (Success 200) {String} password SIP系统注册登录密码
	 * @apiSuccess (Success 200) {String} ipAddress IP地址
	 * @apiSuccess (Success 200) {String} directionNo 方向id
	 * @apiSuccess (Success 200) {String} directionName 方向名称
	 * @apiSuccess (Success 200) {String} facilityNo 所属设施id
	 * @apiSuccess (Success 200) {String} facilityName 所属设施名称
	 * @apiSuccess (Success 200) {String} roadNo 所属路段id
	 * @apiSuccess (Success 200) {String} roadName 所属路段名称
	 * @apiSuccess (Success 200) {String} milePost 桩号
	 * @apiSuccess (Success 200) {String} sipCode 所属SIP服务节点编号
	 * @apiSuccess (Success 200) {String} sipName sip服务名称
	 * @apiSuccess (Success 200) {String} entityId SIP节点唯一标识，通常是绑定IP的MAC地址
	 * @apiSuccess (Success 200) {String} hostName SIP节点主机名称
	 * @apiSuccess (Success 200) {String} publicAddress  SIP节点提供SIP服务的公网访问地址，可以存在多个，以逗号隔开，例如***************:5060,***************:5060
	 * @apiSuccess (Success 200) {String} privateAddress SIP节点提供SIP服务的内网访问地址，可以存在多个，以逗号隔开，例如*************:5060,*************:5060
	 * @apiSuccess (Success 200) {String} hostName SIP节点主机名称
	 * @apiSuccess (Success 200) {String} status 电话实时状态：NOT_INUSE, INUSE, BUSY, INVALID, UNAVAILABLE, RINGING, RINGINUSE, ONHOLD 8种状态
	 * @apiSuccess (Success 200) {Integer} line 0-离线 1-在线
	 * @apiSuccess (Success 200) {Integer} sort 排序号
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 
	 * {	
	 *   "total": 4,
	 * 	 "items": [
	 *		{ 
	 * 			"deviceId": "b536d9c6-4b43-4c2f-9c09-0aed5475a980",
	 * 			"deviceCode": "1300001",
	 * 			"deviceTypeNo": 12,
	 *     		"type": 0,
	 * 			"deviceName": "交科ITS软电话04",
	 *  		"userName": "admin",
	 *  		"password": "admin",
	 *  		"ipAddress": "************",
	 *   		"directionNo": "eb474098-025d-49ad-8d69-b577741283d0",
	 *   		"directionName": "巴马往平果(上行)",
	 *   		"facilityNo": "f0181e83-9858-460b-845c-992d3ecdd97d",
	 *   		"facilityName": "光明山隧道",
	 *   		"roadNo": "216",
	 *   		"roadName": "G7522贵北高速(巴平路)",
	 *   	 	"milePost": "K458+148",
	 *  		"sipCode": "10",
	 *  		"sipName": "上云节点-中心节点",
	 *  		"entityId": "10:io:10:10",
	 *  		"hostName": "大化节点",
	 *  		"publicAddress": "*************:5060,*************:5060",
	 *  		"privateAddress": "*************:5060,*************:5060",
	 *  		"status": "UNAVAILABLE",
	 *   		"line": 0,
	 *  		"receivTimes": 1699411397629
	 *   	}
	 *	]
	 *}
	 * @apiErrorExample {json} Success-Response: HTTP/1.1 200 OK code=401用户未登录状态 
	 * 		{ 
	 * 			"code: 401 
	 * 		}
	 * @apiSampleRequest /pbx/pageDeviceList
	 */
	@Login
	@PostMapping("pageDeviceList")
	public Object pageDeviceList(@Valid PageDTO pageDTO, @RequestBody TreeDTO dto, HttpServletRequest request) {
		String role = (String) request.getAttribute("role");
		if (role == null || role.length() < 1) {
			throw new AuthzException("没有角色权限");
		}
		dto.setUserId(getUserId(request));
		dto.setRoleIds(Arrays.asList(role.split(";")));
		return devicePbxService.pageDeviceList(pageDTO, dto);
	}

	/**
	 * @api {POST} /pbx/selectDeviceStatus 根据设备编码获取设备实时状态 /pbx/selectDeviceStatus
	 * @apiDescription 根据设备编码获取设备实时状态；创建人：宁艺强，修改人：无
	 * @apiGroup  DevicePbxController-包括紧急电话、软件电话、亭内对讲
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam (Success 200) {String[]} deviceCodes 设备编码集合
	 * @apiParamExample {json} Request-Example: 
	 *	{ 
	 * 		"deviceCodes": ["1310002","1310005"]
	 *  }
	 * @apiSuccess (Success 200) {String} deviceCode 设备编号(自研SIP对应注册编号)
	 * @apiSuccess (Success 200) {String} status 电话实时状态：NOT_INUSE, INUSE, BUSY, INVALID, UNAVAILABLE, RINGING, RINGINUSE, ONHOLD,UNKNOWN 9种状态
	 * @apiSuccess (Success 200) {Integer} line 0-离线 1-在线
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 
	 * {	
	 *		{ 
	 * 			"deviceCode": "1310002",
	 *  		"status": "UNAVAILABLE",
	 *   		"line": 0
	 *   	},
	 *  	{ 
	 * 			"deviceCode": "1310005",
	 *  		"status": "UNAVAILABLE",
	 *   		"line": 0
	 *   	}
	 *}
	 * @apiErrorExample {json} Success-Response: HTTP/1.1 200 OK code=401用户未登录状态 
	 * 		{ 
	 * 			"code: 401 
	 * 		}
	 * @apiSampleRequest /pbx/selectDeviceStatus
	 */
	@Login
	@PostMapping("selectDeviceStatus")
	public Object selectDeviceStatus(@RequestBody ParamsDTO dto, HttpServletRequest request) {
		List<String> deviceCodes = dto.getDeviceCodes();
		if (CollectionUtils.isEmpty(deviceCodes)) {
			return new ResponseVO("查询设备状态设备编码不能为空！", 400);
		}
		return devicePbxService.selectDeviceStatus(deviceCodes);
	}
}
