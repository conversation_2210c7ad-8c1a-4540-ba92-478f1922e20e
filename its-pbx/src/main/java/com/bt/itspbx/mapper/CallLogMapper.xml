<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itspbx.mapper.CallLogMapper">
	<resultMap type="com.bt.itspbx.domain.vo.CallLogVO" id="CallLogMap">
        <result column="id" property="id" ></result>
        <result column="call_id" property="callId" ></result>
        <result column="type" property="type" ></result>
        <result column="src" property="src" ></result>
        <result column="dst" property="dst" ></result>
        <result column="start" property="start" ></result>
        <result column="answer" property="answer" ></result>
        <result column="end" property="end" ></result>
        <result column="duration" property="duration" ></result>       
        <result column="create_time" property="createTime" ></result>
        <result column="account_code" property="accountCode" ></result>
        <result column="bill_sec" property="billSec" ></result>
        <result column="dcontext" property="dcontext" ></result>
        <result column="clid" property="clid" ></result>
        <result column="channel" property="channel" ></result>
        <result column="dst_channel" property="dstChannel" ></result>
        <result column="last_app" property="lastApp" ></result>
        <result column="last_data" property="lastData" ></result>
        <result column="disposition" property="disposition" ></result>
        <result column="ama_flags" property="amaFlags" ></result>
        <result column="unique_id" property="uniqueId" ></result>
        <result column="linked_id" property="linkedId" ></result>
        <result column="peer_account" property="peerAccount" ></result>
        <result column="sequence" property="sequence" ></result>
        <result column="user_id" property="userId" ></result>
        <result column="subject" property="subject" ></result>
    </resultMap>

    <select id="selectList" resultMap="CallLogMap" parameterType="com.bt.itspbx.domain.dto.CallLogDTO">
       	SELECT t1.id,t1.call_id,t1.type,t1.src,t1.dst,t1.`start`,t1.answer,t1.`end`,t1.duration,t1.create_time,account_code,t1.subject,
			bill_sec,dcontext,clid,channel,dst_channel,last_app,last_data,disposition,ama_flags,unique_id,linked_id,peer_account,sequence,t2.user_id
		FROM pbx_call_log t1,pbx_call_log_user t2  where  t1.call_id=t2.call_id
		<if test="src != null &amp;&amp; src != '' "> AND src like CONCAT('%', #{src}, '%') </if>
	    <if test="dst != null &amp;&amp; dst != '' "> AND dst like CONCAT('%', #{dst}, '%') </if>	   
	    <if test="disposition != null "> AND disposition=#{disposition} </if>
	    <if test="userId != null &amp;&amp; userId != '' "> AND t2.user_id=#{userId} </if>
	    <if test="keyword != null &amp;&amp; keyword != '' ">
	      AND CONCAT(IFNULL(src, '' ),IFNULL(dst, '' )) like CONCAT('%', #{keyword}, '%')
	    </if>
	    order by `start` desc
    </select>

    <select id="getCallLog" resultMap="CallLogMap" parameterType="java.lang.String">
        SELECT id,call_id,type,src,dst,`start`,answer,`end`,duration,create_time,account_code,bill_sec,dcontext,clid,channel,subject,
        	dst_channel,last_app,last_data,disposition,ama_flags,unique_id,linked_id,peer_account,sequence
		FROM pbx_call_log where id=#{id} LIMIT 1
    </select>

    <insert id="add" parameterType="com.bt.itspbx.domain.dto.CallLogDTO">
		insert into pbx_call_log(id,call_id,type,src,dst,`start`,answer,`end`,duration,create_time,account_code,
			bill_sec,dcontext,clid,channel,dst_channel,last_app,last_data,disposition,ama_flags,unique_id,linked_id,peer_account,sequence,subject)
		values (#{id},#{callId},#{type},#{src},#{dst},#{start},#{answer},#{end},#{duration},#{createTime},#{accountCode},#{billSec},#{dcontext},
				#{clid},#{channel},#{dstChannel},#{lastApp},#{lastData},#{disposition},#{amaFlags},#{uniqueId},#{linkedId},#{peerAccount},#{sequence},#{subject})
	</insert>

    <update id="update" parameterType="com.bt.itspbx.domain.dto.CallLogDTO">
		update pbx_call_log set create_time=#{createTime}
		<if test="callId != null &amp;&amp; callId != '' ">,call_id=#{callId}</if>
		<if test="type != null">,type=#{type}</if>
		<if test="src != null">,src=#{src}</if>
		<if test="dst != null">,dst=#{dst}</if>
		<if test="start != null">,`start`=#{start}</if>
		<if test="answer != null">,answer=#{answer}</if>
		<if test="end != null">,`end`=#{end}</if>
		<if test="duration != null">,duration=#{duration}</if>				
		<if test="accountCode != null">,account_code=#{accountCode}</if>
		<if test="billSec != null">,bill_sec=#{billSec}</if>
		<if test="dcontext != null">,dcontext=#{dcontext}</if>
		<if test="clid != null">,clid=#{clid}</if>
		<if test="channel != null">,channel=#{channel}</if>		
		<if test="lastApp != null">,last_app=#{lastApp}</if>
		<if test="lastData != null">,last_data=#{lastData}</if>
		<if test="disposition != null">,disposition=#{disposition}</if>
		<if test="amaFlags != null">,ama_flags=#{amaFlags}</if>
		<if test="uniqueId != null">,unique_id=#{uniqueId}</if>
		<if test="linkedId != null">,linked_id=#{linkedId}</if>
		<if test="peerAccount != null">,peer_account=#{peerAccount}</if>
		<if test="sequence != null">,sequence=#{sequence}</if>
		<if test="subject != null">,subject=#{subject}</if>
		where id=#{id}
	</update>

    <delete id="delete" parameterType="com.bt.itspbx.domain.dto.CallLogDTO">
		delete from pbx_call_log where id = #{id}
	</delete>

    <delete id="batchDelete" parameterType="com.bt.itspbx.domain.dto.CallLogDTO">
        delete from pbx_call_log where id IN
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>
    <insert id="batchAddCallLog" parameterType="java.util.List" useGeneratedKeys="false">
		insert into pbx_call_log(id,call_id,type,src,dst,`start`,answer,`end`,duration,create_time,account_code,
			bill_sec,dcontext,clid,channel,dst_channel,last_app,last_data,disposition,ama_flags,unique_id,linked_id,peer_account,sequence,subject) values
		<foreach collection="list" item="item" index="index" open="(" separator="),(" close=")">
			#{item.id},#{item.callId},#{item.type},#{item.src},#{item.dst},#{item.start},#{item.answer},#{item.end},#{item.duration},#{item.createTime},
			#{item.accountCode},#{item.billSec},#{item.dcontext},#{item.clid},#{item.channel},#{item.dstChannel},#{item.lastApp},#{item.lastData},
			#{item.disposition},#{item.amaFlags},#{item.uniqueId},#{item.linkedId},#{item.peerAccount},#{item.sequence},#{item.subject}
	    </foreach>
    </insert>

	<select id="selectByCallIdAndDstList" resultMap="CallLogMap">
		SELECT
		a.*
		FROM
		pbx_call_log a
		INNER JOIN
		(SELECT DISTINCT call_id,dst,MAX(`end`) AS `end`
		FROM
		pbx_call_log
		GROUP BY
		call_id,dst
		) b ON a.call_id = b.call_id
		AND a.`end` = b.`end`
		WHERE
		a.call_id=#{callId} AND a.dst IN (
		<foreach collection="list" item="item" separator=",">
			#{item}
		</foreach>
		)
		AND a.disposition !='INIT'
	</select>
</mapper>