package com.bt.itspbx.domain.dto;

import com.bt.itscore.domain.dto.AttachDTO;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 描述：广播任务模型类
 *
 * <AUTHOR>
 * @since 2023-11-08 09:37
 */
public class BroadcastTaskDTO {
    private String id;
    @NotBlank(message = "广播任务名称不能为空")
    @Length(max = 255, message = "广播任务名称字符长度超过限定值")
    private String name; // 广播任务名称
    @NotNull(message = "广播模式不能为空")
    @Range(min = 1, max = 3, message = "广播模式仅支持3种")
    private Integer mode; // 广播模式（1-即时模式，2定时模式，3-触发模式）
    @NotNull(message = "广播类型不能为空")
    @Range(min = 1, max = 3, message = "广播类型仅支持3种")
    private Integer type; // 广播类型（1-音频任务，2-喊话任务，3-文字转语音）

    @Length(max = 32, message = "自定义文件名称字符长度超过限定值")
    private String speechName; // 文字转语音自定义文件名
    @Length(max = 300, message = "文本字符长度超过限定值")
    private String text; // 文字转语音文本，最多支持300个字
    private Integer playSoundId; // 播放音频id（存在附件中）
    private Integer promptSoundId; // 提示音id（存在附件中）
    @Range(min = 1, max = 100, message = "循环播报次数范围1~100")
    private Integer number = 1;// 循环播报次数，默认值1

    @Range(max = 86400, message = "最大播放总时长不能超过24小时")
    private Integer duration;// 播放总时长（单位：秒）
    private String areaNo;// 分区id（预留）
    private Integer priority;// 优先级（1-高，2-中，3-低）
    private Integer playMode;// 播放方式（1-循环一次，0-一直循环）
    @Range(min = 1, max = 100, message = "广播音量范围值1~100")
    private Integer volume; // 广播音量（1~100）
    private String creatorUserId;
    private Long createTime;
    private Long updateTime;
    private String updateUserId;

    private String deviceCode;

    private List<String> deviceIdList;

    private String taskRecordId;

    private AttachDTO attach;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getMode() {
        return mode;
    }

    public void setMode(Integer mode) {
        this.mode = mode;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getPlaySoundId() {
        return playSoundId;
    }

    public void setPlaySoundId(Integer playSoundId) {
        this.playSoundId = playSoundId;
    }

    public Integer getPromptSoundId() {
        return promptSoundId;
    }

    public void setPromptSoundId(Integer promptSoundId) {
        this.promptSoundId = promptSoundId;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public String getAreaNo() {
        return areaNo;
    }

    public void setAreaNo(String areaNo) {
        this.areaNo = areaNo;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Integer getPlayMode() {
        return playMode;
    }

    public void setPlayMode(Integer playMode) {
        this.playMode = playMode;
    }

    public Integer getVolume() {
        return volume;
    }

    public void setVolume(Integer volume) {
        this.volume = volume;
    }

    public String getCreatorUserId() {
        return creatorUserId;
    }

    public void setCreatorUserId(String creatorUserId) {
        this.creatorUserId = creatorUserId;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(String updateUserId) {
        this.updateUserId = updateUserId;
    }

    public List<String> getDeviceIdList() {
        return deviceIdList;
    }

    public void setDeviceIdList(List<String> deviceIdList) {
        this.deviceIdList = deviceIdList;
    }

    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    public String getTaskRecordId() {
        return taskRecordId;
    }

    public void setTaskRecordId(String taskRecordId) {
        this.taskRecordId = taskRecordId;
    }

    public String getSpeechName() {
        return speechName;
    }

    public void setSpeechName(String speechName) {
        this.speechName = speechName;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public AttachDTO getAttach() {
        return attach;
    }

    public void setAttach(AttachDTO attach) {
        this.attach = attach;
    }
}
