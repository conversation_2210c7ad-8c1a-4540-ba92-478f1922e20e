package com.bt.itspbx.domain.vo;

import com.bt.itscore.domain.vo.ResultVO;

/**
 * 描述：广播返回结果参数封装对象
 *
 * <AUTHOR>
 * @since 2023-11-15 11:12
 */
public class BroadcastResVO extends ResultVO {
    private Integer type;
    private String taskId;
    private String taskRecordId;
    private String deviceCode;

    private String callId;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskRecordId() {
        return taskRecordId;
    }

    public void setTaskRecordId(String taskRecordId) {
        this.taskRecordId = taskRecordId;
    }


    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    public String getDeviceCode() {
        return deviceCode;
    }

    public String getCallId() {
        return callId;
    }

    public void setCallId(String callId) {
        this.callId = callId;
    }
}
