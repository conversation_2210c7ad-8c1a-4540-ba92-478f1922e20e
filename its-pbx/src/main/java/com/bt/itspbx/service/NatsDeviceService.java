package com.bt.itspbx.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.bt.itscore.exception.FailException;
import com.bt.itscore.utils.GsonUtils;
import com.bt.itspbx.domain.dto.*;
import com.bt.itspbx.domain.vo.*;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.bt.itscore.domain.vo.DevicePbxCacheVO;
import com.bt.itscore.domain.vo.ResponseVO;
import com.bt.itspbx.component.CommonCache;
import com.bt.itspbx.constants.StatusEnum;
import com.bt.itspbx.domain.entity.NatsBean;
import com.bt.itspbx.mapper.DevicePbxMapper;
import com.bt.itspbx.mapper.SipMapper;
import com.bt.itspbx.utils.ClassCompareUtils;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

@Service("natsDeviceService")
public class NatsDeviceService {
	public static final Log log = LogFactory.getLog(NatsDeviceService.class);
	@Autowired
	private NatsService natsService;
	@Autowired
	private DevicePbxMapper devicePbxMapper;
	@Autowired
	private SyncLogService syncLogService;
	@Autowired
	private SipMapper sipMapper;
	@Autowired
	private RedisService redisService;
	@Autowired
	private WebSocketService webSocketService;
	@Autowired
	private BroadcastTaskService broadcastTaskService;

	/**
	 * @描述 查询路段Aris服务SIP节点现有设备
	 */
	public List<EndpointVO> getArisDevices(String entityId) {
		List<EndpointVO> list = new ArrayList<>();
		NatsBean bean = new NatsBean();
		RequestDTO dto = new RequestDTO();
		dto.setType("list");
		String request = GsonUtils.beanToJson(dto);
		bean.setSubject("aris.devices.list." + entityId.replace(":", ""));// 主题
		bean.setMessage(request);
		ResultVO result = natsService.publishAndGetReply(bean);// 发布并获取消息反馈
		String res_code = result.getRes_code();
		if ("1000".equals(res_code)) {
			String message = result.getMessage();
			if (StringUtils.isNotBlank(message)) {
				JsonObject obj = GsonUtils.String2Object(message);
				if ("1000".equals(obj.get("res_code").getAsString())) {
					JsonArray jsonArray = obj.get("endpoints").isJsonNull() ? null
							: obj.get("endpoints").getAsJsonArray();
					if (jsonArray != null && jsonArray.size() > 0) {
						for (final JsonElement e : jsonArray) {
							list.add(GsonUtils.JsonElementToBean(e, EndpointVO.class));
						}
					}
				}
			}
		}
		return list;

	}

	/**
	 * @描述 删除节点信息
	 */
	public String deleteArisDevices(List<String> delPoints, String entityId) {
		String ret = "";
		NatsBean bean = new NatsBean();
		RequestDTO dto = new RequestDTO();
		dto.setEndpoints(delPoints);// 删除设备信息
		String request = GsonUtils.beanToJson(dto);
		bean.setSubject("aris.devices.delete." + entityId.replace(":", ""));// 主题
		bean.setMessage(request);
		ResultVO result = natsService.publishAndGetReply(bean);// 发布并获取消息反馈
		String res_code = result.getRes_code();
		if ("1000".equals(res_code)) {
			String message = result.getMessage();
			if (StringUtils.isNotBlank(message)) {
				JsonObject obj = GsonUtils.String2Object(message);
				if ("1000".equals(obj.get("res_code").getAsString())) {
					ret = "success";
				} else {
					ret = "fail,删除设备失败";
				}
			}
		} else {
			ret = result.getMessage();// 失败反馈
		}
		return ret;
	}

	/**
	 * @描述 同步更新节点紧急电话、亭内对讲、软电电话等设备
	 */
	public String updateArisDevices(List<EndpointVO> updateList, String type, String entityId) {
		String ret = "";
		NatsBean bean = new NatsBean();
		Request2DTO dto = new Request2DTO();
		dto.setType(type);
		dto.setEndpoints(updateList);// 删除设备信息
		String request = GsonUtils.beanToJson(dto);
		bean.setSubject("aris.devices.update." + entityId.replace(":", ""));// 主题
		bean.setMessage(request);
		ResultVO result = natsService.publishAndGetReply(bean);// 发布并获取消息反馈
		String res_code = result.getRes_code();
		if ("1000".equals(res_code)) {
			String message = result.getMessage();
			if (StringUtils.isNotBlank(message)) {
				JsonObject obj = GsonUtils.String2Object(message);
				if ("1000".equals(obj.get("res_code").getAsString())) {
					ret = "success";
				} else {
					ret = "fail";
				}
			}
		} else {
			ret = result.getMessage();// 失败反馈
		}
		return ret;
	}

	/**
	 *
	 * @描述 手动同步单个节点或多个节点设备
	 */
	public String handSyncDevicePbx(SipDTO dto, String userName) {
		String result = "";
		Integer syncType = dto.getSyncType();
		if (syncType == null) {// 默认为单节点手动同步
			syncType = 1;
		}
		// 单节点同步，编号和节点地址不能为空
		if (syncType == 1) {
			String sipCode = dto.getSipCode(); // 节点编号
			SipDTO sipDto = new SipDTO();
			// sipDto.setEntityId(entityId);
			sipDto.setSipCode(sipCode);
			SipVO vo = sipMapper.getSip(sipDto);
			if (vo != null) {
				result = this.syncSingleSip(vo, 11, userName);// 单个同步
			} else {
				result = "当前节点信息为空，请核对填写的节点编号或者节点地址";
			}
		} else { // 同步所有节点
			List<SipVO> sipList = sipMapper.selectDeviceSip();
			for (SipVO vo : sipList) {
				result += this.syncSingleSip(vo, 11, userName);
			}
		}
		return result;
	}

	/**
	 * 单节点同步设备信息
	 * @param vo
	 * @return
	 */
	public String syncSingleSip(SipVO vo, Integer type, String userName) {
		String result = "";
		String sipCode = vo.getSipCode();// SIP节点
		String entityId = vo.getEntityId();// SIP节点唯一标识，通常是绑定IP的MAC地址
		DevicePbxDTO devicePbxDTO = new DevicePbxDTO();
		devicePbxDTO.setUse(1);
		devicePbxDTO.setSipCode(sipCode);
		// 获取云控当前SIP节点设备信息
		List<DevicePbxVO> allList = devicePbxMapper.selectList(devicePbxDTO);
		Integer syncFlag = 0;
		if (CollectionUtils.isEmpty(allList)) {
			result += "同步节点[" + sipCode + "]设备信息为空！";
		} else {
			List<EndpointVO> syncList = new ArrayList<>();// 更新同步SIP设备
			allList.forEach(x -> {
				EndpointVO point = new EndpointVO();
				point.setEndpoint_code(x.getDeviceCode());
				point.setPassword(x.getPassword());
				point.setUsername(x.getUserName());
				Integer webrtc = x.getWebrtc();
				if (webrtc != null) {
					point.setWebrtc(webrtc == 1 ? true : false);
				}
				Integer iceSupport = x.getIceSupport();
				if (iceSupport != null) {
					point.setIce_support(iceSupport == 1 ? true : false);
				}
				point.setType(x.getType());
				point.setManufacturer_id(x.getManufacturer());
				syncList.add(point);
			});
			// 获取节点服务现有的设备
			List<EndpointVO> endpoints = this.getArisDevices(vo.getEntityId());
			if (CollectionUtils.isEmpty(endpoints)) {// 当前节点未同步，直接调用更新方法
				// 新增设备
				String ret = this.updateArisDevices(syncList, "create", entityId);
				if ("success".equals(ret)) { // 创建成功！
					result += "节点[" + sipCode + "],设备创建成功数量为：" + syncList.size();
				} else {
					result += "节点[" + sipCode + "],设备创建失败。";
				}
			} else {
				List<String> nodePointExist = endpoints.stream().map(a -> a.getEndpoint_code())
						.collect(Collectors.toList());// 节点已有设备
				List<String> deviceCodes = syncList.stream().map(b -> b.getEndpoint_code())
						.collect(Collectors.toList());// 云控设备
				// 1、比较获取需要删除的设备信息
				List<String> delPoints = nodePointExist.stream().filter(a -> !deviceCodes.contains(a))
						.collect(Collectors.toList());
				if (!CollectionUtils.isEmpty(delPoints)) {
					String ret = this.deleteArisDevices(delPoints, entityId);
					if ("success".equals(ret)) {
						result += "节点[" + sipCode + "],删除设备数量：" + delPoints.size() + "。";
					}
				}
				// 2、新增的设备：create
				List<EndpointVO> createList = syncList.stream()
						.filter(a -> !nodePointExist.contains(a.getEndpoint_code())).collect(Collectors.toList());
				if (!CollectionUtils.isEmpty(createList)) {
					String create = this.updateArisDevices(createList, "create", entityId);
					if ("success".equals(create)) { // 创建成功！
						result += "节点[" + sipCode + "],设备创建成功数量为：" + createList.size() + "。";
						;
					} else {
						result += "节点[" + sipCode + "],设备创建失败。";
					}
				}
				// 3、更新的设备：update
				List<EndpointVO> updateList = syncList.stream()
						.filter(a -> nodePointExist.contains(a.getEndpoint_code())).collect(Collectors.toList());
				if (!CollectionUtils.isEmpty(updateList)) {
					// 更新设备比较处理，属性相同的设备不更新
					List<EndpointVO> syncUpdateList = new ArrayList<>();// 更新同步SIP设备
					updateList.forEach(o -> {
						String o_code = o.getEndpoint_code();
						for (EndpointVO e : endpoints) {
							String e_code = e.getEndpoint_code();
							if (o_code.equals(e_code) && !ClassCompareUtils.compareObject(o, e)) {
								syncUpdateList.add(o);
								break;
							}
						}
					});
					if (!CollectionUtils.isEmpty(syncUpdateList)) {
						String update = this.updateArisDevices(syncUpdateList, "update", entityId);
						if ("success".equals(update)) { // 创建成功！
							result += "节点[" + sipCode + "],设备更新成功数量为：" + syncUpdateList.size() + "。";
							;
						} else {
							result += "节点[" + sipCode + "],设备更新失败。";
						}
					} else {
						result += "节点[" + sipCode + "],没有需要更新的设备。";
					}
				}
			}
			syncFlag = 1;
		}
		log.info(result);
		String message = "自动同步节点[" + vo.getSipCode() + "-" + vo.getSipName() + "]设备信息";
		if (type == 11) {
			message = "手动同步节点[" + vo.getSipCode() + "-" + vo.getSipName() + "]设备信息";
		}
		syncLogService.createSyncLog(type, message, userName, syncFlag, result);
		return result;
	}

	/**
	 * 云控自动同步各节点信息
	 * @描述 定时同步各节点设备信息
	 */
	public void autoSyncDevicePbxs() {
		List<SipVO> sipList = sipMapper.selectDeviceSip();
		int interval = 8000;
		if (!CollectionUtils.isEmpty(sipList)) {
			String result = "";
			try {
				for (SipVO vo : sipList) {
					Thread.sleep(interval);
					result += this.syncSingleSip(vo, 10, "");
				}
			} catch (Exception e) {
				result += e.getMessage();
			}
			log.info(result);

		}

	}

	/**
	 * @描述 根据电话号码以及节点标志同步获取节点设备状态
	 */
	public List<DeviceStateVO> getSipDeviceState(List<String> endpoints, String entityId) {
		List<DeviceStateVO> list = this.getArisDeviceStates(endpoints, entityId);
		this.syncDeviceStatus(list);// 更新本地缓存
		return list;

	}

	/**
	 * 查询获取节点设备状态信息
	 * @param entityId
	 * @return
	 */
	public List<DeviceStateVO> getArisDeviceStates(List<String> endpoints, String entityId) {
		List<DeviceStateVO> list = new ArrayList<>();
		NatsBean bean = new NatsBean();
		RequestDTO dto = new RequestDTO();
		dto.setEndpoints(endpoints);// 节点设备编号
		String request = GsonUtils.beanToJson(dto);
		bean.setSubject("aris.deviceStates.list." + entityId.replace(":", ""));// 主题
		bean.setMessage(request);
		ResultVO result = natsService.publishAndGetReply(bean);// 发布并获取消息反馈
		String res_code = result.getRes_code();
		if ("1000".equals(res_code)) {
			String message = result.getMessage();
			if (StringUtils.isNotBlank(message)) {
				JsonObject obj = GsonUtils.String2Object(message);
				if ("1000".equals(obj.get("res_code").getAsString())) {
					JsonArray jsonArray = obj.get("device_states").getAsJsonArray();
					if (jsonArray.size() > 0) {
						for (final JsonElement e : jsonArray) {
							list.add(GsonUtils.JsonElementToBean(e, DeviceStateVO.class));
						}
					}
				}
			}
		}
		return list;

	}

	/**
	 * 云控手动同步各节点设备状态
	 * @描述 定时同步各节点设备信息状态
	 */
	public String handSyncDeviceStatus(ParamsDTO dto, String userName) {
		String result = "";
		List<DeviceCodeVO> sipDeviceList = sipMapper.selectSipDeviceCode(dto);
		int interval = 1000;
		if (!CollectionUtils.isEmpty(sipDeviceList)) {
			Map<String, List<DeviceCodeVO>> groupMap = sipDeviceList.stream()
					.collect(Collectors.groupingBy(DeviceCodeVO::getEntityId));
			for (Map.Entry<String, List<DeviceCodeVO>> entry : groupMap.entrySet()) {
				String entityId = entry.getKey();
				Integer syncFlag = 0;
				String result2 = "";
				try {
					Thread.sleep(interval);
					List<String> endpoints = entry.getValue().stream().map(a -> a.getDeviceCode())
							.collect(Collectors.toList());
					List<DeviceStateVO> statuList = this.getArisDeviceStates(endpoints, entityId);
					this.syncDeviceStatus(statuList);
					result += "SIP节点：" + entityId + ",同步设备成功！";
					result2 = "SIP节点：" + entityId + ",同步设备成功！";
					syncFlag = 1;
				} catch (InterruptedException e) {
					result += "SIP节点：" + entityId + ",同步设备状态异常：" + e.getMessage();
					result2 = "SIP节点：" + entityId + ",同步设备状态异常：" + e.getMessage();
				}
				syncLogService.createSyncLog(21, "手动同步SIP节点[" + entityId + "]设备状态", userName, syncFlag, result2);
			}
			CommonCache.GET_DEVICE_MAP.put("deviceStatus", "success");
		}
		return result;
	}

	public void syncAllDeviceStatus() {
		List<DeviceCodeVO> sipDeviceList = sipMapper.selectSipDeviceCode(new ParamsDTO());
		int interval = 1000;
		if (!CollectionUtils.isEmpty(sipDeviceList)) {
			Map<String, List<DeviceCodeVO>> groupMap = sipDeviceList.stream()
					.collect(Collectors.groupingBy(DeviceCodeVO::getEntityId));
			for (Map.Entry<String, List<DeviceCodeVO>> entry : groupMap.entrySet()) {
				String result = "";
				String entityId = entry.getKey();
				Integer syncFlag = 0;
				try {
					Thread.sleep(interval);
					List<String> endpoints = entry.getValue().stream().map(a -> a.getDeviceCode())
							.collect(Collectors.toList());
					List<DeviceStateVO> statuList = this.getArisDeviceStates(endpoints, entityId);
					this.syncDeviceStatus(statuList);
					syncFlag = 1;
					result = "SIP节点：" + entityId + ",同步设备成功！";
				} catch (InterruptedException e) {
					result = "SIP节点：" + entityId + ",同步设备状态异常：" + e.getMessage();
				}
				syncLogService.createSyncLog(20, "自动同步SIP节点[" + entityId + "]设备状态", "", syncFlag, result);
			}
			CommonCache.GET_DEVICE_MAP.put("deviceStatus", "success");
		}
	}

	private void syncDeviceStatus(List<DeviceStateVO> statuList) {
		if (!CollectionUtils.isEmpty(statuList)) {
			Map<String, DevicePbxCacheVO> values = new HashedMap<>();// 获取更新缓存状态
			List<DeviceStatusDTO> updateList = new ArrayList<DeviceStatusDTO>();
			statuList.stream().forEach(o -> {
				String state = o.getState();
				String deviceCode = o.getEndpoint();
				DevicePbxCacheVO devicePbx = redisService.getDevicePbxCache(deviceCode);// 根据设备编号获取缓存状态
				if (devicePbx != null && !state.equals(devicePbx.getStatus())) { // 判断状态是否不同
					int lineStatus = checkState(state);
					devicePbx.setStatus(state);
					devicePbx.setLine(lineStatus);
					if(lineStatus==0) { //离线 
						devicePbx.setOffLineType("1"); //设备离线
					}else {
						devicePbx.setOffLineType("");
					}
					values.put(deviceCode, devicePbx);
					// 判断是否更新数据库数据
					DeviceStatusDTO deviceStatus = new DeviceStatusDTO();
					deviceStatus.setDeviceCode(deviceCode);
					deviceStatus.setStatus(lineStatus);
					updateList.add(deviceStatus);
					List<String> userIds = redisService.getDeviceUserIds(deviceCode);
					if (!CollectionUtils.isEmpty(userIds)) { // 获取具有操作权限的用户
						webSocketService.pushSipDeviceStatus(devicePbx, userIds); // 有变动推送前端
					}
				}
			});
			// 更新缓存
			if (!CollectionUtils.isEmpty(values)) {
				redisService.updateDevicePbxList(values);
			}
			// 更新数据库状态
			if (!CollectionUtils.isEmpty(updateList)) {
				devicePbxMapper.batchUpdateStatus(updateList);
			}
		}
	}

	private int checkState(String state) {
		int status = 1;
		if (StatusEnum.INVALID.getValue().equals(state) || StatusEnum.UNAVAILABLE.getValue().equals(state)
				|| StatusEnum.UNKNOWN.getValue().equals(state)) {
			status = 0;// 离线
		}
		return status;
	}

	/**
	 * 前端停止指定通道通话
	 * @param paramsDTO
	 * @return
	 */
	public ResponseVO stopChannel(ParamsDTO paramsDTO) {
		String deviceCode = paramsDTO.getDeviceCode();
		boolean flag = false;
		String msg = "";
		DevicePbxCacheVO devicePbx = redisService.getDevicePbxCache(deviceCode);
		if (devicePbx != null) {
			String entityId = devicePbx.getEntityId();
			NatsBean bean = new NatsBean();
			RequestDTO dto = new RequestDTO();
			dto.setCallid(paramsDTO.getCallId());
			dto.setChannelid(paramsDTO.getChannelId());
			// dto.setEndpoint(deviceCode);
			String request = GsonUtils.beanToJson(dto);
			bean.setSubject("aris.dial.stopChannel." + entityId.replace(":", ""));// 主题
			bean.setMessage(request);
			ResultVO result = natsService.publishAndGetReply(bean);// 发布并获取消息反馈
			String res_code = result.getRes_code();
			if ("1000".equals(res_code)) {
				String message = result.getMessage();
				if (StringUtils.isNotBlank(message)) {
					JsonObject obj = GsonUtils.String2Object(message);
					if ("1000".equals(obj.get("res_code").getAsString())) {
						flag = true;
					}
					msg = obj.get("message").getAsString();
				}
			} else {
				msg = "挂断失败，SIP服务没有反馈！";
			}
		} else {
			msg = "挂断失败，没有找到需要挂断的电话！";
		}
		if (flag) {
			return new ResponseVO(msg, 1);
		} else {
			return new ResponseVO(msg, 0);
		}

	}

	/**
	 * 前端挂断指定设备所有通道
	 * @param paramsDTO
	 * @return
	 */
	public ResponseVO stopEndpoint(ParamsDTO paramsDTO) {
		String deviceCode = paramsDTO.getDeviceCode();
		// 增加参数校验，若是属于任务，则禁止挂断
		List<BroadcastTaskRecordDetailVO> detailVOList = broadcastTaskService.selectRunningTaskByDeviceCode(deviceCode);
		if (!CollectionUtils.isEmpty(detailVOList)) {
			throw new FailException("该设备正在广播中，挂断请先停止广播任务（" + detailVOList.get(0).getTaskName()+"）");
		}
		boolean flag = false;
		String msg = "";
		DevicePbxCacheVO devicePbx = redisService.getDevicePbxCache(deviceCode);
		if (devicePbx != null) {
			String entityId = devicePbx.getEntityId();
			NatsBean bean = new NatsBean();
			RequestDTO dto = new RequestDTO();
			dto.setEndpoint(deviceCode);
			String request = GsonUtils.beanToJson(dto);
			bean.setSubject("aris.dial.stopEndpoint." + entityId.replace(":", ""));// 主题
			bean.setMessage(request);
			ResultVO result = natsService.publishAndGetReply(bean);// 发布并获取消息反馈
			String res_code = result.getRes_code();
			if ("1000".equals(res_code)) {
				String message = result.getMessage();
				if (StringUtils.isNotBlank(message)) {
					JsonObject obj = GsonUtils.String2Object(message);
					if ("1000".equals(obj.get("res_code").getAsString())) {
						flag = true;
					}
					msg = obj.get("message").getAsString();
				}
			} else {
				msg = "挂断失败，SIP服务没有反馈！";
			}
		} else {
			msg = "挂断失败，没有找到需要挂断的电话！";
		}
		if (flag) {
			return new ResponseVO(msg, 1);
		} else {
			return new ResponseVO(msg, 0);
		}

	}

	/**
	 * 前端主动挂断整个通话
	 * @param paramsDTO
	 * @return
	 */
	public ResponseVO stop(ParamsDTO paramsDTO) {
		String deviceCode = paramsDTO.getDeviceCode();
		boolean flag = false;
		String msg = "";
		DevicePbxCacheVO devicePbx = redisService.getDevicePbxCache(deviceCode);
		if (devicePbx != null) {
			String entityId = devicePbx.getEntityId();
			NatsBean bean = new NatsBean();
			RequestDTO dto = new RequestDTO();
			dto.setCallid(paramsDTO.getCallId());
			String request = GsonUtils.beanToJson(dto);
			bean.setSubject("aris.dial.stop." + entityId.replace(":", ""));// 主题
			bean.setMessage(request);
			ResultVO result = natsService.publishAndGetReply(bean);// 发布并获取消息反馈
			String res_code = result.getRes_code();
			if ("1000".equals(res_code)) {
				String message = result.getMessage();
				if (StringUtils.isNotBlank(message)) {
					JsonObject obj = GsonUtils.String2Object(message);
					if ("1000".equals(obj.get("res_code").getAsString())) {
						flag = true;
					}
					msg = obj.get("message").getAsString();
				}
			} else {
				msg = "挂断失败，SIP服务没有反馈！";
			}
		} else {
			msg = "挂断失败，没有找到需要挂断的电话！";
		}
		if (flag) {
			return new ResponseVO(msg, 1);
		} else {
			return new ResponseVO(msg, 0);
		}
	}

	/**
	 * @描述 更新、新增同步aris
	 */
	public void syncUpdateAris(DevicePbxDTO dto, String type) {
		// 更新缓存信息
		DevicePbxVO pbxVO = devicePbxMapper.getDevicePbx(dto.getDeviceId());
		if (pbxVO != null) {
			String deviceCode = pbxVO.getDeviceCode();
			String status = "UNAVAILABLE";// 离线
			if ("update".equals(type)) {// 新增
				DevicePbxCacheVO devicePbx = redisService.getDevicePbxCache(deviceCode);
				if (devicePbx != null) {
					status = devicePbx.getStatus();
				}
			}
			DevicePbxCacheVO devicePbxCache = this.getDevicePbxProtity(pbxVO, status);
			redisService.updateDevicePbxCache(deviceCode, devicePbxCache); // 更新缓存设备状态

		}
		String sipCode = dto.getSipCode();
		if (StringUtils.isNotBlank(sipCode)) {
			SipDTO dto2 = new SipDTO();
			dto2.setSipCode(sipCode);
			SipVO sipVO = sipMapper.getSip(dto2);
			if (sipVO != null) {
				List<EndpointVO> list = new ArrayList<>();
				EndpointVO vo = new EndpointVO();
				vo.setEndpoint_code(dto.getDeviceCode());
				vo.setIce_support(dto.getIceSupport() == 1 ? true : false);
				vo.setWebrtc(dto.getWebrtc() == 1 ? true : false);
				vo.setPassword(dto.getPassword());
				vo.setUsername(dto.getUserName());
				list.add(vo);
				this.updateArisDevices(list, type, sipVO.getEntityId());
			}
		}
	}

	/**
	 * @描述 删除同步aris
	 */
	public void syncDeleteAris(List<DevicePbxVO> deleteList, Integer type) {
		if (!CollectionUtils.isEmpty(deleteList)) {
			Map<String, List<DevicePbxVO>> groupMap = deleteList.stream()
					.collect(Collectors.groupingBy(DevicePbxVO::getEntityId));
			groupMap.forEach((key, valus) -> {// 删除同步各节点信息
				List<String> delPoints = valus.stream().map(a -> a.getDeviceCode()).collect(Collectors.toList());
				delPoints.forEach(x -> { // 删除缓存设备
					redisService.deleteDevicePbxCache(x);
					// redisService.deleteDeviceUserIds(x);// 删除设备推送用户权限
				});
				this.deleteArisDevices(delPoints, key);
			});
		}
	}

	private DevicePbxCacheVO getDevicePbxProtity(DevicePbxVO vo, String status) {
		DevicePbxCacheVO bean = new DevicePbxCacheVO();
		bean.setDeviceId(vo.getDeviceId());
		bean.setDeviceCode(vo.getDeviceCode());
		bean.setDeviceName(vo.getDeviceName());
		bean.setDeviceTypeNo(vo.getDeviceTypeNo());
		bean.setUserName(vo.getUserName());
		bean.setPassword(vo.getPassword());
		bean.setIpAddress(vo.getIpAddress());
		bean.setMilePost(vo.getMilePost());
		bean.setDirectionNo(vo.getDirectionNo());
		bean.setDirectionName(vo.getDirectionName());
		bean.setFacilityNo(vo.getFacilityNo());
		bean.setFacilityName(vo.getFacilityName());
		bean.setRoadNo(vo.getRoadNo());
		bean.setRoadName(vo.getRoadName());
		bean.setSipCode(vo.getSipCode());
		bean.setSipName(vo.getSipName());
		bean.setType(vo.getType());
		bean.setEntityId(vo.getEntityId());
		bean.setHostName(vo.getHostName());
		bean.setPrivateAddress(vo.getPublicAddress());
		bean.setPublicAddress(vo.getPublicAddress());
		bean.setStatus(status);
		bean.setSort(vo.getSort() == null ? 0 : vo.getSort());
		bean.setRightFlag(0);
		if (StatusEnum.INVALID.getValue().equals(status) || StatusEnum.UNAVAILABLE.getValue().equals(status)
				|| StatusEnum.UNKNOWN.getValue().equals(status)) {
			bean.setLine(0);// 离线
		} else {
			bean.setLine(1);// 在线
		}
		return bean;
	}

	public SipVO querySipByDeviceCode(String deviceCode) {
		if (StringUtils.isBlank(deviceCode)) {
			return null;
		}
		return sipMapper.querySipByDeviceCode(deviceCode);
	}

}
