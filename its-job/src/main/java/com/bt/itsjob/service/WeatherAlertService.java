package com.bt.itsjob.service;

import java.util.List;
import java.util.UUID;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.bt.itscore.domain.dto.AlarmMessageDTO;
import com.bt.itscore.enums.AlarmTypeEnum;
import com.bt.itscore.enums.OrgRoadEnum;
import com.bt.itscore.utils.HttpClientUtils;
import com.bt.itsjob.domain.dto.AlarmDTO;
import com.bt.itsjob.domain.vo.AlertAreaVO;
import com.bt.itsjob.domain.vo.AlertResultVO;
import com.bt.itsjob.domain.vo.AlertWeatherVO;
import com.bt.itsjob.feign.ItsWebSocketFeignClient;
import com.bt.itsjob.mapper.AlertAreaMapper;
import com.bt.itsjob.sharding.mapper.AlarmMapper;
import com.google.gson.Gson;
import com.xxl.job.core.log.XxlJobLogger;

@Service("weatherAlertService")
public class WeatherAlertService {

	@Value("${weather.daily}")
	private String weatherDailyUrl; // 天气级预报地址
	@Autowired
	private AlertAreaMapper alertAreaMapper;
	@Autowired
	private AlarmMapper alarmMapper;
	@Autowired
	private ItsWebSocketFeignClient itsWebSocketFeignClient;

	public void checkAlert(String param) {
		List<AlertAreaVO> alertAreaVOS = alertAreaMapper.selectList();
		if (CollectionUtils.isEmpty(alertAreaVOS)) {
			XxlJobLogger.log("没有天气告警，alertAreaVOS：{}", alertAreaVOS);
			return ;
		}
		// 分别对应请求各个区域是否有预警信息
		for (AlertAreaVO vo : alertAreaVOS) {
			List<AlertWeatherVO> weatherVOS = getAlert(vo);
			if (CollectionUtils.isEmpty(weatherVOS)) {
				continue;
			}
			for (AlertWeatherVO alertWeatherVO : weatherVOS) {
				if (alertWeatherVO == null) {
					continue;
				}
				String type = toYkAlarmType(alertWeatherVO);
				String code = alertWeatherVO.getCode();
				String alertId = alertWeatherVO.getAlertId();
				String description = alertWeatherVO.getDescription();
				String logDesc = "天气告警编号：" + alertId + "，code：" + code + "，详情：" + description;
				if (StringUtils.isBlank(type)) {
					XxlJobLogger.log("不关注的{}", logDesc);
					continue;
				}
				//校验行政编码adcode是否一致，不一致则过滤告警
//				String adcode = alertWeatherVO.getAdcode();
//				if(StringUtils.isBlank(adcode) || !adcode.substring(0, 4).equals(vo.getAdcode().substring(0, 4))) {
//					XxlJobLogger.log("过滤行政编码不一致的{}", logDesc);
//					continue;
//				}
				AlarmDTO alarmDTO = new AlarmDTO();
				alarmDTO.setType(Integer.valueOf(type));
				int add = addWeatherAlarm(alarmDTO, vo, alertWeatherVO);
				if (add > 0) {
					XxlJobLogger.log("入库成功的{}", logDesc);
					asyncWebSocket(alarmDTO);
				}

			}
		}
	}

	/**
	 * @描述 新增天气告警
	 * @param alarmDTO
	 * @param vo
	 * @param alertWeatherVO
	 * @return
	 */
	private int addWeatherAlarm(AlarmDTO alarmDTO, AlertAreaVO vo, AlertWeatherVO alertWeatherVO) {
		// 查询是否存在该预警信息
		long pubtimestamp = alertWeatherVO.getPubtimestamp();
		alarmDTO.setCreateTime(pubtimestamp);
		alarmDTO.setDeviceId(alertWeatherVO.getAlertId());
		int alertCount = alarmMapper.selectByDeviceId(alarmDTO);
		if (alertCount > 0) {// 告警已入库，不能重复入库
			return 0;
		}
		alarmDTO.setId(UUID.randomUUID().toString());
		alarmDTO.setDetail(alertWeatherVO.getDescription());
		alarmDTO.setTitle(alertWeatherVO.getTitle());
		alarmDTO.setLat(vo.getLat());
		alarmDTO.setLng(vo.getLng());
		alarmDTO.setFacilityNo(vo.getFacilityNo());
		alarmDTO.setRoadName(vo.getRoadName());
		alarmDTO.setRoadNo(vo.getRoadNo());
		alarmDTO.setMilePost(vo.getMilePost());
		alarmDTO.setDirectionName("双向");
		alarmDTO.setDirectionNo(vo.getDirectionNo());
		return alarmMapper.add(alarmDTO);
	}

	/**
	 * @描述 天气告警WebSocket推送
	 * @param alarmDTO
	 */
	private void asyncWebSocket(AlarmDTO alarmDTO) {
		new Thread(new Runnable() {
			@Override
			public void run() {
				XxlJobLogger.log("天气告警websocket推送--------------------");
				AlarmMessageDTO messageDTO = new AlarmMessageDTO();
				messageDTO.setId(alarmDTO.getId());
				messageDTO.setDetail(alarmDTO.getDetail());
				messageDTO.setType(alarmDTO.getType() + "");
				messageDTO.setFacilityNo(alarmDTO.getFacilityNo());
				String orgName = "";
				AlarmTypeEnum alarmTypeEnum = AlarmTypeEnum.getName(alarmDTO.getType());
				String typeName = alarmTypeEnum.getName();
				String ptypeName = alarmTypeEnum.getPname();
				if (typeName == null) {
					typeName = "";
				} else {
					typeName = "“" + typeName + "”";
				}
				Integer roadNo = alarmDTO.getRoadNo();
				if (roadNo != null) {
					orgName = OrgRoadEnum.getName(roadNo);
					if (orgName != null) {
						orgName = orgName + "公司，";
					} else {
						orgName = "";
					}
				}

				messageDTO.setVoicePrompt(orgName + "有" + typeName + ptypeName);
				itsWebSocketFeignClient.pushAlarm(messageDTO);
			}
		}).start();
	}

	private List<AlertWeatherVO> getAlert(AlertAreaVO vo) {
		String url = weatherDailyUrl.replace("#LNG", vo.getLng()).replace("#LAT", vo.getLat());
		String response = HttpClientUtils.get(url, null);
		if (!StringUtils.isEmpty(response)) {
			AlertResultVO alertResultVO = new Gson().fromJson(response, AlertResultVO.class);
			if (alertResultVO != null && alertResultVO.getResult() != null
					&& alertResultVO.getResult().getAlert() != null
					&& alertResultVO.getResult().getAlert().getContent() != null) {
				return alertResultVO.getResult().getAlert().getContent();
			}
		}
		return null;
	}

	/**
	 * @描述 转换成云控的天气告警类型值
	 * @param vo
	 * @return
	 */
	private String toYkAlarmType(AlertWeatherVO vo) {
		String code = vo.getCode();
		if (StringUtils.isNotEmpty(code)) {
			if (code.startsWith("01")) {
				return "801";
			} else if (code.startsWith("02")) {
				return "802";
			} else if (code.startsWith("05")) {
				return "803";
			} else if (code.startsWith("11")) {
				return "804";
			} else if (code.startsWith("12")) {
				return "805";
			} else if (code.startsWith("14")) {
				return "806";
			} else if (code.startsWith("16")) {
				return "807";
			}
		}
		return null;
	}

}
