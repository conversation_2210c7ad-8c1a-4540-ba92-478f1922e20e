package com.bt.itsjob.service;

import com.bt.itscore.utils.HttpClientUtils;
import com.bt.itscore.utils.ServiceUtils;
import com.bt.itsjob.feign.ItsTunnelFeignClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class TunnelErrMoveService {
    @Autowired
    ItsTunnelFeignClient feignClient;
    @Value("${yk.domain}")
    String ykDomain;

    public void move() {
        Map<String, String> head = ServiceUtils.getInnerLoginHead();
        HttpClientUtils.post(ykDomain + "/its-tunnel/tunnel/moveDeviceWorkerErr", head, "{}");
    }
}
