package com.bt.itsjob.service;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bt.itscore.utils.GsonUtils;
import com.bt.itscore.utils.TimeUtils;
import com.bt.itsjob.domain.dto.ChargingpileDayStatDTO;
import com.bt.itsjob.mapper.ChargingpileMapper;
import com.xxl.job.core.log.XxlJobLogger;
/**
 * <AUTHOR>
 * @date 2025年6月13日 09:19:20
 * @Description 充电桩业务处理类
 */
@Service("chargingpileService")
public class ChargingpileService {
	@Autowired
	ChargingpileMapper chargingpileMapper;
	/**
	 * 统计日数据，封装入库
	 * @param date
	 */
	public void addDayStat(String date) {

		// 查询服务区设施，及对应的枪数量
		List<ChargingpileDayStatDTO> list = chargingpileMapper.selectFacilityCharginggun();
		if (list.size() == 0) {
			XxlJobLogger.log("[充电桩日统计数据]无设施充电枪数据");
			return ;
		}
		long startTime = TimeUtils.todayLong();
		if (StringUtils.isNotBlank(date) && date.length() == 10) {
			startTime = TimeUtils.toLong(date, TimeUtils.DATE);
		}
		List<ChargingpileDayStatDTO> statList = chargingpileMapper.dayStat(startTime);
		for (ChargingpileDayStatDTO chargingpileDayStatDTO : statList) {
//			XxlJobLogger.log("[充电桩日统计数据]dayStat:{}", GsonUtils.beanToJson(chargingpileDayStatDTO));
			String facilityNo = chargingpileDayStatDTO.getFacilityNo();
			for (ChargingpileDayStatDTO facilityCharginggun : list) {
				if (facilityCharginggun.getFacilityNo().equals(facilityNo)) {
					facilityCharginggun.setChargeCapacity(chargingpileDayStatDTO.getChargeCapacity());
					facilityCharginggun.setChargeDuration(chargingpileDayStatDTO.getChargeDuration());
					facilityCharginggun.setDeductionAmount(chargingpileDayStatDTO.getDeductionAmount());
					facilityCharginggun.setRechargeAmount(chargingpileDayStatDTO.getRechargeAmount());
					facilityCharginggun.setOrderNum(chargingpileDayStatDTO.getOrderNum());
					XxlJobLogger.log("[充电桩日统计数据]facilityCharginggun:{}", GsonUtils.beanToJson(facilityCharginggun));
					break;
				}
			}
		}
		String statDate = TimeUtils.getTimeString(TimeUtils.FULL_TIME, startTime * 1000);
		String createTime = TimeUtils.getTimeString();
		for (ChargingpileDayStatDTO chargingpileDayStatDTO : list) {
			chargingpileDayStatDTO.setStatDate(statDate);
			chargingpileDayStatDTO.setCreateTime(createTime);
			XxlJobLogger.log("[充电桩日统计数据]selectFacilityCharginggun:{}", GsonUtils.beanToJson(chargingpileDayStatDTO));
		}
		int rows = chargingpileMapper.deleteDayStat(statDate);
		XxlJobLogger.log("[充电桩日统计数据]批量删除：{}", rows);
		rows = chargingpileMapper.batchInsert(list);
		if (rows > 0) {
			XxlJobLogger.log("[充电桩日统计数据]批量入库成功：{}", rows);
		} else {
			XxlJobLogger.log("[充电桩日统计数据]批量入库失败");
		}

	}

}
