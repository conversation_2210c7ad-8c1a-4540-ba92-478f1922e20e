package com.bt.itsjob.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bt.itsjob.domain.vo.NodeServerInternetVO;
import com.bt.itsjob.mapper.NodeServerInternetMapper;

@Service("nodeServerInternetService")
public class NodeServerInternetService {
	@Autowired
	private NodeServerInternetMapper nodeServerInternetMapper;

	public List<NodeServerInternetVO> selectAll() {
		return nodeServerInternetMapper.selectAll();
	}

	public int batchUpdate(List<NodeServerInternetVO> list) {
		return nodeServerInternetMapper.batchUpdate(list);
	}

}
