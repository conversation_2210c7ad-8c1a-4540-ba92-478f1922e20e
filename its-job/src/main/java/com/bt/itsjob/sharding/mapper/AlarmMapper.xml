<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itsjob.sharding.mapper.AlarmMapper">

	<insert id="add" parameterType="com.bt.itsjob.domain.dto.AlarmDTO">
	INSERT INTO alarmshard (id,device_id,type,detail,create_time,road_no,road_name,direction_name,mile_post,lng,lat,title,direction_no,facility_no) VALUES (
	#{id},
	#{deviceId},
	#{type},
	#{detail},
	#{createTime},
	#{roadNo},
	#{roadName},
	#{directionName},
	#{milePost},
	#{lng},
	#{lat},
	#{title},
	#{directionNo},
	#{facilityNo}
	)
	</insert>

	<select id="selectByDeviceId" parameterType="com.bt.itsjob.domain.dto.AlarmDTO" resultType="java.lang.Integer">
		SELECT COUNT(id) FROM alarmshard WHERE device_id = #{deviceId} AND create_time=#{createTime}
	</select>

	<resultMap type="com.bt.itsjob.domain.vo.RadarAlarmSyncVO" id="RadarAlarmSyncMap">
		<id column="id" property="id"/>
		<result column="device_id" property="deviceId"/>
		<result column="alarm_id" property="alarmId"/>
		<result column="create_time" property="alarmTime"/>
		<result column="camera_code" property="gbCode"/>
		<result column="source_id" property="sourceId"/>
	</resultMap>
	<select id="selectRadarAlarmNotSync" resultMap="RadarAlarmSyncMap">
		SELECT ras.id,ras.device_id,ras.alarm_id,a.create_time,dc.camera_code,d.source_id FROM 
			radar_alarm_sync AS ras,alarmshard AS a,device_camera AS dc,device AS d
		WHERE ras.alarm_id=a.id AND ras.device_id=dc.device_id AND ras.device_id=d.device_id AND (upload_status=0 OR upload_status=2) AND create_time>(UNIX_TIMESTAMP()-86400) LIMIT 10
	</select>

	<select id="countGtCreateTime" resultType="int">
	SELECT COUNT(*) FROM alarmshard WHERE create_time > #{createTime}
	</select>

	<select id="countUnhandleGtCreateTime" resultType="int">
	SELECT COUNT(*) FROM alarmshard WHERE create_time > #{createTime} AND handle_status = 0
	</select>

	<select id="countLteqCreateTime" resultType="int">
	SELECT COUNT(*) FROM alarmshard WHERE create_time &lt;= #{createTime}
	</select>

	<select id="countPedalsAlarm" parameterType="com.bt.itsjob.domain.dto.PedalsAlarmDTO" resultType="int">
        SELECT COUNT(*) FROM alarmshard
        WHERE create_time>#{endTime} AND type = 951
          AND report_time >= #{startTime}
          AND facility_no IN
              (
                  SELECT facility_no FROM road_kiosk WHERE kiosk_id = #{kioskId}
              )
          AND device_id IN
              (
                  SELECT device_id FROM road_kiosk_camera WHERE kiosk_id = #{kioskId}
              )
    </select>

    <resultMap id="xfzDataMap" type="com.bt.itsjob.domain.vo.XfzMonthlyDataVO">
        <result column="count" property="count"/>
        <result column="source_id" property="sourceId"/>
    </resultMap>
    <!--路网感知告警次数-->
    <select id="countRoadAlarm" resultMap="xfzDataMap">
        SELECT COUNT(*) as count, r.source_id as source_id
        FROM alarmshard a
        LEFT JOIN road r on a.road_no=r.road_no
        where a.create_time >= #{startTimeStamp} AND a.create_time &lt;= #{endTimeStamp}
        GROUP BY r.source_id
    </select>

    <!--路网感知处理次数 -->
    <select id="countHandledRoadAlarm" resultMap="xfzDataMap">
        SELECT COUNT(*) as count, road.source_id as source_id
        FROM alarmshard a
        LEFT JOIN road r ON a.road_no=r.road_no
        WHERE a.handle_status != 0
        AND a.create_time >= #{startTimeStamp} AND a.create_time &lt;= #{endTimeStamp}
        GROUP BY r.source_id
    </select>

	<select id="selectNotHandleMaster" resultType="com.bt.itsjob.domain.vo.AlarmVO">
	SELECT id,alarm_event_id AS alarmEventId,create_time AS createTime FROM alarm_master_2025 WHERE handle_status=0 AND type &lt; 33 AND reback_status=0 ORDER BY create_time LIMIT 100
	</select>

	<select id="selectAlarmByIdCreateTime" parameterType="com.bt.itsjob.domain.dto.AlarmDTO" resultType="com.bt.itsjob.domain.vo.AlarmVO">
	SELECT handle_status AS handleStatus FROM alarmshard WHERE id=#{id} AND create_time=#{createTime}
	</select>

	<update id="finishAlarm" parameterType="com.bt.itsjob.domain.dto.AlarmDTO">
	UPDATE alarm_master_2025 SET handle_status=#{handleStatus} WHERE id=#{id} AND create_time=#{createTime}
	</update>

	<select id="statHandleStatusAlarm" parameterType="com.bt.itsjob.domain.dto.AlarmDTO" resultType="int">
	SELECT COUNT(*) AS num FROM alarmshard WHERE handle_status=#{handleStatus} AND create_time>#{startTime} AND create_time &lt; #{endTime} AND alarm_event_id=#{alarmEventId}
	</select>

</mapper>