package com.bt.itsjob.handler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.bt.itsjob.service.ChargingpileService;
import com.bt.itsjob.service.OutfieldService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;

@JobHandler(value="chargingpileDayStatHandler")
@Component
public class ChargingpileDayStatHandler  extends IJobHandler {
    @Autowired
    private ChargingpileService chargingpileService;

    @Override
    public ReturnT<String> execute(String date) throws Exception {
    	chargingpileService.addDayStat(date);
        XxlJobLogger.log("[充电桩日统计数据]完成.");
        return SUCCESS;
    }

}
