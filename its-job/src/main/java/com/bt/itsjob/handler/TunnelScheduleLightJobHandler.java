package com.bt.itsjob.handler;

import com.bt.itscore.utils.HttpClientUtils;
import com.bt.itscore.utils.ServiceUtils;
import com.bt.itsjob.feign.ItsTunnelFeignClient;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

@JobHandler(value = "TunnelScheduleLightJobHandler")
@Component
public class TunnelScheduleLightJobHandler extends IJobHandler {
    @Autowired
    private ItsTunnelFeignClient itsTunnelFeignClient;

    @Value("${yk.domain:https://yktest.gxits.cn:8763/s}")
    private String ykDomain;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("Tunnel light operate start.");
        Map<String, String> innerLoginHead = ServiceUtils.getInnerLoginHead();
        HttpClientUtils.post(ykDomain + "/its-tunnel/tunnel/operateLightPlcSwitch", innerLoginHead, "{}");
        XxlJobLogger.log("Tunnel light operate finish.");
        return SUCCESS;
    }
}
