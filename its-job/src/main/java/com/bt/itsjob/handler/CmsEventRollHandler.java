package com.bt.itsjob.handler;


import com.bt.itsjob.service.CmsTimerService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@JobHandler(value="cmsEventRollHandler")
@Component
public class CmsEventRollHandler extends IJobHandler {
    @Autowired
    private CmsTimerService cmsTimerService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        long start = System.currentTimeMillis();
        XxlJobLogger.log("[交通诱导]周期撤回开始.");
        cmsTimerService.timer();
        long finish = System.currentTimeMillis();
        long timeElapsed = (finish - start)/1000;
        XxlJobLogger.log("[交通诱导]周期撤回完成.用时: "+timeElapsed+" s");
        return SUCCESS;
    }

    @Override
    public void init() {
        super.init();
    }
}
