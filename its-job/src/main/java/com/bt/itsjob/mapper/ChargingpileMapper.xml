<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itsjob.mapper.ChargingpileMapper">

<select id="selectFacilityCharginggun" resultType="com.bt.itsjob.domain.dto.ChargingpileDayStatDTO">
SELECT m.source_id sourceId,m.org_id orgId,m.facility_no facilityNo,COUNT(*) gunNum,SUM(CASE WHEN d.line_status = 2 THEN 1 ELSE 0 END) gunOnlineNum FROM 
  (SELECT f.facility_no,org.org_id,org.source_id,ma.station_id FROM facility_charge_station_map ma,facility f,road r,organization_road org 
     WHERE f.facility_no=ma.facility_no AND f.road_no=r.road_no AND r.road_no=org.road_no AND f.facility_type_no=7) m,
device_charge d,device_charge_gun g, facility f 
WHERE d.device_id=g.device_id AND m.station_id=d.station_id AND m.facility_no=f.facility_no AND f.facility_type_no=7 GROUP BY m.org_id,m.facility_no,m.source_id
</select>

<select id="dayStat" parameterType="java.lang.Long" resultType="com.bt.itsjob.domain.dto.ChargingpileDayStatDTO">
		SELECT facility_no facilityNo, COUNT(*) orderNum, SUM(x.charge_capacity) chargeCapacity, SUM(x.charge_duration) chargeDuration, SUM(x.deduction_amount) deductionAmount, SUM(x.recharge_amount) rechargeAmount FROM 
		(
		SELECT t.facility_no,r.* FROM chargingpile_rt_data r
		LEFT JOIN (select m.facility_no,d.station_id,d.device_name from facility_charge_station_map m,device_charge d
		where m.station_id=d.station_id) t ON r.device_name=t.device_name
		WHERE r.open_timestamp >#{startTime} AND r.open_timestamp &lt;= ${startTime}+86400 AND t.facility_no IS NOT NULL
		) x GROUP BY x.facility_no
</select>

<delete id="deleteDayStat" parameterType="java.lang.String">
DELETE FROM chargingpile_day_stat WHERE stat_date=#{statDate}
</delete>

<insert id="batchInsert" parameterType="java.util.List">
<foreach collection="list" item="c" separator=";">
INSERT INTO chargingpile_day_stat (source_id,org_id,facility_no,
<if test="c.chargeCapacity != null">charge_capacity,</if>
<if test="c.chargeDuration != null">charge_duration,</if>
<if test="c.deductionAmount != null">deduction_amount,</if>
<if test="c.rechargeAmount != null">recharge_amount,</if>
<if test="c.orderNum != null">order_num,</if>
<if test="c.gunNum != null">gun_num,</if>
<if test="c.gunOnlineNum != null">gun_online_num,</if>
stat_date,create_time) VALUES 
(#{c.sourceId},#{c.orgId},#{c.facilityNo},
<if test="c.chargeCapacity != null">#{c.chargeCapacity},</if>
<if test="c.chargeDuration != null">#{c.chargeDuration},</if>
<if test="c.deductionAmount != null">#{c.deductionAmount},</if>
<if test="c.rechargeAmount != null">#{c.rechargeAmount},</if>
<if test="c.orderNum != null">#{c.orderNum},</if>
<if test="c.gunNum != null">#{c.gunNum},</if>
<if test="c.gunOnlineNum != null">#{c.gunOnlineNum},</if>
#{c.statDate},#{c.createTime})
</foreach>

</insert>
</mapper>
