package com.bt.itsjob.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.bt.itsjob.domain.dto.ChargingpileDayStatDTO;

@Mapper
public interface ChargingpileMapper {

	List<ChargingpileDayStatDTO> selectFacilityCharginggun();

	List<ChargingpileDayStatDTO> dayStat(@Param("startTime") long startTime);

	int batchInsert(@Param("list") List<ChargingpileDayStatDTO> list);

	int deleteDayStat(@Param("statDate") String statDate);

}
