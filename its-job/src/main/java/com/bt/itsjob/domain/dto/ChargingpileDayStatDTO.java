package com.bt.itsjob.domain.dto;

public class ChargingpileDayStatDTO {
	private Integer id;// 主键自增
	private Integer sourceId;
	private String orgId;// 组织机构ID
	private String facilityNo;// 服务区编号
	private Float chargeCapacity;// 已充电量(度)
	private Integer chargeDuration;// 已充时长(分钟)
	private Float deductionAmount;// 充电消耗金额
	private Float rechargeAmount;// 充值金额
	private Integer orderNum;// 订单数量
	private Integer gunNum;// 充电枪数量
	private Integer gunOnlineNum;// 充电枪在线数量
	private String statDate;// 统计日期
	private String createTime;// 记录生成时间

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getSourceId() {
		return sourceId;
	}

	public void setSourceId(Integer sourceId) {
		this.sourceId = sourceId;
	}

	public String getOrgId() {
		return orgId;
	}

	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}

	public String getFacilityNo() {
		return facilityNo;
	}

	public void setFacilityNo(String facilityNo) {
		this.facilityNo = facilityNo;
	}

	public Float getChargeCapacity() {
		return chargeCapacity;
	}

	public void setChargeCapacity(Float chargeCapacity) {
		this.chargeCapacity = chargeCapacity;
	}

	public Integer getChargeDuration() {
		return chargeDuration;
	}

	public void setChargeDuration(Integer chargeDuration) {
		this.chargeDuration = chargeDuration;
	}

	public Float getDeductionAmount() {
		return deductionAmount;
	}

	public void setDeductionAmount(Float deductionAmount) {
		this.deductionAmount = deductionAmount;
	}

	public Float getRechargeAmount() {
		return rechargeAmount;
	}

	public void setRechargeAmount(Float rechargeAmount) {
		this.rechargeAmount = rechargeAmount;
	}

	public Integer getOrderNum() {
		return orderNum;
	}

	public void setOrderNum(Integer orderNum) {
		this.orderNum = orderNum;
	}

	public Integer getGunNum() {
		return gunNum;
	}

	public void setGunNum(Integer gunNum) {
		this.gunNum = gunNum;
	}

	public Integer getGunOnlineNum() {
		return gunOnlineNum;
	}

	public void setGunOnlineNum(Integer gunOnlineNum) {
		this.gunOnlineNum = gunOnlineNum;
	}

	public String getStatDate() {
		return statDate;
	}

	public void setStatDate(String statDate) {
		this.statDate = statDate;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}
}
