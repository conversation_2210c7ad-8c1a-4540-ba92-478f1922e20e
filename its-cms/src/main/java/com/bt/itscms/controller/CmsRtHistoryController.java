package com.bt.itscms.controller;

import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bt.itscms.domain.dto.AlarmEnum;
import com.bt.itscms.domain.dto.CmsHistory;
import com.bt.itscms.domain.dto.CmsRtHistoryDTO;
import com.bt.itscms.domain.dto.HisPageDTO;
import com.bt.itscms.domain.dto.HisRequest;
import com.bt.itscms.domain.vo.SendCmsVO;
import com.bt.itscms.service.CmsRtHistoryService;
import com.bt.itscms.utils.CommonResult;
import com.bt.itscms.utils.DataUtils;
import com.bt.itscore.domain.dto.PageDTO;
import com.bt.itscore.domain.vo.PageVO;
import com.bt.itscore.domain.vo.ResponseVO;
import com.bt.itscore.utils.ValidUtils;

import io.swagger.annotations.ApiOperation;

@RestController
/**
* @date 2021年6月9日 下午2:11:58
* @Description 诱导记录管理
 */
@RequestMapping("/cmsRtHistory")
public class CmsRtHistoryController {

    @Autowired
    private CmsRtHistoryService cmsRtHistoryService;

    /**
     * .诱导记录列表
     */
    @PostMapping("/page")
    public PageVO page(@Valid PageDTO pageDTO, @RequestBody HisPageDTO dto, BindingResult result) {
    	ValidUtils.error(result);
    	PageVO res = new PageVO(cmsRtHistoryService.page(pageDTO, dto));
        return res;
    }

    @ApiOperation("更新诱导发布")
    @PostMapping("/updaterelease")
    public CommonResult updateRelease(@RequestBody HisRequest hisRequest) {

        return cmsRtHistoryService.updateRelease(hisRequest);
    }

    @ApiOperation("单个撤回诱导发布的情报板")
    @PostMapping("/rollback")
    public CommonResult rollback(@RequestBody HisRequest hisRequest) {

        return cmsRtHistoryService.rollback(hisRequest.getHisId(), hisRequest.getCmsId(), hisRequest.getAccount());
    }

    @ApiOperation("单个诱导记录撤回状态查询")
    @PostMapping("/oneRollstate")
    public CommonResult getCmsRollState(@RequestBody HisRequest hisRequest) {
        CmsHistory result = cmsRtHistoryService.getCmsRollState(hisRequest.getHisId(), hisRequest.getCmsId());
        if (DataUtils.isNull(result)){
           return CommonResult.failed();
        }
        return CommonResult.success(result);
    }

    @ApiOperation("查询诱导所发布的情报板")
    @PostMapping("/sendCmslist")
    public Object sendCmsList(@RequestBody CmsRtHistoryDTO history) {
        List<SendCmsVO> result = cmsRtHistoryService.sendCmsList(history.getHisId());
        if (DataUtils.isEmptyData(result)){
            return CommonResult.failed();
        }
        return CommonResult.success(result);
    }

    @ApiOperation("诱导记录新增情报板发布")
    @PostMapping("/addcms")
    public CommonResult addReleaseCms(@RequestBody HisRequest hisRequest) {
        return cmsRtHistoryService.addReleaseCms(hisRequest.getHisId(), hisRequest.getCmsIds(), hisRequest.getAccount());
    }

    @ApiOperation("批量撤回诱导发布的情报板")
    @PostMapping("/batchroll")
    public CommonResult batchRoll(@RequestBody HisRequest hisRequest) {

        return cmsRtHistoryService.batchRoll(hisRequest.getHisId(), hisRequest.getCmsIds(), hisRequest.getAccount());
    }

    @ApiOperation("立即逾期")
    @PostMapping("/beoverdue")
    public CommonResult beOverdue(@RequestBody HisRequest hisRequest) {
        String alarmId = "";
        if (hisRequest.getEventNo() != null) {
            alarmId = hisRequest.getEventNo();
        } else {
            alarmId = AlarmEnum.getAlarmIdentifying(hisRequest.getAlarmId(),hisRequest.getAlarmType());
        }
        return cmsRtHistoryService.beOverdue(alarmId);
    }

    /**
     * .逾期设定
     */
    @PostMapping("/overdue")
    public ResponseVO overdue(@RequestBody CmsRtHistoryDTO vo) {
        boolean ret = cmsRtHistoryService.overdue(vo);
        return new ResponseVO(ret);
    }

    /**
     * .诱导记录删除
     */
    @PostMapping("/remove")
    public ResponseVO remove(@RequestParam String[] ids) {
        boolean ret = cmsRtHistoryService.remove(ids);
        return new ResponseVO(ret);
    }

    /**
     * .重置记录(用于对撤回错误记录重置)
     */
    @PostMapping("/reset")
    public ResponseVO reset(@RequestParam String hisId, @RequestParam(required = false) String cmsId) {
        boolean ret = cmsRtHistoryService.reset(hisId, cmsId);
        return new ResponseVO(ret);
    }

}
