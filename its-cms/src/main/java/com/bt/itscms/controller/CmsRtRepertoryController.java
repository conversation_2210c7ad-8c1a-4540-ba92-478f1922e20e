package com.bt.itscms.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bt.itscms.domain.dto.CmsRtRepertoryDTO;
import com.bt.itscms.domain.vo.CmsIssueFlagVO;
import com.bt.itscms.service.CmsRtRepertoryService;

/**
* @date 2021年6月9日 上午8:01:52
* @Description 实时节目单管理
 */
@RestController
@RequestMapping("/cmsRtRepertory")
public class CmsRtRepertoryController {

    @Autowired
    private CmsRtRepertoryService cmsRtRepertoryService;

    /**
     * .单个情报板多屏撤回实时节目单
     */
    @PostMapping("/roll")
    public List<CmsIssueFlagVO> roll(@RequestBody CmsRtRepertoryDTO roll) {
        return cmsRtRepertoryService.roll(roll);
    }

    /**
     * .多个情报板多屏撤回实时节目单
     */
    @PostMapping("/batchRoll")
    public List<CmsIssueFlagVO> batchRoll(@RequestBody List<CmsRtRepertoryDTO> dataArr) {
        return cmsRtRepertoryService.batchRoll(dataArr);
    }

}
