<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itscms.mapper.CmsRtDynamicMapper">

	<resultMap id="BaseResultMap" type="com.bt.itscms.domain.vo.CmsRtDynamicVO">
        <id column="id" property="id"/>
        <result column="his_id" property="hisId"/>
        <result column="name" property="name"/>
        <result column="page_id" property="pageId"/>
        <result column="node_id" property="nodeId"/>
        <result column="label_x" property="labelX"/>
        <result column="label_y" property="labelY"/>
        <result column="font_size" property="fontSize"/>
        <result column="font_type" property="fontType"/>
        <result column="font_color" property="fontColor"/>
        <result column="message_body" property="messageBody"/>
        <result column="image_x" property="imageX"/>
        <result column="image_y" property="imageY"/>
        <result column="image_size" property="imageSize"/>
        <result column="image_name" property="imageName"/>
        <result column="image_body" property="imageBody"/>
        <result column="back_color" property="backColor"/>
        <result column="hold_time" property="holdTime"/>
        <result column="speed" property="speed"/>
        <result column="input_mode" property="inputMode"/>
        <result column="output_mode" property="outputMode"/>
        <result column="layout" property="layout"/>
        <result column="device_id" property="deviceId"/>
        <result column="device_name" property="deviceName"/>
        <result column="release_time" property="releaseTime"/>
        <result column="is_cover" property="isCover"/>
    </resultMap>

    <!--批量新增 -->
    <insert id="batchAdd" parameterType="java.util.List">
        insert into cms_rt_dynamic (id, his_id, name,page_id, node_id, label_x,label_y, font_size, font_type,font_color, message_body, image_x,image_y, image_size, image_name,
        image_body, back_color, hold_time,speed, input_mode, output_mode,layout, device_id, device_name,release_time)
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (#{item.id,jdbcType=VARCHAR},#{item.hisId,jdbcType=VARCHAR},#{item.name,jdbcType=VARCHAR},
            #{item.pageId,jdbcType=VARCHAR},#{item.nodeId,jdbcType=VARCHAR},#{item.labelX,jdbcType=VARCHAR},
            #{item.labelY,jdbcType=VARCHAR},#{item.fontSize,jdbcType=VARCHAR},#{item.fontType,jdbcType=VARCHAR},
            #{item.fontColor,jdbcType=VARCHAR},#{item.messageBody,jdbcType=VARCHAR},#{item.imageX,jdbcType=VARCHAR},
            #{item.imageY,jdbcType=VARCHAR},#{item.imageSize,jdbcType=VARCHAR},#{item.imageName,jdbcType=VARCHAR},
            #{item.imageBody,jdbcType=VARCHAR},#{item.backColor,jdbcType=VARCHAR},#{item.holdTime,jdbcType=VARCHAR},
            #{item.speed,jdbcType=VARCHAR},#{item.inputMode,jdbcType=VARCHAR},#{item.outputMode,jdbcType=VARCHAR},
            #{item.layout,jdbcType=VARCHAR},#{item.deviceId,jdbcType=VARCHAR},#{item.deviceName,jdbcType=VARCHAR},
            #{item.releaseTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>
    
    <sql id="Base_Column_List">
	    id, `name`, page_id, node_id, label_x, label_y, font_size, font_type, font_color, message_body,image_x, image_y, image_size, image_name, image_body, back_color, hold_time, speed,input_mode, output_mode, layout, device_id, device_name, release_time
    </sql>
    <!-- 根据情报板标识，查询所有动态消息  -->
    <select id="getAllByCmsId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> from cms_rt_dynamic WHERE device_id = #{cmsId} ORDER BY release_time DESC
    </select>

	<!-- 根据情报板标识，查询所有未撤回，的动态消息  -->
    <select id="getAllNotRollBackByCmsId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT crd.*,crh.is_cover FROM cms_rt_dynamic crd INNER JOIN cms_rt_history crh ON crd.his_id = crh.his_id
        WHERE (crd.his_id,crd.device_id) IN (SELECT ch.his_id,ch.cms_id FROM cms_history ch WHERE ch.cms_id =#{cmsId} AND ch.is_rollback = 0)
        ORDER BY crd.release_time DESC
    </select>
        <!-- 根据诱导标识与情报板标识，查询动态消息 -->
    <select id="getDynamicByHisIdAndCmsId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> from cms_rt_dynamic WHERE his_id =#{hisId} AND device_id=#{cmsId} ORDER BY release_time DESC
    </select>
</mapper>