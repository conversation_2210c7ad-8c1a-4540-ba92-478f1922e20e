package com.bt.itscms.domain.dto;

public class CmsRtDynamic {
    private String id;
    private String hisId;
    private String name; //节目名称
    private String pageId; // 归属页id
    private String nodeId; // 归属页中子项的id
    private Integer labelX; // 文本坐标
    private Integer labelY; // 文本坐标
    private Integer fontSize; // 字号
    private String fontType; // 字体
    private String fontColor; // 字体颜色
    private String messageBody; // 文本内容
    private Integer imageX; // 图片坐标
    private Integer imageY; // 图片坐标
    private Integer imageSize; // 图片尺寸
    private String imageName; // 图片名称
    private String imageBody; // 图片内容
    private String backColor; // 背景颜色
    private Integer holdTime; // 停留时间
    private Integer speed; // 变换速度
    private String inputMode; //入屏方式
    private String outputMode; // 出屏方式
    private String layout; // 布局方式
    private String deviceId;
    private String deviceName;
    private String releaseTime;

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        if (obj instanceof CmsRtDynamic) {
            CmsRtDynamic o = (CmsRtDynamic) obj;
            // 需要比较的字段相等，则这两个对象相等
            if (this.deviceId.equals(o.getDeviceId())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public int hashCode() {
        int result = name != null ? name.hashCode() : 0;
        result = 31 * result + (id != null ? 1 : 0);
        result = 31 * result + (hisId != null ? 1 : 0);
        result = 31 * result + (deviceId != null ? 1 : 0);
        result = 31 * result + (labelX != null ? 1 : 0);
        result = 31 * result + (labelY != null ? 1 : 0);
        result = 31 * result + (fontSize != null ? 1 : 0);
        result = 31 * result + (fontType != null ? fontType.hashCode() : 0);
        result = 31 * result + (fontColor != null ? fontColor.hashCode() : 0);
        result = 31 * result + (messageBody != null ? messageBody.hashCode() : 0);
        result = 31 * result + (imageBody != null ? imageBody.hashCode() : 0);
        result = 31 * result + (backColor != null ? backColor.hashCode() : 0);
        result = 31 * result + (holdTime != null ? 1 : 0);
        result = 31 * result + (speed != null ? 1 : 0);
        result = 31 * result + (inputMode != null ? inputMode.hashCode() : 0);
        result = 31 * result + (outputMode != null ? outputMode.hashCode() : 0);
        result = 31 * result + (layout != null ? layout.hashCode() : 0);
        return result;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getHisId() {
        return hisId;
    }

    public void setHisId(String hisId) {
        this.hisId = hisId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPageId() {
        return pageId;
    }

    public void setPageId(String pageId) {
        this.pageId = pageId;
    }

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    public Integer getLabelX() {
        return labelX;
    }

    public void setLabelX(Integer labelX) {
        this.labelX = labelX;
    }

    public Integer getLabelY() {
        return labelY;
    }

    public void setLabelY(Integer labelY) {
        this.labelY = labelY;
    }

    public Integer getFontSize() {
        return fontSize;
    }

    public void setFontSize(Integer fontSize) {
        this.fontSize = fontSize;
    }

    public String getFontType() {
        return fontType;
    }

    public void setFontType(String fontType) {
        this.fontType = fontType;
    }

    public String getFontColor() {
        return fontColor;
    }

    public void setFontColor(String fontColor) {
        this.fontColor = fontColor;
    }

    public String getMessageBody() {
        return messageBody;
    }

    public void setMessageBody(String messageBody) {
        this.messageBody = messageBody;
    }

    public Integer getImageX() {
        return imageX;
    }

    public void setImageX(Integer imageX) {
        this.imageX = imageX;
    }

    public Integer getImageY() {
        return imageY;
    }

    public void setImageY(Integer imageY) {
        this.imageY = imageY;
    }

    public Integer getImageSize() {
        return imageSize;
    }

    public void setImageSize(Integer imageSize) {
        this.imageSize = imageSize;
    }

    public String getImageName() {
        return imageName;
    }

    public void setImageName(String imageName) {
        this.imageName = imageName;
    }

    public String getImageBody() {
        return imageBody;
    }

    public void setImageBody(String imageBody) {
        this.imageBody = imageBody;
    }

    public String getBackColor() {
        return backColor;
    }

    public void setBackColor(String backColor) {
        this.backColor = backColor;
    }

    public Integer getHoldTime() {
        return holdTime;
    }

    public void setHoldTime(Integer holdTime) {
        this.holdTime = holdTime;
    }

    public Integer getSpeed() {
        return speed;
    }

    public void setSpeed(Integer speed) {
        this.speed = speed;
    }

    public String getInputMode() {
        return inputMode;
    }

    public void setInputMode(String inputMode) {
        this.inputMode = inputMode;
    }

    public String getOutputMode() {
        return outputMode;
    }

    public void setOutputMode(String outputMode) {
        this.outputMode = outputMode;
    }

    public String getLayout() {
        return layout;
    }

    public void setLayout(String layout) {
        this.layout = layout;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

	public String getReleaseTime() {
		return releaseTime;
	}

	public void setReleaseTime(String releaseTime) {
		this.releaseTime = releaseTime;
	}

}