package com.bt.itscms.domain.vo;

public class SendCmsVO {
    private String hisId;
    private String deviceId;
    private String deviceName;
    private String milePost;
    private String specs;
    private String lng;
    private String lat;
    private String roadName;
    private Integer isCover; // 新增方式，0-追加，1-覆盖
    private Integer isRollback; // 撤回状态：0-否，1-是
    private String rollBackTime; // 撤回时间

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        if (obj instanceof SendCmsVO) {
            SendCmsVO o = (SendCmsVO) obj;
            // 需要比较的字段相等，则这两个对象相等
            if (this.deviceId.equals(o.getDeviceId())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public int hashCode() {
        int result = deviceName != null ? deviceName.hashCode() : 0;
        result = 31 * result + (deviceId != null ? 1 : 0);
        result = 31 * result + (milePost != null ? milePost.hashCode() : 0);
        result = 31 * result + (specs != null ? specs.hashCode() : 0);
        result = 31 * result + (lng != null ? lng.hashCode() : 0);
        result = 31 * result + (lat != null ? lat.hashCode() : 0);
        result = 31 * result + (roadName != null ? roadName.hashCode() : 0);
        result = 31 * result + (isCover != null ? 1 : 0);
        result = 31 * result + (isRollback != null ? 1 : 0);
        result = 31 * result + (rollBackTime != null ? rollBackTime.hashCode() : 0);
        return result;
    }

    public String getHisId() {
        return hisId;
    }

    public void setHisId(String hisId) {
        this.hisId = hisId;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getMilePost() {
        return milePost;
    }

    public void setMilePost(String milePost) {
        this.milePost = milePost;
    }

    public String getSpecs() {
        return specs;
    }

    public void setSpecs(String specs) {
        this.specs = specs;
    }

    public String getLng() {
        return lng;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getRoadName() {
        return roadName;
    }

    public void setRoadName(String roadName) {
        this.roadName = roadName;
    }

    public Integer getIsCover() {
        return isCover;
    }

    public void setIsCover(Integer isCover) {
        this.isCover = isCover;
    }

    public Integer getIsRollback() {
        return isRollback;
    }

    public void setIsRollback(Integer isRollback) {
        this.isRollback = isRollback;
    }

    public String getRollBackTime() {
        return rollBackTime;
    }

    public void setRollBackTime(String rollBackTime) {
        this.rollBackTime = rollBackTime;
    }
}
