package com.bt.itscms.config;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import com.bt.itscore.utils.AESUtils;
import com.bt.itscore.utils.DESUtils;
@Component
//@Order(1) //指定顺序
public class InitConfig implements CommandLineRunner {
	
	private final static Logger LOGGER = LoggerFactory.getLogger(InitConfig.class);
    
    @Value("${server.cipher.key}")
    private String serverCipherKey;

    @Value("${server.cipher.iv}")
    private String serverCipherIv;

    @Value("${server.aes.key}")
    private String serverAesKey;

    @Value("${server.aes.iv}")
    private String serverAesIv;

    @Override
    public void run(String... args) throws Exception {
        LOGGER.info("InitConfig 初始化资源===========");
        DESUtils.initCipher(serverCipherKey, serverCipherIv);
        AESUtils.initCipher(serverAesKey, serverAesIv);
    }

}
