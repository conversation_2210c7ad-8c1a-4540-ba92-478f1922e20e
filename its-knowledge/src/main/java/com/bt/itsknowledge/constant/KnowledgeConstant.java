package com.bt.itsknowledge.constant;

/**
 * 知识库模块常量汇总类，方便管理公共使用的常量值
 */
public interface KnowledgeConstant {
    // 视频后缀指定为MP4
    String VIDEO_POSTFIX = "mp4";
    // 视频文件最大支持100MB
    long VIDEO_MAX_SIZE = 104857600L;
    // 分页查询最大每次返回不能超过1000条
    int PAGE_MAX_SIZE = 1000;
    // uuid正则表达式
    String UUID_REGEX = "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$";
    // 视频名称校验正则表达式
    String VIDEO_NAME_REGEX = "^[A-Za-z0-9_\\-\\u4e00-\\u9fa5]{0,64}$";
    // PDF格式后缀
    String PDF_TYPE = "application/pdf";
    // 中间文件存储目录
    String TEM_FILE_PATH = "/home/<USER>/its-knowledge/tmp";
    // 阿里云返回的doc类型type
    String DOC_TYPE = "application/msword";
}
