package com.bt.itsknowledge.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.nacos.api.utils.StringUtils;
import com.bt.itscore.auth.Login;
import com.bt.itscore.domain.vo.ResultVO;
import com.bt.itsknowledge.domain.dto.DevicepingDTO;
import com.bt.itsknowledge.service.OperationService;

/**
 * <AUTHOR>
 * @date 2023年5月30日 上午11:33:05
 * @Description 运维入口类
 */
@RestController
@RequestMapping("operation")
public class OperationController {
    @Autowired
    private OperationService operationService;

    /**
     * @描述 ping内网设备，返回ping的响应信息
     */
    @Login
    @PostMapping("ping")
    public Object ping(@RequestBody DevicepingDTO dto) {
        String deviceId = dto.getDeviceId();
        String ip = dto.getIp();
        if (StringUtils.isBlank(deviceId) && StringUtils.isBlank(ip)) {
            return new ResultVO<>(0, null, "参数错误：设备id或ip至少传一个参数");
        }
        return operationService.ping(dto);
    }

}