package com.bt.itsknowledge.controller;

import com.bt.itscore.auth.Login;
import com.bt.itscore.domain.dto.AttachDTO;
import com.bt.itscore.domain.dto.IdStringBatchDTO;
import com.bt.itscore.domain.dto.PageDTO;
import com.bt.itscore.domain.vo.PageVO;
import com.bt.itscore.domain.vo.ResponseVO;
import com.bt.itscore.utils.OssUtils;
import com.bt.itscore.utils.ValidUtils;
import com.bt.itsknowledge.domain.dto.QueryTutorialParams;
import com.bt.itsknowledge.domain.dto.SystemTutorialDTO;
import com.bt.itsknowledge.domain.vo.SystemTutorialVO;
import com.bt.itsknowledge.service.SysTemTutorialService;
import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 知识库系统教程接口管理类
 *
 * <AUTHOR>
 * @since 2023-1-30
 */
@RestController
@RequestMapping("tutorial")
public class KnowledgeTutorialController {
    private final static Logger LOGGER = LoggerFactory.getLogger(KnowledgeTutorialController.class);

    @Autowired
    private SysTemTutorialService sysTemTutorialService;

    /**
     * 阿里云oss文件上传接口（获取临时授权访问oss权限，前端完成上传动作）
     *
     * @param dto      附件对象
     * @param request  请求参数
     * @param response 响应参数
     * @throws IOException IO流异常抛出
     */
    @Login
    @RequestMapping("ossUpload")
    public void ossUpload(AttachDTO dto, HttpServletRequest request, HttpServletResponse response) throws IOException {
        Map<String, String> map = sysTemTutorialService.ossUpload(request, dto);
        OssUtils.response(request, response, new Gson().toJson(map));
    }

    /**
     * 提供给阿里云oss服务回调的接口
     *
     * @param request  请求参数
     * @param response 响应参数
     * @throws IOException IO流异常抛出
     */
    @RequestMapping("ossCallback")
    public void ossCallback(HttpServletRequest request, HttpServletResponse response) throws IOException {
        AttachDTO attachDTO = sysTemTutorialService.ossCallback(request.getInputStream(),
                NumberUtils.toInt(request.getHeader("content-length")));
        OssUtils.response(request, response, new Gson().toJson(attachDTO));
    }

    /**
     * oss删除附件，本地数据库+远端oss均删除
     *
     * @param dto 附件对象
     * @return 返回布尔值，true-成功，false-失败
     */
    @Login
    @RequestMapping("ossDelete")
    public ResponseVO ossDelete(@RequestBody AttachDTO dto, HttpServletRequest request) {
        boolean ret = sysTemTutorialService.ossDelete(dto, request);
        return new ResponseVO(ret);
    }

    /**
     * 获取临时授权访问URL
     *
     * @param dtos 附件对象集合
     * @return 添加授权访问URL的附件对象集合
     */
    @Login
    @RequestMapping("ossUrl")
    public List<AttachDTO> ossUrl(@RequestBody List<AttachDTO> dtos) {
        return sysTemTutorialService.ossUrl(dtos);
    }

    /**
     * 添加系统教程数据
     *
     * @param request 请求参数
     * @param dto     数据对象
     * @return 响应数据
     */
    @Login
    @PostMapping("addTutorial")
    public ResponseVO addTutorial(HttpServletRequest request, @RequestBody @Valid SystemTutorialDTO dto) {
        boolean isSuccess = sysTemTutorialService.saveTutorial(request, dto);
        return new ResponseVO(isSuccess);
    }

    /**
     * 更新系统教程数据
     *
     * @param request 请求参数
     * @param dto     数据对象
     * @return 响应数据
     */
    @Login
    @PostMapping("updateTutorial")
    public ResponseVO updateTutorial(HttpServletRequest request, @RequestBody @Valid SystemTutorialDTO dto) {
        boolean isSuccess = sysTemTutorialService.saveTutorial(request, dto);
        return new ResponseVO(isSuccess);
    }

    /**
     * 批量删除接口
     *
     * @param request 请求参数
     * @param dto     批量删除dto
     * @param result  数据绑定结果
     * @return 响应数据
     */
    @Login
    @PostMapping("batchDeleteTutorial")
    public ResponseVO batchDeleteTutorial(HttpServletRequest request, @Valid @RequestBody IdStringBatchDTO dto,
                                          BindingResult result) {
        ValidUtils.error(result);
        List<AttachDTO> waitDeleteOssFileList = new ArrayList<>();
        boolean isSuccess = sysTemTutorialService.batchDeleteTutorial(request, dto, waitDeleteOssFileList);
        // 数据库清理完毕再清楚远端oss，防止出现事务回滚，但是远端已被删除的情况
        waitDeleteOssFileList.forEach(item -> sysTemTutorialService.ossDeleteAttach(item));
        return new ResponseVO(isSuccess);
    }

    /**
     * 树结构菜单列表
     *
     * @return 菜单列表
     */
    @Login
    @GetMapping("treeList")
    public Object treeList() {
        return sysTemTutorialService.treeList();
    }

    /**
     * 分页+条件查询
     *
     * @param pageDTO        分页对象
     * @param tutorialParams 条件查询参数
     * @return 数据
     */
    @Login
    @PostMapping("page")
    public Object page(HttpServletRequest request, @Valid PageDTO pageDTO, @RequestBody @Valid QueryTutorialParams tutorialParams, BindingResult result) {
        ValidUtils.error(result);
        PageInfo<SystemTutorialVO> pageInfo = sysTemTutorialService.page(request, pageDTO, tutorialParams);
        return new PageVO(pageInfo);
    }

    /**
     * 菜单查询接口
     *
     * @return 数据
     */
    @Login
    @GetMapping("getMenuList")
    public Object getMenuList() {
        return sysTemTutorialService.getMenuList();
    }

    /**
     * 基于系统教程id查询附件，并返回oss临时授权访问的url
     *
     * @param dto 系统教程对象
     * @return 携带URL的对象
     */
    @Login
    @PostMapping("playVideo")
    public Object playVideo(@RequestBody SystemTutorialDTO dto) {
        return sysTemTutorialService.playVideo(dto.getId());
    }

}