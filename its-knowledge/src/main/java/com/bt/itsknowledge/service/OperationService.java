package com.bt.itsknowledge.service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.bt.itscore.constants.DeviceType;
import com.bt.itscore.domain.dto.CommandDTO;
import com.bt.itscore.domain.vo.ResultVO;
import com.bt.itscore.enums.SourceIdEnum;
import com.bt.itsknowledge.domain.dto.DevicepingDTO;
import com.bt.itsknowledge.domain.vo.DevicePingVO;
import com.bt.itsknowledge.feign.ItsMsFeignClient;
import com.bt.itsknowledge.mapper.OperationMapper;
import com.google.gson.Gson;

/**
 * <AUTHOR>
 * @date 2023年5月30日 下午3:12:10
 * @Description 运维相关的service
 */
@Service("operationService")
public class OperationService {
    @Autowired
    private ItsMsFeignClient itsMsFeignClient;
    @Autowired
    private OperationMapper operationMapper;

    /**
     * @描述 发送ping指令到外网MQ（对应某个内网节点）
     */
    public Object ping(DevicepingDTO dto) {
        DevicePingVO vo = null;
        String deviceId = dto.getDeviceId();
        if (StringUtils.isNoneBlank(deviceId)) {
            vo = operationMapper.selectDeviceByDeviceId(deviceId);
            if (vo == null) {
                return new ResultVO<>(0, null, "该设备不存在");
            }
        } else {
            // 查询设备
            List<DevicePingVO> devices = operationMapper.selectDeviceByIP(dto.getIp());
            if (CollectionUtils.isEmpty(devices)) {// 该IP在设备表中不存在
                return new ResultVO<>(0, null, "该ip在设备表中不存在");
            }
            for (DevicePingVO devicePingVO : devices) {
                String sourceId = devicePingVO.getSourceId();
                String name = SourceIdEnum.getName(NumberUtils.toInt(sourceId));
                devicePingVO.setSourceName(name);
                String deviceTypeName = DeviceType.getName(devicePingVO.getDeviceType());
                devicePingVO.setDeviceTypeName(deviceTypeName);
            }
            int size = devices.size();
            if (size > 1) {
                return new ResultVO<>(1, devices, "该ip在设备表中有" + size + "条记录");
            }
            vo = devices.get(0);
        }
        vo.setCount(dto.getCount());
        String uuid = UUID.randomUUID().toString();
        CommandDTO command = new CommandDTO();
        command.setSourceId(vo.getSourceId());
        command.setType("ping");
        command.setUuid(uuid);
        command.setParams(new Gson().toJson(vo));
        itsMsFeignClient.produce(command);
        vo.setUuid(uuid);
        String sourceId = vo.getSourceId();
        String name = SourceIdEnum.getName(NumberUtils.toInt(sourceId));
        vo.setSourceName(name);
        String deviceTypeName = DeviceType.getName(vo.getDeviceType());
        vo.setDeviceTypeName(deviceTypeName);
        List<DevicePingVO> vos = new ArrayList<>();
        vos.add(vo);
        return new ResultVO<>(1, vos, "");
    }

    public DevicePingVO selectDeviceByDeviceId(String deviceId) {
        return operationMapper.selectDeviceByDeviceId(deviceId);
    }

}
