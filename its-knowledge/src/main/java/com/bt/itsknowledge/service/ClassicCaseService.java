package com.bt.itsknowledge.service;

import com.bt.itscore.config.OssConfig;
import com.bt.itscore.domain.dto.AttachDTO;
import com.bt.itscore.domain.dto.IdStringBatchDTO;
import com.bt.itscore.domain.dto.IdStringDTO;
import com.bt.itscore.domain.dto.PageDTO;
import com.bt.itscore.exception.ArgumentException;
import com.bt.itscore.utils.OssUtils;
import com.bt.itsknowledge.domain.dto.ClassicCaseDTO;
import com.bt.itsknowledge.domain.vo.ClassicCaseVO;
import com.bt.itsknowledge.mapper.ClassicCaseMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;


@Service("classicCaseService")
public class ClassicCaseService {
    private final static Logger LOGGER = LoggerFactory.getLogger(ClassicCaseService.class);

    @Autowired
    private ClassicCaseMapper classicCaseMapper;

    @Autowired
    private OssConfig ossConfig;

    final static String EMERPLAN_URL = "its-knowledge/case/ossCallback";

    public PageInfo<ClassicCaseVO> page(ClassicCaseDTO classicCaseDTO, PageDTO pageDTO) {
        PageHelper.startPage(pageDTO.getPage(), pageDTO.getLimit());
        List<ClassicCaseVO> rows = classicCaseMapper.selectList(classicCaseDTO);
        PageInfo<ClassicCaseVO> pageInfo = new PageInfo<>(rows);
        return pageInfo;
    }

    public List<ClassicCaseVO> selectAll(ClassicCaseDTO classicCaseDTO) {
        return classicCaseMapper.selectAll(classicCaseDTO);
    }

    public boolean selectNameExits(ClassicCaseDTO classicCaseDTO) {
        ClassicCaseVO vo = classicCaseMapper.selectByName(classicCaseDTO);
        if (vo == null || (classicCaseDTO.getId() != null && classicCaseDTO.getId().equals(vo.getId()))) {
            return true;
        }
        return false;
    }

    /**
     * @param classicCaseDTO
     * @return boolean
     * @描述 新增案例，true成功；false失败
     */
    @Transactional
    public boolean add(ClassicCaseDTO classicCaseDTO) {
        classicCaseDTO.setId(UUID.randomUUID().toString());
        if (classicCaseDTO.getAttachType() == 1 && StringUtils.isBlank(classicCaseDTO.getAttachLinkAddr())){
            throw new ArgumentException("典型案例缺少网页链接附件");
        }
        if (classicCaseDTO.getAttach().size() > 0) {
            classicCaseMapper.updateAttach(classicCaseDTO);
        }
        return classicCaseMapper.add(classicCaseDTO) > 0;
    }

    /**
     * @param classicCaseDTO
     * @return boolean
     * @描述 修改案例，true成功；false失败
     */
    @Transactional
    public boolean update(ClassicCaseDTO classicCaseDTO) {
        // 更新knowledge_case_attach表case_id字段
        List<Integer> attach = classicCaseDTO.getAttach();
        // 删除设施附件（清除附件关联字段为空）
        classicCaseMapper.emptyAttach(new IdStringDTO(classicCaseDTO.getId()));
        if (attach.size() > 0) {
            classicCaseMapper.updateAttach(classicCaseDTO);
        }
        return classicCaseMapper.update(classicCaseDTO) > 0;
    }

    public boolean delete(IdStringDTO idStringDTO) {
        String caseId = idStringDTO.getId();
        return classicCaseMapper.delete(caseId) > 0;
    }

    public boolean batchDelete(IdStringBatchDTO idStringBatchDTO) {
        Map<String, Object> map = new HashMap<>();
        map.put("ids", idStringBatchDTO.getIds());
        return classicCaseMapper.batchDelete(map) > 0;
    }

    public Map<String, String> ossUpload(AttachDTO dto) {
        Map<String, String> map = OssUtils.getSignature(ossConfig, dto, EMERPLAN_URL);
        return map;
    }

    public AttachDTO ossCallback(HttpServletRequest request) throws IOException {
        AttachDTO dto = OssUtils.callback(request.getInputStream(), NumberUtils.toInt(request.getHeader("content-length")));
        // 保存附件表记录
        classicCaseMapper.addAttach(dto);
        return dto;
    }

    public boolean ossDelete(AttachDTO dto) {
        //删除附件记录
        boolean ret = classicCaseMapper.deleteAttach(dto) > 0;
        List<String> keys = new ArrayList<String>();
        keys.add(dto.getDiskFileName());
        OssUtils.batchDelete(ossConfig, keys);
        return ret;
    }

    public List<AttachDTO> ossUrl(List<AttachDTO> dtos) {
        return OssUtils.getUrl(ossConfig, dtos);
    }
}
