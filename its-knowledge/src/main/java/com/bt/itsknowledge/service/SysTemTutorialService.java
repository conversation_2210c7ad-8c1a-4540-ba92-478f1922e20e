package com.bt.itsknowledge.service;

import com.bt.itscore.config.OssConfig;
import com.bt.itscore.domain.dto.AttachDTO;
import com.bt.itscore.domain.dto.IdStringBatchDTO;
import com.bt.itscore.domain.dto.PageDTO;
import com.bt.itscore.exception.ArgumentException;
import com.bt.itscore.exception.AuthzException;
import com.bt.itscore.exception.FailException;
import com.bt.itscore.utils.OssUtils;
import com.bt.itsknowledge.constant.KnowledgeConstant;
import com.bt.itsknowledge.domain.dto.QueryTutorialParams;
import com.bt.itsknowledge.domain.dto.SystemTutorialDTO;
import com.bt.itsknowledge.domain.dto.TutorialAttachDTO;
import com.bt.itsknowledge.domain.vo.MenuVO;
import com.bt.itsknowledge.domain.vo.SystemTutorialTreeVO;
import com.bt.itsknowledge.domain.vo.SystemTutorialVO;
import com.bt.itsknowledge.mapper.SysTemTutorialMapper;
import com.bt.itsknowledge.util.FileUtils;
import com.bt.itsknowledge.util.RegexUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 知识库系统教程接口业务处理类
 *
 * <AUTHOR>
 * @since 2023-1-30
 */
@Service
public class SysTemTutorialService {
    private final static Logger LOGGER = LoggerFactory.getLogger(SysTemTutorialService.class);
    /**
     * 阿里云oss服务上传完成后的回调URL配置
     */
    private final static String OSS_CALLBACK_URL = "its-knowledge/tutorial/ossCallback";

    @Autowired
    private SysTemTutorialMapper sysTemTutorialMapper;

    @Autowired
    private OssConfig ossConfig;

    /**
     * oss上传
     *
     * @param request 请求参数
     * @param dto     附件对象参数
     * @return 上传成功参数
     */
    public Map<String, String> ossUpload(HttpServletRequest request, AttachDTO dto) {
        validAdminPermission(request, "非超级管理员身份，禁止上传教程数据");
        String fileName = dto.getFileName();
        if (!FileUtils.validFileName(fileName)) {
            throw new ArgumentException("上传视频文件名异常或者文件名长度超限，只允许包含数字、大小写字母、中文、下划线、中划线、点、括号");
        }
        return OssUtils.getSignature(ossConfig, dto, OSS_CALLBACK_URL);
    }

    /**
     * oss回调接口，提供给阿里云服务回调
     *
     * @param inputStream 输入流
     * @param length      请求参数长度
     * @return 附件插入表后的数据
     * @throws IOException IO异常抛出
     */
    public AttachDTO ossCallback(ServletInputStream inputStream, int length) throws IOException {
        AttachDTO dto = OssUtils.callback(inputStream, length);
        // 保存附件表记录
        dto.setCreateTime(dto.getCreateTime() * 1000);
        sysTemTutorialMapper.addAttach(dto);
        return dto;
    }

    /**
     * oss删除附件，本地数据库+远端oss均删除
     *
     * @param dto     附件对象
     * @param request 请求参数
     * @return 返回布尔值，true-成功，false-失败
     */
    public boolean ossDelete(AttachDTO dto, HttpServletRequest request) {
        validAdminPermission(request, "非超级管理员身份，禁止删除教程数据");
        // 删除本地附件记录
        boolean ret = sysTemTutorialMapper.deleteAttach(dto) > 0;
        // 删除远端oss文件
        ossDeleteAttach(dto);
        return ret;
    }

    /**
     * 执行远端删除
     *
     * @param dto 附件对象
     */
    public void ossDeleteAttach(AttachDTO dto) {
        List<String> keys = new ArrayList<>();
        keys.add(dto.getDiskFileName());
        OssUtils.batchDelete(ossConfig, keys);
    }

    /**
     * 获取临时授权访问URL
     *
     * @param dtoList 附件对象集合
     * @return 添加授权访问URL的附件对象集合
     */
    public List<AttachDTO> ossUrl(List<AttachDTO> dtoList) {
        // 过期时间的毫秒值，当前设定是10min
        final long expireTimeMil = 10 * 60 * 1000;
        return OssUtils.getUrl(ossConfig, dtoList, expireTimeMil);
    }

    /**
     * 添加/更新系统教程数据
     *
     * @param request 请求参数
     * @param dto     数据对象
     * @return 响应数据
     */
    @Transactional
    public boolean saveTutorial(HttpServletRequest request, SystemTutorialDTO dto) {
        // 参数校验
        validParams(request, dto);
        // 保证全局唯一
        String videoName = dto.getVideoName();
        List<SystemTutorialDTO> tutorialDTOList = sysTemTutorialMapper.queryTutorialByName(videoName);
        List<Integer> attachIds = dto.getAttachIds();
        if (CollectionUtils.isEmpty(attachIds)) {
            throw new ArgumentException("系统教程数据缺失附件信息，请求参数异常");
        }
        if (attachIds.size() != 1) {
            throw new ArgumentException("系统教程视频附件只支持1个，请求参数异常");
        }
        String tutorialId = dto.getId();
        boolean isSuccess = false;
        if (StringUtils.isEmpty(tutorialId)) { //执行新增插入操作
            isSuccess = addTutorial(dto, tutorialDTOList);
        } else {  // 执行更新操作
            isSuccess = updateTutorial(dto, tutorialDTOList);
        }
        if (isSuccess) {
            LOGGER.info("Save system tutorial successes.");
        }
        return isSuccess;
    }

    private boolean addTutorial(SystemTutorialDTO dto, List<SystemTutorialDTO> tutorialDTOList) {
        if (tutorialDTOList.size() > 0) {
            throw new ArgumentException("该系统教程名称已存在，请重新修改");
        }
        // 教程数据入库
        dto.setId(UUID.randomUUID().toString());
        int insert = sysTemTutorialMapper.addTutorial(dto);
        // 先查询，无数据则报错处理
        List<Integer> attachIds = dto.getAttachIds();
        List<TutorialAttachDTO> attachDTOList = sysTemTutorialMapper.queryAttachByAttachId(attachIds);
        if (CollectionUtils.isEmpty(attachDTOList)) {
            throw new FailException("附件信息不存在，请重新修改");
        }
        AtomicBoolean hasTutorialId = new AtomicBoolean(false);
        attachDTOList.forEach(item -> {
            if (!StringUtils.isEmpty(item.getTutorialId())) {
                hasTutorialId.set(true);
            }
        });
        if (hasTutorialId.get()) {
            throw new FailException("附件信息不属于当前附件教程，请重试修改");
        }
        // 更新附件关联的教程id
        int updateAttach = sysTemTutorialMapper.batchUpdateAttach(dto.getId(), attachIds);
        return insert > 0 && updateAttach > 0;
    }

    private boolean updateTutorial(SystemTutorialDTO dto, List<SystemTutorialDTO> tutorialDTOList) {
        String tutorialId = dto.getId();
        if (!RegexUtils.regex(KnowledgeConstant.UUID_REGEX, tutorialId)) {
            throw new ArgumentException("请求参数格式异常，请重试");
        }
        SystemTutorialDTO oldTutorial = sysTemTutorialMapper.queryTutorialById(tutorialId);
        for (SystemTutorialDTO tutorialDTO : tutorialDTOList) {
            if (!tutorialDTO.getId().equals(oldTutorial.getId())) {
                throw new ArgumentException("该系统教程名称已存在，请重新修改");
            }
        }
        oldTutorial.setVideoName(dto.getVideoName());
        oldTutorial.setVideoSize(dto.getVideoSize());
        oldTutorial.setVideoDuration(dto.getVideoDuration());
        oldTutorial.setUploadUserId(dto.getUploadUserId());
        oldTutorial.setBelongMenuId(dto.getBelongMenuId());
        oldTutorial.setUploadTime(dto.getUploadTime());
        int update = sysTemTutorialMapper.updateTutorial(oldTutorial);
        // 对比附件是否存在变更，变更则删除，重新绑定关联id
        List<AttachDTO> attachDTOList = sysTemTutorialMapper.queryAttachByTutorialId(tutorialId);
        List<Integer> attachIds = dto.getAttachIds();
        Set<Integer> set = new HashSet<>(attachIds); // 新的，需要找出被删除的列表，并进行删除
        attachDTOList.forEach(item -> {
            if (!set.contains(item.getId())) {
                sysTemTutorialMapper.deleteAttach(item);
            }
        });
        int updateAttach = sysTemTutorialMapper.batchUpdateAttach(tutorialId, attachIds);
        return update > 0 && updateAttach > 0;
    }

    private void validParams(HttpServletRequest request, SystemTutorialDTO dto) {
        // 校验是否是超级管理员角色，非管理员不能进行增删改
        String userId = (String) request.getAttribute("userId");
        if (!dto.getUploadUserId().equals(userId)) {
            throw new ArgumentException("用户名称为无效用户，请求参数异常");
        }
        String role = (String) request.getAttribute("role");
        boolean isAdmin = validUserHasAdminPermission(userId, role);
        if (!isAdmin) {
            throw new AuthzException("非超级管理员身份，禁止修改系统教程数据");
        }
        List<Integer> attachIds = dto.getAttachIds();
        if (CollectionUtils.isEmpty(attachIds)) {
            throw new ArgumentException("当前添加教程数据无附件，请求参数异常");
        }
    }

    private boolean validUserHasAdminPermission(String userId, String role) {
        if (StringUtils.isEmpty(role)) {
            throw new ArgumentException("非超级管理员身份，禁止修改知识库数据");
        }
        List<String> roleList = new ArrayList<>();
        final String splitFlag = ";";
        if (role.contains(splitFlag)) {
            roleList.addAll(Arrays.asList(role.split(splitFlag)));
        } else {
            roleList.add(role);
        }
        List<Map<String, String>> mapList = sysTemTutorialMapper.queryAdminByIdAndRole(userId, roleList);
        final String adminName = "超级管理员";
        AtomicBoolean isAdmin = new AtomicBoolean(false);
        mapList.forEach(item -> {
            if (adminName.equals(item.get("roleName"))) {
                isAdmin.set(true);
            }
        });
        return isAdmin.get();
    }

    /**
     * 批量删除
     *
     * @param request               请求参数
     * @param dto                   批量ids对象
     * @param waitDeleteOssFileList 等待被删除的文件信息集合
     * @return 操作结果
     */
    @Transactional
    public boolean batchDeleteTutorial(HttpServletRequest request, IdStringBatchDTO dto, List<AttachDTO> waitDeleteOssFileList) {
        // 参数校验
        List<String> ids = dto.getIds();
        if (CollectionUtils.isEmpty(ids)) {
            throw new ArgumentException("请求参数为空，请重试");
        }
        validAdminPermission(request, "非超级管理员身份，禁止修改系统教程数据");
        int count = 0;
        for (String id : ids) {
            if (StringUtils.isEmpty(id) || !RegexUtils.regex(KnowledgeConstant.UUID_REGEX, id)) {
                throw new ArgumentException("请求参数为空或者格式异常，请重试");
            }
            // 幂等设计，如查询的教程数据不存在，多次传入不存在数据的id，默认是删除成功
            SystemTutorialDTO tutorialDTO = sysTemTutorialMapper.queryTutorialById(id);
            if (tutorialDTO == null) {
                count++;
                continue;
            }
            List<AttachDTO> attachDTOList = sysTemTutorialMapper.queryAttachByTutorialId(id);
            // 删除附件表
            if (!CollectionUtils.isEmpty(attachDTOList)) {
                sysTemTutorialMapper.batchDeleteAttach(attachDTOList);
            }
            // 删除教程表
            count += sysTemTutorialMapper.deleteTutorial(id);
            waitDeleteOssFileList.addAll(attachDTOList);
        }
        return count == ids.size();
    }

    /**
     * 树结构菜单查询
     *
     * @return 结果数据
     */
    public Object treeList() {
        // 查出所有一级菜单menu
        List<MenuVO> menuVOList = sysTemTutorialMapper.queryAllParentMenu();
        // 查询系统教程当前涉及的二级菜单的数据
        List<MenuVO> tutorialMenuList = sysTemTutorialMapper.queryTutorialMenu();
        // 封装一二级菜单目录
        List<SystemTutorialTreeVO> resultList = new ArrayList<>();
        menuVOList.forEach(item -> {
            for (MenuVO menuVO : tutorialMenuList) {
                if (menuVO.getPid().intValue() == item.getId().intValue()) {
                    SystemTutorialTreeVO treeVO = new SystemTutorialTreeVO();
                    treeVO.setParentBelongMenuId(item.getId());
                    treeVO.setParentBelongMenu(item.getTitle());
                    treeVO.setBelongMenuId(menuVO.getId());
                    treeVO.setBelongMenu(menuVO.getTitle());
                    resultList.add(treeVO);
                }
            }
        });
        // 查出系统教程所有数据并匹配到对应目录
        List<SystemTutorialVO> tutorialVOList = sysTemTutorialMapper.queryAllTutorial();
        resultList.forEach(item -> {
            Integer belongMenuId = item.getBelongMenuId();
            List<SystemTutorialVO> list = item.getTutorialVOList();
            tutorialVOList.forEach(tutorialVO -> {
                if (belongMenuId.intValue() == tutorialVO.getBelongMenuId()) {
                    list.add(tutorialVO);
                }
            });
        });
        return resultList;
    }

    /**
     * 分页+条件查询
     *
     * @param request
     * @param pageDTO        分页对象
     * @param tutorialParams 查询参数
     * @return 数据
     */
    public PageInfo<SystemTutorialVO> page(HttpServletRequest request, PageDTO pageDTO, QueryTutorialParams tutorialParams) {
        // 校验管理员权限
        validAdminPermission(request, "非超级管理员身份，禁止调用教程管理接口");
        Integer limit = pageDTO.getLimit();
        if (limit > KnowledgeConstant.PAGE_MAX_SIZE) {
            throw new ArgumentException("分页参数异常，每页数据不能超过限定值");
        }
        String videoName = tutorialParams.getVideoName();
        if (!StringUtils.isEmpty(videoName) && !RegexUtils.regex(KnowledgeConstant.VIDEO_NAME_REGEX, videoName)) {
            throw new ArgumentException("视频名称格式异常，请重新输入");
        }
        PageHelper.startPage(pageDTO.getPage(), limit);
        List<SystemTutorialVO> resultList = sysTemTutorialMapper.queryTutorialByParams(tutorialParams.getBelongMenuId(), tutorialParams.getVideoName());
        // 查出所有一级菜单menu
        List<MenuVO> menuVOList = sysTemTutorialMapper.queryAllParentMenu();
        Map<Integer, String> menuMap = new HashMap<>();
        menuVOList.forEach(item -> menuMap.put(item.getId(), item.getTitle()));
        resultList.forEach(item -> {
            String title = item.getBelongMenu();
            String parentMenuName = menuMap.get(item.getBelongParentMenuId());
            item.setBelongParentMenu(parentMenuName);
            item.setBelongMenu(parentMenuName + "/" + title);
        });
        return new PageInfo<>(resultList);
    }

    private void validAdminPermission(HttpServletRequest request, String message) {
        String userId = (String) request.getAttribute("userId");
        String role = (String) request.getAttribute("role");
        boolean isAdmin = validUserHasAdminPermission(userId, role);
        if (!isAdmin) {
            throw new AuthzException(message);
        }
    }

    /**
     * 获取全量二级菜单列表
     *
     * @return 数据
     */
    public List<MenuVO> getMenuList() {
        List<MenuVO> menuList = sysTemTutorialMapper.getMenuList();
        if (CollectionUtils.isEmpty(menuList)) {
            return new ArrayList<>();
        }
        menuList.forEach(item -> {
            String title = item.getTitle();
            item.setTitle(item.getParentTile() + "/" + title);
        });
        return menuList;
    }

    /**
     * 基于系统教程id查询附件，并返回oss临时授权访问的url
     *
     * @param tutorialId 教程id
     * @return 教程对象
     */
    public List<AttachDTO> playVideo(String tutorialId) {
        if (StringUtils.isEmpty(tutorialId) || !RegexUtils.regex(KnowledgeConstant.UUID_REGEX, tutorialId)) {
            throw new ArgumentException("系统教程id参数校验异常");
        }
        SystemTutorialDTO systemTutorialDTO = sysTemTutorialMapper.queryTutorialById(tutorialId);
        if (systemTutorialDTO == null) {
            throw new FailException("系统教程数据不存在，请重试");
        }
        List<AttachDTO> attachDTOList = sysTemTutorialMapper.queryAttachByTutorialId(tutorialId);
        if (CollectionUtils.isEmpty(attachDTOList)) {
            throw new FailException("无法查找到系统教程附件视频文件，请重试");
        }
        return ossUrl(attachDTOList);
    }
}