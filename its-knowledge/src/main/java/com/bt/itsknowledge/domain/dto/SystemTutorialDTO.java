package com.bt.itsknowledge.domain.dto;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.ArrayList;
import java.util.List;

/**
 * 描述：接收前端参数的数据模型类
 *
 * <AUTHOR>
 * @since 2023-02-01 14:16
 */
public class SystemTutorialDTO {
    // 主键id
    private String id;
    // 附件ids
    List<Integer> attachIds = new ArrayList<>();
    // 视频名称
    @NotNull(message = "视频名称不能为空")
    @Length(max = 64, message = "视频名称字符长度超过限定值")
    @Pattern(regexp = "^[A-Za-z0-9_\\-\\u4e00-\\u9fa5]+$", message = "视频名称格式校验异常")
    private String videoName;
    // 视频文件大小(max为100MB)
    @NotNull(message = "视频大小不能为空")
    @Range(max = 104857600, message = "视频大小不能超过100MB")
    private Long videoSize;
    // 视频时长
    @NotNull(message = "视频时长不能为空")
    private String videoDuration;
    // 所属菜单id，菜单id上限是10000个
    @NotNull(message = "所属功能菜单不能为空")
    @Range(min = 1, max = 10000, message = "所属功能菜单超过限定值")
    private Integer belongMenuId;
    // 上传人名称
    @Length(max = 512, message = "上传人名称字符超过限定值")
    private String uploadUserName;
    // 上传人id
    @NotNull(message = "上传人不能为空")
    @Length(max = 36, message = "上传人字符长度超过限定值")
    private String uploadUserId;
    // 上传时间
    @NotNull(message = "上传时间不能为空")
    @Range(max = 1000000000000L, message = "上传时间范围超过限定值")
    private Long uploadTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVideoName() {
        return videoName;
    }

    public void setVideoName(String videoName) {
        this.videoName = videoName;
    }

    public Long getVideoSize() {
        return videoSize;
    }

    public void setVideoSize(Long videoSize) {
        this.videoSize = videoSize;
    }

    public String getVideoDuration() {
        return videoDuration;
    }

    public void setVideoDuration(String videoDuration) {
        this.videoDuration = videoDuration;
    }

    public Integer getBelongMenuId() {
        return belongMenuId;
    }

    public void setBelongMenuId(Integer belongMenuId) {
        this.belongMenuId = belongMenuId;
    }

    public String getUploadUserName() {
        return uploadUserName;
    }

    public void setUploadUserName(String uploadUserName) {
        this.uploadUserName = uploadUserName;
    }

    public String getUploadUserId() {
        return uploadUserId;
    }

    public void setUploadUserId(String uploadUserId) {
        this.uploadUserId = uploadUserId;
    }

    public long getUploadTime() {
        return uploadTime;
    }

    public void setUploadTime(Long uploadTime) {
        this.uploadTime = uploadTime;
    }

    public List<Integer> getAttachIds() {
        return attachIds;
    }

    public void setAttachIds(List<Integer> attachIds) {
        this.attachIds = attachIds;
    }
}
