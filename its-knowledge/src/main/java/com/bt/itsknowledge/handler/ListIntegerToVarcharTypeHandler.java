package com.bt.itsknowledge.handler;

import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.apache.ibatis.type.TypeHandler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 *
 * <AUTHOR>
 * @data 2023/2/14
 * @description: 类型转换器，用于数据库的varchar和Java中List<String>类型的相互转换
 */
@MappedJdbcTypes(JdbcType.VARCHAR)
@MappedTypes(List.class)
public class ListIntegerToVarcharTypeHandler implements TypeHandler<List<Integer>> {
    @Override
    public void setParameter(PreparedStatement preparedStatement, int i, List<Integer> integers, JdbcType jdbcType) throws SQLException {
        // 遍历List类型的入参，拼装为String类型，使用Statement对象插入数据库
        StringBuffer sb = new StringBuffer();
        for (int j = 0; j < integers.size(); j++) {
            if (j == integers.size() - 1) {
                sb.append(integers.get(j));
            } else {
                sb.append(integers.get(j)).append(",");
            }
        }
        preparedStatement.setString(i, sb.toString());
    }

    @Override
    public List<Integer> getResult(ResultSet resultSet, String s) throws SQLException {
        // 获取String类型的结果，使用","分割为List后返回
        String resultString = resultSet.getString(s);
        String[] items = resultString.split(",");
        if (StringUtils.isNotEmpty(resultString)) {
            return Stream.of(items).map(Integer::parseInt).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public List<Integer> getResult(ResultSet resultSet, int i) throws SQLException {
        // 获取String类型的结果，使用","分割为List后返回
        String resultString = resultSet.getString(i);
        String[] items = resultString.split(",");
        if (StringUtils.isNotEmpty(resultString)) {
            return Stream.of(items).map(Integer::parseInt).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public List<Integer> getResult(CallableStatement callableStatement, int i) throws SQLException {
        // 获取String类型的结果，使用","分割为List后返回
        String resultString = callableStatement.getString(i);
        String[] items = resultString.split(",");
        if (StringUtils.isNotEmpty(resultString)) {
            return Stream.of(items).map(Integer::parseInt).collect(Collectors.toList());
        }
        return null;
    }
}