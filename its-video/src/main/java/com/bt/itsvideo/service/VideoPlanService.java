package com.bt.itsvideo.service;

import java.util.List;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.bt.itscore.domain.dto.IdStringDTO;
import com.bt.itscore.domain.dto.PageDTO;
import com.bt.itscore.domain.vo.ResponseVO;
import com.bt.itscore.utils.TimeUtils;
import com.bt.itsvideo.domain.dto.VideoQbPlanDTO;
import com.bt.itsvideo.domain.dto.VideoQbPlanDivDTO;
import com.bt.itsvideo.domain.vo.VideoQbPlanDivVO;
import com.bt.itsvideo.domain.vo.VideoQbPlanVO;
import com.bt.itsvideo.mapper.VideoQbPlanMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

@Service("videoPlanService")
public class VideoPlanService {

	@Autowired
	private VideoQbPlanMapper videoQbPlanMapper;

	@Transactional
	public ResponseVO add(VideoQbPlanDTO dto) {
		String planName = dto.getPlanName();
		int count = videoQbPlanMapper.checkPlanName(planName);
		if (count > 0) {
			return new ResponseVO("预案名称已经存在！", 0);
		} else {
			String id = UUID.randomUUID().toString();
			dto.setId(id);
			dto.setCreateTime(TimeUtils.getTimeString());
			dto.setSetFlag(0);
			int ret = videoQbPlanMapper.add(dto);
			if (ret > 0) {
				List<VideoQbPlanDivDTO> palnDivList = dto.getPalnDivList();
				if (!palnDivList.isEmpty()) {// 更新预案摄像机从表
					palnDivList.stream().forEach(x -> {
						x.setId(UUID.randomUUID().toString());
						x.setPlanId(id);
					});
					// 先删除，后新增
					videoQbPlanMapper.deleteDivsById(id);
					videoQbPlanMapper.addDivs(palnDivList);
				}
				return new ResponseVO(1, id);
			} else {
				return new ResponseVO(0);
			}
		}

	}

	@Transactional
	public ResponseVO update(VideoQbPlanDTO dto) {
		String id = dto.getId();
		int ret = videoQbPlanMapper.update(dto);
		if (ret > 0) {
			List<VideoQbPlanDivDTO> palnDivList = dto.getPalnDivList();
			if (!palnDivList.isEmpty()) {// 更新预案摄像机从表
				palnDivList.stream().forEach(x -> {
					x.setId(UUID.randomUUID().toString());
					x.setPlanId(id);
				});
				// 先删除，后新增
				videoQbPlanMapper.deleteDivsById(id);
				videoQbPlanMapper.addDivs(palnDivList);
			}
			return new ResponseVO(1, id);
		} else {
			return new ResponseVO(0);
		}
	}

	public ResponseVO setPlan(IdStringDTO dto) {
		boolean ret = videoQbPlanMapper.setPlan(dto) > 0;
		return new ResponseVO(ret);
	}
	
	@Transactional
	public ResponseVO delete(IdStringDTO dto) {
		String id = dto.getId();
		int ret = videoQbPlanMapper.delete(dto);
		if (ret > 0) {
			videoQbPlanMapper.deleteDivsById(id); // 删除从表
			return new ResponseVO(1, id);
		} else {
			return new ResponseVO(0);
		}
	}

	public PageInfo<VideoQbPlanVO> page(PageDTO pageDTO, VideoQbPlanDTO dto) {
		PageHelper.startPage(pageDTO.getPage(), pageDTO.getLimit());
		List<VideoQbPlanVO> list = videoQbPlanMapper.selectList(dto);
		PageInfo<VideoQbPlanVO> pageInfo = new PageInfo<>(list);
		return pageInfo;
	}

	public List<VideoQbPlanDivVO> selectDivListById(IdStringDTO dto) {
		return videoQbPlanMapper.selectDivListById(dto);
	}

}
