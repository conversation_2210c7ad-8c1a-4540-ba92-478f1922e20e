package com.bt.itsvideo.service;

import java.net.MalformedURLException;
import java.net.URL;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.configuration.jsse.TLSClientParameters;
import org.apache.cxf.configuration.security.AuthorizationPolicy;
import org.apache.cxf.endpoint.Client;
import org.apache.cxf.endpoint.ConduitSelector;
import org.apache.cxf.endpoint.Endpoint;
import org.apache.cxf.endpoint.EndpointImpl;
import org.apache.cxf.service.model.EndpointInfo;
import org.apache.cxf.transport.http.HTTPConduit;
import org.apache.cxf.transport.http.HTTPConduitConfigurer;
import org.apache.cxf.transport.http.auth.DefaultBasicAuthSupplier;
import org.apache.cxf.transport.http.auth.HttpAuthSupplier;
import org.apache.cxf.transports.http.configuration.HTTPClientPolicy;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.bt.itscore.constants.FacilityType;
import com.bt.itscore.domain.dto.CommandDTO;
import com.bt.itscore.domain.dto.IdStringBatchDTO;
import com.bt.itscore.domain.dto.IdStringDTO;
import com.bt.itscore.domain.dto.VideoDeviceDTO;
import com.bt.itscore.domain.dto.VideoDownloadDTO;
import com.bt.itscore.domain.vo.ResponseVO;
import com.bt.itscore.enums.AlarmTypeEnum;
import com.bt.itscore.exception.ArgumentException;
import com.bt.itscore.exception.FailException;
import com.bt.itscore.utils.GsonUtils;
import com.bt.itscore.utils.HttpClientUtils;
import com.bt.itscore.utils.IpAddressUtils;
import com.bt.itscore.utils.MilePostUtils;
import com.bt.itscore.utils.TimeUtils;
import com.bt.itsvideo.domain.dto.CheckRtspResultDTO;
import com.bt.itsvideo.domain.dto.DeviceStatu2DTO;
import com.bt.itsvideo.domain.dto.DeviceStatuDTO;
import com.bt.itsvideo.domain.dto.DeviceStatus2DTO;
import com.bt.itsvideo.domain.dto.DeviceStatusDTO;
import com.bt.itsvideo.domain.dto.EventMatchDTO;
import com.bt.itsvideo.domain.dto.PlayStreamDTO;
import com.bt.itsvideo.domain.dto.PlayingVideoDTO;
import com.bt.itsvideo.domain.dto.PtzDTO;
import com.bt.itsvideo.domain.dto.SysMetricsDTO;
import com.bt.itsvideo.domain.dto.VideoCallCountDTO;
import com.bt.itsvideo.domain.dto.VideoCloudDTO;
import com.bt.itsvideo.domain.dto.VideoControlDTO;
import com.bt.itsvideo.domain.dto.VideoDTO;
import com.bt.itsvideo.domain.dto.VideoDirDTO;
import com.bt.itsvideo.domain.dto.VideoEventDTO;
import com.bt.itsvideo.domain.dto.VideoMilePostDTO;
import com.bt.itsvideo.domain.dto.VideoOnlineDTO;
import com.bt.itsvideo.domain.dto.VideoPingDTO;
import com.bt.itsvideo.domain.dto.VideoPlanDTO;
import com.bt.itsvideo.domain.dto.VideoPlanDivDTO;
import com.bt.itsvideo.domain.dto.VideoPlanDivListDTO;
import com.bt.itsvideo.domain.dto.VideoPlayDTO;
import com.bt.itsvideo.domain.dto.VideoPostionDTO;
import com.bt.itsvideo.domain.dto.VideoTapeDTO;
import com.bt.itsvideo.domain.vo.CheckRtspResultVO;
import com.bt.itsvideo.domain.vo.DeviceVO;
import com.bt.itsvideo.domain.vo.HistoryVideoVO;
import com.bt.itsvideo.domain.vo.SourceStatusVO;
import com.bt.itsvideo.domain.vo.TunnelMilePostsVO;
import com.bt.itsvideo.domain.vo.VideoDeviceVO;
import com.bt.itsvideo.domain.vo.VideoDirVO;
import com.bt.itsvideo.domain.vo.VideoMenuVO;
import com.bt.itsvideo.domain.vo.VideoPingVO;
import com.bt.itsvideo.domain.vo.VideoPlanDivVO;
import com.bt.itsvideo.domain.vo.VideoPlanVO;
import com.bt.itsvideo.domain.vo.VideoStatusVO;
import com.bt.itsvideo.domain.vo.VideoVO;
import com.bt.itsvideo.feign.ItsMsFeignClient;
import com.bt.itsvideo.mapper.VideoCallCountMapper;
import com.bt.itsvideo.mapper.VideoMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import direction.videomonitor.monitor.sipserviceconsumer.GetRecordFileListResponse;
import direction.videomonitor.monitor.sipserviceconsumer.StorageFileInfo;
import io.prometheus.client.Counter;

@Service("videoService")
public class VideoService {
	private final static Logger LOGGER = LoggerFactory.getLogger(VideoService.class);
	private static final Integer TIME_OUT_MILI = 2000;
	private static final Integer EXPIRE_TIME = 3;
	private static Counter moduleCounter;
	@Autowired
	private RedisTemplate redisTemplate;
	@Autowired
	VideoMapper videoMapper;
	@Autowired
	RestTemplate restTemplate;
	@Autowired
	VideoStorageService videoStorageService;
	@Autowired
	StringRedisTemplate stringRedisTemplate;
	@Autowired
	ItsMsFeignClient itsMsFeignClient;
	@Autowired
	VideoCallCountMapper videoCallCountMapper;

	@Value("${video.tape.storage.ipport}")
	String storageIpPort;
	@Value("${video.play.secret.key}")
	String secretKey;
	// http://************:8082/video-cloud
	@Value("${video.cloud.path}")
	String videoCloudPath;
	@Value("${video.tape.prefix}")
	String playAddressPrefix;
	@Value("${video.play.time}")
	Integer palyTime;
	@Value("${video.inner.switch:false}")
	private boolean innerSwitch; // 是否是内网服务 true:是
	@Value("${video.inner.apiserve:false}")
	String innerApiServe; // 内网流媒体API服务
	@Value("${video.inner.streamserve:false}")
	String innerStreamServe; // 流媒体服务地址
	@Value("${video.inner.streamport:8004}")
	Integer innerStreamPort;// 流媒体服务端口
	@Value("${video.inner.minport:8005}")
	Integer innerMinPort;// 视频播流流媒体默认最小端口
	@Value("${video.inner.maxport:8006}")
	Integer innerMaxPort;// 视频播流流媒体默认最大端口
	@Value("${aiot.url:https://www.aiot-y.com/api}")
	String aiotUrl;// AI摄像机对接平台地址
	@Value("${aiot.secret:2f5c894344f34fd2bac1bfe1e1a2179a}")
	String aiotSecret;// AI摄像机对接平台地址
	@Value("${aiot.appkey:055fb33b77754841b8493a76a4eadacf}")
	String aiotAppkey;// AI摄像机对接平台地址
	public static final String AIOT_ACCESS_TOKEN = "video:aiot_access_token"; // AI平台token，有效期默认为7天

	private final String TOLL_PLAZA_POSITION = "3aff1795-f641-430a-abb5-4c27082442e7";// 收费站广场 位置
	private final String TUNNEL_1_POSITION = "16ef1a78-68b2-40d6-aebf-9a896ade004b";// 隧道内 位置
	private final String TUNNEL_2_POSITION = "38c4f1cb-079a-494b-9a76-26d95f9ba079";// 隧道口 位置
	public static List<Integer> PORT_LIST = new ArrayList<>();

	// Prometheus监控埋点初始化
	static {
		moduleCounter = Counter.build().name("video_module_counter").help("counts video module").labelNames("modules")
				.register();
	}
//  @Autowired
//  ItsWebSocketFeignClient itsWebSocketFeignClient;

//  static {
//      JaxWsDynamicClientFactory dcf = JaxWsDynamicClientFactory.newInstance();
//      client = dcf.createClient("http://45.84.209.11:9100/VideoMonitor/services/AppVodService?wsdl");
//      client = dcf.createClient("http://111.59.224.120:9100/VideoMonitor/services/AppVodService?wsdl");
//  }

//    public synchronized void createClient(String url) {
//        if(client == null) {
//            JaxWsDynamicClientFactory dcf = JaxWsDynamicClientFactory.newInstance();
////          System.out.println("haha1.5");
//            try {
//
//                LOGGER.info("create connection to {}", url);
//                client = dcf.createClient(url);
//                // 设置超时单位为毫秒
//                HTTPConduit conduit = (HTTPConduit)client.getConduit();
//                HTTPClientPolicy policy = new HTTPClientPolicy();
//                policy.setConnectionTimeout(2000);
//                policy.setAllowChunking(false);
//                policy.setReceiveTimeout(2000);
//                policy.setConnectionRequestTimeout(2000);
//                conduit.setClient(policy);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//    }

	public List<VideoMenuVO> tree(VideoDTO dto, String[] roles) {
		Map<String, Object> map = new HashMap<>();
		map.put("keywords", dto.getKeywords());
		List<VideoVO> ret = videoMapper.selectAll(map);
		// 查询登录人有哪些权限的详细位置的视频
		map.put("roles", roles);
		List<String> positionIds = videoMapper.selectDevicePositionIdsByRoleIds(map);
		ret = ret.stream().filter(videoMenu -> (videoMenu.getDevicePositionId() == null
				|| positionIds.contains(videoMenu.getDevicePositionId()))).collect(Collectors.toList());

		if (dto.getGroupType() == 1) {
			// 按公司分类
			return groupCompany(ret);
		} else if (dto.getGroupType() == 2) {
			// 按路段分类
			return groupRoad(ret);
		}

		return null;
	}

	/**
	 * @描述 按照运营中心分类的树形菜单
	 */
	private List<VideoMenuVO> groupCompany(List<VideoVO> videoMenus) {
		List<VideoMenuVO> orgNodes = new ArrayList<>();

		// 过滤公司名，路名，设施类名，设施名为空的设备
		List<VideoVO> filteredVideos = videoMenus.stream()
				.filter(videoMenu -> videoMenu.getOrgId() != null && videoMenu.getRoadNo() != null
						&& videoMenu.getFacilityTypeNo() != null && videoMenu.getFacilityNo() != null
						&& videoMenu.getFacilityTypeNo() != 1)
				.collect(Collectors.toList());

		List<VideoVO> scVideos = videoMenus.stream()
				.filter(videoMenu -> videoMenu.getOrgId() != null && videoMenu.getRoadNo() != null
						&& videoMenu.getFacilityTypeNo() != null && videoMenu.getFacilityNo() != null
						&& videoMenu.getFacilityTypeNo() == 1)
				.collect(Collectors.toList());

		filteredVideos.stream().sorted(Comparator.comparing(videoMenu -> videoMenu.getOrgSort()))
				.collect(Collectors.groupingBy(VideoVO::getOrgShortName, LinkedHashMap::new, Collectors.toList()))
				.forEach((orgShortName, orgIdlist) -> {
					String orgId = orgIdlist.get(0).getOrgId();
					VideoMenuVO orgNode = new VideoMenuVO();
					orgNode.setId("orgId_" + orgId);
					orgNode.setpId("");
					orgNode.setName(orgShortName);				
					int orgOffline = Math
							.toIntExact(orgIdlist.stream().filter(videoMenu -> videoMenu.getStatus() == 0).count());
					orgNode.setOnlineCount(orgIdlist.size() - orgOffline);
					orgNode.setOfflineCount(orgOffline);
					orgNodes.add(orgNode);
					List<VideoMenuVO> roadNodes = new ArrayList<>();
					orgNode.setChildren(roadNodes);
					orgIdlist.stream()
							.collect(Collectors.groupingBy(VideoVO::getRoadSort, TreeMap::new, Collectors.toList()))
							.forEach((roadNo, camerasOfTheSameRoad) -> {
								addRoadNode(roadNodes, camerasOfTheSameRoad, orgId, roadNo);
							});
				});
		List<VideoMenuVO> scMenus = new ArrayList<>();
		scVideos.stream()
				.collect(Collectors.groupingBy(VideoVO::getFacilityNo, LinkedHashMap::new, Collectors.toList()))
				.forEach((facilityNo, orgIdlist) -> {
					String orgId = orgIdlist.get(0).getOrgId();
					VideoMenuVO scNode = new VideoMenuVO();
					scNode.setId("facilityTypeNo_" + orgId + "_" + orgIdlist.get(0).getRoadSort() + "_"
							+ orgIdlist.get(0).getFacilityTypeNo());
//			scNode.setId("roadNo_" + facilityNo);
					scNode.setpId("orgId_" + orgId);
					scNode.setName(orgIdlist.get(0).getFacilityName());
					int orgOffline = Math
							.toIntExact(orgIdlist.stream().filter(videoMenu -> videoMenu.getStatus() == 0).count());
					scNode.setOnlineCount(orgIdlist.size() - orgOffline);
					scNode.setOfflineCount(orgOffline);
					scMenus.add(scNode);
					List<VideoMenuVO> videoNodes = new ArrayList<>();
					scNode.setChildren(videoNodes);
					addCameraNode(videoNodes, orgIdlist, orgId, 0, orgIdlist.get(0).getFacilityTypeNo(), facilityNo);
				});
		if (!CollectionUtils.isEmpty(scMenus)) {
			for (VideoMenuVO orgMenu : orgNodes) {
				String id = orgMenu.getId();
				for (VideoMenuVO scMenu : scMenus) {
					String pid = scMenu.getpId();
					if (id.equals(pid)) {
						orgMenu.getChildren().add(0, scMenu);
					}
				}
			}
		}

		return orgNodes;
	}

	private void addRoadNode(List<VideoMenuVO> roadNodes, List<VideoVO> camerasOfTheSameRoad, String orgId,
			Integer roadNo) {
		String roadName = camerasOfTheSameRoad.get(0).getRoadName();
		VideoMenuVO roadNode = new VideoMenuVO();
		if (orgId == null) {
			roadNode.setId("roadNo_" + roadNo);
			roadNode.setpId("");
		} else {
			roadNode.setId("roadNo_" + orgId + "_" + roadNo);
			roadNode.setpId("orgId_" + orgId);
		}
		roadNode.setName(roadName);
		roadNode.setOnlineCount(
				Math.toIntExact(camerasOfTheSameRoad.stream().filter(videoMenu -> videoMenu.getStatus() == 1).count()));
		roadNode.setOfflineCount(
				Math.toIntExact(camerasOfTheSameRoad.stream().filter(videoMenu -> videoMenu.getStatus() == 0).count()));
		roadNodes.add(roadNode);

		List<VideoMenuVO> facilityTypeNodes = new ArrayList<>();
		roadNode.setChildren(facilityTypeNodes);
		camerasOfTheSameRoad.stream().sorted(Comparator.comparing(videoMenu -> {
			switch (videoMenu.getFacilityTypeNo()) {
			// 分组排序 隧道 收费站 服务区 路面
			case 2:
				return 10;
			case 8:
				return 20;
			case 7:
				return 30;
			case 11:
				return 40;
			case 3:
				return 50;
			default:
				return 60;
			}
		})).collect(Collectors.groupingBy(VideoVO::getFacilityTypeNo, LinkedHashMap::new, Collectors.toList()))
				.forEach((facilityTypeNo, camerasOfTheSameFacilityType) -> {
					addFacilityTypeNode(facilityTypeNodes, camerasOfTheSameFacilityType, orgId, roadNo, facilityTypeNo);
				});
	}

	private void addFacilityTypeNode(List<VideoMenuVO> facilityTypeNodes, List<VideoVO> camerasOfTheSameFacilityType,
			String orgId, Integer roadNo, Integer facilityTypeNo) {
		VideoMenuVO facilityTypeNode = new VideoMenuVO();
		if (orgId == null) {
			facilityTypeNode.setId("facilityTypeNo_" + roadNo + "_" + facilityTypeNo);
			facilityTypeNode.setpId("roadNo_" + roadNo);
		} else {
			facilityTypeNode.setId("facilityTypeNo_" + orgId + "_" + roadNo + "_" + facilityTypeNo);
			facilityTypeNode.setpId("roadNo_" + orgId + "_" + roadNo);
		}
		facilityTypeNode.setName(camerasOfTheSameFacilityType.get(0).getFacilityTypeName());
		facilityTypeNode.setOnlineCount(Math.toIntExact(
				camerasOfTheSameFacilityType.stream().filter(videoMenu -> videoMenu.getStatus() == 1).count()));
		facilityTypeNode.setOfflineCount(Math.toIntExact(
				camerasOfTheSameFacilityType.stream().filter(videoMenu -> videoMenu.getStatus() == 0).count()));
		facilityTypeNodes.add(facilityTypeNode);

		List<VideoMenuVO> facilityNodes = new ArrayList<>();
		facilityTypeNode.setChildren(facilityNodes);
		camerasOfTheSameFacilityType.stream().sorted(Comparator.comparing(videoMenu -> videoMenu.getFacilityMilePost()))
				.collect(Collectors.groupingBy(VideoVO::getFacilityNo, LinkedHashMap::new, Collectors.toList()))
				.forEach((facilityNo, camerasOfTheSameFacility) -> {
					if (facilityTypeNo == 2) {// 隧道，需要添加一层
						// 特殊，隧道区分 上行、下行、其他
						addTunnelFacilityNode(facilityNodes, camerasOfTheSameFacility, orgId, roadNo, facilityTypeNo,
								facilityNo);
					} else {
						addFacilityNode(facilityNodes, camerasOfTheSameFacility, orgId, roadNo, facilityTypeNo,
								facilityNo);
					}
				});
	}

	private void addTunnelFacilityNode(List<VideoMenuVO> facilityNodes, List<VideoVO> camerasOfTheSameFacility,
			String orgId, Integer roadNo, Integer facilityTypeNo, String facilityNo) {
		// 上行
		List<VideoVO> sortedCameraListOfTheSameFacility0 = camerasOfTheSameFacility.stream()
				.filter(videoMenu -> videoMenu.getRoadLine() == 0 && !videoMenu.getDirectionName().contains("双向"))
				.sorted(Comparator.comparing(videoMenu -> videoMenu.getSort()))// 按照sort排序，同一个设施对应的摄像机列表
				.collect(Collectors.toList());
		// 下行
		List<VideoVO> sortedCameraListOfTheSameFacility1 = camerasOfTheSameFacility.stream()
				.filter(videoMenu -> videoMenu.getRoadLine() == 1)
				.sorted(Comparator.comparing(videoMenu -> videoMenu.getSort()))// 按照sort排序，同一个设施对应的摄像机列表
				.collect(Collectors.toList());
		// 其他
		List<VideoVO> sortedCameraListOfTheSameFacility2 = camerasOfTheSameFacility.stream()
				.filter(videoMenu -> videoMenu.getRoadLine() == 0 && videoMenu.getDirectionName().contains("双向"))
				.sorted(Comparator.comparing(videoMenu -> videoMenu.getSort()))// 按照sort排序，同一个设施对应的摄像机列表
				.collect(Collectors.toList());

		if (!CollectionUtils.isEmpty(sortedCameraListOfTheSameFacility0)) {
			VideoMenuVO facilityNode0 = new VideoMenuVO();
			if (orgId == null) {
				facilityNode0.setId("facilityNo_" + roadNo + "_" + facilityTypeNo + "_" + facilityNo + "_0");
				facilityNode0.setpId("facilityTypeNo_" + roadNo + "_" + facilityTypeNo);
			} else {
				facilityNode0.setId("facilityNo_" + orgId + "_" + roadNo + "_" + facilityTypeNo + "_" + facilityNo);
				facilityNode0.setpId("facilityTypeNo_" + orgId + "_" + roadNo + "_" + facilityTypeNo);
			}
			facilityNode0.setName(sortedCameraListOfTheSameFacility0.get(0).getFacilityName() + "(上行)");
			facilityNode0.setOnlineCount(Math.toIntExact(sortedCameraListOfTheSameFacility0.stream()
					.filter(videoMenu -> videoMenu.getStatus() == 1).count()));
			facilityNode0.setOfflineCount(Math.toIntExact(sortedCameraListOfTheSameFacility0.stream()
					.filter(videoMenu -> videoMenu.getStatus() == 0).count()));

			List<VideoMenuVO> cameraNodes0 = new ArrayList<>();
			facilityNode0.setChildren(cameraNodes0);
			addCameraNode(cameraNodes0, sortedCameraListOfTheSameFacility0, orgId, roadNo, facilityTypeNo, facilityNo);
			facilityNodes.add(facilityNode0);
		}

		if (!CollectionUtils.isEmpty(sortedCameraListOfTheSameFacility1)) {
			VideoMenuVO facilityNode1 = new VideoMenuVO();
			if (orgId == null) {
				facilityNode1.setId("facilityNo_" + roadNo + "_" + facilityTypeNo + "_" + facilityNo);
				facilityNode1.setpId("facilityTypeNo_" + roadNo + "_" + facilityTypeNo);
			} else {
				facilityNode1
						.setId("facilityNo_" + orgId + "_" + roadNo + "_" + facilityTypeNo + "_" + facilityNo + "_1");
				facilityNode1.setpId("facilityTypeNo_" + orgId + "_" + roadNo + "_" + facilityTypeNo);
			}
			facilityNode1.setName(sortedCameraListOfTheSameFacility1.get(0).getFacilityName() + "(下行)");
			facilityNode1.setOnlineCount(Math.toIntExact(sortedCameraListOfTheSameFacility1.stream()
					.filter(videoMenu -> videoMenu.getStatus() == 1).count()));
			facilityNode1.setOfflineCount(Math.toIntExact(sortedCameraListOfTheSameFacility1.stream()
					.filter(videoMenu -> videoMenu.getStatus() == 0).count()));

			List<VideoMenuVO> cameraNodes1 = new ArrayList<>();
			facilityNode1.setChildren(cameraNodes1);
			addCameraNode(cameraNodes1, sortedCameraListOfTheSameFacility1, orgId, roadNo, facilityTypeNo, facilityNo);
			facilityNodes.add(facilityNode1);
		}

		if (!CollectionUtils.isEmpty(sortedCameraListOfTheSameFacility2)) {
			VideoMenuVO facilityNode2 = new VideoMenuVO();
			if (orgId == null) {
				facilityNode2.setId("facilityNo_" + roadNo + "_" + facilityTypeNo + "_" + facilityNo);
				facilityNode2.setpId("facilityTypeNo_" + roadNo + "_" + facilityTypeNo);
			} else {
				facilityNode2
						.setId("facilityNo_" + orgId + "_" + roadNo + "_" + facilityTypeNo + "_" + facilityNo + "_2");
				facilityNode2.setpId("facilityTypeNo_" + orgId + "_" + roadNo + "_" + facilityTypeNo);
			}
			facilityNode2.setName(sortedCameraListOfTheSameFacility2.get(0).getFacilityName() + "(其他)");
			facilityNode2.setOnlineCount(Math.toIntExact(sortedCameraListOfTheSameFacility2.stream()
					.filter(videoMenu -> videoMenu.getStatus() == 1).count()));
			facilityNode2.setOfflineCount(Math.toIntExact(sortedCameraListOfTheSameFacility2.stream()
					.filter(videoMenu -> videoMenu.getStatus() == 0).count()));

			List<VideoMenuVO> cameraNodes2 = new ArrayList<>();
			facilityNode2.setChildren(cameraNodes2);
			addCameraNode(cameraNodes2, sortedCameraListOfTheSameFacility2, orgId, roadNo, facilityTypeNo, facilityNo);
			facilityNodes.add(facilityNode2);

		}

	}

	private void addFacilityNode(List<VideoMenuVO> facilityNodes, List<VideoVO> camerasOfTheSameFacility, String orgId,
			Integer roadNo, Integer facilityTypeNo, String facilityNo) {
		List<VideoVO> sortedCameraListOfTheSameFacility = camerasOfTheSameFacility.stream()
				.sorted(Comparator.comparing(videoMenu -> videoMenu.getSort()))// 按照sort排序，同一个设施对应的摄像机列表
				.collect(Collectors.toList());
		VideoMenuVO facilityNode = new VideoMenuVO();
		if (orgId == null) {
			facilityNode.setId("facilityNo_" + roadNo + "_" + facilityTypeNo + "_" + facilityNo);
			facilityNode.setpId("facilityTypeNo_" + roadNo + "_" + facilityTypeNo);
		} else {
			facilityNode.setId("facilityNo_" + orgId + "_" + roadNo + "_" + facilityTypeNo + "_" + facilityNo);
			facilityNode.setpId("facilityTypeNo_" + orgId + "_" + roadNo + "_" + facilityTypeNo);
		}
		facilityNode.setName(sortedCameraListOfTheSameFacility.get(0).getFacilityName());
		facilityNode.setOnlineCount(Math.toIntExact(
				sortedCameraListOfTheSameFacility.stream().filter(videoMenu -> videoMenu.getStatus() == 1).count()));
		facilityNode.setOfflineCount(Math.toIntExact(
				sortedCameraListOfTheSameFacility.stream().filter(videoMenu -> videoMenu.getStatus() == 0).count()));

		List<VideoMenuVO> cameraNodes = new ArrayList<>();
		facilityNode.setChildren(cameraNodes);
		addCameraNode(cameraNodes, sortedCameraListOfTheSameFacility, orgId, roadNo, facilityTypeNo, facilityNo);
		facilityNodes.add(facilityNode);
	}

	private void addCameraNode(List<VideoMenuVO> cameraNodes, List<VideoVO> sortedCameraListOfTheSameFacility,
			String orgId, Integer roadNo, Integer facilityTypeNo, String facilityNo) {
		sortedCameraListOfTheSameFacility.forEach(videoMenu -> {
			VideoMenuVO cameraNode = new VideoMenuVO();
			cameraNode.setId(videoMenu.getDeviceId());
			if (orgId == null) {
				cameraNode.setpId("facilityNo_" + roadNo + "_" + facilityTypeNo + "_" + facilityNo);
			} else {
				cameraNode.setpId("facilityNo_" + orgId + "_" + roadNo + "_" + facilityTypeNo + "_" + facilityNo);
			}
			Integer ptz = videoMenu.getPtz();
			String ptzName = (ptz == null || ptz == 0 || ptz == 2) ? "" : "(云)";
			if (videoMenu.getFacilityTypeNo() == 8 || videoMenu.getFacilityTypeNo() == 7) {
				cameraNode.setName(videoMenu.getDeviceShortName() + ptzName);
			} else {
				Integer roadLine = videoMenu.getRoadLine();
				String directionName = roadLine == null ? ""
						: (roadLine == 1 ? "[下行]" : ("双向".equals(videoMenu.getDirectionName()) ? "" : "[上行]"));
				cameraNode.setName(videoMenu.getMilePost() + videoMenu.getDeviceShortName() + directionName + ptzName);
			}
			cameraNode.setOnlineCount(videoMenu.getStatus());
			cameraNode.setCameraCode(videoMenu.getCameraCode());
			cameraNode.setIp(videoMenu.getIpcIpaddress());
			cameraNode.setPort(videoMenu.getIpcPort());
			cameraNode.setDvrProtocol(videoMenu.getDvrProtocol());
			cameraNode.setPtz(videoMenu.getPtz());
			cameraNode.setRoadNo(videoMenu.getRoadNo());
			cameraNode.setDeviceMilePost(videoMenu.getMilePost());
			cameraNode.setFacilityMilePost(videoMenu.getFacilityMilePostStr());
			cameraNode.setSpUpdateTime(videoMenu.getSpUpdateTime());
			cameraNode.setCameraType(videoMenu.getCameraType());
			cameraNode.setProtocol(videoMenu.getProtocol());
			cameraNodes.add(cameraNode);
		});
	}

	/**
	 * @描述 按照路段分类的树形菜单
	 */
	private List<VideoMenuVO> groupRoad(List<VideoVO> videoMenus) {
		List<VideoMenuVO> roadNodes = new ArrayList<>();
		// 过滤公司名，路名，设施类名，设施名为空的设备
		List<VideoVO> filterNull = videoMenus.stream().filter(videoMenu -> videoMenu.getRoadNo() != null
				&& videoMenu.getFacilityTypeNo() != null && videoMenu.getFacilityNo() != null)
				.collect(Collectors.toList());
		filterNull.stream().collect(Collectors.groupingBy(VideoVO::getRoadSort, TreeMap::new, Collectors.toList()))
				.forEach((roadNo, camerasOfTheSameRoad) -> {
					addRoadNode(roadNodes, camerasOfTheSameRoad, null, roadNo);
				});
		return roadNodes;
	}

	/**
	 * @描述 视频播流接口
	 * @return
	 */
	public Object play(VideoPlayDTO dto, String userId) {
		if (innerSwitch) { // 内网服务，调用内网流媒体播流
			return this.playInner(dto, userId);
		} else {
			VideoVO vo = videoMapper.selectByDeviceId(dto.getId());
			// 根据摄像机类别判断播流请求接口，
			Integer cameraType = vo.getCameraType();
			if (cameraType != null && cameraType == 7) { // AI摄像机类型
				return playByAiot(dto, userId);
			} else {
				return playByVideoCloud(dto, userId);
			}
		}
	}

	/**
	 * @描述 视频云请求播流地址
	 * @return
	 */
	private Object playByVideoCloud(VideoPlayDTO dto, String userId) {
		String id = dto.getId();
		Integer type = dto.getType();
		if (type == null) {
			type = 0;
		}
		Map<String, Object> param = new HashMap<String, Object>();
		param.put("id", id);
		Integer duration = dto.getDuration();
		if (duration != null && duration > 0) {
			param.put("duration", duration);
		} else {
			param.put("duration", palyTime);
		}
		param.put("type", type);

		Object entity = postBody(videoCloudPath + "/api/play", param);
		if (entity == null) {
			return new ResponseVO(false);
		}
		HttpEntity httpEntity = (HttpEntity) entity;
		LinkedHashMap ret = (LinkedHashMap) httpEntity.getBody();
		Object codeObj = ret.get("code");
		int code = 0;
		if (codeObj != null) {
			code = (int) codeObj;
		}
		if (code == 1) {
			String m3u8Url = (String) ret.get("m3u8Url");// http://livepull.test.gxits.cn/live/45070200021320600006_low_test.m3u8?txSecret=3b8e34f481b0ee7d4c2002773126a592&txTime=63EB2E31
			String playId = (String) ret.get("playId");// http://livepull.test.gxits.cn/live/45070200021320600006_low_test.m3u8?txSecret=3b8e34f481b0ee7d4c2002773126a592&txTime=63EB2E31
			String videoPlayCount = stringRedisTemplate.opsForValue().get("video:play-" + type + "-count-" + id);
			if (videoPlayCount == null) {
				stringRedisTemplate.opsForValue().set("video:play-" + type + "-count-" + id, "1");
			} else {
				stringRedisTemplate.opsForValue().set("video:play-" + type + "-count-" + id,
						NumberUtils.toInt(videoPlayCount) + 1 + "");
			}
//			String playId = (String)ret.get("playId");
			stringRedisTemplate.opsForValue().set("video:play-" + type + "-" + id, System.currentTimeMillis() + "");
			stringRedisTemplate.opsForValue().set("video:playId-" + id + "-" + type + "-" + playId, "1");
			LOGGER.info("获取视频URL成功:{},{}", id, m3u8Url);
			// 入库或更新正在播放的视频
			PlayingVideoDTO playingVideoDTO = new PlayingVideoDTO();
			playingVideoDTO.setId(id);
			playingVideoDTO.setType(type);
			playingVideoDTO.setCreateTime(TimeUtils.getTimeString());
			int row = videoMapper.checkPlayingVideo(playingVideoDTO);
			if (row == 0) {
				row = videoMapper.addPlayingVideo(playingVideoDTO);
				LOGGER.info("有新的视频播放:{},{}", id, m3u8Url);
			} else {
				row = videoMapper.updatePlayingVideo(playingVideoDTO);
				LOGGER.info("更新视频播放时间:{},{}", id, m3u8Url);
			}

			// 接口调用次数统计，请求返回流成功才计算次数
			callCount(dto, userId);
		}
		return entity;
	}

	@Async("apiCountExecutor")
	private void callCount(VideoPlayDTO dto, String userId) {
		// 获取模块名称
		String moduleName = "未知";
		if (StringUtils.isNotBlank(dto.getModuleCode())) {
			moduleName = videoCallCountMapper.getNameByCode(dto.getModuleCode());
		}
		Date createTime = new Date();

		VideoCallCountDTO videoCallCountDTO = new VideoCallCountDTO();
		videoCallCountDTO.setModuleCode(dto.getModuleCode());
		videoCallCountDTO.setUserId(userId);
		videoCallCountDTO.setCreateTime(createTime);
		videoCallCountDTO.setCount(1);

		videoCallCountMapper.add(videoCallCountDTO);

		try {
			// Prometheus埋点
			moduleCounter.labels(moduleName).inc();
		} catch (Exception e) {
			LOGGER.error(e.getMessage(), e);
		}
	}

	public Object playForWeixin(VideoPlayDTO dto, String openid) {
		String unionid = stringRedisTemplate.opsForValue().get("weixin:unionnid-" + openid);
		return play(dto, unionid);
	}

	public Object stop(VideoPlayDTO dto) {
		String id = dto.getId();
		Integer type = dto.getType();
		String videoPlayCount = stringRedisTemplate.opsForValue().get("video:play-" + type + "-count-" + id);
		int playCount = NumberUtils.toInt(videoPlayCount);
		String playId = dto.getPlayId();
		if (playId != null) {
			stringRedisTemplate.delete("video:playId-" + id + "-" + type + "-" + playId);
		}
		if (videoPlayCount == null || playCount <= 1) {
			stringRedisTemplate.delete("video:play-" + type + "-" + id);
			stringRedisTemplate.delete("video:play-" + type + "-count-" + id);
			return stopPlay(dto);
		} else {
			LOGGER.info("值：{}", videoPlayCount);
			stringRedisTemplate.opsForValue().set("video:play-" + type + "-count-" + id, (playCount - 1) + "");
		}
		return new ResponseVO(true);
	}

	/**
	 * @描述 根据内外网要求，内网播流不需要发提流指令
	 * @param dto
	 * @return
	 */
	public Object stopPlay(VideoPlayDTO dto) {
		if (innerSwitch) { // 内网服务不用提流，页面停播后，由流媒体判断自动踢流
			return new ResponseVO("踢流成功", 1);
		} else {
			VideoVO vo = videoMapper.selectByDeviceId(dto.getId());
			Integer cameraType = vo.getCameraType();
			if (cameraType != null && cameraType == 7) { // AI摄像机类型
				return new ResponseVO("踢流成功", 1);
			} else {
				return stopVideoCloudPlay(dto);
			}
		}
	}

	public Object stopVideoCloudPlay(VideoPlayDTO dto) {
		String id = dto.getId();
		Integer type = dto.getType();
		Map<String, Object> param = new HashMap<String, Object>();
		param.put("deviceId", id);
		param.put("type", type);
		Object entity = postBody(videoCloudPath + "/api/stopPlay", param);
		LOGGER.info("entity" + entity.toString());
		if (entity != null) {
			HttpEntity httpEntity = (HttpEntity) entity;
			LinkedHashMap ret = (LinkedHashMap) httpEntity.getBody();
			Object codeObj = ret.get("code");
			int code = 0;
			if (codeObj != null) {
				code = (int) codeObj;
			}
			if (code == 1) {
				stringRedisTemplate.delete("video:play-" + type + "-count-" + id);
				stringRedisTemplate.delete("video:play-" + type + "-" + id);
				String pattern = "video:playId-" + id + "-" + type + "-";
				Set<String> keys = stringRedisTemplate.keys(pattern + "*");
				stringRedisTemplate.delete(keys);
				// 移除正在播放的视频
				videoMapper.deletePlayingVideo(dto);
			}
		}
		return entity;
	}

//	@SuppressWarnings("unchecked")
	public List<HistoryVideoVO> tape(VideoTapeDTO dto) {
		Date begin = TimeUtils.parseTime(dto.getDate() + " 00:00:00", TimeUtils.FULL_TIME);
		Date end = TimeUtils.parseTime(dto.getDate() + " 23:59:59", TimeUtils.FULL_TIME);

		// 检查redis中是否已有数据
		String key = dto.getCameraCode() + "_" + dto.getDate();
//		String cache = stringRedisTemplate.opsForValue().get(key);
//		if (StringUtils.isNotEmpty(cache)) {
//			// redis中有缓存数据
//			LOGGER.info("camera:{}在{}有缓存数据，直接返回", dto.getCameraCode(), dto.getDate());
//			// 反序列化成为list对象
//			List<HistoryVideoVO> resultList = JSONObject.parseArray(cache, HistoryVideoVO.class);
//			return resultList;
//		}

		Client client;
		LOGGER.info("camera:{}在{}没有缓存数据，将查询wsdl", dto.getCameraCode(), dto.getDate());
		String wsUrlStr = videoStorageService.source(dto.getCameraCode());
		String domain = videoStorageService.getDomainByCode(dto.getCameraCode());
		client = videoStorageService.createClientWithAuth(wsUrlStr, dto.getCameraCode());

		if (client == null) {
			LOGGER.warn("client初始化失败！将返回空的录像文件列表");
			return new ArrayList<>();
		}

		List<StorageFileInfo> list = new ArrayList<>();
		List<HistoryVideoVO> resultList = new ArrayList<>();
		try {
			URL wsUrl = new URL(wsUrlStr);

			// 第二次访问wsdl时需要手动修改地址为外网地址
			ConduitSelector conduitSelector = client.getConduitSelector();
			EndpointInfo epInfo = conduitSelector.getEndpoint().getEndpointInfo();
			epInfo.setAddress(wsUrlStr);
			Endpoint ep = new EndpointImpl(client.getBus(), conduitSelector.getEndpoint().getService(), epInfo);
			conduitSelector.setEndpoint(ep);

			// 访问获取录像列表的接口
			Object[] objArr = client.invoke("getRecordFileList", dto.getCameraCode(),
					TimeUtils.getXMLGregorianCalendar(begin), TimeUtils.getXMLGregorianCalendar(end));
			if (objArr != null && objArr.length > 0) {
				GetRecordFileListResponse response = (GetRecordFileListResponse) objArr[0];
				if (response != null && response.getReturn() != null) {
					list = response.getReturn();

					// 解析返回结果
					for (StorageFileInfo storageFileInfo : list) {
						// 处理播放地址，改为外网播放地址
						String playUrl = storageFileInfo.getPlayUrl();
						String internetPlayUrl = getInternetPlayUrl(playUrl, wsUrl, domain);
						storageFileInfo.setPlayUrl(internetPlayUrl);

						HistoryVideoVO historyVideoVO = new HistoryVideoVO(storageFileInfo);
						resultList.add(historyVideoVO);
					}

					// 将结果存储至redis
					String resultJSONStr = JSON.toJSONString(resultList);
					stringRedisTemplate.opsForValue().set(key, resultJSONStr, EXPIRE_TIME, TimeUnit.DAYS); // 默认缓存3天
				}
			}

		} catch (Exception e) {
			String error = e.getMessage();
			LOGGER.error("getRecordFileList:{}", error, e);
		}
		return resultList;
	}

	/**
	 * 根据内网的播放地址，拼接处理成外网可播放的地址
	 * @param playUrlInner 内网播放地址
	 * @return 外网播放地址
	 */
	private String getInternetPlayUrl(String playUrlInner, URL wsUrl, String domain) {
		String result = StringUtils.EMPTY;
		StringBuilder stringBuilder = new StringBuilder();

		try {
			// 获取播放url的文件路径
			URL innerPlayUrl = new URL(playUrlInner);
			String path = innerPlayUrl.getPath();
			String innerHost = innerPlayUrl.getHost();
			String query = innerPlayUrl.getQuery();

			// 处理转发层的host
			// String[] dispatchHostArr = dispatchHost.split("\\.");
			String host = domain;

			// 处理播放参数
			if (StringUtils.isEmpty(query)) {
				query = "meta=1";
			}
			if (!query.contains("meta=")) {
				query = query + "&meta=1";
			}

			// 处理内网的host
			String[] innerHostArr = innerHost.split("\\.");

			// 拼接最终的外网播放url
			stringBuilder.append(host);
			stringBuilder.append(playAddressPrefix); // 统一的转发前缀
			stringBuilder.append(path);
//            stringBuilder.append("?").append("n1=").append(dispatchHostArr[0]);
//            stringBuilder.append("&").append("n2=").append(dispatchHostArr[1]);
//            stringBuilder.append("&").append("n3=").append(dispatchHostArr[2]);
//            stringBuilder.append("&").append("n4=").append(dispatchHostArr[3]);
			stringBuilder.append("?").append("t1=").append(innerHostArr[0]);
			stringBuilder.append("&").append("t2=").append(innerHostArr[1]);
			stringBuilder.append("&").append("t3=").append(innerHostArr[2]);
			stringBuilder.append("&").append("t4=").append(innerHostArr[3]);
			stringBuilder.append("&").append(query);

		} catch (MalformedURLException e) {
			LOGGER.error(e.getMessage(), e);
			return result;
		} catch (Exception e) {
			LOGGER.error(e.getMessage(), e);
			return result;
		}

		result = stringBuilder.toString();
		// LOGGER.info("访问的根url为：{}", result);
		return result;
	}

	public VideoPingVO ping(VideoPingDTO dto) {
		boolean ping = IpAddressUtils.ping(dto.getIp());
		int status = ping ? 1 : 0;
		LOGGER.info("status:{}", status);
		if (dto.getStatus() != status) {
			dto.setStatus(status);
			videoMapper.updateStatus(dto);
		}
		VideoPingVO vo = new VideoPingVO();
		BeanUtils.copyProperties(dto, vo);
		return vo;
	}

	public ResponseVO switchTape() {
//        createClient(storageIpPort);
		return new ResponseVO(0);
	}

	/**
	 * @描述 区分内外网
	 * @param dto
	 * @return
	 */
	public Object ptz(VideoControlDTO dto) {
		String id = dto.getId();
		VideoVO vo = videoMapper.selectByDeviceId(id);
		if (vo == null) {
			return new ResponseVO("云控摄像机信息不存在！", 0);
		}
		Integer cameraType = vo.getCameraType();
		if (cameraType != null && cameraType == 7) { // AI摄像
			return new ResponseVO("AI摄像机暂时不支持云台控制", 0);
		}
		if (innerSwitch) { // 内网服务，调用内网流媒体播流
			PtzDTO ptzDTO = new PtzDTO();
			ptzDTO.setDevice_id(vo.getCameraCode());
			ptzDTO.setPtz_cmd(dto.getControlType());
			ptzDTO.setStep(dto.getStep());
			return this.innerPtz(ptzDTO);
		} else {
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("device_id", id);
			param.put("ptz_cmd", dto.getControlType());
			param.put("step", dto.getStep());
			return postBody(videoCloudPath + "/api/controlPtz", param);
		}

	}

	public List<VideoVO> selectByPosition(VideoPostionDTO dto) {
		return videoMapper.selectByPosition(dto);
	}

	/**
	 * 事件匹配视频
	 * 1. 服务区事件(服务区路况、服务区类型事件)
	 * 2. 收费站事件
	 * 3. 隧道事件（涉隧、隧道类型事件）
	 * 4. 其他事件
	 * @return 
	 */
	public List<VideoVO> matchByEvent(EventMatchDTO eventMatchDTO) {
		String eventId = eventMatchDTO.getEventId();
		String facilityNo = eventMatchDTO.getFacilityNo();
		// 判断是什么类型事件，（服务区、收费站、隧道、路面）
		FacilityType facilityType = specialFacilityType(facilityNo, eventMatchDTO.getEventThreeType(),
				FacilityType.ROAD);

		VideoPostionDTO videoPostionDTO = new VideoPostionDTO();
		videoPostionDTO.setRoadNo(eventMatchDTO.getRoadNo());
		videoPostionDTO.setLat(eventMatchDTO.getLat());
		videoPostionDTO.setLon(eventMatchDTO.getLon());
		List<VideoVO> matchedVideos = null;

		// 具体的事件类型匹配视频
		switch (facilityType) {
		case SA:
			LOGGER.info("SA 服务区事件");
			matchedVideos = saEventMatch(facilityNo, videoPostionDTO, eventMatchDTO);
			break;
		case STATION:
			LOGGER.info("STATION 收费站事件");
			matchedVideos = stationEventMatch(facilityNo, videoPostionDTO, eventMatchDTO);
			break;
		case TUNNEL:
			LOGGER.info("TUNNEL 隧道事件");
			matchedVideos = tunnelEventMatch(facilityNo, videoPostionDTO, eventMatchDTO);
			break;
		default:
			LOGGER.info("ROAD 路面事件");
			matchedVideos = roadEventMatch(facilityNo, videoPostionDTO, eventMatchDTO);
		}

		// 查询该事件锁定的视频，若果有，放到返回列表的第一位置
		VideoVO lockVideoVO = null;
		if (StringUtils.isNotBlank(eventId)) {
			String cameraId = videoMapper.selectCameraIdByEventId(eventId);
			if (StringUtils.isNotBlank(cameraId)) {
				lockVideoVO = videoMapper.selectByDeviceId(cameraId);
			}
		}
		if (lockVideoVO != null) {
			matchedVideos.add(0, lockVideoVO);
		}
		return matchedVideos;
	}

	/**
	 * @描述 服务区事件匹配<br>
	 * 第一步：facilityNo为空时，查询最近的与事件同方向的服务区设施，赋值到facilityNo;<br>
	 * 第二步：facilityNo不为空时，查询该facilityNo下经纬度不为空的摄像机；否则返回空列表 
	 */
	private List<VideoVO> saEventMatch(String facilityNo, VideoPostionDTO videoPostionDTO,
			EventMatchDTO eventMatchDTO) {
		return matchFaciltyVideo(FacilityType.SA, facilityNo, videoPostionDTO, eventMatchDTO);
	}

	private List<VideoVO> stationEventMatch(String facilityNo, VideoPostionDTO videoPostionDTO,
			EventMatchDTO eventMatchDTO) {
		return matchFaciltyVideo(FacilityType.STATION, facilityNo, videoPostionDTO, eventMatchDTO);
	}

	/**
	 * @描述 路面事件，匹配：2公里内的路面视频、涉及的隧道（没有，则最近的隧道）内事件同方向视频和洞口视频
	 */
	private List<VideoVO> tunnelEventMatch(String facilityNo, VideoPostionDTO videoPostionDTO,
			EventMatchDTO eventMatchDTO) {
		List<VideoVO> facilityVideos = new ArrayList<>();
		List<VideoVO> roadVideos = videoMapper.matchRoadVideo(eventMatchDTO);// 匹配2公里路面视频
		facilityVideos = matchFaciltyVideo(FacilityType.TUNNEL, facilityNo, videoPostionDTO, eventMatchDTO);
		facilityVideos.addAll(roadVideos);
		facilityVideos.sort(Comparator.comparing(videoDevice -> videoDevice.getDistance()));
		return facilityVideos;
	}

	/**
	 * @描述 路面事件，匹配：2公里内的路面视频、2公里收费站广场视频、2公里内隧道内事件同方向视频、2公里内隧道洞口视频
	 */
	private List<VideoVO> roadEventMatch(String facilityNo, VideoPostionDTO videoPostionDTO,
			EventMatchDTO eventMatchDTO) {
		List<VideoVO> facilityVideos = new ArrayList<>();
		List<VideoVO> roadEventVideos = videoMapper.matchRoadEventVideo(eventMatchDTO);
		// 过滤掉2公里的隧道内不同方向视频
		roadEventVideos = roadEventVideos.stream()
				.filter(item -> ((eventMatchDTO.getDirectionNo().equals(item.getDirectionNo())
						&& TUNNEL_1_POSITION.equals(item.getDevicePositionId())) // 隧道内同方向视频
						|| TUNNEL_2_POSITION.equals(item.getDevicePositionId()) // 隧道洞口视频
						|| (item.getFacilityTypeNo() != FacilityType.TUNNEL.value()) // 不是隧道的视频
				)).collect(Collectors.toList());
		eventMatchDTO.setFacilityTypeNo(FacilityType.STATION.value());
		eventMatchDTO.setDirectionNo(null);
		eventMatchDTO.setFacilityNo(null);
		facilityVideos = videoMapper.matchFaciltyVideo(eventMatchDTO); // 2公里收费站的视频
		facilityVideos = facilityVideos.stream().filter(item -> TOLL_PLAZA_POSITION.equals(item.getDevicePositionId())) // 收费站广场视频
				.collect(Collectors.toList());
		facilityVideos.addAll(roadEventVideos);
		facilityVideos.sort(Comparator.comparing(videoDevice -> videoDevice.getDistance()));
		return facilityVideos;
	}

	/**
	 * @描述 匹配设施下满足要求的视频
	 */
	private List<VideoVO> matchFaciltyVideo(FacilityType facilityType, String facilityNo,
			VideoPostionDTO videoPostionDTO, EventMatchDTO eventMatchDTO) {
		if (StringUtils.isBlank(facilityNo)) {
			facilityNo = selectNearestFacility(facilityType, videoPostionDTO, eventMatchDTO.getDirectionNo());
		}
		if (StringUtils.isBlank(facilityNo)) {
			return Collections.emptyList();
		}
		eventMatchDTO.setFacilityTypeNo(facilityType.value());
		if (facilityType == FacilityType.STATION) {// 收费站事件，匹配视频不区分方向
			eventMatchDTO.setDirectionNo(null);
		}
		eventMatchDTO.setKm(null);
		eventMatchDTO.setFacilityNo(facilityNo);
		List<VideoVO> faciltyVideo = videoMapper.matchFaciltyVideo(eventMatchDTO);

		if (facilityType == FacilityType.TUNNEL) {// 如果是隧道类型事件，需要额外匹配隧道洞口的视频
			eventMatchDTO.setDirectionNo(null);
			List<VideoVO> tunnelFaciltyVideos = videoMapper.matchFaciltyVideo(eventMatchDTO);
			tunnelFaciltyVideos = tunnelFaciltyVideos.stream()
					.filter(item -> (TUNNEL_2_POSITION.equals(item.getDevicePositionId())))
					.collect(Collectors.toList());
			faciltyVideo.addAll(0, tunnelFaciltyVideos);
		}
		return faciltyVideo;
	}

	/** 附近最近的facilityType类型的设施 **/
	private String selectNearestFacility(FacilityType facilityType, VideoPostionDTO videoPostionDTO,
			String directionNo) {
		videoPostionDTO.setFacilityTypeNo(facilityType.value());
		if (facilityType == FacilityType.SA) {
			videoPostionDTO.setDirectionNo(directionNo);
		} else {
			videoPostionDTO.setDirectionNo(null);
		}
		return videoMapper.selectFacilityByLatLon(videoPostionDTO);
	}

	/**
	 * @description 优先事件三级类型匹配，匹配不上再使用事件涉及的facilityNo确定类型
	 * @param facilityNo 设施编号
	 * @param eventThreeType 事件三级类型
	 * @param facilityType 默认类型
	 * @return 设施类型
	 */
	private FacilityType specialFacilityType(String facilityNo, int eventThreeType, FacilityType facilityType) {
		if (facilityNo != null) {
			Integer facilityTypeNo = videoMapper.selectFacilityTypeNo(facilityNo);
			if (facilityTypeNo != null) {
				facilityType = FacilityType.findByValue(facilityTypeNo);
			}
		}
		facilityType = specialFacilityType(eventThreeType, facilityType);
		if (facilityType == null) {
			facilityType = FacilityType.ROAD;
		}
		return facilityType;
	}

	/**
	 * @param eventThreeType 事件三级类型
	 * @param defaultType 默认类型
	 * @return 不是特定的事件三级类型，返回默认类型
	 */
	private FacilityType specialFacilityType(int eventThreeType, FacilityType defaultType) {
		if (eventThreeType == 34 || eventThreeType == 78) {
			return FacilityType.SA;
		}
		if (eventThreeType == 51 || eventThreeType == 77) {
			return FacilityType.TUNNEL;
		}
		if (eventThreeType == 43 || eventThreeType == 97) {
			return FacilityType.STATION;
		}
		return defaultType;
	}

	/*
	 * 根据新需要，拆分过滤摄像机查找方法 告警类型、事件类型
	 */
	public List<VideoVO> selectByLat(VideoPostionDTO videoPostionDTO) {
		/*
		 * Integer eventType = videoPostionDTO.getEventType(); Integer alarmType =
		 * videoPostionDTO.getAlarmType(); if (eventType == null && alarmType == null) {
		 * throw new ArgumentException("参数有问题"); }
		 */
		/** 查询2公里内，符合条件的摄像机 */
		List<VideoVO> videoList = videoMapper.selectByLat(videoPostionDTO);

		/** 在videoList基础上，根据事件类型或告警类型，筛选或者添加其他符合特定条件的 */
		List<VideoVO> list = this.filterList(videoPostionDTO, videoList);

		// 如传参有deviceId并且该设备是摄像机，则优先匹配该设备视频
		if (StringUtils.isNotBlank(videoPostionDTO.getDeviceId())) {
			VideoVO vo = videoMapper.selectByDeviceId(videoPostionDTO.getDeviceId());
			if (vo != null && StringUtils.isNoneBlank(vo.getDeviceId()) && !CollectionUtils.isEmpty(list)) {
				Iterator<VideoVO> it = list.iterator();// 删除重复
				while (it.hasNext()) {
					VideoVO v = it.next();
					if (v.getDeviceId().equals(vo.getDeviceId())) {
						it.remove();
						break;
					}
				}
				list.add(0, vo);// 添加记录至首位
			}
		}
		// 统一做去重操作，基于deviceId
		Set<String> deviceSet = new HashSet<>();
		Iterator<VideoVO> iterator = list.iterator();
		while (iterator.hasNext()) {
			VideoVO vo = iterator.next();
			if (!deviceSet.contains(vo.getDeviceId())) {
				deviceSet.add(vo.getDeviceId());
				continue;
			}
			iterator.remove();
		}
		return list;
	}

	/**
	 * 根据条件过滤摄像机
	 * 应急救援事件（服务区、桥梁、隧道、路面事件）
	 * 告警（电力监控、视频事件检测、隧道）
	 * 1、变电房设备，只包括变电房摄像机
	 * 2、服务区，包括同方向服务区和路面，服务区为先
	 * 3、桥梁，包括桥梁和路面，桥梁为先
	 * 4、隧道，包括同方向隧道摄像机以及路面摄像机（区分消防水泵、手报烟感分两种情况）
	 * @param list
	 * @param videoPostionDTO
	 * @return
	 */
	private List<VideoVO> filterList(VideoPostionDTO videoPostionDTO, List<VideoVO> videoList) {
		String facilityNo = videoPostionDTO.getFacilityNo();// 路况类型（设施编号）
		Integer roadNo = videoPostionDTO.getRoadNo(); // 路段
		Integer mpValue = videoPostionDTO.getMpValue();// 桩号值
		Integer facilityTypeNo = 0;
		if (StringUtils.isNotBlank(facilityNo)) {
			facilityTypeNo = videoMapper.selectFacilityTypeNo(facilityNo);// 根据设施编号获取当前路况类型的设施类型
		}
		videoPostionDTO.setFacilityTypeNo(facilityTypeNo);
		Integer eventFlag = videoPostionDTO.getEventFlag();

		VideoEventDTO videoEventDTO = new VideoEventDTO();// 构造事件分类摄像机查询条件
		videoEventDTO.setEventFlag(eventFlag);
		videoEventDTO.setFacilityTypeNo(facilityTypeNo);
		videoEventDTO.setRoadNo(roadNo);
		videoEventDTO.setMpValue(mpValue == null ? 0 : mpValue);
		videoEventDTO.setGisFlag(videoPostionDTO.getGisFlag());
		videoEventDTO.setFacilityNo(facilityNo);

		Integer eventType = videoPostionDTO.getEventType();
		Integer alarmType = videoPostionDTO.getAlarmType();
		String eventId = videoPostionDTO.getEventId();
		VideoVO lockVideoVO = null;
		if (StringUtils.isNotBlank(eventId)) {
			String cameraId = videoMapper.selectCameraIdByEventId(eventId);
			if (StringUtils.isNotBlank(cameraId)) {
				lockVideoVO = videoMapper.selectByDeviceId(cameraId);
			}
		}
		Integer customType = null;// 自定义的过滤类型（事件或告警类型）
		if (eventFlag != null && eventFlag == 1) {
			customType = this.getEventFilterType(eventType, facilityTypeNo);
			if (customType != null) {
				videoList = customEventTypeFilterVideo(customType, videoPostionDTO, videoEventDTO, videoList);
				if (lockVideoVO != null) {
					videoList.add(0, lockVideoVO);
				}
				return videoList;
			}
		} else {
			customType = getAlarmFilterType(alarmType);
			if (customType != null) {
				return customAlarmTypeFilterVideo(customType, videoPostionDTO, videoList);
			}
		}
		videoList = otherTypeFilterVideo(videoPostionDTO, videoEventDTO, videoList);
		if (lockVideoVO != null) {
			videoList.add(0, lockVideoVO);
		}
		return videoList;
	}

	private List<VideoVO> customEventTypeFilterVideo(Integer customType, VideoPostionDTO videoPostionDTO,
			VideoEventDTO videoEventDTO, List<VideoVO> videoList) {
		String directionNo = videoPostionDTO.getDirectionNo();
		Integer facilityTypeNo = videoPostionDTO.getFacilityTypeNo();
		String facilityNo = videoPostionDTO.getFacilityNo();
		switch (customType) {
		case 21:// 7-事件服务区类型
			List<VideoVO> saList = videoMapper.selectByVideoEvent(videoEventDTO);// 优先获取服务区视频
			if (saList.size() > 0) {
				saList = this.getFacilitySaEventVideo(saList, directionNo);
				saList.sort(Comparator.comparing(videoDevice -> videoDevice.getSort()));
				videoList.addAll(0, saList);
			}
			break;
		case 22:// 桥梁
			if (facilityTypeNo == 0) {
				videoEventDTO.setFacilityTypeNo(FacilityType.BRIDGE.value());
			}
			List<VideoVO> bridgeList = videoMapper.selectByVideoEvent(videoEventDTO);// 优先获取桥梁
			if (bridgeList.size() > 0) {
				if (bridgeList.size() < 6) {
					List<VideoVO> list3 = videoList.stream()
							.filter(e -> e.getFacilityTypeNo() == FacilityType.BRIDGE.value())
							.collect(Collectors.toList());
					videoList = this.getSortList(list3, videoList);
					videoList = this.getSortList(bridgeList, videoList);
				} else {
					videoList.clear();
					videoList.addAll(bridgeList);
				}
			} else {
				List<VideoVO> list3 = videoList.stream()
						.filter(e -> e.getFacilityTypeNo() == FacilityType.BRIDGE.value()).collect(Collectors.toList());
				videoList = this.getSortList(list3, videoList);
			}

			break;
		case 23:// 隧道，排除变电房摄像机
			if (facilityTypeNo == 0) {
				videoEventDTO.setFacilityTypeNo(FacilityType.TUNNEL.value());
			}
			List<VideoVO> tunnelList = videoMapper.selectByVideoEvent(videoEventDTO);// 优先获取隧道视频
			if (tunnelList.size() > 0) {
				if (StringUtils.isBlank(facilityNo)) {
					String facilityNo_1 = tunnelList.get(0).getFacilityNo();
					tunnelList = tunnelList.stream().filter(e -> e.getFacilityNo().equals(facilityNo_1))
							.collect(Collectors.toList());
				}
				tunnelList = this.getTunnelVideoList(tunnelList, directionNo, 1);
				if (tunnelList.size() < 6) {
					videoList = this.getTunnelVideoList(videoList, directionNo, 1);
					videoList = this.getSortList(tunnelList, videoList);
				} else {
					videoList.clear();
					videoList.addAll(tunnelList);
				}

			} else {
				videoList = this.getTunnelVideoList(videoList, directionNo, 1);
			}
			break;
		case 24: // 处理收费站的事件
			List<VideoVO> stationList = handleStationDevice(videoPostionDTO, videoEventDTO, true);
			videoList.addAll(0, stationList);
			break;
		}
		return videoList;
	}

	private List<VideoVO> customAlarmTypeFilterVideo(Integer customType, VideoPostionDTO videoPostionDTO,
			List<VideoVO> videoList) {
		String directionNo = videoPostionDTO.getDirectionNo();
		Integer roadNo = videoPostionDTO.getRoadNo();
		Integer mpValue = videoPostionDTO.getMpValue();
		switch (customType) {
		case 11:// 44-变电房
			videoList = videoList.stream().filter(e -> e.getFacilityTypeNo() == FacilityType.POWER.value())
					.collect(Collectors.toList());
			break;
		case 12:// 12-普通隧道内设备报警
			videoList = this.getTunnelVideoList(videoList, directionNo, 1);
			break;
		case 1203:// 隧道手报、烟感
			Integer checkFlag = 0;
			if (roadNo != null && mpValue != null && StringUtils.isNotBlank(directionNo)) {
				String directionName = videoMapper.selectDirectionName(directionNo);// 设备方向名称
				if (checkMpValueIsTunnel(roadNo, mpValue, directionName)) {
					checkFlag = 1;
				}
			}
			videoList = this.getTunnelVideoList(videoList, directionNo, checkFlag);
			break;
		case 1214:// 隧道消防水泵
			videoList = this.getTunnelWaterVideoList(videoList, videoPostionDTO.getDeviceId(), directionNo);
			break;
		case 13:// 告警服务区类型
			videoList = this.getFacilitySaVideoList(videoList, directionNo);
			break;
		}
		return videoList;
	}

	/**
	 * @描述 常规通用匹配
	 */
	private List<VideoVO> otherTypeFilterVideo(VideoPostionDTO videoPostionDTO, VideoEventDTO videoEventDTO,
			List<VideoVO> videoList) {
		Integer mpValue = videoPostionDTO.getMpValue();
		Integer roadNo = videoPostionDTO.getRoadNo();
		String directionNo = videoPostionDTO.getDirectionNo();// 方向
		// 其他告警、事件查询隧道摄像机，过滤不同方向左右洞摄像机
		if (roadNo != null && mpValue != null && StringUtils.isNotBlank(directionNo)) {
			String directionName = videoMapper.selectDirectionName(directionNo);// 设备方向名称
			// 对于告警或者事件，涉及隧道的都统一处理
			videoList = handleTunnelDevice(videoList, roadNo, mpValue, directionName);
			Integer eventType = videoPostionDTO.getEventType();
			boolean isEvent = eventType != null && eventType > 0;
			if (!isEvent) {
				return videoList;
			}
			// 属于事件：处理普通路面，摄像机数据（排除服务区）
			videoList = videoList.stream().filter(item -> FacilityType.SA.value() != item.getFacilityTypeNo())
					.collect(Collectors.toList());
			// （排除2公里内的非内外广场的收费站视频）
			videoList = videoList.stream()
					.filter(item -> (FacilityType.STATION.value() != item.getFacilityTypeNo()
							|| (FacilityType.STATION.value() == item.getFacilityTypeNo()
									&& TOLL_PLAZA_POSITION.equals(item.getDevicePositionId()))))
					.collect(Collectors.toList());

			// 涉及隧道，额外处理隧道内的事件相关设备，去掉机房、水池、柜房
			final String upDirection = "上行";
			final String downDirection = "下行";
			Iterator<VideoVO> iterator = videoList.iterator();
			while (iterator.hasNext()) {
				VideoVO next = iterator.next();
				Integer typeNo = next.getFacilityTypeNo();
				String deviceName = next.getDeviceName();
				String nextDirectionName = next.getDirectionName();
				if (typeNo == null) {
					continue;
				}
				if (FacilityType.TUNNEL.value() == typeNo) {
					// 根据上行、下行方向过滤
					String curDirection = directionName.contains(upDirection) ? upDirection : downDirection;
					if (StringUtils.isNotEmpty(nextDirectionName) && !nextDirectionName.contains(curDirection)) {
						iterator.remove();
						continue;
					}
					// 去掉机房、水池、柜房
					if (StringUtils.isNotEmpty(deviceName)
							&& (deviceName.contains("机房") || deviceName.contains("水池") || deviceName.contains("柜房"))) {
						iterator.remove();
					}
				}

			}
		}
		return videoList;
	}

	private List<VideoVO> handleTunnelDevice(List<VideoVO> list, Integer roadNo, Integer mpValue,
			String directionName) {
		if (checkMpValueIsTunnel(roadNo, mpValue, directionName)) {// 判断当前桩号是否在隧道范围内
			list = list.stream().filter(e -> e.getFacilityTypeNo() != FacilityType.POWER.value())
					.collect(Collectors.toList());
			if (directionName.contains("上行")) { // 上线删除下行摄像机
				list = list.stream().filter(e -> !e.getDirectionName().contains("下行")).collect(Collectors.toList());
			}
			if (directionName.contains("下行")) {// 下行删除上行摄像机
				list = list.stream().filter(e -> !e.getDirectionName().contains("上行")).collect(Collectors.toList());
			}
		}
		return list;
	}

	private List<VideoVO> handleStationDevice(VideoPostionDTO dto, VideoEventDTO eventDto, boolean isStation) {
		// 如果是收费站类型，设置成收费站(todo 收费站暂时只配置了内外广场的数据)
		if (isStation) {
			eventDto.setFacilityTypeNo(FacilityType.STATION.value());
			dto.setFacilityTypeNo(eventDto.getFacilityTypeNo());
			String facilityNo = videoMapper.selectFacilityByLatLon(dto);
			if (StringUtils.isEmpty(facilityNo)) {
				return new ArrayList<>();
			}
			eventDto.setFacilityNo(facilityNo);
			List<VideoVO> stationList = videoMapper.selectByVideoEvent(eventDto);
			if (CollectionUtils.isEmpty(stationList)) {
				return new ArrayList<>();
			}
			stationList.sort(Comparator.comparing(videoDevice -> videoDevice.getSort()));
			return stationList;
		}
		return new ArrayList<>();
	}

	/**
	 * @描述 服务区事件使用
	 * @param list
	 * @param directionNo
	 * @return
	 */
	private List<VideoVO> getFacilitySaEventVideo(List<VideoVO> list, String directionNo) {
		if (StringUtils.isNotBlank(directionNo)) {// 方向不为空，获取属于服务区以及行车方向相同的视频
			list = list.stream().filter(
					e -> e.getFacilityTypeNo() == FacilityType.SA.value() && e.getDirectionNo().equals(directionNo))
					.collect(Collectors.toList());
		}
		return list;
	}

	/**
	 * @描述 服务区告警使用
	 * @param list
	 * @param directionNo
	 * @return
	 */
	private List<VideoVO> getFacilitySaVideoList(List<VideoVO> list, String directionNo) {
		if (StringUtils.isNotBlank(directionNo)) {// 方向不为空，获取属于服务区以及行车方向相同的视频
			List<VideoVO> list1 = list.stream().filter(
					e -> e.getFacilityTypeNo() == FacilityType.SA.value() && e.getDirectionNo().equals(directionNo))
					.collect(Collectors.toList());
			List<String> ids = list.stream().filter(// 排查其他服务区行车方向不同的视频
					e -> e.getFacilityTypeNo() == FacilityType.SA.value() && !e.getDirectionNo().equals(directionNo))
					.map(b -> b.getDeviceId()).collect(Collectors.toList());
			List<VideoVO> addList = list.stream().filter(a -> !ids.contains(a.getDeviceId()))
					.collect(Collectors.toList());
			list = this.getSortList(list1, addList);
		}
		return list;
	}

	/**
	 * 消防水泵报警处理
	 * @param list
	 * @param deviceId
	 * @param directionNo
	 * @return
	 */
	private List<VideoVO> getTunnelWaterVideoList(List<VideoVO> list, String deviceId, String directionNo) {
		List<VideoVO> list_tunnel = this.getTunnelVideoList(list, directionNo, 1);
		List<VideoVO> list_water = new ArrayList<>();
		if (StringUtils.isNotBlank(deviceId)) { // 根据报警设备id，查询对应的高低位水池摄像机
			String facilityNo2 = videoMapper.selectFacilityNo(deviceId);
			if (StringUtils.isNotBlank(facilityNo2)) {
				list_water = videoMapper.selectWaterCamera(facilityNo2);
			}
		}
		return getSortList(list_water, list_tunnel);
	}

	/**
	 * 判断当前路段桩号是否在隧道内
	 * @param roadNo
	 * @param mpValue
	 * @param directionNo
	 * @return
	 */
	private boolean checkMpValueIsTunnel(Integer roadNo, Integer mpValue, String directionName) {
		boolean flag = false;
		List<TunnelMilePostsVO> tunnelList = videoMapper.selectTunnelMilePostsList(roadNo);
		if (tunnelList.size() > 0) {// 判断当前桩号是否在隧道范围内
			if (StringUtils.isNotBlank(directionName)) {
				for (TunnelMilePostsVO vo : tunnelList) {
					Integer start = 0, end = 0;
					String startMilePostRight = vo.getStartMilePostRight();
					String endMilePostRight = vo.getEndMilePostRight();
					String startMilePost = vo.getStartMilePost();
					String endMilePost = vo.getEndMilePost();
					if (directionName.contains("上行")) {// 右洞上线
						start = StringUtils.isNotBlank(startMilePostRight)
								? MilePostUtils.pileno2IntValue(startMilePostRight)
								: 0;
						end = StringUtils.isNotBlank(endMilePostRight) ? MilePostUtils.pileno2IntValue(endMilePostRight)
								: 0;
					}
					if (directionName.contains("下行")) {// 左洞下行
						start = StringUtils.isNotBlank(startMilePost) ? MilePostUtils.pileno2IntValue(startMilePost)
								: 0;
						end = StringUtils.isNotBlank(endMilePost) ? MilePostUtils.pileno2IntValue(endMilePost) : 0;
					}
					if (mpValue >= start && mpValue <= end) { // 判断当前桩号是否在隧道起始桩号范围内
						flag = true;
						break;
					}
				}
			}
		}
		return flag;
	}

	/**
	 *
	 * @param list
	 * @param directionNo
	 * @param inFlag :0-不在隧道内，1-在隧道内
	 * @return
	 */
	private List<VideoVO> getTunnelVideoList(List<VideoVO> list, String directionNo, Integer inFlag) {
		if (inFlag == 1) {// 在隧道内,排查隧道以及变电房摄像机
			list = list.stream().filter(e -> e.getFacilityTypeNo() != FacilityType.POWER.value())
					.collect(Collectors.toList());
		}
		if (StringUtils.isNotBlank(directionNo)) {
			String directionName = videoMapper.selectDirectionName(directionNo);// 设备方向名称
			if (list.size() > 0 && StringUtils.isNotBlank(directionName)) {
				if (directionName.contains("上行")) { // 上线删除下行摄像机
					list = list.stream().filter(e -> !e.getDirectionName().contains("下行")).collect(Collectors.toList());
				}
				if (directionName.contains("下行")) {// 下行删除上行摄像机
					list = list.stream().filter(e -> !e.getDirectionName().contains("上行")).collect(Collectors.toList());
				}
			}
		}
		return list;
	}

	// 集合去重后合并
	private List<VideoVO> getSortList(List<VideoVO> check_list, List<VideoVO> allList) {
		List<VideoVO> list = new ArrayList<>();
		if (check_list.size() > 0) {
			List<String> ids = check_list.stream().map(b -> b.getDeviceId()).collect(Collectors.toList());
			List<VideoVO> addList = allList.stream().filter(a -> !ids.contains(a.getDeviceId()))
					.collect(Collectors.toList());
			list.addAll(0, addList);
			list.addAll(0, check_list);
		} else {
			list.addAll(0, allList);
		}
		return list;
	}

	/*
	 * 告警类型判断
	 */
	private Integer getAlarmFilterType(Integer alarmType) {
		Integer filterType = null;// 不做处理
		if (alarmType == null) {
			return null;
		}
		AlarmTypeEnum type = AlarmTypeEnum.getName(alarmType);
		if (type == null) {
			return null;
		}
		if ("电力告警".equals(type.getPname())) {
			filterType = 11;// 电力监控
		}
		if ("隧道告警".equals(type.getPname())) { // 隧道告警，要区分消防水泵告警、
			switch (alarmType) {
			case 503:
				filterType = 1203;// 手报
				break;
			case 504:
				filterType = 1203;// 烟感
				break;
			case 514:
				filterType = 1214;// 消防水泵
				break;
			default:
				filterType = 12; // 普通隧道告警
				break;
			}

		}
		if ("服务区告警".equals(type.getPname())) { // 服务区告警
			filterType = 13;// 服务区

		}
		return filterType;
	}

	/**
	 * 先判断是否是告警类型，事件类型：事件类型和路况类型都不为空，以路况为准，如果路况为空，以事件类型为准
	 * @param evenType
	 * @param facilityTypeNo
	 * @return
	 */
	private Integer getEventFilterType(Integer evenType, Integer facilityTypeNo) {
		Integer filterType = null;
		if (evenType != null) {
			if (evenType == 34 || evenType == 78) { // 服务区类型
				filterType = 21;
			}
			if (evenType == 61) { // 桥梁类型
				filterType = 22;
			}
			if (evenType == 51 || evenType == 77) { // 隧道类型
				filterType = 23;
			}
			if (evenType == 43 || evenType == 97) { // 收费站类型
				filterType = 24;
			}
		}
		if (filterType == null && facilityTypeNo != null) {
			if (facilityTypeNo == FacilityType.SA.value()) { // 服务区类型
				filterType = 21;
			}
			if (facilityTypeNo == FacilityType.BRIDGE.value()) { // 桥梁类型
				filterType = 22;
			}
			if (facilityTypeNo == FacilityType.TUNNEL.value()) { // 隧道类型
				filterType = 23;
			}
		}
		return filterType;
	}

	/**
	 * 判断当前桩号是否在隧道范围内
	 * @param tunnelList
	 * @param directionName
	 * @return
	 */
	private boolean checkIsTunnel(List<TunnelMilePostsVO> tunnelList, Integer mpValue, String directionName) {
		boolean flag = false;
		for (TunnelMilePostsVO vo : tunnelList) {
			Integer start = 0, end = 0;
			String startMilePostRight = vo.getStartMilePostRight();
			String endMilePostRight = vo.getEndMilePostRight();
			String startMilePost = vo.getStartMilePost();
			String endMilePost = vo.getEndMilePost();
			if (directionName.contains("上行")) {// 右洞上线
				start = StringUtils.isNotBlank(startMilePostRight) ? MilePostUtils.pileno2IntValue(startMilePostRight)
						: 0;
				end = StringUtils.isNotBlank(endMilePostRight) ? MilePostUtils.pileno2IntValue(endMilePostRight) : 0;
			}
			if (directionName.contains("下行")) {// 左洞下行
				start = StringUtils.isNotBlank(startMilePost) ? MilePostUtils.pileno2IntValue(startMilePost) : 0;
				end = StringUtils.isNotBlank(endMilePost) ? MilePostUtils.pileno2IntValue(endMilePost) : 0;
			}
			if (mpValue >= start && mpValue <= end) { // 判断当前桩号是否在隧道起始桩号范围内
				flag = true;
				break;
			}

		}
		return flag;
	}

	public List<VideoVO> selectByMilePost(VideoMilePostDTO dto) {
		int start = NumberUtils.toInt(dto.getStartMilePost().replace("K", "").replace("+", ""));
		int end = start > 0 ? start : 0;
		if (dto.getEndMilePost() != null) {
			end = NumberUtils.toInt(dto.getEndMilePost().replace("K", "").replace("+", ""));
		}
		start = start - (dto.getKm() * 1000);
		end = end + dto.getKm() * 1000;
		if (start > end) {
			int tmp = end;
			end = start;
			start = tmp;
		}
		dto.setStart(start);
		dto.setEnd(end);
		return videoMapper.selectByMilePost(dto);
	}

	private String createToken(long timestamp) {
		String token = com.bt.itscore.utils.EncodeUtils.encode("SDAFHJSADHFKJWEUUEWRJDSFHJDSFESA" + timestamp, "MD5");
//		String token = com.bt.itscore.utils.EncodeUtils.encode(secretKey + timestamp, "MD5");
		LOGGER.info("timestamp:{},token:{}", timestamp, token);
		return token;
	}

	private Object postBody(String url, Map<String, Object> param) {
		long timestamp = System.currentTimeMillis();
		String token = createToken(timestamp);
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setContentType(MediaType.parseMediaType(MediaType.APPLICATION_JSON_VALUE));
		httpHeaders.add("token", token);
		httpHeaders.add("timestamp", "" + timestamp);
		HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(param, httpHeaders);
		Object o = null;
		try {
			o = restTemplate.postForEntity(url, httpEntity, Object.class);
		} catch (Exception e) {
			e.printStackTrace();
			LOGGER.info("success:{}", 0);
		}
		LOGGER.info("success:{}", 1);
		return o;
	}

	// 测试使用
	private Object post(String url, Map<String, Object> param) {
		long timestamp = System.currentTimeMillis();
		String token = createToken(timestamp);
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setContentType(MediaType.parseMediaType(MediaType.APPLICATION_JSON_VALUE));
		httpHeaders.add("token", token);
		httpHeaders.add("timestamp", "" + timestamp);
		HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(param, httpHeaders);
		ResponseEntity o = null;
		try {
			SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
			requestFactory.setConnectTimeout(30000);// 设置连接超时，单位毫秒
			requestFactory.setReadTimeout(30000); // 设置读取超时
			RestTemplate restTemplate = new RestTemplate();
			restTemplate.setRequestFactory(requestFactory);
			o = restTemplate.postForEntity(url, httpEntity, Object.class);
			HttpStatus statusCode = o.getStatusCode();
			if (statusCode == HttpStatus.OK) {
				Object body = o.getBody();
				LinkedHashMap ret = (LinkedHashMap) body;
				int code = (int) ret.get("code");
				if (code == 1) {
					String m3u8Url = (String) ret.get("m3u8Url");// http://livepull.test.gxits.cn/live/45070200021320600006_low_test.m3u8?txSecret=3b8e34f481b0ee7d4c2002773126a592&txTime=63EB2E31
					String playId = (String) ret.get("playId");// playId=2CDA2A1952C84B38A17F0CBC5ED6881A
					System.out.println("成功");
				}
				System.out.println(body);
			}

		} catch (Exception e) {
			e.printStackTrace();
			LOGGER.info("success:{}", 0);
		}
		LOGGER.info("success:{}", 1);

		return o;
	}

	public static void main2(String[] args) {
		Map<String, Object> param = new HashMap<String, Object>();
		param.put("id", "057658d8-f0c7-48f9-8bb4-35a7b363432d");
		Integer duration = 1;
		if (duration != null && duration > 0) {
			param.put("duration", duration);
		}
		param.put("type", 0);
		new VideoService().post("http://************:8085/video-cloud" + "/api/play", param);
	}

	public Object add(VideoDeviceDTO dto) {
		Map<String, Object> param = new HashMap<String, Object>();
		param.put("id", dto.getId());
		param.put("gbId", dto.getGbId());
		param.put("deviceName", dto.getDeviceName());
		param.put("sourceId", dto.getSourceId());
		param.put("isPtz", dto.getIsPtz());
		param.put("ptzMode", dto.getPtzMode());
		param.put("ip", dto.getIp());
		param.put("rtspUrl", dto.getRtspUrl());
		param.put("rtspUrlHd", dto.getRtspUrlHd());
		param.put("username", dto.getUsername());
		param.put("password", dto.getPassword());
		return postBody(videoCloudPath + "/api/addDevice", param);
	}

	public Object update(VideoDeviceDTO dto) {
		Map<String, Object> param = new HashMap<String, Object>();
		param.put("id", dto.getId());
		param.put("gbId", dto.getGbId());
		param.put("newGbId", dto.getNewGbId());
		param.put("deviceName", dto.getDeviceName());
		param.put("sourceId", dto.getSourceId());
		param.put("isPtz", dto.getIsPtz());
		param.put("ptzMode", dto.getPtzMode());
		param.put("ip", dto.getIp());
		param.put("rtspUrl", dto.getRtspUrl());
		param.put("rtspUrlHd", dto.getRtspUrlHd());
		param.put("username", dto.getUsername());
		param.put("password", dto.getPassword());
		return postBody(videoCloudPath + "/api/updateDevice", param);
	}

	public Object delete(IdStringDTO dto) {
		Map<String, Object> param = new HashMap<String, Object>();
		param.put("id", dto.getId());
		return postBody(videoCloudPath + "/api/deleteDeivce", param);
	}

	public Object batchDelete(IdStringBatchDTO dto) {
		Map<String, Object> param = new HashMap<String, Object>();
		param.put("ids", dto.getIds());
		return postBody(videoCloudPath + "/api/batchDeleteDevice", param);
	}

	/**
	 * 调用视频云同步数据接口
	 * @return
	 */
	public void videoCloudSync(List<VideoDeviceDTO> deviceList) {
		if (deviceList.size() > 0) {
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("deviceList", deviceList);
			LOGGER.info("调用视频云同步接口：" + videoCloudPath + "/api/deviceSync");
			LOGGER.info("数据量长度：" + deviceList.size());
			postBody(videoCloudPath + "/api/deviceSync", param);
		} else {
			LOGGER.info("当前同步视频云数据不能为空！");
			throw new FailException("当前同步视频云数据不能为空！");
		}
	}

	public boolean addVideoDir(VideoDirDTO dto) {
		int ret = videoMapper.checkVideoDir(dto);
		if (ret > 0) {
			throw new FailException("该目录名称已存在，请重新编辑。");
		}
		dto.setId(UUID.randomUUID().toString());
		dto.setCreateTime(System.currentTimeMillis() / 1000);
		return videoMapper.addVideoDir(dto) > 0;
	}

	public boolean updateVideoDir(VideoDirDTO dto) {
		if (StringUtils.isBlank(dto.getId())) {
			throw new ArgumentException("id不能为空");
		}
		int ret = videoMapper.checkVideoDir(dto);
		if (ret > 0) {
			throw new FailException("该目录名称已存在，请重新编辑。");
		}
		return videoMapper.updateVideoDir(dto) > 0;
	}

	public boolean deleteVideoDir(IdStringDTO dto) {
		// 该目录下是否存在视频预案
		List<VideoPlanVO> vos = videoMapper.selectVideoPlanByVideoDir(dto);
		if (vos.size() > 0) {
			throw new ArgumentException("该目录下存在视频预案，请先删除预案。");
		}
		return videoMapper.deleteVideoDir(dto) > 0;
	}

	public boolean addVideoPlan(VideoPlanDTO dto) {
		// 当前目录下是否已存在该预案videoDirId和planName
		int count = videoMapper.checkVideoPlan(dto);
		if (count > 0) {
			throw new ArgumentException("当前目录下已存在该预案。");
		}
		dto.setId(UUID.randomUUID().toString());
		dto.setCreateTime(System.currentTimeMillis() / 1000);
		return videoMapper.addVideoPlan(dto) > 0;
	}

	public boolean updateVideoPlanName(VideoPlanDTO dto) {
		if (StringUtils.isBlank(dto.getId())) {
			throw new ArgumentException("预案ID不能为空。");
		}
		// 当前目录下是否已存在该预案videoDirId和planName
		int count = videoMapper.checkVideoPlan(dto);
		if (count > 0) {
			throw new ArgumentException("当前目录下已存在该预案名称。");
		}
		return videoMapper.updateVideoPlanName(dto) > 0;
	}

	public boolean deleteVideoPlan(IdStringDTO dto) {
		return videoMapper.deleteVideoPlan(dto) > 0;
	}

	@Transactional
	public boolean saveVideoPlanDiv(VideoPlanDivListDTO dto) {
		int ret = videoMapper.updateVideoPlanTime(dto);
		videoMapper.batchDeleteVideoPlanDiv(dto);
		if (!CollectionUtils.isEmpty(dto.getList())) {
			Long createTime = System.currentTimeMillis() / 1000;
			for (VideoPlanDivDTO tmp : dto.getList()) {
				tmp.setCreateTime(createTime);
			}
			ret = videoMapper.saveVideoPlanDiv(dto);
		}
		return ret > 0;
	}

	public List<VideoDirVO> selectVideoDirByOrgId(IdStringDTO dto) {
		return videoMapper.selectVideoDirByOrgId(dto);
	}

	public List<VideoPlanVO> selectVideoPlanByVideoDir(IdStringDTO dto) {
		List<VideoPlanVO> ret = videoMapper.selectVideoPlanByVideoDir(dto);
		for (VideoPlanVO vo : ret) {
			List<DeviceVO> devices = vo.getDevices();
			if (devices == null || devices.size() == 0) {
				vo.setOffline(0);
				vo.setTotal(0);
				continue;
			}
			vo.setTotal(devices.size());
			int offline = 0;
			for (DeviceVO tmp : devices) {
				if (tmp.getStatus() == null || tmp.getStatus() == 0) {
					offline++;
				}
			}
			vo.setOffline(offline);
		}
		return ret;
	}

	/**
	 * <>接收视频云的在线状态推送
	 */
	public boolean batchUpdateOnline(List<VideoOnlineDTO> list) {
		int size = list.size();
		LOGGER.info("视频云批量更新摄像机状态，更新数量：{}", size);
		// 每50个分组
		final int groupNum = 50;
		int toIndex = groupNum;
		Map<String, Object> map = new HashMap<String, Object>();
		int ret = 0;
		for (int i = 0; i < size; i += groupNum) {
			if (i + groupNum > size) {
				// 作用为toIndex最后没有50条数据则剩余几条newList中就装几条
				toIndex = size - i;
			}
			List<VideoOnlineDTO> subList = list.subList(i, i + toIndex);
			map.put("cameras", subList);
			ret += videoMapper.batchUpdateOnline(map);
		}
		LOGGER.info("视频云批量更新摄像机状态，更新成功数量：{}", ret);

		return ret > 0;
	}

	/**
	 * <>接收视频云请求、返回视频云需要的同步数据
	 */
	public Object getVideoList(VideoCloudDTO dto) {
		List<VideoDeviceVO> list = videoMapper.selectVideoDeviceList(dto);
		if (list.size() > 0) {
			list.stream().forEach(vo -> {
				String ptzProtocol = vo.getPtzProtocol();
				if (StringUtils.isNotBlank(ptzProtocol) && ptzProtocol.indexOf(":") > -1) {
					String[] ptz = ptzProtocol.split(":");
					vo.setPtzForwardIp(ptz[0]);
					vo.setPtzForwardPort(ptz[1]);
				}
			});
		}
		return list;
	}

	public List<VideoPlanDivVO> selectVideoPlanDiv(IdStringDTO dto) {
		return videoMapper.selectVideoPlanDiv(dto);
	}

	@SuppressWarnings("rawtypes")
	public Object updateStatusById(IdStringDTO dto) {
		if (innerSwitch) {
			return updateInnerStatusById(dto.getId());
		} else {
			VideoVO vo = videoMapper.selectByDeviceId(dto.getId());
			Integer cameraType = vo.getCameraType();
			if (cameraType != null && cameraType == 7) { // AI摄像机类型,直接返回数据库状态
				VideoStatusVO ret = new VideoStatusVO();
				ret.setId(vo.getDeviceId());
				ret.setGbId(vo.getCameraCode());
				ret.setIp(vo.getIp());
				ret.setStatus(vo.getStatus());
				return ret;
			} else {
				Map<String, Object> param = new HashMap<String, Object>();
				param.put("id", dto.getId());
				ResponseEntity retEntity = (ResponseEntity) postBody(videoCloudPath + "/api/selectById", param);
				LOGGER.info("retEntity.getBody():" + retEntity.getBody());
				Map ret = (Map) retEntity.getBody();
				if (ret == null) {
					throw new ArgumentException("视频云未找到相关设备，请联系系统运维人员添加！");
				}
				if (ret.get("status") != null) {
					VideoPingDTO dto2 = new VideoPingDTO();
					dto2.setId((String) ret.get("id"));
					dto2.setStatus((Integer) ret.get("status"));
					dto2.setOfflineTime((String) ret.get("offlineTime"));// 新增离线更新时间
					videoMapper.updateStatusById(dto2);
				}
				return ret;
			}

		}

	}

	public static class MyHTTPConduitConfigurer implements HTTPConduitConfigurer {
		private final String _username;

		private final String _password;

		public MyHTTPConduitConfigurer(final String username, final String password) {
			_username = username;
			_password = password;
		}

		@Override
		public void configure(final String name, final String address, final HTTPConduit conduit) {
			// 设置用户名密码鉴权
			final AuthorizationPolicy authorization = new AuthorizationPolicy();
			authorization.setUserName(_username);
			authorization.setPassword(_password);
			conduit.setAuthorization(authorization);
			final HttpAuthSupplier supplier = new DefaultBasicAuthSupplier();
			conduit.setAuthSupplier(supplier);

			// 设置http超时时间
			HTTPClientPolicy policy = new HTTPClientPolicy();
			policy.setConnectionTimeout(TIME_OUT_MILI);
			policy.setAllowChunking(false);
			policy.setReceiveTimeout(TIME_OUT_MILI);
			policy.setConnectionRequestTimeout(TIME_OUT_MILI);
			conduit.setClient(policy);

			// 忽略tls证书校验
			TLSClientParameters clientParameters = new TLSClientParameters();
			clientParameters.setHostnameVerifier(new HostnameVerifier() {
				@Override
				public boolean verify(String hostname, SSLSession session) {
					return true;
				}
			});
			clientParameters.setTrustManagers(new TrustManager[] { new X509TrustManager() {
				@Override
				public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
					// do nothing, you're the client
				}

				@Override
				public X509Certificate[] getAcceptedIssuers() {
					// also only relevant for servers
					return null;
				}

				@Override
				public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
					/*
					 * chain[chain.length -1] is the candidate for the root certificate. Look it up
					 * to see whether it's in your list. If not, ask the user for permission to add
					 * it. If not granted, reject. Validate the chain using CertPathValidator and
					 * your list of trusted roots.
					 */
				}
			} });
			clientParameters.setDisableCNCheck(true);
			conduit.setTlsClientParameters(clientParameters);
		}
	}

	public String download(VideoDownloadDTO dto) {
		VideoVO vo = videoMapper.selectByDeviceId(dto.getDeviceId());
		if (vo == null || StringUtils.isBlank(vo.getCameraCode()) || vo.getSourceId() == null) {
			throw new ArgumentException("设备信息有误，请检查设备国标编码、源站ID是否正确");
		}
		dto.setStartTime(TimeUtils.toUTCTime(TimeUtils.FULL_TIME, dto.getStartTime()));
		CommandDTO commandDTO = new CommandDTO();
		commandDTO.setSourceId("" + vo.getSourceId());
		commandDTO.setUuid(UUID.randomUUID().toString());
		commandDTO.setType("VideoDownload");
		dto.setCameraCode(vo.getCameraCode());
		commandDTO.setParams(new Gson().toJson(dto));

		return itsMsFeignClient.produce(commandDTO);
	}

	/**
	 *
	 * @param dto
	 * @return
	 */
	public Object checkStream(PlayStreamDTO dto) {
		Map<String, Object> param = new HashMap<String, Object>();
		param.put("deviceId", dto.getId());
		param.put("txTime", dto.getTxTime());
		param.put("type", dto.getType());
		return postBody(videoCloudPath + "/api/checkStream", param);
	}

	/**
	 * @描述 接收视频云的检测结果进行更新
	 */
	public void checkRtspResult(List<CheckRtspResultVO> list) {
		LOGGER.info("视频云批量更新摄像机检测状态：" + list.size());
		if (CollectionUtils.isEmpty(list)) {
			LOGGER.info("更新信息为空" + list.size());
		} else {
			List<CheckRtspResultDTO> resultList = new ArrayList<CheckRtspResultDTO>();
			list.forEach(x -> {
				CheckRtspResultDTO dto = new CheckRtspResultDTO();
				Integer rtspResult = x.getRtspResult();
				String lastSuccessTime = x.getLastSuccessTime();// 标清
				Integer rtspHdResult = x.getRtspHdResult();
				String lastSuccessHdTime = x.getLastSuccessHdTime();// 高清
				dto.setGbId(x.getGbId());
				if (rtspResult == 0 && rtspResult == 0) {
					dto.setCheckState(1);// 成功
					dto.setFailReason("-");
					dto.setCheckTime("标清：" + getCheckTime(lastSuccessTime) + ",高清：" + getCheckTime(lastSuccessHdTime));
				} else {
					dto.setCheckState(0);// 失败
					dto.setFailReason("标清：" + getFailReason(rtspResult) + ",高清：" + getFailReason(rtspHdResult));
					dto.setCheckTime("标清：" + getCheckTime(lastSuccessTime) + ",高清：" + getCheckTime(lastSuccessHdTime));
				}
				resultList.add(dto);
			});
			videoMapper.updateRtspResult(resultList);
		}
	}

	/**
	 * @描述 更新源站状态
	 */
	public void syncSourceStatus(List<SourceStatusVO> list) {
		LOGGER.info("更新源站状态：" + list.size());
		if (CollectionUtils.isEmpty(list)) {
			LOGGER.info("更新源站状态信息为空！" + list.size());
		} else {
			list.forEach(x -> {
				SysMetricsDTO dto = new SysMetricsDTO();
				dto.setCreateTime(x.getOnlineTime());
				dto.setSourceTime(x.getKeepTime());
				dto.setSourceId(x.getId());
				dto.setSourceName(x.getStationName());
				dto.setServiceName(x.getStationName());
				dto.setIp(x.getIpAddress());
				int count = videoMapper.countByIpServiceSourceName(dto);
				if (count > 0) {
					videoMapper.updateService(dto);
				} else {
					videoMapper.addService(dto);
				}
			});
		}
	}

	/**
	 * @描述 处理时间
	 */
	private String getCheckTime(String time) {
		if (StringUtils.isNoneBlank(time)) {
			return time.replace("T", " ");
		} else {
			return "-";
		}

	}

	/**
	 * @描述 返回失败原因
	 * @param 标清结果：-1-rtsp地址为空,0=成功 1-无效URL 2-连接拒绝 3-帐号密码错误 4-非H264
	 */
	private String getFailReason(Integer result) {
		String message = "";
		switch (result) {
		case 0:
			message = "成功";
			break;
		case 1:
			message = "无效URL";
			break;
		case 2:
			message = "连接拒绝";
			break;
		case 3:
			message = "帐号密码错误";
			break;
		case 4:
			message = "非H264";
			break;
		case -1:
			message = "rtsp地址为空";
			break;
		}
		return message;
	}

	/**
	 * @描述，摄像机内网播放，返回摄像机播流地址
	 * @param dto
	 * @param userId
	 * @return
	 */
	public Object playInner(VideoPlayDTO dto, String userId) {
		Integer code = 1;
		String message = "0";
		String id = dto.getId();
		VideoVO vo = videoMapper.selectByDeviceId(id);
		if (vo == null) {
			message = "云控摄像机信息不存在！";
		}
		Integer type = dto.getType();
		if (type == null) {
			type = 0;
		}
		String gb_code = vo.getCameraCode();
		// 根据用户id,计算内网视频播流端口，一个客户端端口只能用6次
		Integer port = this.getStramPort(userId);
		String result = doGet(dto, gb_code, port);
		LOGGER.info("gb_code有新的视频播放:{},{}", id, gb_code);
		/*
		 * 1 表示成功 1401 RTSP的帐号密码认证错误 1402 RTSP连接失败 1403 码流不是H264编码 1404 摄像机连接超时
		 */
		String playUrl = result;
		if (result.equals("1401")) {
			code = 0;
			message = "RTSP的帐号密码认证错误";
		} else if (result.equals("1402")) {
			code = 0;
			message = "RTSP连接失败";
		} else if (result.equals("1403")) {
			code = 0;
			message = "码流不是H264编码";
		} else if (result.equals("1404")) {
			code = 0;
			message = "摄像机连接超时";
		} else {
			code = 1;
			message = "摄像取流正常";
			LOGGER.info("获取视频URL成功:{},{}", id, playUrl);
		}
		callCount(dto, userId);// 接口调用次数统计，请求返回流成功才计算次数
		JsonObject entity = new JsonObject(); // 构建jison返回值
		entity.addProperty("id", id);
		entity.addProperty("type", type);
		entity.addProperty("playUrl", playUrl);
		entity.addProperty("flvUrl", playUrl);
		entity.addProperty("m3u8Url", playUrl);
		entity.addProperty("webrtcUrl", playUrl);
		entity.addProperty("code", code);
		entity.addProperty("message", message);
		entity.addProperty("playId", UUID.randomUUID().toString().replace("-", "").toUpperCase());
		return entity.toString();
	}

	private Integer getStramPort(String userId) {
		Integer minPort = innerMinPort; // 默认最小端口
		int size = innerMaxPort - minPort + 1;
		Integer port = minPort;// 默认端口
		// 获取当前的端口号
		if (PORT_LIST.isEmpty()) {
			PORT_LIST = this.createPorList(minPort, size); // 初始化端口信息
		}
		String key = "video:streamport-count-" + userId;
		String portCount = stringRedisTemplate.opsForValue().get(key);
		if (portCount == null) {
			stringRedisTemplate.opsForValue().set(key, "1");
			port = PORT_LIST.get(0);
		} else {
			Integer count = NumberUtils.toInt(portCount);
			if (count < (6 * size - 1)) { // 同一个用户每个端口默认只能打开6个视频，小于
				stringRedisTemplate.opsForValue().set(key, (count + 1) + "");
			} else {
				stringRedisTemplate.delete(key); // 删除，重新计算
			}
			port = PORT_LIST.get(count);
		}

		return port;
	}

	private List<Integer> createPorList(Integer port, int size) {
		int[] numbers = new int[size]; // 流媒体分配6个端口号
		for (int i = 0; i < size; i++) {
			numbers[i] = port + i;
		}
		List<Integer> portList = new ArrayList<>();
		for (int num : numbers) {// 每个号码添加6次到列表中
			for (int i = 0; i < size; i++) {
				portList.add(num);
			}
		}
		// 打乱列表以保证随机性
		// Collections.shuffle(usageList);
		return portList;
	}

	/**
	 * @描述 http请求，通过get，返回摄像机内网播流地址
	 */
	static RequestConfig config = RequestConfig.custom().setConnectionRequestTimeout(5000).setSocketTimeout(5000)
			.setConnectTimeout(5000).build();

	public String doGet(VideoPlayDTO deviceCamera, String cameraCode, Integer port) {
		String playUrl = "1404";
		String result = "0";
		String json = "";
		String httpUrl = innerApiServe + "/playBycode?gbCode=" + cameraCode;
		LOGGER.info("httpUrl请求API服务地址为：-->" + httpUrl);
		HttpPost httpPost = new HttpPost(httpUrl);
		httpPost.setConfig(config);
		CloseableHttpClient httpClient = HttpClients.createDefault();
		try {
			BasicResponseHandler handler = new BasicResponseHandler();
			StringEntity entity = new StringEntity(json, "utf-8");// 解决中文乱码问题
			entity.setContentEncoding("UTF-8");
			entity.setContentType("application/json");
			httpPost.setEntity(entity);
			httpPost.setConfig(config);
			result = httpClient.execute(httpPost, handler);
		} catch (Exception e) {
			// e.printStackTrace();
			LOGGER.info("内网播流地址获取失败：" + e.getMessage());
		} finally {
			/*
			 * 0 表示成功 1401 RTSP的帐号密码认证错误 1402 RTSP连接失败 1403 码流不是H264编码 1404 摄像机连接超时
			 */
			try {
				LOGGER.info("Rtmp-Api流媒体服务的返回结果： " + result);
				if (!StringUtils.isNotBlank(result)) {
					return "1404";
				} else if (result.equals("0")) {
					String camera_code = cameraCode;
					// 播流地址调整为https
					playUrl = innerStreamServe.replaceAll("http", "https") + ":" + port + "/live/" + camera_code
							+ ".flv";

				} else {
					playUrl = result;
				}
				httpClient.close();
			} catch (Exception e) {
				// e.printStackTrace();
				LOGGER.info("内网播流地址获取失败：" + e.getMessage());
			}
		}
		return playUrl;
	}

	/**
	 * 内网步控
	 * @param dto
	 * @return
	 */
	public Object innerPtz(PtzDTO ptzDTO) {
		String ptzUrl = innerStreamServe + ":" + innerStreamPort + "/ptz";
		String response = (String) postHttp(ptzUrl, ptzDTO);
		if (response == null) {
			return new ResponseVO("控制成功", 1);
		} else {
			return new ResponseVO("控制失败", 0);
		}
	}

	/**
	 * @描述 定时同步摄像机状态
	 */
	public void syncInnerDeviceStatus() {
		String statusUrl = innerStreamServe + ":" + innerStreamPort + "/get_status";
		DeviceStatus2DTO dto = new DeviceStatus2DTO();
		String response = (String) postHttp(statusUrl, dto);
		if (response != null) {
			DeviceStatusDTO deviceStatusDTO = new Gson().fromJson(response, DeviceStatusDTO.class);
			if (deviceStatusDTO != null) {
				List<DeviceStatuDTO> streams = deviceStatusDTO.getStreams();
				if (!streams.isEmpty()) {
					List<VideoOnlineDTO> dtos = new ArrayList<VideoOnlineDTO>();
					streams.forEach(x -> {
						VideoOnlineDTO onlineDto = new VideoOnlineDTO();
						onlineDto.setDevice_id(x.getDevice_id());
						onlineDto.setOnline(x.getStatus().equals("online") ? 1 : 0);
						onlineDto.setOfflineTime(TimeUtils.getNowTime());
						dtos.add(onlineDto);
					});
					this.batchUpdateOnline(dtos); // 更新云控数据库
				}
			}

		}
	}

	/**
	 * @描述 单个刷新设备状态
	 */
	public Object updateInnerStatusById(String id) {
		VideoStatusVO ret = new VideoStatusVO();
		ret.setId(id);
		VideoVO vo = videoMapper.selectByDeviceId(id);
		ret.setGbId(vo.getCameraCode());
		ret.setIp(vo.getIp());
		String statusUrl = innerStreamServe + ":" + innerStreamPort + "/get_status";
		DeviceStatus2DTO dto = new DeviceStatus2DTO();
		List<DeviceStatu2DTO> streams2 = new ArrayList<DeviceStatu2DTO>();
		DeviceStatu2DTO dto2 = new DeviceStatu2DTO();
		dto2.setDevice_id(vo.getCameraCode());
		streams2.add(dto2);
		dto.setStreams(streams2);
		String response = (String) postHttp(statusUrl, dto);
		if (response != null) {
			DeviceStatusDTO deviceStatusDTO = new Gson().fromJson(response, DeviceStatusDTO.class);
			if (deviceStatusDTO != null) {
				List<DeviceStatuDTO> streams = deviceStatusDTO.getStreams();
				if (!streams.isEmpty()) {
					List<VideoOnlineDTO> dtos = new ArrayList<VideoOnlineDTO>();
					DeviceStatuDTO x = streams.get(0);
					VideoOnlineDTO onlineDto = new VideoOnlineDTO();
					onlineDto.setDevice_id(x.getDevice_id());
					onlineDto.setOnline(x.getStatus().equals("online") ? 1 : 0);
					ret.setStatus(x.getStatus().equals("online") ? 1 : 0); // 返回状态
					ret.setOfflineTime(TimeUtils.getNowTime());
					onlineDto.setOfflineTime(TimeUtils.getNowTime());
					dtos.add(onlineDto);
					this.batchUpdateOnline(dtos); // 更新云控数据库
				}
			}

		}
		return ret;

	}

	/**
	 * post请求封装
	 * @param url 请求地址
	 * @param params 参数对象
	 * @return
	 */
	public Object postHttp(final String url, final Object params) {
		if (StringUtils.isEmpty(url)) { // 未发现请求地址
			return null;
		}
		ObjectMapper objectMapper = new ObjectMapper();
		String requestJson = null;
		try {
			requestJson = objectMapper.writeValueAsString(params);
		} catch (Exception e) {
			LOGGER.error("Http请求异常：{}", e.getMessage());
		}
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
		HttpEntity<String> entity = new HttpEntity<String>(requestJson, headers);
		ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class, params);
		Integer code = response.getStatusCode().value();
		if (200 == code) { // 请求成功
			return response.getBody();
		}
		return ("访问失败");
	}

	/**
	 * @描述 钦北大屏视频监控
	 * @param dto
	 * @param roles
	 * @return
	 */
	public List<VideoMenuVO> qbTree(VideoDTO dto, String[] roles) {
		Map<String, Object> map = new HashMap<>();
		map.put("keywords", dto.getKeywords());
		List<VideoVO> ret = videoMapper.selectAll(map);
		int[] QB_NUMBER = { 7, 8, 9 }; // 钦北三公司
		List<Integer> sourceIds = Arrays.stream(QB_NUMBER).boxed().collect(Collectors.toList());
		ret = ret.stream().filter(x -> sourceIds.contains(x.getSourceId())).collect(Collectors.toList());
		// 查询登录人有哪些权限的详细位置的视频
		map.put("roles", roles);
		List<String> positionIds = videoMapper.selectDevicePositionIdsByRoleIds(map);
		ret = ret.stream().filter(videoMenu -> (videoMenu.getDevicePositionId() == null
				|| positionIds.contains(videoMenu.getDevicePositionId()))).collect(Collectors.toList());
		if (dto.getGroupType() == 1) {
			// 按公司分类
			return groupCompany(ret);
		} else if (dto.getGroupType() == 2) {
			// 按路段分类
			return groupRoad(ret);
		}
		return null;
	}

	/**
	 * @描述 通过视频云取内网播流地址
	 * @param dto	
	 * @return
	 */
	public Object playInnerByVideoCloud(VideoPlayDTO dto) {
		String id = dto.getId();
		Integer type = dto.getType();
		if (type == null) {
			type = 0;
		}
		Map<String, Object> param = new HashMap<String, Object>();
		param.put("id", id);
		Integer duration = dto.getDuration();
		if (duration != null && duration > 0) {
			param.put("duration", duration);
		} else {
			param.put("duration", palyTime);
		}
		param.put("type", type);
		Object entity = postBody(videoCloudPath + "/api/playInner", param);
		if (entity == null) {
			return new ResponseVO(false);
		}
		return entity;

	}

	/**
	 * @描述 内网云台控制
	 * @param dto
	 * @return
	 */
	public Object ptzInner(VideoControlDTO dto) {
		String id = dto.getId();
		VideoVO vo = videoMapper.selectByDeviceId(id);
		if (vo == null) {
			return new ResponseVO("云控摄像机信息不存在！", 0);
		}
		Map<String, Object> param = new HashMap<String, Object>();
		param.put("device_id", id);
		param.put("ptz_cmd", dto.getControlType());
		param.put("step", dto.getStep());
		return postBody(videoCloudPath + "/api/controlPtzInner", param);

	}

	/**
	 * @描述 外部系统默认有效期为7天，云控2天更新一次
	 * @return
	 */
	private String getAiotAccessToken() {
		String accessToken = stringRedisTemplate.opsForValue().get(AIOT_ACCESS_TOKEN);
		if (accessToken == null || accessToken.isEmpty()) {
			accessToken = updateAiotAccessToken();
		}
		return accessToken;
	}

	/**
	 * @描述 实时拉取更新AccessToken
	 * @return
	 */
	private String updateAiotAccessToken() {
		String accessToken = "";
		String url = aiotUrl + "/user-service/lapp/v1/token/get";
		Map<String, String> bodyParams = new HashMap<String, String>();
		bodyParams.put("appKey", aiotAppkey);
		bodyParams.put("secret", aiotSecret);
		String result = HttpClientUtils.postWithBody(url, null, bodyParams);
		if (StringUtils.isNotBlank(result)) {
			JsonObject object = GsonUtils.String2Object(result);
			Integer code = object.get("code").getAsInt();
			if (code.equals(200)) {
				accessToken = object.get("data").getAsJsonObject().get("accessToken").getAsString();
				stringRedisTemplate.opsForValue().set(AIOT_ACCESS_TOKEN, accessToken, 2, TimeUnit.DAYS);
			}

		}
		return accessToken;
	}

	/**
	 * @描述 AIOT平台
	 * @param dto
	 * @param userId
	 * @return
	 */
	private Object playByAiot(VideoPlayDTO dto, String userId) {
		// 构造播流参数
		String id=dto.getId();
		VideoVO vo = videoMapper.selectByDeviceId(id);
		if(vo==null) {
			return new ResponseVO("云控摄像机信息不存在！", 0);
		}
		String url = aiotUrl + "/device-service/lapp/v1/live/address/get";
		Map<String, String> bodyParams = new HashMap<String, String>();
		bodyParams.put("accessToken", this.getAiotAccessToken());
		bodyParams.put("deviceSn", id);
		bodyParams.put("channelNo", "1");// 默认是1
		if (dto.getDuration() != null) {
			bodyParams.put("expireTime", String.valueOf(dto.getDuration() * 60));
		} else {
			bodyParams.put("expireTime", "600");
		}
		bodyParams.put("protocol", vo.getProtocol());// 流播放协议，0-rtc、1-hls、2-rtsp、3-rtmp、4-flv，默认为 0
		bodyParams.put("quality", String.valueOf(dto.getType()));// 视频清晰度，0-子码流、1-主码流 默认为 0
		String ret = HttpClientUtils.postWithBody(url, null, bodyParams);
		String playUrl = "";
		Integer code = 0; // 失败
		String message = "";
		if (StringUtils.isNotBlank(ret)) {
			JsonObject object = GsonUtils.String2Object(ret);
			Integer resCode = object.get("code").getAsInt();
			switch (resCode) {
			case 200:
				code = 1;
				playUrl = object.get("data").getAsJsonObject().get("url").getAsString();
				message = "摄像机取流正常";
				callCount(dto, userId);// 接口调用次数统计，请求返回流成功才计算次数
				LOGGER.info("获取视频URL成功:{},{}", dto.getId(), playUrl);
				break;
			case 10000:
				message = "参数为空或格式不正确";
				break;
			case 20001:
				message = "检查终端是否属于当前账户";
				break;
			case 20002:
				message = "终端不存在";
				break;
			case 20003:
				message = "终端不在线";
				break;
			case 20006:
				message = "AI摄像机通道号错误,通道号不匹配";
				break;
			case 20007:
				message = "AI盒子通道号错误,,通道号不匹配";
				break;
			case 10042:
				message = "accessToken过期，请重新请求播流地址";
				this.updateAiotAccessToken(); // 更新登录token
				break;
			}

		}
		JsonObject entity = new JsonObject();
		entity.addProperty("id", dto.getId());
		entity.addProperty("type", dto.getType());
		entity.addProperty("playUrl", playUrl);
		entity.addProperty("flvUrl", playUrl);
		entity.addProperty("m3u8Url", playUrl);
		entity.addProperty("webrtcUrl", playUrl);
		entity.addProperty("code", code);
		entity.addProperty("message", message);
		entity.addProperty("playId", UUID.randomUUID().toString().replace("-", "").toUpperCase());
		return entity.toString();

	}

	/**
	 * @描述 定时同步AI摄像机状态
	 */
	public void syncAiotDeviceStatus() {
		String url = aiotUrl + "/device-service/lapp/v1/camera/list";
		List<VideoOnlineDTO> list = new ArrayList<VideoOnlineDTO>();
		Map<String, String> bodyParams = new HashMap<String, String>();
		bodyParams.put("accessToken", this.updateAiotAccessToken());
		bodyParams.put("pageSize", "50");// 最大每页查询50个
		for (int i = 1; i < 10; i++) {
			bodyParams.put("pageNum", String.valueOf(i));
			String ret = HttpClientUtils.postWithBody(url, null, bodyParams);
			if (StringUtils.isNotBlank(ret)) {
				JsonObject object = GsonUtils.String2Object(ret);
				Integer code = object.get("code").getAsInt();
				if (code.equals(200)) { // 返回成功
					JsonObject data = object.get("data").getAsJsonObject();
					if (data.isJsonObject()) {
						if (data.has("records") && data.get("records") != null && data.get("records").isJsonArray()) {
							JsonArray array = data.getAsJsonArray("records");
							if (array.size() > 0) {
								for (JsonElement element : array) {
									if (element.isJsonObject()) {
										JsonObject obj = element.getAsJsonObject();
										VideoOnlineDTO dto = new VideoOnlineDTO();
										dto.setDevice_id(obj.get("deviceSn").getAsString());
										dto.setOnline(obj.get("onlineStatus").getAsInt());
										JsonElement time = obj.get("offlineTime");
										if (!time.isJsonNull()) {
											dto.setOfflineTime(time.getAsString());
										}
										list.add(dto);
									}
								}
							} else {
								break;
							}
						}
					}
				}
			}
		}
		if (!CollectionUtils.isEmpty(list)) {
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("cameras", list);
			videoMapper.batchUpdateOnlineById(map); // 批量同步设备状态
		}

	}
}