package com.bt.itsvideo.config;

import javax.sql.DataSource;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.jta.atomikos.AtomikosDataSourceBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import com.mysql.cj.jdbc.MysqlXADataSource;

@Configuration
@MapperScan(basePackages = "com.bt.itsvideo.mapper", sqlSessionFactoryRef = "db1SqlSessionFactory")
public class DataSource1Config {
	private static final Logger log = LoggerFactory.getLogger(DataSource1Config.class);

	@Value("${spring.datasource.maxLifetime}")
	private Integer maxLifetime;

	@Value("${spring.datasource.reapTimeout}")
	private Integer reapTimeout;

    @Value("${spring.datasource.minPoolSize:5}")
    private Integer minPoolSize;
    
    @Value("${spring.datasource.maxPoolSize:20}")
    private Integer maxPoolSize;

    @Value("${spring.datasource.borrowConnectionTimeout:10}")
    private Integer borrowConnectionTimeout;

    @Bean(name = "db1SqlSessionFactory")
    @Primary
    public SqlSessionFactory testSqlSessionFactory(@Qualifier("db1DataSource") DataSource dataSource)
            throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
         bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:com/bt/itsvideo/mapper/*.xml"));
        return bean.getObject();
    }

    @Bean(name = "db1DataSource")
    @Primary
    public DataSource db1DataSource(DB1Config config) {
        MysqlXADataSource mysqlXADataSource=new MysqlXADataSource();
        mysqlXADataSource.setUrl(config.getUrl());
        mysqlXADataSource.setPassword(config.getPassword());
        mysqlXADataSource.setUser(config.getUsername());

        AtomikosDataSourceBean atomikosDataSourceBean=new AtomikosDataSourceBean();
        atomikosDataSourceBean.setXaDataSource(mysqlXADataSource);
        atomikosDataSourceBean.setUniqueResourceName("db1Datasource");

        atomikosDataSourceBean.setMinPoolSize(minPoolSize);// yaml不配置参数，默认5
        atomikosDataSourceBean.setMaxPoolSize(maxPoolSize);// yaml不配置参数，默认20
        
//        atomikosDataSourceBean.setTestQuery("SELECT 1 FROM DUAL");
//        atomikosDataSourceBean.setMaxLifetime(0);
        atomikosDataSourceBean.setMaxLifetime(maxLifetime);
        atomikosDataSourceBean.setReapTimeout(reapTimeout);
        atomikosDataSourceBean.setBorrowConnectionTimeout(borrowConnectionTimeout);
        
        log.info("MaxPoolSize:"+atomikosDataSourceBean.getMaxPoolSize());
        log.info("MinPoolSize:"+atomikosDataSourceBean.getMinPoolSize());
        log.info("getBorrowConnectionTimeout:"+atomikosDataSourceBean.getBorrowConnectionTimeout());//30 单位:秒
        log.info("getMaxIdleTime:"+atomikosDataSourceBean.getMaxIdleTime());//60 单位:秒
        log.info("getMaxLifetime:"+atomikosDataSourceBean.getMaxLifetime());
        
        return atomikosDataSourceBean;
    }


//    @Bean(name = "db1TransactionManager")
//    @Primary
//    public DataSourceTransactionManager testTransactionManager(@Qualifier("db1DataSource") DataSource dataSource) {
//        return new DataSourceTransactionManager(dataSource);
//    }

    @Bean(name = "db1SqlSessionTemplate")
    @Primary
    public SqlSessionTemplate db1SqlSessionTemplate(
            @Qualifier("db1SqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

}