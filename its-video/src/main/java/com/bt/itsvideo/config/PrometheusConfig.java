package com.bt.itsvideo.config;

import io.prometheus.client.exporter.MetricsServlet;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Prometheus埋点初始化
 * @Author: lsy
 * @Date: 2023年02月17日15:44
 * @Description:
 **/
@Configuration
public class PrometheusConfig {
    @Bean
    public ServletRegistrationBean servletRegistrationBean(){
        // 将埋点指标吐出到 /metrics 节点
        return new ServletRegistrationBean(new MetricsServlet(), "/metrics-video");
    }
}
