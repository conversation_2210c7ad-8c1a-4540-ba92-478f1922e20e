package com.bt.itsvideo.auth;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.bt.itscore.utils.EncodeUtils;

@Aspect
@Component
public class VideoAuthAspect {
	private final static Logger LOGGER = LoggerFactory.getLogger(VideoAuthAspect.class);
	@Value("${video.play.secret.key}")
	String secretKey;

	@Around("@annotation(com.bt.itsvideo.auth.TokenAuth)")
	public Object checkTokenAuth(ProceedingJoinPoint point) throws Throwable {
		try {
			validationTokenAuth();
			return point.proceed();
		} catch (Throwable throwable) {
			throw throwable;
		}
	}

	private HttpServletRequest validationTokenAuth() {
		// 1. 校验token
		ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		HttpServletRequest request = attributes.getRequest();
		String token = request.getHeader("token");
		String timestamp = request.getHeader("timestamp");
		// 2. 校验token是否合法，如果合法就认为用户已登录，如果不合法，就返回401
		if (StringUtils.isEmpty(token) || StringUtils.isEmpty(timestamp)) {
			LOGGER.error("Token or timestamp 没有传！");
			throw new SecurityException("Token or timestamp 没有传！");
		}
		Boolean isValid = checkToken(token, timestamp);
		if (!isValid) {
			LOGGER.error("Token非法！！");
			throw new SecurityException("Token非法！！");
		}
		return request;
	}

	/**
	   * 校验token
	   * @param token     签名
	   * @param timestamp 时间戳
	   * @return 校验结果
	   */
	private Boolean checkToken(String token, String timestamp) {
		if (StringUtils.isEmpty(token) || StringUtils.isEmpty(timestamp)) {
			return false;
		}
		long now = System.currentTimeMillis();
		long time = Long.parseLong(timestamp);
		if (now - time > 300000) {
			LOGGER.error("时间戳已过期[{}][{}][{}]", now, time, (now - time));
			return false;
		}
		String crypt = EncodeUtils.encode(secretKey + timestamp, "MD5");
		return StringUtils.equals(crypt, token);
	}

}
