package com.bt.itsvideo.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.bt.itscore.domain.dto.Event96333DTO;
import com.bt.itscore.domain.dto.Event96333NewOrderDTO;
import com.bt.itscore.domain.dto.Event96333OrderSuccessCodeDTO;
import com.bt.itscore.domain.vo.ResponseVO;

@FeignClient(name = "its-event")
public interface ItsEventFeignClient {
    @PostMapping(value = "/event/add96333Rescue")
    public ResponseVO add96333Rescue(@RequestBody Event96333DTO dto);

    @PostMapping(value = "/event/addEvent96333Order")
	public ResponseVO addEvent96333Order(@RequestBody Event96333NewOrderDTO dto);

    @PostMapping(value = "/event/updateEvent96333ResultCode")
	public ResponseVO updateEvent96333ResultCode(@RequestBody Event96333OrderSuccessCodeDTO dto);

    @PostMapping(value = "/event/updateEvent96333VisitCode")
	public ResponseVO updateEvent96333VisitCode(@RequestBody Event96333OrderSuccessCodeDTO dto);

    @PostMapping(value = "/event/cancel96333Order")
	public ResponseVO cancel96333Order(@RequestBody Event96333NewOrderDTO dto);

}
