package com.bt.itsvideo.domain.vo;

public class VideoQbPlanDivVO {
	private String id; // id
	private String planId;// 预案id
	private String deviceId;// 摄像机id
	private Double xRatio;// X轴
	private Double yRatio;// Y轴
	private Double divWidth;// 宽
	private Double divHeight;// 高
	private String deviceName;// 摄像机设备名称
	private String deviceNameRemark;// 摄像机设备名称简称
	private String milePost;// 设备桩号
	private String directionName;//路段方向名称	
    private String facilityName; //所属设施名称
    private Integer status; //状态(0离线 1在线)
    
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getPlanId() {
		return planId;
	}
	public void setPlanId(String planId) {
		this.planId = planId;
	}
	public String getDeviceId() {
		return deviceId;
	}
	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}
	public Double getxRatio() {
		return xRatio;
	}
	public void setxRatio(Double xRatio) {
		this.xRatio = xRatio;
	}
	public Double getyRatio() {
		return yRatio;
	}
	public void setyRatio(Double yRatio) {
		this.yRatio = yRatio;
	}
	public Double getDivWidth() {
		return divWidth;
	}
	public void setDivWidth(Double divWidth) {
		this.divWidth = divWidth;
	}
	public Double getDivHeight() {
		return divHeight;
	}
	public void setDivHeight(Double divHeight) {
		this.divHeight = divHeight;
	}
	public String getDeviceName() {
		return deviceName;
	}
	public void setDeviceName(String deviceName) {
		this.deviceName = deviceName;
	}
	public String getDeviceNameRemark() {
		return deviceNameRemark;
	}
	public void setDeviceNameRemark(String deviceNameRemark) {
		this.deviceNameRemark = deviceNameRemark;
	}
	public String getMilePost() {
		return milePost;
	}
	public void setMilePost(String milePost) {
		this.milePost = milePost;
	}
	public String getDirectionName() {
		return directionName;
	}
	public void setDirectionName(String directionName) {
		this.directionName = directionName;
	}
	public String getFacilityName() {
		return facilityName;
	}
	public void setFacilityName(String facilityName) {
		this.facilityName = facilityName;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	
	
}
