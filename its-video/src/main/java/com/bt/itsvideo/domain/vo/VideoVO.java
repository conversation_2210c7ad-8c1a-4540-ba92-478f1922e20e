package com.bt.itsvideo.domain.vo;

public class VideoVO {
	private String deviceId;
	private Integer sort;// 排序字段
	private String deviceShortName;// 摄像机简称（PC使用）
	private String deviceName;
	private Integer status;
	private Integer roadNo;
	private String roadName;
	private String facilityNo;
	private String facilityName;
	private Integer facilityTypeNo;
	private String facilityTypeName;
	private Integer facilityMilePost;
	private String facilityMilePostStr;
	private Integer deviceMilePost;// 设备桩号数值（米）
	private String milePost;// 设备桩号
	private Integer orgSort;//组织机构排序字段
	private String orgId;
	private String orgName;//组织机构名称
	private String orgShortName;// 组织机构简称
	private String ipcIpaddress;
	private Integer ipcPort;
	private String ip;
	private Integer port;
	private String cameraCode;
	private String dvrProtocol;
	private Integer ptz;
	private String directionNo;
	private String directionName;
	private Integer roadLine;
	private int roadSort;// 路段排序字段	
	private Integer sourceId;//源站ID，沿海1,罗城2,大化3,昭平4,灵山5
	private String spUpdateTime; // 视频共享平台更新时间（为null说明不是视频组的相机）
    private String devicePositionId;// 设备位置类型ID（device_position_type表id）
    private Integer distance;
    private Integer cameraType; //摄像机类型
    private String protocol; //流播放协议，0-rtc、1-hls、2-rtsp、3-rtmp、4-flv，默认为 0

	public String getDeviceId() {
		return deviceId;
	}

	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}

	public Integer getSort() {
		return sort;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}

	public String getDeviceShortName() {
		return deviceShortName;
	}

	public void setDeviceShortName(String deviceShortName) {
		this.deviceShortName = deviceShortName;
	}

	public String getDeviceName() {
		return deviceName;
	}

	public void setDeviceName(String deviceName) {
		this.deviceName = deviceName;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getRoadNo() {
		return roadNo;
	}

	public void setRoadNo(Integer roadNo) {
		this.roadNo = roadNo;
	}

	public String getRoadName() {
		return roadName;
	}

	public void setRoadName(String roadName) {
		this.roadName = roadName;
	}

	public String getFacilityNo() {
		return facilityNo;
	}

	public void setFacilityNo(String facilityNo) {
		this.facilityNo = facilityNo;
	}

	public String getFacilityName() {
		return facilityName;
	}

	public String getFacilityTypeName() {
		return facilityTypeName;
	}

	public void setFacilityTypeName(String facilityTypeName) {
		this.facilityTypeName = facilityTypeName;
	}

	public void setFacilityName(String facilityName) {
		this.facilityName = facilityName;
	}

	public Integer getFacilityTypeNo() {
		return facilityTypeNo;
	}

	public void setFacilityTypeNo(Integer facilityTypeNo) {
		this.facilityTypeNo = facilityTypeNo;
	}

	public Integer getFacilityMilePost() {
		return facilityMilePost;
	}

	public void setFacilityMilePost(Integer facilityMilePost) {
		this.facilityMilePost = facilityMilePost;
	}

	public String getFacilityMilePostStr() {
		return facilityMilePostStr;
	}

	public void setFacilityMilePostStr(String facilityMilePostStr) {
		this.facilityMilePostStr = facilityMilePostStr;
	}

	public Integer getDeviceMilePost() {
		return deviceMilePost;
	}

	public void setDeviceMilePost(Integer deviceMilePost) {
		this.deviceMilePost = deviceMilePost;
	}

	public String getMilePost() {
		return milePost;
	}

	public void setMilePost(String milePost) {
		this.milePost = milePost;
	}

	public Integer getOrgSort() {
		return orgSort;
	}

	public void setOrgSort(Integer orgSort) {
		this.orgSort = orgSort;
	}

	public String getOrgId() {
		return orgId;
	}

	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getOrgShortName() {
		return orgShortName;
	}

	public void setOrgShortName(String orgShortName) {
		this.orgShortName = orgShortName;
	}

	public String getIpcIpaddress() {
		return ipcIpaddress;
	}

	public void setIpcIpaddress(String ipcIpaddress) {
		this.ipcIpaddress = ipcIpaddress;
	}

	public Integer getIpcPort() {
		return ipcPort;
	}

	public void setIpcPort(Integer ipcPort) {
		this.ipcPort = ipcPort;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public Integer getPort() {
		return port;
	}

	public void setPort(Integer port) {
		this.port = port;
	}

	public String getCameraCode() {
		return cameraCode;
	}

	public void setCameraCode(String cameraCode) {
		this.cameraCode = cameraCode;
	}

	public String getDvrProtocol() {
		return dvrProtocol;
	}

	public void setDvrProtocol(String dvrProtocol) {
		this.dvrProtocol = dvrProtocol;
	}

	public String getDirectionNo() {
		return directionNo;
	}

	public void setDirectionNo(String directionNo) {
		this.directionNo = directionNo;
	}

	public String getDirectionName() {
		return directionName;
	}

	public void setDirectionName(String directionName) {
		this.directionName = directionName;
	}

	public Integer getRoadLine() {
		return roadLine;
	}

	public void setRoadLine(Integer roadLine) {
		this.roadLine = roadLine;
	}

	public Integer getPtz() {
		return ptz;
	}

	public void setPtz(Integer ptz) {
		this.ptz = ptz;
	}

	public int getRoadSort() {
		return roadSort;
	}

	public void setRoadSort(int roadSort) {
		this.roadSort = roadSort;
	}

	public Integer getSourceId() {
		return sourceId;
	}

	public void setSourceId(Integer sourceId) {
		this.sourceId = sourceId;
	}

	public String getSpUpdateTime() {
		return spUpdateTime;
	}

	public void setSpUpdateTime(String spUpdateTime) {
		this.spUpdateTime = spUpdateTime;
	}

    public String getDevicePositionId() {
        return devicePositionId;
    }

    public void setDevicePositionId(String devicePositionId) {
        this.devicePositionId = devicePositionId;
    }

	public Integer getDistance() {
		return distance;
	}

	public void setDistance(Integer distance) {
		this.distance = distance;
	}

	public Integer getCameraType() {
		return cameraType;
	}

	public void setCameraType(Integer cameraType) {
		this.cameraType = cameraType;
	}

	public String getProtocol() {
		return protocol;
	}

	public void setProtocol(String protocol) {
		this.protocol = protocol;
	}
	
	
}
