package com.bt.itsvideo.domain.dto;

import java.util.List;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024年2月5日 下午3:46:19
 * @Description 视频巡检任务DTO
 */
public class VideoInspectTaskDTO {
	private String id;// 主键id
	@NotBlank(message = "视频巡检任务名称不能为空")
	private String taskName;// 视频巡检任务名称
	@NotNull(message = "巡检完毕是否循环不能为空")
	private Integer loop;// 是否无限循环，1是，0否 （巡检完毕是否循环）
	@NotNull(message = "单页巡检窗口数不能为空")
	private Integer windowNumber;// 单页巡检窗口数，1/4/6
	@NotNull(message = "单页巡检停留时间不能为空")
	private Integer stayTime;// 单页巡检停留时间，取值30~300，默认值30秒
	@NotNull(message = "是否使用预加载模式不能为空")
	private Integer preload;// 是否使用预加载模式：默认是1，在单页巡检还剩20秒结束时，预先请求下一页巡检视频的播放地址，否0
	@NotNull(message = "默认巡检码流不能为空")
	@Min(value = 0, message = "巡检码流值无效")
	@Max(value = 1, message = "巡检码流值无效")
	private Integer codeStream;// 默认巡检码流：默认标清0,高清1
	private Integer videoTotalNumber;// 视频总数量
	@NotBlank(message = "组织机构ID不能为空")
	private String orgId;// 运营公司id
	@NotEmpty(message = "设施类型不能为空")
	private List<Integer> facilityTypeNos;// 设施类型列表
	@NotEmpty(message = "摄像机列表不能为空")
	private List<String> deviceIds;// 摄像机列表
	private String createTime;// 创建时间
	private String updateTime;// 修改时间

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getTaskName() {
		return taskName;
	}

	public void setTaskName(String taskName) {
		this.taskName = taskName;
	}

	public Integer getLoop() {
		return loop;
	}

	public void setLoop(Integer loop) {
		this.loop = loop;
	}

	public Integer getWindowNumber() {
		return windowNumber;
	}

	public void setWindowNumber(Integer windowNumber) {
		this.windowNumber = windowNumber;
	}

	public Integer getStayTime() {
		return stayTime;
	}

	public void setStayTime(Integer stayTime) {
		this.stayTime = stayTime;
	}

	public Integer getPreload() {
		return preload;
	}

	public void setPreload(Integer preload) {
		this.preload = preload;
	}

	public Integer getCodeStream() {
		return codeStream;
	}

	public void setCodeStream(Integer codeStream) {
		this.codeStream = codeStream;
	}

	public Integer getVideoTotalNumber() {
		return videoTotalNumber;
	}

	public void setVideoTotalNumber(Integer videoTotalNumber) {
		this.videoTotalNumber = videoTotalNumber;
	}

	public String getOrgId() {
		return orgId;
	}

	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}

	public List<Integer> getFacilityTypeNos() {
		return facilityTypeNos;
	}

	public void setFacilityTypeNos(List<Integer> facilityTypeNos) {
		this.facilityTypeNos = facilityTypeNos;
	}

	public List<String> getDeviceIds() {
		return deviceIds;
	}

	public void setDeviceIds(List<String> deviceIds) {
		this.deviceIds = deviceIds;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}

}
