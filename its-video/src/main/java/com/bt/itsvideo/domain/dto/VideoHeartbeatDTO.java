package com.bt.itsvideo.domain.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023年2月14日 上午10:52:12
 * @Description 视频播放心跳DTO
 */
public class VideoHeartbeatDTO {
	@NotBlank(message = "摄像机id不能为空")
	private String id;// 摄像机id
	@NotBlank(message = "flvUrl不能为空")
	private String flvUrl;// 播放唯一标识
	@NotBlank(message = "playId不能为空")
	private String playId;// 播放唯一标识
	@NotNull(message = "type不能为空")
	private Integer type;// type:0标清，1高清

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getFlvUrl() {
		return flvUrl;
	}

	public void setFlvUrl(String flvUrl) {
		this.flvUrl = flvUrl;
	}

	public String getPlayId() {
		return playId;
	}

	public void setPlayId(String playId) {
		this.playId = playId;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

}
