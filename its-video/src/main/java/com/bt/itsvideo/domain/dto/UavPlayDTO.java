package com.bt.itsvideo.domain.dto;

public class UavPlayDTO {
	private String url_type;// 直播协议类型。 0:声网Agora, 1:RTMP, 2:RTSP, 3:GB28181, 4:WebRTC
	private String video_id;// 直播视频流的 ID 某路在推视频码流的标识符，格式为 {sn}/{camera_index}/{video_index}。其中 {sn}
							// 为视频源设备序列号。{camera_index} 为相机索引，{video_index} 为该相机级别的视频源可以选择的码流索引。
	private String video_quality;// 直播质量。 0:自适应,1:流畅,2:标清,3:高清,4:超清
	private String video_type;// 直播视频流镜头类型。 normal:默认, ir:红外, wide:广角, zoom:变焦
	private String camera_position;// 机场相机位置。 0:舱内,1:舱外
	private String device_sn;// 设备SN编码

	public String getUrl_type() {
		return url_type;
	}

	public void setUrl_type(String url_type) {
		this.url_type = url_type;
	}

	public String getVideo_id() {
		return video_id;
	}

	public void setVideo_id(String video_id) {
		this.video_id = video_id;
	}

	public String getVideo_quality() {
		return video_quality;
	}

	public void setVideo_quality(String video_quality) {
		this.video_quality = video_quality;
	}

	public String getVideo_type() {
		return video_type;
	}

	public void setVideo_type(String video_type) {
		this.video_type = video_type;
	}

	public String getCamera_position() {
		return camera_position;
	}

	public void setCamera_position(String camera_position) {
		this.camera_position = camera_position;
	}

	public String getDevice_sn() {
		return device_sn;
	}

	public void setDevice_sn(String device_sn) {
		this.device_sn = device_sn;
	}

}
