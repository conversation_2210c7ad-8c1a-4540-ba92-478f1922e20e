package com.bt.itsvideo.controller;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.nacos.api.utils.StringUtils;
import com.bt.itscore.auth.Login;
import com.bt.itscore.domain.dto.IdStringDTO;
import com.bt.itscore.domain.dto.PageDTO;
import com.bt.itscore.domain.vo.PageVO;
import com.bt.itscore.domain.vo.ResponseVO;
import com.bt.itscore.exception.ArgumentException;
import com.bt.itscore.exception.AuthzException;
import com.bt.itscore.utils.ValidUtils;
import com.bt.itsvideo.domain.dto.VideoInspectTaskDTO;
import com.bt.itsvideo.service.VideoInspectService;

/**
 * <AUTHOR>
 * @date 2024年2月19日 上午8:43:31
 * @Description 视频巡检处理类
 */
@RestController
@RequestMapping("videoInspect")
public class VideoInspectController {

	@Autowired
	private VideoInspectService videoInspectService;

	/**
	 * 新增视频巡检任务<br>
	 * 参数：请查看com.bt.itsvideo.domain.dto.VideoInspectTaskDTO<br>
	 */
	@Login
	@PostMapping("add")
	public Object add(@Valid @RequestBody VideoInspectTaskDTO dto, BindingResult result) {
		ValidUtils.error(result);
		return new ResponseVO(videoInspectService.add(dto) > 0);
	}

	/**
	 * 删除视频巡检任务
	 */
	@Login
	@PostMapping("delete")
	public Object delete(@Valid @RequestBody IdStringDTO dto, BindingResult result) {
		ValidUtils.error(result);
		return new ResponseVO(videoInspectService.delete(dto) > 0);
	}

	/**
	 * 修改视频巡检任务
	 */
	@Login
	@PostMapping("update")
	public Object update(@Valid @RequestBody VideoInspectTaskDTO dto, BindingResult result) {
		ValidUtils.error(result);
		if (StringUtils.isBlank(dto.getId())) {
			throw new ArgumentException("id不能为空");
		}
		return new ResponseVO(videoInspectService.update(dto) > 0);
	}

	/**
	 * 分页查询视频巡检任务
	 */
	@Login
	@PostMapping("page")
	public Object page(@Valid PageDTO pageDTO, @RequestBody VideoInspectTaskDTO dto, BindingResult result, HttpServletRequest request) {
		ValidUtils.error(result);
		String role = (String)request.getAttribute("role");
		if(role == null || role.length() < 1) {
			throw new AuthzException("没有角色权限");
		}
		return new PageVO(videoInspectService.page(pageDTO, dto));
	}

	/**
	 * 通过视频巡检任务id查询摄像机列表
	 */
	@Login
	@PostMapping("selectCameraListById")
	public Object selectCameraListById(@Valid @RequestBody IdStringDTO dto, BindingResult result) {
		ValidUtils.error(result);
		return videoInspectService.selectCameraListById(dto);
	}

}
