// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: RadarTwinBean.proto

package com.bt.itswebsocket.domain.protobuf;

public final class RadarTwinBeansProto {
  private RadarTwinBeansProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface RadarTwinBeansOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.bt.itswebsocket.domain.protobuf.RadarTwinBeans)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>required string websocketType = 1;</code>
     */
    boolean hasWebsocketType();
    /**
     * <code>required string websocketType = 1;</code>
     */
    java.lang.String getWebsocketType();
    /**
     * <code>required string websocketType = 1;</code>
     */
    com.google.protobuf.ByteString
        getWebsocketTypeBytes();

    /**
     * <code>repeated .com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean message = 2;</code>
     */
    java.util.List<com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean> 
        getMessageList();
    /**
     * <code>repeated .com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean message = 2;</code>
     */
    com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean getMessage(int index);
    /**
     * <code>repeated .com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean message = 2;</code>
     */
    int getMessageCount();
    /**
     * <code>repeated .com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean message = 2;</code>
     */
    java.util.List<? extends com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBeanOrBuilder> 
        getMessageOrBuilderList();
    /**
     * <code>repeated .com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean message = 2;</code>
     */
    com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBeanOrBuilder getMessageOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.bt.itswebsocket.domain.protobuf.RadarTwinBeans}
   */
  public static final class RadarTwinBeans extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:com.bt.itswebsocket.domain.protobuf.RadarTwinBeans)
      RadarTwinBeansOrBuilder {
    // Use RadarTwinBeans.newBuilder() to construct.
    private RadarTwinBeans(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RadarTwinBeans(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RadarTwinBeans defaultInstance;
    public static RadarTwinBeans getDefaultInstance() {
      return defaultInstance;
    }

    public RadarTwinBeans getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RadarTwinBeans(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              websocketType_ = bs;
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                message_ = new java.util.ArrayList<com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean>();
                mutable_bitField0_ |= 0x00000002;
              }
              message_.add(input.readMessage(com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          message_ = java.util.Collections.unmodifiableList(message_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.internal_static_com_bt_itswebsocket_domain_protobuf_RadarTwinBeans_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.internal_static_com_bt_itswebsocket_domain_protobuf_RadarTwinBeans_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.class, com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.Builder.class);
    }

    public static com.google.protobuf.Parser<RadarTwinBeans> PARSER =
        new com.google.protobuf.AbstractParser<RadarTwinBeans>() {
      public RadarTwinBeans parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RadarTwinBeans(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RadarTwinBeans> getParserForType() {
      return PARSER;
    }

    public interface RadarTwinBeanOrBuilder extends
        // @@protoc_insertion_point(interface_extends:com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>required string facilityNo = 1;</code>
       */
      boolean hasFacilityNo();
      /**
       * <code>required string facilityNo = 1;</code>
       */
      java.lang.String getFacilityNo();
      /**
       * <code>required string facilityNo = 1;</code>
       */
      com.google.protobuf.ByteString
          getFacilityNoBytes();

      /**
       * <code>required int32 line = 2;</code>
       */
      boolean hasLine();
      /**
       * <code>required int32 line = 2;</code>
       */
      int getLine();

      /**
       * <code>optional string idLane = 3;</code>
       */
      boolean hasIdLane();
      /**
       * <code>optional string idLane = 3;</code>
       */
      java.lang.String getIdLane();
      /**
       * <code>optional string idLane = 3;</code>
       */
      com.google.protobuf.ByteString
          getIdLaneBytes();

      /**
       * <code>required string id = 4;</code>
       */
      boolean hasId();
      /**
       * <code>required string id = 4;</code>
       */
      java.lang.String getId();
      /**
       * <code>required string id = 4;</code>
       */
      com.google.protobuf.ByteString
          getIdBytes();

      /**
       * <code>optional string cls = 5;</code>
       */
      boolean hasCls();
      /**
       * <code>optional string cls = 5;</code>
       */
      java.lang.String getCls();
      /**
       * <code>optional string cls = 5;</code>
       */
      com.google.protobuf.ByteString
          getClsBytes();

      /**
       * <code>optional double xRatio = 6;</code>
       */
      boolean hasXRatio();
      /**
       * <code>optional double xRatio = 6;</code>
       */
      double getXRatio();

      /**
       * <code>optional double yRatio = 7;</code>
       */
      boolean hasYRatio();
      /**
       * <code>optional double yRatio = 7;</code>
       */
      double getYRatio();

      /**
       * <code>optional double xRatioYk = 8;</code>
       */
      boolean hasXRatioYk();
      /**
       * <code>optional double xRatioYk = 8;</code>
       */
      double getXRatioYk();

      /**
       * <code>optional double fuseFlag = 9;</code>
       */
      boolean hasFuseFlag();
      /**
       * <code>optional double fuseFlag = 9;</code>
       */
      double getFuseFlag();

      /**
       * <code>optional string fuseDataId = 10;</code>
       */
      boolean hasFuseDataId();
      /**
       * <code>optional string fuseDataId = 10;</code>
       */
      java.lang.String getFuseDataId();
      /**
       * <code>optional string fuseDataId = 10;</code>
       */
      com.google.protobuf.ByteString
          getFuseDataIdBytes();

      /**
       * <code>optional double speed = 11;</code>
       */
      boolean hasSpeed();
      /**
       * <code>optional double speed = 11;</code>
       */
      double getSpeed();

      /**
       * <code>optional string finishFlag = 12;</code>
       */
      boolean hasFinishFlag();
      /**
       * <code>optional string finishFlag = 12;</code>
       */
      java.lang.String getFinishFlag();
      /**
       * <code>optional string finishFlag = 12;</code>
       */
      com.google.protobuf.ByteString
          getFinishFlagBytes();

      /**
       * <code>optional int64 sTime = 13;</code>
       */
      boolean hasSTime();
      /**
       * <code>optional int64 sTime = 13;</code>
       */
      long getSTime();

      /**
       * <code>optional string detectTime = 14;</code>
       */
      boolean hasDetectTime();
      /**
       * <code>optional string detectTime = 14;</code>
       */
      java.lang.String getDetectTime();
      /**
       * <code>optional string detectTime = 14;</code>
       */
      com.google.protobuf.ByteString
          getDetectTimeBytes();

      /**
       * <code>optional string pushTime = 15;</code>
       */
      boolean hasPushTime();
      /**
       * <code>optional string pushTime = 15;</code>
       */
      java.lang.String getPushTime();
      /**
       * <code>optional string pushTime = 15;</code>
       */
      com.google.protobuf.ByteString
          getPushTimeBytes();

      /**
       * <code>optional string szPlate = 16;</code>
       */
      boolean hasSzPlate();
      /**
       * <code>optional string szPlate = 16;</code>
       */
      java.lang.String getSzPlate();
      /**
       * <code>optional string szPlate = 16;</code>
       */
      com.google.protobuf.ByteString
          getSzPlateBytes();

      /**
       * <code>optional int32 nPlateColor = 17;</code>
       */
      boolean hasNPlateColor();
      /**
       * <code>optional int32 nPlateColor = 17;</code>
       */
      int getNPlateColor();

      /**
       * <code>optional int32 nAxleNum = 18;</code>
       */
      boolean hasNAxleNum();
      /**
       * <code>optional int32 nAxleNum = 18;</code>
       */
      int getNAxleNum();

      /**
       * <code>optional int32 nVehicleType = 19;</code>
       */
      boolean hasNVehicleType();
      /**
       * <code>optional int32 nVehicleType = 19;</code>
       */
      int getNVehicleType();

      /**
       * <code>optional string szPlatePic = 20;</code>
       */
      boolean hasSzPlatePic();
      /**
       * <code>optional string szPlatePic = 20;</code>
       */
      java.lang.String getSzPlatePic();
      /**
       * <code>optional string szPlatePic = 20;</code>
       */
      com.google.protobuf.ByteString
          getSzPlatePicBytes();

      /**
       * <code>optional string szSidePic = 21;</code>
       */
      boolean hasSzSidePic();
      /**
       * <code>optional string szSidePic = 21;</code>
       */
      java.lang.String getSzSidePic();
      /**
       * <code>optional string szSidePic = 21;</code>
       */
      com.google.protobuf.ByteString
          getSzSidePicBytes();

      /**
       * <code>optional string szCarPic = 22;</code>
       */
      boolean hasSzCarPic();
      /**
       * <code>optional string szCarPic = 22;</code>
       */
      java.lang.String getSzCarPic();
      /**
       * <code>optional string szCarPic = 22;</code>
       */
      com.google.protobuf.ByteString
          getSzCarPicBytes();
    }
    /**
     * Protobuf type {@code com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean}
     */
    public static final class RadarTwinBean extends
        com.google.protobuf.GeneratedMessage implements
        // @@protoc_insertion_point(message_implements:com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean)
        RadarTwinBeanOrBuilder {
      // Use RadarTwinBean.newBuilder() to construct.
      private RadarTwinBean(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
        super(builder);
        this.unknownFields = builder.getUnknownFields();
      }
      private RadarTwinBean(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

      private static final RadarTwinBean defaultInstance;
      public static RadarTwinBean getDefaultInstance() {
        return defaultInstance;
      }

      public RadarTwinBean getDefaultInstanceForType() {
        return defaultInstance;
      }

      private final com.google.protobuf.UnknownFieldSet unknownFields;
      @java.lang.Override
      public final com.google.protobuf.UnknownFieldSet
          getUnknownFields() {
        return this.unknownFields;
      }
      private RadarTwinBean(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        initFields();
        int mutable_bitField0_ = 0;
        com.google.protobuf.UnknownFieldSet.Builder unknownFields =
            com.google.protobuf.UnknownFieldSet.newBuilder();
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!parseUnknownField(input, unknownFields,
                                       extensionRegistry, tag)) {
                  done = true;
                }
                break;
              }
              case 10: {
                com.google.protobuf.ByteString bs = input.readBytes();
                bitField0_ |= 0x00000001;
                facilityNo_ = bs;
                break;
              }
              case 16: {
                bitField0_ |= 0x00000002;
                line_ = input.readInt32();
                break;
              }
              case 26: {
                com.google.protobuf.ByteString bs = input.readBytes();
                bitField0_ |= 0x00000004;
                idLane_ = bs;
                break;
              }
              case 34: {
                com.google.protobuf.ByteString bs = input.readBytes();
                bitField0_ |= 0x00000008;
                id_ = bs;
                break;
              }
              case 42: {
                com.google.protobuf.ByteString bs = input.readBytes();
                bitField0_ |= 0x00000010;
                cls_ = bs;
                break;
              }
              case 49: {
                bitField0_ |= 0x00000020;
                xRatio_ = input.readDouble();
                break;
              }
              case 57: {
                bitField0_ |= 0x00000040;
                yRatio_ = input.readDouble();
                break;
              }
              case 65: {
                bitField0_ |= 0x00000080;
                xRatioYk_ = input.readDouble();
                break;
              }
              case 73: {
                bitField0_ |= 0x00000100;
                fuseFlag_ = input.readDouble();
                break;
              }
              case 82: {
                com.google.protobuf.ByteString bs = input.readBytes();
                bitField0_ |= 0x00000200;
                fuseDataId_ = bs;
                break;
              }
              case 89: {
                bitField0_ |= 0x00000400;
                speed_ = input.readDouble();
                break;
              }
              case 98: {
                com.google.protobuf.ByteString bs = input.readBytes();
                bitField0_ |= 0x00000800;
                finishFlag_ = bs;
                break;
              }
              case 104: {
                bitField0_ |= 0x00001000;
                sTime_ = input.readInt64();
                break;
              }
              case 114: {
                com.google.protobuf.ByteString bs = input.readBytes();
                bitField0_ |= 0x00002000;
                detectTime_ = bs;
                break;
              }
              case 122: {
                com.google.protobuf.ByteString bs = input.readBytes();
                bitField0_ |= 0x00004000;
                pushTime_ = bs;
                break;
              }
              case 130: {
                com.google.protobuf.ByteString bs = input.readBytes();
                bitField0_ |= 0x00008000;
                szPlate_ = bs;
                break;
              }
              case 136: {
                bitField0_ |= 0x00010000;
                nPlateColor_ = input.readInt32();
                break;
              }
              case 144: {
                bitField0_ |= 0x00020000;
                nAxleNum_ = input.readInt32();
                break;
              }
              case 152: {
                bitField0_ |= 0x00040000;
                nVehicleType_ = input.readInt32();
                break;
              }
              case 162: {
                com.google.protobuf.ByteString bs = input.readBytes();
                bitField0_ |= 0x00080000;
                szPlatePic_ = bs;
                break;
              }
              case 170: {
                com.google.protobuf.ByteString bs = input.readBytes();
                bitField0_ |= 0x00100000;
                szSidePic_ = bs;
                break;
              }
              case 178: {
                com.google.protobuf.ByteString bs = input.readBytes();
                bitField0_ |= 0x00200000;
                szCarPic_ = bs;
                break;
              }
            }
          }
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(this);
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(this);
        } finally {
          this.unknownFields = unknownFields.build();
          makeExtensionsImmutable();
        }
      }
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.internal_static_com_bt_itswebsocket_domain_protobuf_RadarTwinBeans_RadarTwinBean_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.internal_static_com_bt_itswebsocket_domain_protobuf_RadarTwinBeans_RadarTwinBean_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean.class, com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean.Builder.class);
      }

      public static com.google.protobuf.Parser<RadarTwinBean> PARSER =
          new com.google.protobuf.AbstractParser<RadarTwinBean>() {
        public RadarTwinBean parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return new RadarTwinBean(input, extensionRegistry);
        }
      };

      @java.lang.Override
      public com.google.protobuf.Parser<RadarTwinBean> getParserForType() {
        return PARSER;
      }

      private int bitField0_;
      public static final int FACILITYNO_FIELD_NUMBER = 1;
      private java.lang.Object facilityNo_;
      /**
       * <code>required string facilityNo = 1;</code>
       */
      public boolean hasFacilityNo() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required string facilityNo = 1;</code>
       */
      public java.lang.String getFacilityNo() {
        java.lang.Object ref = facilityNo_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            facilityNo_ = s;
          }
          return s;
        }
      }
      /**
       * <code>required string facilityNo = 1;</code>
       */
      public com.google.protobuf.ByteString
          getFacilityNoBytes() {
        java.lang.Object ref = facilityNo_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          facilityNo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int LINE_FIELD_NUMBER = 2;
      private int line_;
      /**
       * <code>required int32 line = 2;</code>
       */
      public boolean hasLine() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 line = 2;</code>
       */
      public int getLine() {
        return line_;
      }

      public static final int IDLANE_FIELD_NUMBER = 3;
      private java.lang.Object idLane_;
      /**
       * <code>optional string idLane = 3;</code>
       */
      public boolean hasIdLane() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional string idLane = 3;</code>
       */
      public java.lang.String getIdLane() {
        java.lang.Object ref = idLane_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            idLane_ = s;
          }
          return s;
        }
      }
      /**
       * <code>optional string idLane = 3;</code>
       */
      public com.google.protobuf.ByteString
          getIdLaneBytes() {
        java.lang.Object ref = idLane_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          idLane_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int ID_FIELD_NUMBER = 4;
      private java.lang.Object id_;
      /**
       * <code>required string id = 4;</code>
       */
      public boolean hasId() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>required string id = 4;</code>
       */
      public java.lang.String getId() {
        java.lang.Object ref = id_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            id_ = s;
          }
          return s;
        }
      }
      /**
       * <code>required string id = 4;</code>
       */
      public com.google.protobuf.ByteString
          getIdBytes() {
        java.lang.Object ref = id_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          id_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int CLS_FIELD_NUMBER = 5;
      private java.lang.Object cls_;
      /**
       * <code>optional string cls = 5;</code>
       */
      public boolean hasCls() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional string cls = 5;</code>
       */
      public java.lang.String getCls() {
        java.lang.Object ref = cls_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cls_ = s;
          }
          return s;
        }
      }
      /**
       * <code>optional string cls = 5;</code>
       */
      public com.google.protobuf.ByteString
          getClsBytes() {
        java.lang.Object ref = cls_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cls_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int XRATIO_FIELD_NUMBER = 6;
      private double xRatio_;
      /**
       * <code>optional double xRatio = 6;</code>
       */
      public boolean hasXRatio() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional double xRatio = 6;</code>
       */
      public double getXRatio() {
        return xRatio_;
      }

      public static final int YRATIO_FIELD_NUMBER = 7;
      private double yRatio_;
      /**
       * <code>optional double yRatio = 7;</code>
       */
      public boolean hasYRatio() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional double yRatio = 7;</code>
       */
      public double getYRatio() {
        return yRatio_;
      }

      public static final int XRATIOYK_FIELD_NUMBER = 8;
      private double xRatioYk_;
      /**
       * <code>optional double xRatioYk = 8;</code>
       */
      public boolean hasXRatioYk() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <code>optional double xRatioYk = 8;</code>
       */
      public double getXRatioYk() {
        return xRatioYk_;
      }

      public static final int FUSEFLAG_FIELD_NUMBER = 9;
      private double fuseFlag_;
      /**
       * <code>optional double fuseFlag = 9;</code>
       */
      public boolean hasFuseFlag() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <code>optional double fuseFlag = 9;</code>
       */
      public double getFuseFlag() {
        return fuseFlag_;
      }

      public static final int FUSEDATAID_FIELD_NUMBER = 10;
      private java.lang.Object fuseDataId_;
      /**
       * <code>optional string fuseDataId = 10;</code>
       */
      public boolean hasFuseDataId() {
        return ((bitField0_ & 0x00000200) == 0x00000200);
      }
      /**
       * <code>optional string fuseDataId = 10;</code>
       */
      public java.lang.String getFuseDataId() {
        java.lang.Object ref = fuseDataId_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fuseDataId_ = s;
          }
          return s;
        }
      }
      /**
       * <code>optional string fuseDataId = 10;</code>
       */
      public com.google.protobuf.ByteString
          getFuseDataIdBytes() {
        java.lang.Object ref = fuseDataId_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fuseDataId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int SPEED_FIELD_NUMBER = 11;
      private double speed_;
      /**
       * <code>optional double speed = 11;</code>
       */
      public boolean hasSpeed() {
        return ((bitField0_ & 0x00000400) == 0x00000400);
      }
      /**
       * <code>optional double speed = 11;</code>
       */
      public double getSpeed() {
        return speed_;
      }

      public static final int FINISHFLAG_FIELD_NUMBER = 12;
      private java.lang.Object finishFlag_;
      /**
       * <code>optional string finishFlag = 12;</code>
       */
      public boolean hasFinishFlag() {
        return ((bitField0_ & 0x00000800) == 0x00000800);
      }
      /**
       * <code>optional string finishFlag = 12;</code>
       */
      public java.lang.String getFinishFlag() {
        java.lang.Object ref = finishFlag_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            finishFlag_ = s;
          }
          return s;
        }
      }
      /**
       * <code>optional string finishFlag = 12;</code>
       */
      public com.google.protobuf.ByteString
          getFinishFlagBytes() {
        java.lang.Object ref = finishFlag_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          finishFlag_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int STIME_FIELD_NUMBER = 13;
      private long sTime_;
      /**
       * <code>optional int64 sTime = 13;</code>
       */
      public boolean hasSTime() {
        return ((bitField0_ & 0x00001000) == 0x00001000);
      }
      /**
       * <code>optional int64 sTime = 13;</code>
       */
      public long getSTime() {
        return sTime_;
      }

      public static final int DETECTTIME_FIELD_NUMBER = 14;
      private java.lang.Object detectTime_;
      /**
       * <code>optional string detectTime = 14;</code>
       */
      public boolean hasDetectTime() {
        return ((bitField0_ & 0x00002000) == 0x00002000);
      }
      /**
       * <code>optional string detectTime = 14;</code>
       */
      public java.lang.String getDetectTime() {
        java.lang.Object ref = detectTime_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            detectTime_ = s;
          }
          return s;
        }
      }
      /**
       * <code>optional string detectTime = 14;</code>
       */
      public com.google.protobuf.ByteString
          getDetectTimeBytes() {
        java.lang.Object ref = detectTime_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          detectTime_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int PUSHTIME_FIELD_NUMBER = 15;
      private java.lang.Object pushTime_;
      /**
       * <code>optional string pushTime = 15;</code>
       */
      public boolean hasPushTime() {
        return ((bitField0_ & 0x00004000) == 0x00004000);
      }
      /**
       * <code>optional string pushTime = 15;</code>
       */
      public java.lang.String getPushTime() {
        java.lang.Object ref = pushTime_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            pushTime_ = s;
          }
          return s;
        }
      }
      /**
       * <code>optional string pushTime = 15;</code>
       */
      public com.google.protobuf.ByteString
          getPushTimeBytes() {
        java.lang.Object ref = pushTime_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          pushTime_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int SZPLATE_FIELD_NUMBER = 16;
      private java.lang.Object szPlate_;
      /**
       * <code>optional string szPlate = 16;</code>
       */
      public boolean hasSzPlate() {
        return ((bitField0_ & 0x00008000) == 0x00008000);
      }
      /**
       * <code>optional string szPlate = 16;</code>
       */
      public java.lang.String getSzPlate() {
        java.lang.Object ref = szPlate_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            szPlate_ = s;
          }
          return s;
        }
      }
      /**
       * <code>optional string szPlate = 16;</code>
       */
      public com.google.protobuf.ByteString
          getSzPlateBytes() {
        java.lang.Object ref = szPlate_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          szPlate_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int NPLATECOLOR_FIELD_NUMBER = 17;
      private int nPlateColor_;
      /**
       * <code>optional int32 nPlateColor = 17;</code>
       */
      public boolean hasNPlateColor() {
        return ((bitField0_ & 0x00010000) == 0x00010000);
      }
      /**
       * <code>optional int32 nPlateColor = 17;</code>
       */
      public int getNPlateColor() {
        return nPlateColor_;
      }

      public static final int NAXLENUM_FIELD_NUMBER = 18;
      private int nAxleNum_;
      /**
       * <code>optional int32 nAxleNum = 18;</code>
       */
      public boolean hasNAxleNum() {
        return ((bitField0_ & 0x00020000) == 0x00020000);
      }
      /**
       * <code>optional int32 nAxleNum = 18;</code>
       */
      public int getNAxleNum() {
        return nAxleNum_;
      }

      public static final int NVEHICLETYPE_FIELD_NUMBER = 19;
      private int nVehicleType_;
      /**
       * <code>optional int32 nVehicleType = 19;</code>
       */
      public boolean hasNVehicleType() {
        return ((bitField0_ & 0x00040000) == 0x00040000);
      }
      /**
       * <code>optional int32 nVehicleType = 19;</code>
       */
      public int getNVehicleType() {
        return nVehicleType_;
      }

      public static final int SZPLATEPIC_FIELD_NUMBER = 20;
      private java.lang.Object szPlatePic_;
      /**
       * <code>optional string szPlatePic = 20;</code>
       */
      public boolean hasSzPlatePic() {
        return ((bitField0_ & 0x00080000) == 0x00080000);
      }
      /**
       * <code>optional string szPlatePic = 20;</code>
       */
      public java.lang.String getSzPlatePic() {
        java.lang.Object ref = szPlatePic_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            szPlatePic_ = s;
          }
          return s;
        }
      }
      /**
       * <code>optional string szPlatePic = 20;</code>
       */
      public com.google.protobuf.ByteString
          getSzPlatePicBytes() {
        java.lang.Object ref = szPlatePic_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          szPlatePic_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int SZSIDEPIC_FIELD_NUMBER = 21;
      private java.lang.Object szSidePic_;
      /**
       * <code>optional string szSidePic = 21;</code>
       */
      public boolean hasSzSidePic() {
        return ((bitField0_ & 0x00100000) == 0x00100000);
      }
      /**
       * <code>optional string szSidePic = 21;</code>
       */
      public java.lang.String getSzSidePic() {
        java.lang.Object ref = szSidePic_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            szSidePic_ = s;
          }
          return s;
        }
      }
      /**
       * <code>optional string szSidePic = 21;</code>
       */
      public com.google.protobuf.ByteString
          getSzSidePicBytes() {
        java.lang.Object ref = szSidePic_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          szSidePic_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int SZCARPIC_FIELD_NUMBER = 22;
      private java.lang.Object szCarPic_;
      /**
       * <code>optional string szCarPic = 22;</code>
       */
      public boolean hasSzCarPic() {
        return ((bitField0_ & 0x00200000) == 0x00200000);
      }
      /**
       * <code>optional string szCarPic = 22;</code>
       */
      public java.lang.String getSzCarPic() {
        java.lang.Object ref = szCarPic_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            szCarPic_ = s;
          }
          return s;
        }
      }
      /**
       * <code>optional string szCarPic = 22;</code>
       */
      public com.google.protobuf.ByteString
          getSzCarPicBytes() {
        java.lang.Object ref = szCarPic_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          szCarPic_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      private void initFields() {
        facilityNo_ = "";
        line_ = 0;
        idLane_ = "";
        id_ = "";
        cls_ = "";
        xRatio_ = 0D;
        yRatio_ = 0D;
        xRatioYk_ = 0D;
        fuseFlag_ = 0D;
        fuseDataId_ = "";
        speed_ = 0D;
        finishFlag_ = "";
        sTime_ = 0L;
        detectTime_ = "";
        pushTime_ = "";
        szPlate_ = "";
        nPlateColor_ = 0;
        nAxleNum_ = 0;
        nVehicleType_ = 0;
        szPlatePic_ = "";
        szSidePic_ = "";
        szCarPic_ = "";
      }
      private byte memoizedIsInitialized = -1;
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        if (!hasFacilityNo()) {
          memoizedIsInitialized = 0;
          return false;
        }
        if (!hasLine()) {
          memoizedIsInitialized = 0;
          return false;
        }
        if (!hasId()) {
          memoizedIsInitialized = 0;
          return false;
        }
        memoizedIsInitialized = 1;
        return true;
      }

      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        getSerializedSize();
        if (((bitField0_ & 0x00000001) == 0x00000001)) {
          output.writeBytes(1, getFacilityNoBytes());
        }
        if (((bitField0_ & 0x00000002) == 0x00000002)) {
          output.writeInt32(2, line_);
        }
        if (((bitField0_ & 0x00000004) == 0x00000004)) {
          output.writeBytes(3, getIdLaneBytes());
        }
        if (((bitField0_ & 0x00000008) == 0x00000008)) {
          output.writeBytes(4, getIdBytes());
        }
        if (((bitField0_ & 0x00000010) == 0x00000010)) {
          output.writeBytes(5, getClsBytes());
        }
        if (((bitField0_ & 0x00000020) == 0x00000020)) {
          output.writeDouble(6, xRatio_);
        }
        if (((bitField0_ & 0x00000040) == 0x00000040)) {
          output.writeDouble(7, yRatio_);
        }
        if (((bitField0_ & 0x00000080) == 0x00000080)) {
          output.writeDouble(8, xRatioYk_);
        }
        if (((bitField0_ & 0x00000100) == 0x00000100)) {
          output.writeDouble(9, fuseFlag_);
        }
        if (((bitField0_ & 0x00000200) == 0x00000200)) {
          output.writeBytes(10, getFuseDataIdBytes());
        }
        if (((bitField0_ & 0x00000400) == 0x00000400)) {
          output.writeDouble(11, speed_);
        }
        if (((bitField0_ & 0x00000800) == 0x00000800)) {
          output.writeBytes(12, getFinishFlagBytes());
        }
        if (((bitField0_ & 0x00001000) == 0x00001000)) {
          output.writeInt64(13, sTime_);
        }
        if (((bitField0_ & 0x00002000) == 0x00002000)) {
          output.writeBytes(14, getDetectTimeBytes());
        }
        if (((bitField0_ & 0x00004000) == 0x00004000)) {
          output.writeBytes(15, getPushTimeBytes());
        }
        if (((bitField0_ & 0x00008000) == 0x00008000)) {
          output.writeBytes(16, getSzPlateBytes());
        }
        if (((bitField0_ & 0x00010000) == 0x00010000)) {
          output.writeInt32(17, nPlateColor_);
        }
        if (((bitField0_ & 0x00020000) == 0x00020000)) {
          output.writeInt32(18, nAxleNum_);
        }
        if (((bitField0_ & 0x00040000) == 0x00040000)) {
          output.writeInt32(19, nVehicleType_);
        }
        if (((bitField0_ & 0x00080000) == 0x00080000)) {
          output.writeBytes(20, getSzPlatePicBytes());
        }
        if (((bitField0_ & 0x00100000) == 0x00100000)) {
          output.writeBytes(21, getSzSidePicBytes());
        }
        if (((bitField0_ & 0x00200000) == 0x00200000)) {
          output.writeBytes(22, getSzCarPicBytes());
        }
        getUnknownFields().writeTo(output);
      }

      private int memoizedSerializedSize = -1;
      public int getSerializedSize() {
        int size = memoizedSerializedSize;
        if (size != -1) return size;

        size = 0;
        if (((bitField0_ & 0x00000001) == 0x00000001)) {
          size += com.google.protobuf.CodedOutputStream
            .computeBytesSize(1, getFacilityNoBytes());
        }
        if (((bitField0_ & 0x00000002) == 0x00000002)) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt32Size(2, line_);
        }
        if (((bitField0_ & 0x00000004) == 0x00000004)) {
          size += com.google.protobuf.CodedOutputStream
            .computeBytesSize(3, getIdLaneBytes());
        }
        if (((bitField0_ & 0x00000008) == 0x00000008)) {
          size += com.google.protobuf.CodedOutputStream
            .computeBytesSize(4, getIdBytes());
        }
        if (((bitField0_ & 0x00000010) == 0x00000010)) {
          size += com.google.protobuf.CodedOutputStream
            .computeBytesSize(5, getClsBytes());
        }
        if (((bitField0_ & 0x00000020) == 0x00000020)) {
          size += com.google.protobuf.CodedOutputStream
            .computeDoubleSize(6, xRatio_);
        }
        if (((bitField0_ & 0x00000040) == 0x00000040)) {
          size += com.google.protobuf.CodedOutputStream
            .computeDoubleSize(7, yRatio_);
        }
        if (((bitField0_ & 0x00000080) == 0x00000080)) {
          size += com.google.protobuf.CodedOutputStream
            .computeDoubleSize(8, xRatioYk_);
        }
        if (((bitField0_ & 0x00000100) == 0x00000100)) {
          size += com.google.protobuf.CodedOutputStream
            .computeDoubleSize(9, fuseFlag_);
        }
        if (((bitField0_ & 0x00000200) == 0x00000200)) {
          size += com.google.protobuf.CodedOutputStream
            .computeBytesSize(10, getFuseDataIdBytes());
        }
        if (((bitField0_ & 0x00000400) == 0x00000400)) {
          size += com.google.protobuf.CodedOutputStream
            .computeDoubleSize(11, speed_);
        }
        if (((bitField0_ & 0x00000800) == 0x00000800)) {
          size += com.google.protobuf.CodedOutputStream
            .computeBytesSize(12, getFinishFlagBytes());
        }
        if (((bitField0_ & 0x00001000) == 0x00001000)) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt64Size(13, sTime_);
        }
        if (((bitField0_ & 0x00002000) == 0x00002000)) {
          size += com.google.protobuf.CodedOutputStream
            .computeBytesSize(14, getDetectTimeBytes());
        }
        if (((bitField0_ & 0x00004000) == 0x00004000)) {
          size += com.google.protobuf.CodedOutputStream
            .computeBytesSize(15, getPushTimeBytes());
        }
        if (((bitField0_ & 0x00008000) == 0x00008000)) {
          size += com.google.protobuf.CodedOutputStream
            .computeBytesSize(16, getSzPlateBytes());
        }
        if (((bitField0_ & 0x00010000) == 0x00010000)) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt32Size(17, nPlateColor_);
        }
        if (((bitField0_ & 0x00020000) == 0x00020000)) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt32Size(18, nAxleNum_);
        }
        if (((bitField0_ & 0x00040000) == 0x00040000)) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt32Size(19, nVehicleType_);
        }
        if (((bitField0_ & 0x00080000) == 0x00080000)) {
          size += com.google.protobuf.CodedOutputStream
            .computeBytesSize(20, getSzPlatePicBytes());
        }
        if (((bitField0_ & 0x00100000) == 0x00100000)) {
          size += com.google.protobuf.CodedOutputStream
            .computeBytesSize(21, getSzSidePicBytes());
        }
        if (((bitField0_ & 0x00200000) == 0x00200000)) {
          size += com.google.protobuf.CodedOutputStream
            .computeBytesSize(22, getSzCarPicBytes());
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSerializedSize = size;
        return size;
      }

      private static final long serialVersionUID = 0L;
      @java.lang.Override
      protected java.lang.Object writeReplace()
          throws java.io.ObjectStreamException {
        return super.writeReplace();
      }

      public static com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return PARSER.parseFrom(input);
      }
      public static com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return PARSER.parseFrom(input, extensionRegistry);
      }
      public static com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return PARSER.parseDelimitedFrom(input);
      }
      public static com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return PARSER.parseDelimitedFrom(input, extensionRegistry);
      }
      public static com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return PARSER.parseFrom(input);
      }
      public static com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return PARSER.parseFrom(input, extensionRegistry);
      }

      public static Builder newBuilder() { return Builder.create(); }
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder(com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean prototype) {
        return newBuilder().mergeFrom(prototype);
      }
      public Builder toBuilder() { return newBuilder(this); }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * Protobuf type {@code com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessage.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean)
          com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBeanOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.internal_static_com_bt_itswebsocket_domain_protobuf_RadarTwinBeans_RadarTwinBean_descriptor;
        }

        protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.internal_static_com_bt_itswebsocket_domain_protobuf_RadarTwinBeans_RadarTwinBean_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean.class, com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean.Builder.class);
        }

        // Construct using com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean.newBuilder()
        private Builder() {
          maybeForceBuilderInitialization();
        }

        private Builder(
            com.google.protobuf.GeneratedMessage.BuilderParent parent) {
          super(parent);
          maybeForceBuilderInitialization();
        }
        private void maybeForceBuilderInitialization() {
          if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          }
        }
        private static Builder create() {
          return new Builder();
        }

        public Builder clear() {
          super.clear();
          facilityNo_ = "";
          bitField0_ = (bitField0_ & ~0x00000001);
          line_ = 0;
          bitField0_ = (bitField0_ & ~0x00000002);
          idLane_ = "";
          bitField0_ = (bitField0_ & ~0x00000004);
          id_ = "";
          bitField0_ = (bitField0_ & ~0x00000008);
          cls_ = "";
          bitField0_ = (bitField0_ & ~0x00000010);
          xRatio_ = 0D;
          bitField0_ = (bitField0_ & ~0x00000020);
          yRatio_ = 0D;
          bitField0_ = (bitField0_ & ~0x00000040);
          xRatioYk_ = 0D;
          bitField0_ = (bitField0_ & ~0x00000080);
          fuseFlag_ = 0D;
          bitField0_ = (bitField0_ & ~0x00000100);
          fuseDataId_ = "";
          bitField0_ = (bitField0_ & ~0x00000200);
          speed_ = 0D;
          bitField0_ = (bitField0_ & ~0x00000400);
          finishFlag_ = "";
          bitField0_ = (bitField0_ & ~0x00000800);
          sTime_ = 0L;
          bitField0_ = (bitField0_ & ~0x00001000);
          detectTime_ = "";
          bitField0_ = (bitField0_ & ~0x00002000);
          pushTime_ = "";
          bitField0_ = (bitField0_ & ~0x00004000);
          szPlate_ = "";
          bitField0_ = (bitField0_ & ~0x00008000);
          nPlateColor_ = 0;
          bitField0_ = (bitField0_ & ~0x00010000);
          nAxleNum_ = 0;
          bitField0_ = (bitField0_ & ~0x00020000);
          nVehicleType_ = 0;
          bitField0_ = (bitField0_ & ~0x00040000);
          szPlatePic_ = "";
          bitField0_ = (bitField0_ & ~0x00080000);
          szSidePic_ = "";
          bitField0_ = (bitField0_ & ~0x00100000);
          szCarPic_ = "";
          bitField0_ = (bitField0_ & ~0x00200000);
          return this;
        }

        public Builder clone() {
          return create().mergeFrom(buildPartial());
        }

        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.internal_static_com_bt_itswebsocket_domain_protobuf_RadarTwinBeans_RadarTwinBean_descriptor;
        }

        public com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean getDefaultInstanceForType() {
          return com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean.getDefaultInstance();
        }

        public com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean build() {
          com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        public com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean buildPartial() {
          com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean result = new com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean(this);
          int from_bitField0_ = bitField0_;
          int to_bitField0_ = 0;
          if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
            to_bitField0_ |= 0x00000001;
          }
          result.facilityNo_ = facilityNo_;
          if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
            to_bitField0_ |= 0x00000002;
          }
          result.line_ = line_;
          if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
            to_bitField0_ |= 0x00000004;
          }
          result.idLane_ = idLane_;
          if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
            to_bitField0_ |= 0x00000008;
          }
          result.id_ = id_;
          if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
            to_bitField0_ |= 0x00000010;
          }
          result.cls_ = cls_;
          if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
            to_bitField0_ |= 0x00000020;
          }
          result.xRatio_ = xRatio_;
          if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
            to_bitField0_ |= 0x00000040;
          }
          result.yRatio_ = yRatio_;
          if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
            to_bitField0_ |= 0x00000080;
          }
          result.xRatioYk_ = xRatioYk_;
          if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
            to_bitField0_ |= 0x00000100;
          }
          result.fuseFlag_ = fuseFlag_;
          if (((from_bitField0_ & 0x00000200) == 0x00000200)) {
            to_bitField0_ |= 0x00000200;
          }
          result.fuseDataId_ = fuseDataId_;
          if (((from_bitField0_ & 0x00000400) == 0x00000400)) {
            to_bitField0_ |= 0x00000400;
          }
          result.speed_ = speed_;
          if (((from_bitField0_ & 0x00000800) == 0x00000800)) {
            to_bitField0_ |= 0x00000800;
          }
          result.finishFlag_ = finishFlag_;
          if (((from_bitField0_ & 0x00001000) == 0x00001000)) {
            to_bitField0_ |= 0x00001000;
          }
          result.sTime_ = sTime_;
          if (((from_bitField0_ & 0x00002000) == 0x00002000)) {
            to_bitField0_ |= 0x00002000;
          }
          result.detectTime_ = detectTime_;
          if (((from_bitField0_ & 0x00004000) == 0x00004000)) {
            to_bitField0_ |= 0x00004000;
          }
          result.pushTime_ = pushTime_;
          if (((from_bitField0_ & 0x00008000) == 0x00008000)) {
            to_bitField0_ |= 0x00008000;
          }
          result.szPlate_ = szPlate_;
          if (((from_bitField0_ & 0x00010000) == 0x00010000)) {
            to_bitField0_ |= 0x00010000;
          }
          result.nPlateColor_ = nPlateColor_;
          if (((from_bitField0_ & 0x00020000) == 0x00020000)) {
            to_bitField0_ |= 0x00020000;
          }
          result.nAxleNum_ = nAxleNum_;
          if (((from_bitField0_ & 0x00040000) == 0x00040000)) {
            to_bitField0_ |= 0x00040000;
          }
          result.nVehicleType_ = nVehicleType_;
          if (((from_bitField0_ & 0x00080000) == 0x00080000)) {
            to_bitField0_ |= 0x00080000;
          }
          result.szPlatePic_ = szPlatePic_;
          if (((from_bitField0_ & 0x00100000) == 0x00100000)) {
            to_bitField0_ |= 0x00100000;
          }
          result.szSidePic_ = szSidePic_;
          if (((from_bitField0_ & 0x00200000) == 0x00200000)) {
            to_bitField0_ |= 0x00200000;
          }
          result.szCarPic_ = szCarPic_;
          result.bitField0_ = to_bitField0_;
          onBuilt();
          return result;
        }

        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean) {
            return mergeFrom((com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean other) {
          if (other == com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean.getDefaultInstance()) return this;
          if (other.hasFacilityNo()) {
            bitField0_ |= 0x00000001;
            facilityNo_ = other.facilityNo_;
            onChanged();
          }
          if (other.hasLine()) {
            setLine(other.getLine());
          }
          if (other.hasIdLane()) {
            bitField0_ |= 0x00000004;
            idLane_ = other.idLane_;
            onChanged();
          }
          if (other.hasId()) {
            bitField0_ |= 0x00000008;
            id_ = other.id_;
            onChanged();
          }
          if (other.hasCls()) {
            bitField0_ |= 0x00000010;
            cls_ = other.cls_;
            onChanged();
          }
          if (other.hasXRatio()) {
            setXRatio(other.getXRatio());
          }
          if (other.hasYRatio()) {
            setYRatio(other.getYRatio());
          }
          if (other.hasXRatioYk()) {
            setXRatioYk(other.getXRatioYk());
          }
          if (other.hasFuseFlag()) {
            setFuseFlag(other.getFuseFlag());
          }
          if (other.hasFuseDataId()) {
            bitField0_ |= 0x00000200;
            fuseDataId_ = other.fuseDataId_;
            onChanged();
          }
          if (other.hasSpeed()) {
            setSpeed(other.getSpeed());
          }
          if (other.hasFinishFlag()) {
            bitField0_ |= 0x00000800;
            finishFlag_ = other.finishFlag_;
            onChanged();
          }
          if (other.hasSTime()) {
            setSTime(other.getSTime());
          }
          if (other.hasDetectTime()) {
            bitField0_ |= 0x00002000;
            detectTime_ = other.detectTime_;
            onChanged();
          }
          if (other.hasPushTime()) {
            bitField0_ |= 0x00004000;
            pushTime_ = other.pushTime_;
            onChanged();
          }
          if (other.hasSzPlate()) {
            bitField0_ |= 0x00008000;
            szPlate_ = other.szPlate_;
            onChanged();
          }
          if (other.hasNPlateColor()) {
            setNPlateColor(other.getNPlateColor());
          }
          if (other.hasNAxleNum()) {
            setNAxleNum(other.getNAxleNum());
          }
          if (other.hasNVehicleType()) {
            setNVehicleType(other.getNVehicleType());
          }
          if (other.hasSzPlatePic()) {
            bitField0_ |= 0x00080000;
            szPlatePic_ = other.szPlatePic_;
            onChanged();
          }
          if (other.hasSzSidePic()) {
            bitField0_ |= 0x00100000;
            szSidePic_ = other.szSidePic_;
            onChanged();
          }
          if (other.hasSzCarPic()) {
            bitField0_ |= 0x00200000;
            szCarPic_ = other.szCarPic_;
            onChanged();
          }
          this.mergeUnknownFields(other.getUnknownFields());
          return this;
        }

        public final boolean isInitialized() {
          if (!hasFacilityNo()) {
            
            return false;
          }
          if (!hasLine()) {
            
            return false;
          }
          if (!hasId()) {
            
            return false;
          }
          return true;
        }

        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean parsedMessage = null;
          try {
            parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            parsedMessage = (com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean) e.getUnfinishedMessage();
            throw e;
          } finally {
            if (parsedMessage != null) {
              mergeFrom(parsedMessage);
            }
          }
          return this;
        }
        private int bitField0_;

        private java.lang.Object facilityNo_ = "";
        /**
         * <code>required string facilityNo = 1;</code>
         */
        public boolean hasFacilityNo() {
          return ((bitField0_ & 0x00000001) == 0x00000001);
        }
        /**
         * <code>required string facilityNo = 1;</code>
         */
        public java.lang.String getFacilityNo() {
          java.lang.Object ref = facilityNo_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            if (bs.isValidUtf8()) {
              facilityNo_ = s;
            }
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>required string facilityNo = 1;</code>
         */
        public com.google.protobuf.ByteString
            getFacilityNoBytes() {
          java.lang.Object ref = facilityNo_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            facilityNo_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>required string facilityNo = 1;</code>
         */
        public Builder setFacilityNo(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
          facilityNo_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>required string facilityNo = 1;</code>
         */
        public Builder clearFacilityNo() {
          bitField0_ = (bitField0_ & ~0x00000001);
          facilityNo_ = getDefaultInstance().getFacilityNo();
          onChanged();
          return this;
        }
        /**
         * <code>required string facilityNo = 1;</code>
         */
        public Builder setFacilityNoBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
          facilityNo_ = value;
          onChanged();
          return this;
        }

        private int line_ ;
        /**
         * <code>required int32 line = 2;</code>
         */
        public boolean hasLine() {
          return ((bitField0_ & 0x00000002) == 0x00000002);
        }
        /**
         * <code>required int32 line = 2;</code>
         */
        public int getLine() {
          return line_;
        }
        /**
         * <code>required int32 line = 2;</code>
         */
        public Builder setLine(int value) {
          bitField0_ |= 0x00000002;
          line_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>required int32 line = 2;</code>
         */
        public Builder clearLine() {
          bitField0_ = (bitField0_ & ~0x00000002);
          line_ = 0;
          onChanged();
          return this;
        }

        private java.lang.Object idLane_ = "";
        /**
         * <code>optional string idLane = 3;</code>
         */
        public boolean hasIdLane() {
          return ((bitField0_ & 0x00000004) == 0x00000004);
        }
        /**
         * <code>optional string idLane = 3;</code>
         */
        public java.lang.String getIdLane() {
          java.lang.Object ref = idLane_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            if (bs.isValidUtf8()) {
              idLane_ = s;
            }
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>optional string idLane = 3;</code>
         */
        public com.google.protobuf.ByteString
            getIdLaneBytes() {
          java.lang.Object ref = idLane_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            idLane_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>optional string idLane = 3;</code>
         */
        public Builder setIdLane(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
          idLane_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>optional string idLane = 3;</code>
         */
        public Builder clearIdLane() {
          bitField0_ = (bitField0_ & ~0x00000004);
          idLane_ = getDefaultInstance().getIdLane();
          onChanged();
          return this;
        }
        /**
         * <code>optional string idLane = 3;</code>
         */
        public Builder setIdLaneBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
          idLane_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object id_ = "";
        /**
         * <code>required string id = 4;</code>
         */
        public boolean hasId() {
          return ((bitField0_ & 0x00000008) == 0x00000008);
        }
        /**
         * <code>required string id = 4;</code>
         */
        public java.lang.String getId() {
          java.lang.Object ref = id_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            if (bs.isValidUtf8()) {
              id_ = s;
            }
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>required string id = 4;</code>
         */
        public com.google.protobuf.ByteString
            getIdBytes() {
          java.lang.Object ref = id_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            id_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>required string id = 4;</code>
         */
        public Builder setId(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
          id_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>required string id = 4;</code>
         */
        public Builder clearId() {
          bitField0_ = (bitField0_ & ~0x00000008);
          id_ = getDefaultInstance().getId();
          onChanged();
          return this;
        }
        /**
         * <code>required string id = 4;</code>
         */
        public Builder setIdBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
          id_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object cls_ = "";
        /**
         * <code>optional string cls = 5;</code>
         */
        public boolean hasCls() {
          return ((bitField0_ & 0x00000010) == 0x00000010);
        }
        /**
         * <code>optional string cls = 5;</code>
         */
        public java.lang.String getCls() {
          java.lang.Object ref = cls_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            if (bs.isValidUtf8()) {
              cls_ = s;
            }
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>optional string cls = 5;</code>
         */
        public com.google.protobuf.ByteString
            getClsBytes() {
          java.lang.Object ref = cls_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            cls_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>optional string cls = 5;</code>
         */
        public Builder setCls(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
          cls_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>optional string cls = 5;</code>
         */
        public Builder clearCls() {
          bitField0_ = (bitField0_ & ~0x00000010);
          cls_ = getDefaultInstance().getCls();
          onChanged();
          return this;
        }
        /**
         * <code>optional string cls = 5;</code>
         */
        public Builder setClsBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
          cls_ = value;
          onChanged();
          return this;
        }

        private double xRatio_ ;
        /**
         * <code>optional double xRatio = 6;</code>
         */
        public boolean hasXRatio() {
          return ((bitField0_ & 0x00000020) == 0x00000020);
        }
        /**
         * <code>optional double xRatio = 6;</code>
         */
        public double getXRatio() {
          return xRatio_;
        }
        /**
         * <code>optional double xRatio = 6;</code>
         */
        public Builder setXRatio(double value) {
          bitField0_ |= 0x00000020;
          xRatio_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>optional double xRatio = 6;</code>
         */
        public Builder clearXRatio() {
          bitField0_ = (bitField0_ & ~0x00000020);
          xRatio_ = 0D;
          onChanged();
          return this;
        }

        private double yRatio_ ;
        /**
         * <code>optional double yRatio = 7;</code>
         */
        public boolean hasYRatio() {
          return ((bitField0_ & 0x00000040) == 0x00000040);
        }
        /**
         * <code>optional double yRatio = 7;</code>
         */
        public double getYRatio() {
          return yRatio_;
        }
        /**
         * <code>optional double yRatio = 7;</code>
         */
        public Builder setYRatio(double value) {
          bitField0_ |= 0x00000040;
          yRatio_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>optional double yRatio = 7;</code>
         */
        public Builder clearYRatio() {
          bitField0_ = (bitField0_ & ~0x00000040);
          yRatio_ = 0D;
          onChanged();
          return this;
        }

        private double xRatioYk_ ;
        /**
         * <code>optional double xRatioYk = 8;</code>
         */
        public boolean hasXRatioYk() {
          return ((bitField0_ & 0x00000080) == 0x00000080);
        }
        /**
         * <code>optional double xRatioYk = 8;</code>
         */
        public double getXRatioYk() {
          return xRatioYk_;
        }
        /**
         * <code>optional double xRatioYk = 8;</code>
         */
        public Builder setXRatioYk(double value) {
          bitField0_ |= 0x00000080;
          xRatioYk_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>optional double xRatioYk = 8;</code>
         */
        public Builder clearXRatioYk() {
          bitField0_ = (bitField0_ & ~0x00000080);
          xRatioYk_ = 0D;
          onChanged();
          return this;
        }

        private double fuseFlag_ ;
        /**
         * <code>optional double fuseFlag = 9;</code>
         */
        public boolean hasFuseFlag() {
          return ((bitField0_ & 0x00000100) == 0x00000100);
        }
        /**
         * <code>optional double fuseFlag = 9;</code>
         */
        public double getFuseFlag() {
          return fuseFlag_;
        }
        /**
         * <code>optional double fuseFlag = 9;</code>
         */
        public Builder setFuseFlag(double value) {
          bitField0_ |= 0x00000100;
          fuseFlag_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>optional double fuseFlag = 9;</code>
         */
        public Builder clearFuseFlag() {
          bitField0_ = (bitField0_ & ~0x00000100);
          fuseFlag_ = 0D;
          onChanged();
          return this;
        }

        private java.lang.Object fuseDataId_ = "";
        /**
         * <code>optional string fuseDataId = 10;</code>
         */
        public boolean hasFuseDataId() {
          return ((bitField0_ & 0x00000200) == 0x00000200);
        }
        /**
         * <code>optional string fuseDataId = 10;</code>
         */
        public java.lang.String getFuseDataId() {
          java.lang.Object ref = fuseDataId_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            if (bs.isValidUtf8()) {
              fuseDataId_ = s;
            }
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>optional string fuseDataId = 10;</code>
         */
        public com.google.protobuf.ByteString
            getFuseDataIdBytes() {
          java.lang.Object ref = fuseDataId_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            fuseDataId_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>optional string fuseDataId = 10;</code>
         */
        public Builder setFuseDataId(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000200;
          fuseDataId_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>optional string fuseDataId = 10;</code>
         */
        public Builder clearFuseDataId() {
          bitField0_ = (bitField0_ & ~0x00000200);
          fuseDataId_ = getDefaultInstance().getFuseDataId();
          onChanged();
          return this;
        }
        /**
         * <code>optional string fuseDataId = 10;</code>
         */
        public Builder setFuseDataIdBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000200;
          fuseDataId_ = value;
          onChanged();
          return this;
        }

        private double speed_ ;
        /**
         * <code>optional double speed = 11;</code>
         */
        public boolean hasSpeed() {
          return ((bitField0_ & 0x00000400) == 0x00000400);
        }
        /**
         * <code>optional double speed = 11;</code>
         */
        public double getSpeed() {
          return speed_;
        }
        /**
         * <code>optional double speed = 11;</code>
         */
        public Builder setSpeed(double value) {
          bitField0_ |= 0x00000400;
          speed_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>optional double speed = 11;</code>
         */
        public Builder clearSpeed() {
          bitField0_ = (bitField0_ & ~0x00000400);
          speed_ = 0D;
          onChanged();
          return this;
        }

        private java.lang.Object finishFlag_ = "";
        /**
         * <code>optional string finishFlag = 12;</code>
         */
        public boolean hasFinishFlag() {
          return ((bitField0_ & 0x00000800) == 0x00000800);
        }
        /**
         * <code>optional string finishFlag = 12;</code>
         */
        public java.lang.String getFinishFlag() {
          java.lang.Object ref = finishFlag_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            if (bs.isValidUtf8()) {
              finishFlag_ = s;
            }
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>optional string finishFlag = 12;</code>
         */
        public com.google.protobuf.ByteString
            getFinishFlagBytes() {
          java.lang.Object ref = finishFlag_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            finishFlag_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>optional string finishFlag = 12;</code>
         */
        public Builder setFinishFlag(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000800;
          finishFlag_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>optional string finishFlag = 12;</code>
         */
        public Builder clearFinishFlag() {
          bitField0_ = (bitField0_ & ~0x00000800);
          finishFlag_ = getDefaultInstance().getFinishFlag();
          onChanged();
          return this;
        }
        /**
         * <code>optional string finishFlag = 12;</code>
         */
        public Builder setFinishFlagBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000800;
          finishFlag_ = value;
          onChanged();
          return this;
        }

        private long sTime_ ;
        /**
         * <code>optional int64 sTime = 13;</code>
         */
        public boolean hasSTime() {
          return ((bitField0_ & 0x00001000) == 0x00001000);
        }
        /**
         * <code>optional int64 sTime = 13;</code>
         */
        public long getSTime() {
          return sTime_;
        }
        /**
         * <code>optional int64 sTime = 13;</code>
         */
        public Builder setSTime(long value) {
          bitField0_ |= 0x00001000;
          sTime_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>optional int64 sTime = 13;</code>
         */
        public Builder clearSTime() {
          bitField0_ = (bitField0_ & ~0x00001000);
          sTime_ = 0L;
          onChanged();
          return this;
        }

        private java.lang.Object detectTime_ = "";
        /**
         * <code>optional string detectTime = 14;</code>
         */
        public boolean hasDetectTime() {
          return ((bitField0_ & 0x00002000) == 0x00002000);
        }
        /**
         * <code>optional string detectTime = 14;</code>
         */
        public java.lang.String getDetectTime() {
          java.lang.Object ref = detectTime_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            if (bs.isValidUtf8()) {
              detectTime_ = s;
            }
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>optional string detectTime = 14;</code>
         */
        public com.google.protobuf.ByteString
            getDetectTimeBytes() {
          java.lang.Object ref = detectTime_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            detectTime_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>optional string detectTime = 14;</code>
         */
        public Builder setDetectTime(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00002000;
          detectTime_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>optional string detectTime = 14;</code>
         */
        public Builder clearDetectTime() {
          bitField0_ = (bitField0_ & ~0x00002000);
          detectTime_ = getDefaultInstance().getDetectTime();
          onChanged();
          return this;
        }
        /**
         * <code>optional string detectTime = 14;</code>
         */
        public Builder setDetectTimeBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00002000;
          detectTime_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object pushTime_ = "";
        /**
         * <code>optional string pushTime = 15;</code>
         */
        public boolean hasPushTime() {
          return ((bitField0_ & 0x00004000) == 0x00004000);
        }
        /**
         * <code>optional string pushTime = 15;</code>
         */
        public java.lang.String getPushTime() {
          java.lang.Object ref = pushTime_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            if (bs.isValidUtf8()) {
              pushTime_ = s;
            }
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>optional string pushTime = 15;</code>
         */
        public com.google.protobuf.ByteString
            getPushTimeBytes() {
          java.lang.Object ref = pushTime_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            pushTime_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>optional string pushTime = 15;</code>
         */
        public Builder setPushTime(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00004000;
          pushTime_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>optional string pushTime = 15;</code>
         */
        public Builder clearPushTime() {
          bitField0_ = (bitField0_ & ~0x00004000);
          pushTime_ = getDefaultInstance().getPushTime();
          onChanged();
          return this;
        }
        /**
         * <code>optional string pushTime = 15;</code>
         */
        public Builder setPushTimeBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00004000;
          pushTime_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object szPlate_ = "";
        /**
         * <code>optional string szPlate = 16;</code>
         */
        public boolean hasSzPlate() {
          return ((bitField0_ & 0x00008000) == 0x00008000);
        }
        /**
         * <code>optional string szPlate = 16;</code>
         */
        public java.lang.String getSzPlate() {
          java.lang.Object ref = szPlate_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            if (bs.isValidUtf8()) {
              szPlate_ = s;
            }
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>optional string szPlate = 16;</code>
         */
        public com.google.protobuf.ByteString
            getSzPlateBytes() {
          java.lang.Object ref = szPlate_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            szPlate_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>optional string szPlate = 16;</code>
         */
        public Builder setSzPlate(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00008000;
          szPlate_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>optional string szPlate = 16;</code>
         */
        public Builder clearSzPlate() {
          bitField0_ = (bitField0_ & ~0x00008000);
          szPlate_ = getDefaultInstance().getSzPlate();
          onChanged();
          return this;
        }
        /**
         * <code>optional string szPlate = 16;</code>
         */
        public Builder setSzPlateBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00008000;
          szPlate_ = value;
          onChanged();
          return this;
        }

        private int nPlateColor_ ;
        /**
         * <code>optional int32 nPlateColor = 17;</code>
         */
        public boolean hasNPlateColor() {
          return ((bitField0_ & 0x00010000) == 0x00010000);
        }
        /**
         * <code>optional int32 nPlateColor = 17;</code>
         */
        public int getNPlateColor() {
          return nPlateColor_;
        }
        /**
         * <code>optional int32 nPlateColor = 17;</code>
         */
        public Builder setNPlateColor(int value) {
          bitField0_ |= 0x00010000;
          nPlateColor_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>optional int32 nPlateColor = 17;</code>
         */
        public Builder clearNPlateColor() {
          bitField0_ = (bitField0_ & ~0x00010000);
          nPlateColor_ = 0;
          onChanged();
          return this;
        }

        private int nAxleNum_ ;
        /**
         * <code>optional int32 nAxleNum = 18;</code>
         */
        public boolean hasNAxleNum() {
          return ((bitField0_ & 0x00020000) == 0x00020000);
        }
        /**
         * <code>optional int32 nAxleNum = 18;</code>
         */
        public int getNAxleNum() {
          return nAxleNum_;
        }
        /**
         * <code>optional int32 nAxleNum = 18;</code>
         */
        public Builder setNAxleNum(int value) {
          bitField0_ |= 0x00020000;
          nAxleNum_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>optional int32 nAxleNum = 18;</code>
         */
        public Builder clearNAxleNum() {
          bitField0_ = (bitField0_ & ~0x00020000);
          nAxleNum_ = 0;
          onChanged();
          return this;
        }

        private int nVehicleType_ ;
        /**
         * <code>optional int32 nVehicleType = 19;</code>
         */
        public boolean hasNVehicleType() {
          return ((bitField0_ & 0x00040000) == 0x00040000);
        }
        /**
         * <code>optional int32 nVehicleType = 19;</code>
         */
        public int getNVehicleType() {
          return nVehicleType_;
        }
        /**
         * <code>optional int32 nVehicleType = 19;</code>
         */
        public Builder setNVehicleType(int value) {
          bitField0_ |= 0x00040000;
          nVehicleType_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>optional int32 nVehicleType = 19;</code>
         */
        public Builder clearNVehicleType() {
          bitField0_ = (bitField0_ & ~0x00040000);
          nVehicleType_ = 0;
          onChanged();
          return this;
        }

        private java.lang.Object szPlatePic_ = "";
        /**
         * <code>optional string szPlatePic = 20;</code>
         */
        public boolean hasSzPlatePic() {
          return ((bitField0_ & 0x00080000) == 0x00080000);
        }
        /**
         * <code>optional string szPlatePic = 20;</code>
         */
        public java.lang.String getSzPlatePic() {
          java.lang.Object ref = szPlatePic_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            if (bs.isValidUtf8()) {
              szPlatePic_ = s;
            }
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>optional string szPlatePic = 20;</code>
         */
        public com.google.protobuf.ByteString
            getSzPlatePicBytes() {
          java.lang.Object ref = szPlatePic_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            szPlatePic_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>optional string szPlatePic = 20;</code>
         */
        public Builder setSzPlatePic(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00080000;
          szPlatePic_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>optional string szPlatePic = 20;</code>
         */
        public Builder clearSzPlatePic() {
          bitField0_ = (bitField0_ & ~0x00080000);
          szPlatePic_ = getDefaultInstance().getSzPlatePic();
          onChanged();
          return this;
        }
        /**
         * <code>optional string szPlatePic = 20;</code>
         */
        public Builder setSzPlatePicBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00080000;
          szPlatePic_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object szSidePic_ = "";
        /**
         * <code>optional string szSidePic = 21;</code>
         */
        public boolean hasSzSidePic() {
          return ((bitField0_ & 0x00100000) == 0x00100000);
        }
        /**
         * <code>optional string szSidePic = 21;</code>
         */
        public java.lang.String getSzSidePic() {
          java.lang.Object ref = szSidePic_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            if (bs.isValidUtf8()) {
              szSidePic_ = s;
            }
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>optional string szSidePic = 21;</code>
         */
        public com.google.protobuf.ByteString
            getSzSidePicBytes() {
          java.lang.Object ref = szSidePic_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            szSidePic_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>optional string szSidePic = 21;</code>
         */
        public Builder setSzSidePic(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00100000;
          szSidePic_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>optional string szSidePic = 21;</code>
         */
        public Builder clearSzSidePic() {
          bitField0_ = (bitField0_ & ~0x00100000);
          szSidePic_ = getDefaultInstance().getSzSidePic();
          onChanged();
          return this;
        }
        /**
         * <code>optional string szSidePic = 21;</code>
         */
        public Builder setSzSidePicBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00100000;
          szSidePic_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object szCarPic_ = "";
        /**
         * <code>optional string szCarPic = 22;</code>
         */
        public boolean hasSzCarPic() {
          return ((bitField0_ & 0x00200000) == 0x00200000);
        }
        /**
         * <code>optional string szCarPic = 22;</code>
         */
        public java.lang.String getSzCarPic() {
          java.lang.Object ref = szCarPic_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            if (bs.isValidUtf8()) {
              szCarPic_ = s;
            }
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>optional string szCarPic = 22;</code>
         */
        public com.google.protobuf.ByteString
            getSzCarPicBytes() {
          java.lang.Object ref = szCarPic_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            szCarPic_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>optional string szCarPic = 22;</code>
         */
        public Builder setSzCarPic(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00200000;
          szCarPic_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>optional string szCarPic = 22;</code>
         */
        public Builder clearSzCarPic() {
          bitField0_ = (bitField0_ & ~0x00200000);
          szCarPic_ = getDefaultInstance().getSzCarPic();
          onChanged();
          return this;
        }
        /**
         * <code>optional string szCarPic = 22;</code>
         */
        public Builder setSzCarPicBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00200000;
          szCarPic_ = value;
          onChanged();
          return this;
        }

        // @@protoc_insertion_point(builder_scope:com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean)
      }

      static {
        defaultInstance = new RadarTwinBean(true);
        defaultInstance.initFields();
      }

      // @@protoc_insertion_point(class_scope:com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean)
    }

    private int bitField0_;
    public static final int WEBSOCKETTYPE_FIELD_NUMBER = 1;
    private java.lang.Object websocketType_;
    /**
     * <code>required string websocketType = 1;</code>
     */
    public boolean hasWebsocketType() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required string websocketType = 1;</code>
     */
    public java.lang.String getWebsocketType() {
      java.lang.Object ref = websocketType_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          websocketType_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string websocketType = 1;</code>
     */
    public com.google.protobuf.ByteString
        getWebsocketTypeBytes() {
      java.lang.Object ref = websocketType_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        websocketType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int MESSAGE_FIELD_NUMBER = 2;
    private java.util.List<com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean> message_;
    /**
     * <code>repeated .com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean message = 2;</code>
     */
    public java.util.List<com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean> getMessageList() {
      return message_;
    }
    /**
     * <code>repeated .com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean message = 2;</code>
     */
    public java.util.List<? extends com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBeanOrBuilder> 
        getMessageOrBuilderList() {
      return message_;
    }
    /**
     * <code>repeated .com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean message = 2;</code>
     */
    public int getMessageCount() {
      return message_.size();
    }
    /**
     * <code>repeated .com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean message = 2;</code>
     */
    public com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean getMessage(int index) {
      return message_.get(index);
    }
    /**
     * <code>repeated .com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean message = 2;</code>
     */
    public com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBeanOrBuilder getMessageOrBuilder(
        int index) {
      return message_.get(index);
    }

    private void initFields() {
      websocketType_ = "";
      message_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      if (!hasWebsocketType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      for (int i = 0; i < getMessageCount(); i++) {
        if (!getMessage(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getWebsocketTypeBytes());
      }
      for (int i = 0; i < message_.size(); i++) {
        output.writeMessage(2, message_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getWebsocketTypeBytes());
      }
      for (int i = 0; i < message_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, message_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.bt.itswebsocket.domain.protobuf.RadarTwinBeans}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.bt.itswebsocket.domain.protobuf.RadarTwinBeans)
        com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeansOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.internal_static_com_bt_itswebsocket_domain_protobuf_RadarTwinBeans_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.internal_static_com_bt_itswebsocket_domain_protobuf_RadarTwinBeans_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.class, com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.Builder.class);
      }

      // Construct using com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getMessageFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        websocketType_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        if (messageBuilder_ == null) {
          message_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          messageBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.internal_static_com_bt_itswebsocket_domain_protobuf_RadarTwinBeans_descriptor;
      }

      public com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans getDefaultInstanceForType() {
        return com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.getDefaultInstance();
      }

      public com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans build() {
        com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans buildPartial() {
        com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans result = new com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.websocketType_ = websocketType_;
        if (messageBuilder_ == null) {
          if (((bitField0_ & 0x00000002) == 0x00000002)) {
            message_ = java.util.Collections.unmodifiableList(message_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.message_ = message_;
        } else {
          result.message_ = messageBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans) {
          return mergeFrom((com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans other) {
        if (other == com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.getDefaultInstance()) return this;
        if (other.hasWebsocketType()) {
          bitField0_ |= 0x00000001;
          websocketType_ = other.websocketType_;
          onChanged();
        }
        if (messageBuilder_ == null) {
          if (!other.message_.isEmpty()) {
            if (message_.isEmpty()) {
              message_ = other.message_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureMessageIsMutable();
              message_.addAll(other.message_);
            }
            onChanged();
          }
        } else {
          if (!other.message_.isEmpty()) {
            if (messageBuilder_.isEmpty()) {
              messageBuilder_.dispose();
              messageBuilder_ = null;
              message_ = other.message_;
              bitField0_ = (bitField0_ & ~0x00000002);
              messageBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getMessageFieldBuilder() : null;
            } else {
              messageBuilder_.addAllMessages(other.message_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasWebsocketType()) {
          
          return false;
        }
        for (int i = 0; i < getMessageCount(); i++) {
          if (!getMessage(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object websocketType_ = "";
      /**
       * <code>required string websocketType = 1;</code>
       */
      public boolean hasWebsocketType() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required string websocketType = 1;</code>
       */
      public java.lang.String getWebsocketType() {
        java.lang.Object ref = websocketType_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            websocketType_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string websocketType = 1;</code>
       */
      public com.google.protobuf.ByteString
          getWebsocketTypeBytes() {
        java.lang.Object ref = websocketType_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          websocketType_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string websocketType = 1;</code>
       */
      public Builder setWebsocketType(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        websocketType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string websocketType = 1;</code>
       */
      public Builder clearWebsocketType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        websocketType_ = getDefaultInstance().getWebsocketType();
        onChanged();
        return this;
      }
      /**
       * <code>required string websocketType = 1;</code>
       */
      public Builder setWebsocketTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        websocketType_ = value;
        onChanged();
        return this;
      }

      private java.util.List<com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean> message_ =
        java.util.Collections.emptyList();
      private void ensureMessageIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          message_ = new java.util.ArrayList<com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean>(message_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean, com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean.Builder, com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBeanOrBuilder> messageBuilder_;

      /**
       * <code>repeated .com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean message = 2;</code>
       */
      public java.util.List<com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean> getMessageList() {
        if (messageBuilder_ == null) {
          return java.util.Collections.unmodifiableList(message_);
        } else {
          return messageBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean message = 2;</code>
       */
      public int getMessageCount() {
        if (messageBuilder_ == null) {
          return message_.size();
        } else {
          return messageBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean message = 2;</code>
       */
      public com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean getMessage(int index) {
        if (messageBuilder_ == null) {
          return message_.get(index);
        } else {
          return messageBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean message = 2;</code>
       */
      public Builder setMessage(
          int index, com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean value) {
        if (messageBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMessageIsMutable();
          message_.set(index, value);
          onChanged();
        } else {
          messageBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean message = 2;</code>
       */
      public Builder setMessage(
          int index, com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean.Builder builderForValue) {
        if (messageBuilder_ == null) {
          ensureMessageIsMutable();
          message_.set(index, builderForValue.build());
          onChanged();
        } else {
          messageBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean message = 2;</code>
       */
      public Builder addMessage(com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean value) {
        if (messageBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMessageIsMutable();
          message_.add(value);
          onChanged();
        } else {
          messageBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean message = 2;</code>
       */
      public Builder addMessage(
          int index, com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean value) {
        if (messageBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMessageIsMutable();
          message_.add(index, value);
          onChanged();
        } else {
          messageBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean message = 2;</code>
       */
      public Builder addMessage(
          com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean.Builder builderForValue) {
        if (messageBuilder_ == null) {
          ensureMessageIsMutable();
          message_.add(builderForValue.build());
          onChanged();
        } else {
          messageBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean message = 2;</code>
       */
      public Builder addMessage(
          int index, com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean.Builder builderForValue) {
        if (messageBuilder_ == null) {
          ensureMessageIsMutable();
          message_.add(index, builderForValue.build());
          onChanged();
        } else {
          messageBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean message = 2;</code>
       */
      public Builder addAllMessage(
          java.lang.Iterable<? extends com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean> values) {
        if (messageBuilder_ == null) {
          ensureMessageIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, message_);
          onChanged();
        } else {
          messageBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean message = 2;</code>
       */
      public Builder clearMessage() {
        if (messageBuilder_ == null) {
          message_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          messageBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean message = 2;</code>
       */
      public Builder removeMessage(int index) {
        if (messageBuilder_ == null) {
          ensureMessageIsMutable();
          message_.remove(index);
          onChanged();
        } else {
          messageBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean message = 2;</code>
       */
      public com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean.Builder getMessageBuilder(
          int index) {
        return getMessageFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean message = 2;</code>
       */
      public com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBeanOrBuilder getMessageOrBuilder(
          int index) {
        if (messageBuilder_ == null) {
          return message_.get(index);  } else {
          return messageBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean message = 2;</code>
       */
      public java.util.List<? extends com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBeanOrBuilder> 
           getMessageOrBuilderList() {
        if (messageBuilder_ != null) {
          return messageBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(message_);
        }
      }
      /**
       * <code>repeated .com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean message = 2;</code>
       */
      public com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean.Builder addMessageBuilder() {
        return getMessageFieldBuilder().addBuilder(
            com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean.getDefaultInstance());
      }
      /**
       * <code>repeated .com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean message = 2;</code>
       */
      public com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean.Builder addMessageBuilder(
          int index) {
        return getMessageFieldBuilder().addBuilder(
            index, com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean.getDefaultInstance());
      }
      /**
       * <code>repeated .com.bt.itswebsocket.domain.protobuf.RadarTwinBeans.RadarTwinBean message = 2;</code>
       */
      public java.util.List<com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean.Builder> 
           getMessageBuilderList() {
        return getMessageFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean, com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean.Builder, com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBeanOrBuilder> 
          getMessageFieldBuilder() {
        if (messageBuilder_ == null) {
          messageBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean, com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBean.Builder, com.bt.itswebsocket.domain.protobuf.RadarTwinBeansProto.RadarTwinBeans.RadarTwinBeanOrBuilder>(
                  message_,
                  ((bitField0_ & 0x00000002) == 0x00000002),
                  getParentForChildren(),
                  isClean());
          message_ = null;
        }
        return messageBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:com.bt.itswebsocket.domain.protobuf.RadarTwinBeans)
    }

    static {
      defaultInstance = new RadarTwinBeans(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:com.bt.itswebsocket.domain.protobuf.RadarTwinBeans)
  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_bt_itswebsocket_domain_protobuf_RadarTwinBeans_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_com_bt_itswebsocket_domain_protobuf_RadarTwinBeans_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_bt_itswebsocket_domain_protobuf_RadarTwinBeans_RadarTwinBean_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_com_bt_itswebsocket_domain_protobuf_RadarTwinBeans_RadarTwinBean_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\023RadarTwinBean.proto\022#com.bt.itswebsock" +
      "et.domain.protobuf\"\217\004\n\016RadarTwinBeans\022\025\n" +
      "\rwebsocketType\030\001 \002(\t\022R\n\007message\030\002 \003(\0132A." +
      "com.bt.itswebsocket.domain.protobuf.Rada" +
      "rTwinBeans.RadarTwinBean\032\221\003\n\rRadarTwinBe" +
      "an\022\022\n\nfacilityNo\030\001 \002(\t\022\014\n\004line\030\002 \002(\005\022\016\n\006" +
      "idLane\030\003 \001(\t\022\n\n\002id\030\004 \002(\t\022\013\n\003cls\030\005 \001(\t\022\016\n" +
      "\006xRatio\030\006 \001(\001\022\016\n\006yRatio\030\007 \001(\001\022\020\n\010xRatioY" +
      "k\030\010 \001(\001\022\020\n\010fuseFlag\030\t \001(\001\022\022\n\nfuseDataId\030" +
      "\n \001(\t\022\r\n\005speed\030\013 \001(\001\022\022\n\nfinishFlag\030\014 \001(\t",
      "\022\r\n\005sTime\030\r \001(\003\022\022\n\ndetectTime\030\016 \001(\t\022\020\n\010p" +
      "ushTime\030\017 \001(\t\022\017\n\007szPlate\030\020 \001(\t\022\023\n\013nPlate" +
      "Color\030\021 \001(\005\022\020\n\010nAxleNum\030\022 \001(\005\022\024\n\014nVehicl" +
      "eType\030\023 \001(\005\022\022\n\nszPlatePic\030\024 \001(\t\022\021\n\tszSid" +
      "ePic\030\025 \001(\t\022\020\n\010szCarPic\030\026 \001(\tB:\n#com.bt.i" +
      "tswebsocket.domain.protobufB\023RadarTwinBe" +
      "ansProto"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_com_bt_itswebsocket_domain_protobuf_RadarTwinBeans_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_bt_itswebsocket_domain_protobuf_RadarTwinBeans_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_com_bt_itswebsocket_domain_protobuf_RadarTwinBeans_descriptor,
        new java.lang.String[] { "WebsocketType", "Message", });
    internal_static_com_bt_itswebsocket_domain_protobuf_RadarTwinBeans_RadarTwinBean_descriptor =
      internal_static_com_bt_itswebsocket_domain_protobuf_RadarTwinBeans_descriptor.getNestedTypes().get(0);
    internal_static_com_bt_itswebsocket_domain_protobuf_RadarTwinBeans_RadarTwinBean_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_com_bt_itswebsocket_domain_protobuf_RadarTwinBeans_RadarTwinBean_descriptor,
        new java.lang.String[] { "FacilityNo", "Line", "IdLane", "Id", "Cls", "XRatio", "YRatio", "XRatioYk", "FuseFlag", "FuseDataId", "Speed", "FinishFlag", "STime", "DetectTime", "PushTime", "SzPlate", "NPlateColor", "NAxleNum", "NVehicleType", "SzPlatePic", "SzSidePic", "SzCarPic", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
