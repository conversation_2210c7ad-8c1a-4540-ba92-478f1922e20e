spring:
  application:
    name: its-device
  profiles:
    active: dev

---
#开发环境
spring:
  profiles: dev
  cloud:
    nacos:
      config:
        username: nacos
        password: UM1h5uZ7WHDypJK
        server-addr: 10.101.5.40:8848
        file-extension: yaml
        namespace: ns_dev
        extension-configs:
        - data-id: bt-itsway-common.yaml
      discovery:
        username: nacos
        password: UM1h5uZ7WHDypJK
        server-addr: 10.101.5.40:8848
        namespace: ns_dev
        
---
#测试环境
spring:
  profiles: test
  cloud:
    nacos:
      config:
        server-addr: 10.101.5.42:8848
        file-extension: yaml
        namespace: ns_dev
        extension-configs:
        - data-id: bt-itsway-common.yaml
      discovery:
        server-addr: 10.101.5.42:8848
        namespace: ns_dev

---
#生产环境
spring:
  profiles: prod
  cloud:
    nacos:
      config:
        server-addr: 8.134.77.241:8848
        file-extension: yaml
        namespace: ns_dev
        extension-configs:
        - data-id: bt-itsway-common.yaml
      discovery:
        server-addr: 8.134.77.241:8848
        namespace: ns_dev

---
#阿里云测试环境
spring:
  profiles: aliyunTest
  cloud:
    nacos:
      config:
        server-addr: 8.134.57.103:8848
        file-extension: yaml
        namespace: ns_test
        extension-configs:
        - data-id: bt-itsway-common.yaml
      discovery:
        server-addr: 8.134.57.103:8848
        namespace: ns_test