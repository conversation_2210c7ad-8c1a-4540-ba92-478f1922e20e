package com.bt.itsdevice.service;

import com.bt.itscore.constants.DeviceType;
import com.bt.itscore.domain.dto.PageDTO;
import com.bt.itscore.domain.dto.RoadPileNoDTO;
import com.bt.itscore.domain.vo.RoadPileNoVO;
import com.bt.itscore.domain.dto.DeviceFoginductDTO;
import com.bt.itscore.utils.ExcelUtils;
import com.bt.itsdevice.domain.dto.DeviceCameraDTO;
import com.bt.itsdevice.domain.dto.FacilityDTO;
import com.bt.itscore.domain.vo.DeviceFoginductVO;
import com.bt.itscore.domain.vo.FacilityVO;
import com.bt.itsdevice.domain.vo.DeviceCameraVO;
import com.bt.itsdevice.feign.RoadFeignClient;
import com.bt.itsdevice.mapper.DeviceFoginductMapper;
import com.bt.itsdevice.mapper.DeviceMapper;
import com.bt.itsdevice.mapper.FacilityMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Service("deviceFoginductService")
public class DeviceFoginductService {
    @Autowired
    private DeviceFoginductMapper deviceFoginductMapper;
    @Autowired
    private FacilityMapper facilityMapper;
    @Autowired
    private DeviceService deviceService;
    @Autowired
    private RoadFeignClient roadFeignClient;
    @Autowired
    private DeviceMapper deviceMapper;
    @Value("${file.export.path}")
    private String fileExportPath;
    @Value("${file.export.max}")
    private int fileExportMaxCount;
    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceCameraService.class);


    public PageInfo<DeviceFoginductVO> page(DeviceFoginductDTO deviceFoginductDTO, PageDTO pageDTO) {
        PageHelper.startPage(pageDTO.getPage(), pageDTO.getLimit());
        List<DeviceFoginductVO> rows = deviceFoginductMapper.selectList(deviceFoginductDTO);
        PageInfo<DeviceFoginductVO> pageInfo = new PageInfo<>(rows);
        return pageInfo;
    }

    public List<DeviceFoginductVO> selectAll(DeviceFoginductDTO deviceFoginductDTO) {
        List<DeviceFoginductVO> rows = deviceFoginductMapper.selectList(deviceFoginductDTO);
        return rows;
    }

    public boolean hasIP(String ip)
    {
        return deviceFoginductMapper.hasIP(ip)>0;
    }

    public boolean update(DeviceFoginductDTO deviceFoginductDTO) {
        //  mp_value, 经纬度
        String[] mile = deviceFoginductDTO.getMilePost().replace("K", "").split("\\+");
        if (mile.length == 2) {
            deviceFoginductDTO.setMpValue(NumberUtils.toInt(mile[0]) * 1000 + NumberUtils.toInt(mile[1]));
        } else {
            deviceFoginductDTO.setMpValue(NumberUtils.toInt(mile[0]) * 1000);
        }
        FacilityDTO facilityDTO = new FacilityDTO(deviceFoginductDTO.getFacilityNo());
        FacilityVO facilityVO = facilityMapper.selectRoadno(facilityDTO);
        RoadPileNoDTO roadPileNoDTO = new RoadPileNoDTO(facilityVO.getRoadNo(), deviceFoginductDTO.getDirectionNo(), deviceFoginductDTO.getMilePost());
        RoadPileNoVO roadPileNoVO = roadFeignClient.pileno2Lnglat(roadPileNoDTO);
        if (roadPileNoVO != null) {
            deviceFoginductDTO.setLng(roadPileNoVO.getLng());
            deviceFoginductDTO.setLat(roadPileNoVO.getLat());
        }
        deviceFoginductDTO.setDeviceIp(deviceFoginductDTO.getIpAddress());
        deviceService.update(deviceFoginductDTO);
        return deviceFoginductMapper.update(deviceFoginductDTO) > 0;
    }

    public boolean add(DeviceFoginductDTO deviceFoginductDTO) {
        String[] mile = deviceFoginductDTO.getMilePost().replace("K", "").split("\\+");
        if (mile.length == 2) {
            deviceFoginductDTO.setMpValue(NumberUtils.toInt(mile[0]) * 1000 + NumberUtils.toInt(mile[1]));
        } else {
            deviceFoginductDTO.setMpValue(NumberUtils.toInt(mile[0]) * 1000);
        }
        FacilityDTO facilityDTO = new FacilityDTO(deviceFoginductDTO.getFacilityNo());
        FacilityVO facilityVO = facilityMapper.selectRoadno(facilityDTO);
        // 桩号转经纬度 表road_pile_no
        RoadPileNoDTO roadPileNoDTO = new RoadPileNoDTO(facilityVO.getRoadNo(), deviceFoginductDTO.getDirectionNo(), deviceFoginductDTO.getMilePost());
        RoadPileNoVO roadPileNoVO = roadFeignClient.pileno2Lnglat(roadPileNoDTO);
        if (roadPileNoVO != null) {
            deviceFoginductDTO.setLng(roadPileNoVO.getLng());
            deviceFoginductDTO.setLat(roadPileNoVO.getLat());
        }
        deviceFoginductDTO.setDeviceTypeNo(DeviceType.FOG.value());
        deviceFoginductDTO.setDeviceIp(deviceFoginductDTO.getIpAddress());
        deviceService.add(deviceFoginductDTO);
        return deviceFoginductMapper.add(deviceFoginductDTO) > 0;
    }

    public boolean exportExcel(DeviceFoginductDTO dto, HttpServletResponse response) {
        List<DeviceFoginductVO> deviceFoginductVOList = deviceFoginductMapper.selectList(dto);
        LOGGER.info("查询出的雾区设备数量：" + deviceFoginductVOList.size());
        if (deviceFoginductVOList.size() > 0) {
            //1.构造Excel的基础文件结构
            String[] titles = {"序号", "设备IP", "设备名称", "所属路段", "安装位置", "设备桩号", "设备方向", "设备状态", "设备经度", "设备纬度", "是否启用"};
            String fileName = "雾区设备列表";
            String filePath = fileExportPath + File.separator + fileName + "##" + UUID.randomUUID().toString()
                    + "%%.xls";
//			String filePath = "C:\\Users\\<USER>\\Desktop" + File.separator + fileName + "##" + UUID.randomUUID().toString()
//					+ "%%.xls";

            ExcelUtils tool = new ExcelUtils();
            int cellWidth[] = {8, 22, 25, 25, 25, 15, 15, 15, 18, 18, 15};
            tool.init(filePath, "sheet1");
            tool.writeTitle(fileName, 0, 0, 10, 0);
            tool.writeHeadStr(titles, cellWidth, 1);

            // 2.填充Excel文件中的每一行数据
            List<String> list = new ArrayList<String>();
            for (int i = 0; i < deviceFoginductVOList.size(); i++) {
                if (i == fileExportMaxCount) {
                    break;
                }
                // 数据格式
                String useStr = null;
                String statusStr = null;
                if (deviceFoginductVOList.get(i).getUse()!=null && deviceFoginductVOList.get(i).getUse() == 0) {
                    useStr = "停用";
                    statusStr = "-";
                }
                if (deviceFoginductVOList.get(i).getUse()!=null && deviceFoginductVOList.get(i).getUse() == 1) {
                    useStr = "启用";
                    if (deviceFoginductVOList.get(i).getStatus()!=null && deviceFoginductVOList.get(i).getStatus() == 0) {
                        statusStr = "离线";
                    }
                    if (deviceFoginductVOList.get(i).getStatus()!=null && deviceFoginductVOList.get(i).getStatus() == 1) {
                        statusStr = "在线";
                    }
                }

                list.clear();
                list.add(String.valueOf(i + 1));//序号
                list.add(deviceFoginductVOList.get(i).getIpAddress());//设备ip
                list.add(deviceFoginductVOList.get(i).getDeviceName());//设备名称
                list.add(deviceFoginductVOList.get(i).getRoadName());//所属路段
                list.add(deviceMapper.selectFacility(deviceFoginductVOList.get(i).getDeviceId()));//安装位置
                list.add(deviceFoginductVOList.get(i).getMilePost());//设备桩号
                list.add(deviceFoginductVOList.get(i).getDirectionName());//设备方向
                list.add(statusStr);//设备状态
                list.add(deviceFoginductVOList.get(i).getLng());//设备经度
                list.add(deviceFoginductVOList.get(i).getLat());//设备纬度
                list.add(useStr);//是否启用

                tool.writeContentRow(list, 2 + i);//写入一行数据
            }
            tool.writeBook(false); // 写入整个Excel表
            LOGGER.info("雾区设备列表写入完成");

            tool.downloadFile(filePath, response);
            ExcelUtils.deleteFile(filePath);
            return true;
        } else {
            return false;
        }
    }

}
