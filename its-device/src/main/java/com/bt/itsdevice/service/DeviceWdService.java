package com.bt.itsdevice.service;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import com.bt.itscore.utils.ExcelUtils;
import com.bt.itsdevice.domain.dto.DeviceCameraDTO;
import com.bt.itsdevice.domain.vo.DeviceCameraVO;
import com.bt.itsdevice.mapper.DeviceMapper;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.bt.itscore.constants.DeviceType;
import com.bt.itscore.domain.dto.PageDTO;
import com.bt.itscore.domain.dto.RoadPileNoDTO;
import com.bt.itscore.domain.vo.RoadPileNoVO;
import com.bt.itsdevice.domain.dto.DeviceWdDTO;
import com.bt.itsdevice.domain.dto.FacilityDTO;
import com.bt.itsdevice.domain.vo.DeviceWdVO;
import com.bt.itscore.domain.vo.FacilityVO;
import com.bt.itsdevice.feign.RoadFeignClient;
import com.bt.itsdevice.mapper.DeviceWdMapper;
import com.bt.itsdevice.mapper.FacilityMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import javax.servlet.http.HttpServletResponse;

@Service("deviceWdService")
public class DeviceWdService {

    @Autowired
    private DeviceWdMapper deviceWdMapper;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private FacilityMapper facilityMapper;

    @Autowired
    private RoadFeignClient roadFeignClient;

    @Autowired
    private DeviceMapper deviceMapper;

    @Value("${file.export.path}")
    private String fileExportPath;
    @Value("${file.export.max}")
    private int fileExportMaxCount;
    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceCameraService.class);

    public PageInfo<DeviceWdVO> page(DeviceWdDTO deviceWdDTO, PageDTO pageDTO){
        PageHelper.startPage(pageDTO.getPage() , pageDTO.getLimit());
        List<DeviceWdVO> rows = deviceWdMapper.selectList(deviceWdDTO);
        PageInfo<DeviceWdVO> pageInfo = new PageInfo<>(rows);
        return pageInfo;
    }

    public List<DeviceWdVO> selectAll(DeviceWdDTO deviceWdDTO){
        return deviceWdMapper.selectList(deviceWdDTO);
    }
    public boolean add(DeviceWdDTO deviceWdDTO){
        String[] mile = deviceWdDTO.getMilePost().replace("K", "").split("\\+");
        if(mile.length == 2){
            deviceWdDTO.setMpValue(NumberUtils.toInt(mile[0])*1000+NumberUtils.toInt(mile[1]));
        } else {
            deviceWdDTO.setMpValue(NumberUtils.toInt(mile[0])*1000);
        }
        FacilityDTO facilityDTO = new FacilityDTO(deviceWdDTO.getFacilityNo());
        FacilityVO facilityVO = facilityMapper.selectRoadno(facilityDTO);
        // 桩号转经纬度 表road_pile_no
        RoadPileNoDTO roadPileNoDTO = new RoadPileNoDTO(facilityVO.getRoadNo(),deviceWdDTO.getDirectionNo(),deviceWdDTO.getMilePost());
        RoadPileNoVO roadPileNoVO = roadFeignClient.pileno2Lnglat(roadPileNoDTO);
        if(roadPileNoVO != null ){
            deviceWdDTO.setLng(roadPileNoVO.getLng());
            deviceWdDTO.setLat(roadPileNoVO.getLat());
        }
        deviceWdDTO.setDeviceTypeNo(DeviceType.WD.value());
        deviceWdDTO.setDeviceIp(deviceWdDTO.getIpAddress());
        deviceService.add(deviceWdDTO);
        return deviceWdMapper.add(deviceWdDTO) > 0;
    }

    public boolean update(DeviceWdDTO deviceWdDTO){
        String[] mile = deviceWdDTO.getMilePost().replace("K", "").split("\\+");
        if(mile.length == 2){
            deviceWdDTO.setMpValue(NumberUtils.toInt(mile[0])*1000+NumberUtils.toInt(mile[1]));
        } else {
            deviceWdDTO.setMpValue(NumberUtils.toInt(mile[0])*1000);
        }
        FacilityDTO facilityDTO = new FacilityDTO(deviceWdDTO.getFacilityNo());
        FacilityVO facilityVO = facilityMapper.selectRoadno(facilityDTO);
        RoadPileNoDTO roadPileNoDTO = new RoadPileNoDTO(facilityVO.getRoadNo(),deviceWdDTO.getDirectionNo(),deviceWdDTO.getMilePost());
        RoadPileNoVO roadPileNoVO = roadFeignClient.pileno2Lnglat(roadPileNoDTO);
        if(roadPileNoVO != null ){
            deviceWdDTO.setLng(roadPileNoVO.getLng());
            deviceWdDTO.setLat(roadPileNoVO.getLat());
        }
        deviceWdDTO.setDeviceIp(deviceWdDTO.getIpAddress());
        deviceService.update(deviceWdDTO);
        return deviceWdMapper.update(deviceWdDTO) > 0;
    }

    public boolean hasIP(String ip)
    {
        return deviceWdMapper.hasIP(ip)>0;
    }


//    public boolean delete(DeviceWdDTO deviceWdDTO) {
//        deviceService.delete(deviceWdDTO);
//        deviceWdDTO.setTableName(DataConstants.DEVICE_WD);
//        return deviceService.deleteSub(deviceWdDTO);
//    }
//
//    public boolean batchDelete(DeviceWdDTO deviceWdDTO) {
//        deviceService.batchDelete(deviceWdDTO);
//        return deviceWdMapper.batchDelete(deviceWdDTO) > 0;
//    }

    public boolean exportExcel(DeviceWdDTO dto, HttpServletResponse response) {
        List<DeviceWdVO> deviceWdVOList = deviceWdMapper.selectList(dto);
        LOGGER.info("查询出的气象检测器设备数量：" + deviceWdVOList.size());
        if (deviceWdVOList.size() > 0) {
            //1.构造Excel的基础文件结构
            String[] titles = {"序号", "设备IP", "设备名称", "所属路段", "安装位置", "设备桩号", "设备方向", "设备状态", "设备经度", "设备纬度", "是否启用"};
            String fileName = "气象检测器设备列表";
            String filePath = fileExportPath + File.separator + fileName + "##" + UUID.randomUUID().toString()
                    + "%%.xls";
//			String filePath = "C:\\Users\\<USER>\\Desktop" + File.separator + fileName + "##" + UUID.randomUUID().toString()
//					+ "%%.xls";

            ExcelUtils tool = new ExcelUtils();
            int cellWidth[] = {8, 22, 25, 25, 25, 15, 15, 15, 18, 18, 15};
            tool.init(filePath, "sheet1");
            tool.writeTitle(fileName, 0, 0, 10, 0);
            tool.writeHeadStr(titles, cellWidth, 1);

            // 2.填充Excel文件中的每一行数据
            List<String> list = new ArrayList<String>();
            for (int i = 0; i < deviceWdVOList.size(); i++) {
                if (i == fileExportMaxCount) {
                    break;
                }
                // 数据格式
                String useStr = null;
                String statusStr = null;
                if (deviceWdVOList.get(i).getUse()!=null && deviceWdVOList.get(i).getUse() == 0) {
                    useStr = "停用";
                    statusStr = "-";
                }
                if (deviceWdVOList.get(i).getUse()!=null && deviceWdVOList.get(i).getUse() == 1) {
                    useStr = "启用";
                    if (deviceWdVOList.get(i).getStatus()!=null && deviceWdVOList.get(i).getStatus() == 0) {
                        statusStr = "离线";
                    }
                    if (deviceWdVOList.get(i).getStatus()!=null && deviceWdVOList.get(i).getStatus() == 1) {
                        statusStr = "在线";
                    }
                }

                list.clear();
                list.add(String.valueOf(i + 1));//序号
                list.add(deviceWdVOList.get(i).getIpAddress());//设备ip
                list.add(deviceWdVOList.get(i).getDeviceName());//设备名称
                list.add(deviceWdVOList.get(i).getRoadName());//所属路段
                list.add(deviceMapper.selectFacility(deviceWdVOList.get(i).getDeviceId()));//安装位置
                list.add(deviceWdVOList.get(i).getMilePost());//设备桩号
                list.add(deviceWdVOList.get(i).getDirectionName());//设备方向
                list.add(statusStr);//设备状态
                list.add(deviceWdVOList.get(i).getLng());//设备经度
                list.add(deviceWdVOList.get(i).getLat());//设备纬度
                list.add(useStr);//是否启用

                tool.writeContentRow(list, 2 + i);//写入一行数据
            }
            tool.writeBook(false); // 写入整个Excel表
            LOGGER.info("气象检测器设备列表写入完成");

            tool.downloadFile(filePath, response);
            ExcelUtils.deleteFile(filePath);
            return true;
        } else {
            return false;
        }
    }
}
