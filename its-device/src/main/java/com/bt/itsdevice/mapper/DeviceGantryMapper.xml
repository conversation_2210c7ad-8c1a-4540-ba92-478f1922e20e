<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itsdevice.mapper.DeviceGantryMapper">
    <resultMap type="com.bt.itsdevice.domain.vo.DeviceGantryVO" id="deviceGantryMap">
        <result property="facilityNo" column="facility_no"/>
        <result property="directionNo" column="direction_no"/>
        <result property="deviceName" column="device_name"/>
        <result property="deviceTypeNo" column="device_type_no"/>
        <result property="milePost" column="mile_post"/>
        <result property="mpValue" column="mp_value"/>
        <result property="use" column="use"/>
        <result property="status" column="status"/>
        <result property="lng" column="lng"/>
        <result property="lat" column="lat"/>
        <result property="orgId" column="org_id"/>
        <result property="roadNo" column="road_no"/>
        <result property="roadName" column="road_name"/>
        <result property="computeSwitch" column="compute_switch"/>
        <result property="directionName" column="direction_name"/>
        <result property="facilityName" column="facility_name"/>
        <result property="deviceId" column="device_id"/>
        <result property="code" column="code"/>
        <result property="type" column="type"/>
    </resultMap>

    <select id="selectList" resultMap="deviceGantryMap" parameterType="com.bt.itsdevice.domain.dto.DeviceGantryDTO">
        select f.facility_name,f.facility_type_no,f.road_no,dd.*,e.direction_name,r.road_name from
        ( select d.*,g.code,g.type from device d,device_gantry g
        <if test="roadNo != null &amp;&amp; roadNo > 0 ">,facility f2 </if>
        where d.device_type_no=10 and d.device_id=g.device_id
        AND d.del_status = 0
        <if test="deviceName != null &amp;&amp; deviceName != '' "> AND (d.device_name like CONCAT('%', #{deviceName}, '%') or d.mile_post like CONCAT('%', #{deviceName}, '%')) </if>
        <if test="facilityNo != null &amp;&amp; facilityNo != '' "> AND d.facility_no =#{facilityNo}</if>
        <if test="use != null &amp;&amp; use>=0 "> AND d.use =#{use}</if>
        <if test="directionNo != null &amp;&amp; directionNo>=0 "> AND d.direction_no =#{directionNo}</if>
        <if test="roadNo != null &amp;&amp; roadNo > 0 "> and f2.road_no =#{roadNo} and f2.facility_no =d.facility_no </if>
        <if test="status != null"> AND d.status=#{status} </if>
        ) dd
        left join direction e on dd.direction_no=e.direction_no
        left join facility f on dd.facility_no=f.facility_no
        LEFT JOIN road r ON r.road_no = f.road_no
        WHERE dd.facility_no IN (Facility-Permissions-Check)
        <if test="facilityTypeNo != null &amp;&amp; facilityTypeNo>0 "> AND f.facility_type_no=#{facilityTypeNo} </if>
        order by dd.device_id desc
    </select>

    <select id="selectMaxDeviceId" resultMap="deviceGantryMap">
		select IFNULL(max(device_id),0) deviceId from device_gantry
	</select>

    <select id="selectByCode" resultMap="deviceGantryMap" parameterType="com.bt.itsdevice.domain.dto.DeviceGantryDTO">
		select device_id from device_gantry where code = #{code} limit 0,1
	</select>

    <insert id="add" parameterType="com.bt.itsdevice.domain.dto.DeviceGantryDTO">
		insert into device_gantry
		(
            device_id,
            code,
            `type`
		)
        values
        (
            #{deviceId},
            #{code},
            #{type}
        )
	</insert>

    <update id="update" parameterType="com.bt.itsdevice.domain.dto.DeviceGantryDTO">
		update device_gantry
		set
		`type`=#{type},
		code=#{code}
        where device_id=#{deviceId}
	</update>

    <delete id="delete" parameterType="com.bt.itsdevice.domain.dto.DeviceGantryDTO">
		delete from device_gantry where device_id = #{deviceId}
	</delete>
    <delete id="batchDelete" parameterType="com.bt.itsdevice.domain.dto.DeviceGantryDTO">
        delete from device_gantry where device_id IN
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>


</mapper>