<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itsdevice.mapper.FacilityBayonetMapper">
    <resultMap type="com.bt.itsdevice.domain.vo.FacilityBayonetVO" id="facilityBayonetMap">
        <result property="facilityNo" column="facility_no"/>
        <result property="facilityName" column="facility_name"></result>
        <result property="roadNo" column="road_no" ></result>
        <result property="facilityTypeNo" column="facility_type_no"></result>
        <result property="milePost"  column="mile_post"></result>
        <result property="upFacilityNo" column="up_facility_no"></result>
        <result property="mpValue" column="mp_value"></result>
        <result property="sort" column="sort"></result>
        <result property="lng" column="lng"></result>
        <result property="lat"  column="lat"></result>
        <result property="roadName"  column="road_name"></result>
        <result property="facilityName" column="facility_name"/>
        <result property="lane" column="lane"/>
        <result property="captureNo" column="capture_no"/>
        <result property="captureType" column="capture_type"/>
        <result property="serviceareaNo" column="servicearea_no"/>
    	<collection property="attachs" ofType="com.bt.itscore.domain.vo.FacilityAttachVO"
			select="selectAttach" column="{facilityNo=facility_no}">
    	</collection>
    </resultMap>
    
    <resultMap type="com.bt.itscore.domain.vo.FacilityAttachVO" id="AttachMap">
		<id column="id" property="id"/>
		<result column="facility_no" property="facilityNo"/>
		<result column="file_name" property="fileName"/>
		<result column="disk_file_name" property="diskFileName"/>
		<result column="file_size" property="fileSize"/>
		<result column="content_type" property="contentType"/>
		<result column="digest" property="digest"/>
		<result column="disk_directory" property="diskDirectory"/>
		<result column="create_time" property="createTime"/>
	</resultMap>
	<select id="selectAttach" parameterType="com.bt.itsdevice.domain.dto.FacilityDTO" resultMap="AttachMap">
		SELECT * FROM facility_attach WHERE facility_no=#{facilityNo}	
	</select>

    <select id="selectList" resultMap="facilityBayonetMap" parameterType="com.bt.itsdevice.domain.dto.FacilityBayonetDTO">
        SELECT f.facility_name,f.road_no,f.facility_type_no,f.mile_post,f.up_facility_no,f.mp_value,f.sort,f.lng,f.lat,f.facility_code,r.road_name,fb.*
        FROM facility f, facility_bayonet fb,road r
        WHERE f.facility_no=fb.facility_no AND f.road_no=r.road_no AND f.facility_no IN (Facility-Permissions-Check)
        <if test="facilityName != null &amp;&amp; facilityName != '' "> AND f.facility_name like CONCAT('%', #{facilityName}, '%') </if>
        <if test="roadNo != null &amp;&amp; roadNo > 0 "> AND f.road_no=#{roadNo}</if>
        ORDER BY f.facility_no DESC
    </select>

    <select id="selectMaxFacilityNo" resultMap="facilityBayonetMap">
		select IFNULL(max(facility_no),0) facility_no from facility_bayonet
	</select>
    <insert id="add" parameterType="com.bt.itsdevice.domain.dto.FacilityBayonetDTO">
		insert into
		facility_bayonet
		(
		facility_no,
		capture_no,
		lane,
		capture_type,
		servicearea_no)
		values
		(
		#{facilityNo},
		#{captureNo},
		#{lane},
		#{captureType},
		#{serviceareaNo}
		)
	</insert>
    <update id="update" parameterType="com.bt.itsdevice.domain.dto.FacilityBayonetDTO">
		update facility_bayonet
		set
         capture_no=#{captureNo},
		 lane=#{lane},
		 capture_type=#{captureType},
		 servicearea_no=#{serviceareaNo}
		 where facility_no = #{facilityNo}
	</update>
    <delete id="delete" parameterType="com.bt.itsdevice.domain.dto.FacilityBayonetDTO">
		delete from facility_bayonet where facility_no = #{facilityNo}
	</delete>
    <delete id="batchDelete" parameterType="com.bt.itsdevice.domain.dto.FacilityBayonetDTO">
        delete from facility_bayonet where facility_no IN
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>


</mapper>