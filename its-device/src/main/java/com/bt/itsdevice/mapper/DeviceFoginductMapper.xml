<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bt.itsdevice.mapper.DeviceFoginductMapper">
    <resultMap type="com.bt.itscore.domain.vo.DeviceFoginductVO" id="deviceFoginductMap">
        <result property="facilityNo" column="facility_no"/>
        <result property="directionNo" column="direction_no"/>
        <result property="deviceName" column="device_name"/>
        <result property="deviceTypeNo" column="device_type_no"/>
        <result property="milePost" column="mile_post"/>
        <result property="mpValue" column="mp_value"/>
        <result property="use" column="use"/>
        <result property="status" column="status"/>
        <result property="lng" column="lng"/>
        <result property="lat" column="lat"/>
        <result property="roadNo" column="road_no"/>
        <result property="directionName" column="direction_name"/>
        <result property="facilityName" column="facility_name"/>
        <result property="roadName" column="road_name"/>
        <result property="id" column="id"/>
        <result property="deviceId" column="device_id"/>
        <result property="type" column="type"/>
        <result property="protocol" column="protocol"/>
        <result property="ipAddress" column="ip_address"/>
        <result property="port" column="port"/>
        <result property="hostId" column="host_id"/>
        <result property="sourceId" column="source_id"/>
        <result property="lightNumber" column="light_number"/>
        <result property="visibilityId" column="visibility_id"></result>
        <result property="cameraId" column="camera_id"></result>
    </resultMap>

    <select id="selectList" resultMap="deviceFoginductMap" parameterType="com.bt.itscore.domain.dto.DeviceFoginductDTO">
        select f.facility_name,f.road_no,dd.*,e.direction_name,r.road_name from
        ( select d.device_id,d.device_name,d.mile_post,d.mp_value,d.device_type_no,d.lng,d.lat,d.facility_no,d.status,d.source_id,
        c.visibility_id,c.camera_id,c.host_id,c.type,c.protocol,c.ip_address,c.port,d.use,d.direction_no,c.light_number
        from device d, device_foginduct c
        <if test="roadNo != null &amp;&amp; roadNo > 0 ">,facility f2 </if>
        where d.device_type_no=9 and d.device_id=c.device_id
        AND d.del_status = 0
        <if test="deviceName != null &amp;&amp; deviceName != '' "> AND (d.device_name like CONCAT('%', #{deviceName}, '%')
            or d.mile_post like CONCAT('%', #{deviceName}, '%'))</if>
        <if test="use != null &amp;&amp; use>=0 "> AND d.use =#{use}</if>
        <if test="roadNo != null &amp;&amp; roadNo > 0 "> and f2.road_no =#{roadNo} and f2.facility_no =d.facility_no </if>
        <if test="facilityNo != null &amp;&amp; facilityNo != '' "> AND d.facility_no =#{facilityNo}</if>
        <if test="status != null "> AND d.status=#{status} </if>
        ) dd
        left join facility f on dd.facility_no=f.facility_no
        left join direction e on dd.direction_no=e.direction_no
        LEFT JOIN road r ON r.road_no = f.road_no
        WHERE dd.facility_no IN (Facility-Permissions-Check)
        order by dd.device_id desc
    </select>

    <insert id="add" parameterType="com.bt.itscore.domain.dto.DeviceFoginductDTO">
        insert into device_foginduct (device_id,type,protocol,ip_address,port,host_id,visibility_id,camera_id,light_number)
        values (#{deviceId},#{type},#{protocol},#{ipAddress},#{port},#{hostId},#{visibilityId},#{cameraId},#{lightNumber})
    </insert>

    <update id="update" parameterType="com.bt.itscore.domain.dto.DeviceFoginductDTO">
        update device_foginduct f set
        <if test="ipAddress != null &amp;&amp; ipAddress != '' "> f.ip_address=#{ipAddress},</if>
        <if test="port != null &amp;&amp; port>=0 "> f.port=#{port},</if>
        <if test="type != null &amp;&amp; type>=0 "> f.type=#{type},</if>
        <if test="lightNumber != null &amp;&amp; lightNumber>=0 "> f.light_number=#{lightNumber},</if>
        <if test="protocol != null &amp;&amp; protocol != '' "> f.protocol=#{protocol}, </if>
        <if test="cameraId != null &amp;&amp; cameraId != '' "> f.camera_id=#{cameraId}, </if>
        <if test="visibilityId != null &amp;&amp; visibilityId != '' "> f.visibility_id=#{visibilityId}, </if>
        f.host_id=#{hostId}
        where f.device_id = #{deviceId}
    </update>

    <delete id="delete">
        delete f.* from device_foginduct f where f.device_id = #{deviceId};
    </delete>

    <delete id="batchDelete" parameterType="com.bt.itscore.domain.dto.DeviceFoginductDTO">
        delete from device_foginduct where device_id IN
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>

    <select id="hasIP" resultType="java.lang.Integer" parameterType="java.lang.String">
        select count(*) from device_foginduct where ip_address = #{ipAddress}
    </select>



</mapper>