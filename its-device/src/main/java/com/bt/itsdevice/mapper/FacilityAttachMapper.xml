<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itsdevice.mapper.FacilityAttachMapper">
	<resultMap type="com.bt.itscore.domain.dto.AttachDTO" id="AttachMap">
		<id column="id" property="id"/>
		<result column="file_name" property="fileName"/>
		<result column="disk_file_name" property="diskFileName"/>
		<result column="file_size" property="fileSize"/>
		<result column="content_type" property="contentType"/>
		<result column="digest" property="digest"/>
		<result column="disk_directory" property="diskDirectory"/>
		<result column="create_time" property="createTime"/>
	</resultMap>

	<insert id="addAttach" parameterType="com.bt.itscore.domain.dto.AttachDTO" keyProperty="id" useGeneratedKeys="true">
		INSERT INTO facility_attach (file_name,disk_file_name,file_size,content_type,digest,disk_directory,create_time) VALUES 
		(#{fileName}, #{diskFileName}, #{fileSize}, #{contentType}, #{digest},#{diskDirectory},#{createTime})
	</insert>
	
	<delete id="deleteAttach" parameterType="com.bt.itscore.domain.dto.AttachDTO">
		DELETE FROM facility_attach WHERE id=#{id}
	</delete>
	
	<delete id="emptyAttach" parameterType="com.bt.itscore.domain.dto.IdStringDTO">
		UPDATE facility_attach SET facility_no=null WHERE facility_no=#{id}
	</delete>
	
	<delete id="deleteAttachByFacilityNo" parameterType="com.bt.itscore.domain.dto.IdStringDTO">
		delete from facility_attach where facility_no=#{id}
	</delete>
	
	<select id="selectByFacilityNo" parameterType="com.bt.itscore.domain.dto.IdStringDTO" resultMap="AttachMap">
		select * from facility_attach where facility_no=#{id}
	</select>
	
	<update id="updateAttach" parameterType="com.bt.itsdevice.domain.dto.FacilityDTO">
		update facility_attach set facility_no=#{facilityNo} WHERE id IN 
		<foreach collection="attachs" item="item" index="index" open="(" close=")" separator=",">#{item}
		</foreach> 
	</update>
</mapper>