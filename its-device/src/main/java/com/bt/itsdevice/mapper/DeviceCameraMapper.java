package com.bt.itsdevice.mapper;

import com.bt.itscore.domain.dto.IdStringBatchDTO;
import com.bt.itscore.domain.dto.IdStringDTO;
import com.bt.itscore.domain.dto.VideoDeviceDTO;
import com.bt.itsdevice.domain.dto.CameraDTO;
import com.bt.itsdevice.domain.dto.DeviceCameraDTO;
import com.bt.itsdevice.domain.vo.DeviceCameraVO;
import com.bt.itsdevice.domain.vo.DeviceMasterVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DeviceCameraMapper {

    public List<DeviceCameraVO>  selectList(DeviceCameraDTO deviceCameraDTO);

    public List<DeviceMasterVO> selectMasterList();

    public DeviceCameraVO selectByGb(@Param("cameraCode") String cameraCode);

    public int add(DeviceCameraDTO deviceCameraDTO);
	public int addLaneId(String laneId);
	public String selectLaneId(String laneId);

    public int update(DeviceCameraDTO deviceCameraDTO);

	public DeviceCameraVO selectSourceId(DeviceCameraDTO dto);

	public List<DeviceCameraVO> selectAll(DeviceCameraDTO deviceCameraDTO);

	public List<DeviceCameraVO> selectFacilityCamera(CameraDTO dto);

	public List<DeviceCameraVO> selectCameraListDetail(IdStringBatchDTO dto);

	public VideoDeviceDTO selectCameraCodeById(IdStringDTO dto);

	public VideoDeviceDTO selectDeviceId(IdStringDTO dto);

	public int updateFromShareData(List<DeviceCameraDTO> list);

	public String selectMaxSpUpdateTime();

	public List<DeviceCameraVO> selectAppletCameraList(DeviceCameraDTO deviceCameraDTO);
	
	public List<Integer> selectRoadNoBySourceId(Integer sourceId);
	
	public List<DeviceCameraVO> selectTowerCameraAll(DeviceCameraDTO deviceCameraDTO);

	public List<DeviceCameraVO> selectCameraWithCms(IdStringDTO dto);
}
