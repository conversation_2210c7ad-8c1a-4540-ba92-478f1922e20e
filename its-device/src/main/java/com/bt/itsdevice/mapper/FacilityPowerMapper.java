package com.bt.itsdevice.mapper;

import java.util.List;

import com.bt.itscore.domain.dto.PowerCmdDTO;
import org.apache.ibatis.annotations.Mapper;

import com.bt.itscore.domain.dto.FacilityCommonDTO;
import com.bt.itsdevice.domain.dto.FacilityPowerDTO;
import com.bt.itsdevice.domain.vo.TreePowerVO;
import com.bt.itscore.domain.vo.FacilityPowerVO;

@Mapper
public interface FacilityPowerMapper {
	List<FacilityPowerVO> selectList(FacilityPowerDTO dto);

	int add(FacilityPowerDTO dto);

	int update(FacilityPowerDTO dto);

	int delete(FacilityPowerDTO dto);

	int batchDelete(FacilityPowerDTO dto);

	List<FacilityPowerVO> selectAll(FacilityPowerDTO dto);

	int deleteVoltageLevels(String facilityNo);

	int updateVoltageLevels(FacilityPowerDTO dto);

	int deleteBackupPowers(String facilityNo);

	int updateBackupPowers(FacilityPowerDTO dto);
	
	List<FacilityPowerVO> selectFacilityAll(FacilityPowerDTO dto);
	
	List<FacilityPowerVO> selectPowerAll(FacilityCommonDTO dto);
	
	FacilityPowerVO getPowerCommons(String facilityNo);
	
	List<TreePowerVO> getPageTypeChildren(String facilityNo);

	List<PowerCmdDTO> queryPowerCmds(String facilityNo);
}
