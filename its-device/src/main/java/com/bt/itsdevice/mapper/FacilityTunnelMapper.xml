<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itsdevice.mapper.FacilityTunnelMapper">
    <resultMap type="com.bt.itsdevice.domain.vo.FacilityTunnelVO" id="facilityTunnelMap">
        <result property="facilityNo" column="facility_no"/>
        <result property="facilityName" column="facility_name" ></result>
        <result property="roadNo" column="road_no" ></result>
        <result property="facilityTypeNo" column="facility_type_no" ></result>
        <result property="milePost"  column="mile_post"></result>
        <result property="upFacilityNo" column="up_facility_no" ></result>
        <result property="mpValue" column="mp_value" ></result>
        <result property="sort" column="sort" ></result>
        <result property="lng"  column="lng"></result>
        <result property="lat" column="lat"></result>
        <result property="roadName" column="road_name" ></result>
        <result property="facilityCode" column="facility_code" ></result>
        <result property="beforeFacilityNo" column="before_facility_no"/>
        <result property="afterFacilityNo" column="after_facility_no"/>
        <result property="startMilePost" column="start_mile_post"/>
        <result property="endMilePost" column="end_mile_post"/>
        <result property="length" column="length"/>
        <result column="start_mile_post_right" property="startMilePostRight" ></result>  
        <result column="end_mile_post_right" property="endMilePostRight" ></result>
        <result column="length_right" property="lengthRight" ></result>
        <result property="addressName" column="address_name"/>
        <result property="region" column="region"/>
        <result property="acrossMountain" column="across_mountain"/>
        <result property="type" column="type"/>
        <result property="heightLimit" column="height_limit"/>
        <result property="speedLimit" column="speed_limit"/>
        <result property="safeRequire" column="safe_require"/>
        <result property="pClocations" column="p_clocations"/>
        <result property="fireLocations" column="fire_locations"/>
        <result property="isCharge" column="is_charge"/>
        <result property="oneLanes" column="one_lanes"/>
        <result property="twoLanes" column="two_lanes"/>
        <result property="useFlag" column="use_flag"/>
        <result property="img" column="img"/>
        <result property="images" column="images"/>
        <collection property="attachs" ofType="com.bt.itscore.domain.vo.FacilityAttachVO"
			select="selectAttach" column="{facilityNo=facility_no}">
    	</collection>
    </resultMap>

    <resultMap type="com.bt.itsdevice.domain.vo.FacilityTunnelVO" id="facilityTunnelDetailMap">
        <result property="facilityNo" column="facility_no"/>
        <result property="facilityName" column="facility_name" ></result>
        <result property="roadNo" column="road_no" ></result>
        <result property="facilityTypeNo" column="facility_type_no" ></result>
        <result property="milePost"  column="mile_post"></result>
        <result property="upFacilityNo" column="up_facility_no" ></result>
        <result property="mpValue" column="mp_value" ></result>
        <result property="sort" column="sort" ></result>
        <result property="lng"  column="lng"></result>
        <result property="lat" column="lat"></result>
        <result property="roadName" column="road_name" ></result>
        <result property="facilityCode" column="facility_code" ></result>
        <result property="beforeFacilityNo" column="before_facility_no"/>
        <result property="afterFacilityNo" column="after_facility_no"/>
        <result property="startMilePost" column="start_mile_post"/>
        <result property="endMilePost" column="end_mile_post"/>
        <result property="length" column="length"/>
        <result column="start_mile_post_right" property="startMilePostRight" ></result>  
        <result column="end_mile_post_right" property="endMilePostRight" ></result>
        <result column="length_right" property="lengthRight" ></result>
        <result property="addressName" column="address_name"/>
        <result property="region" column="region"/>
        <result property="acrossMountain" column="across_mountain"/>
        <result property="type" column="type"/>
        <result property="heightLimit" column="height_limit"/>
        <result property="speedLimit" column="speed_limit"/>
        <result property="safeRequire" column="safe_require"/>
        <result property="pClocations" column="p_clocations"/>
        <result property="fireLocations" column="fire_locations"/>
        <result property="isCharge" column="is_charge"/>
        <result property="oneLanes" column="one_lanes"/>
        <result property="twoLanes" column="two_lanes"/>
        <result property="useFlag" column="use_flag"/>
        <result property="img" column="img"/>
        <result property="images" column="images"/>
        <collection property="attachs" ofType="com.bt.itscore.domain.vo.FacilityAttachVO">
	        <id column="id" property="id"/>
			<result column="facility_no" property="facilityNo"/>
			<result column="file_name" property="fileName"/>
			<result column="disk_file_name" property="diskFileName"/>
			<result column="file_size" property="fileSize"/>
			<result column="content_type" property="contentType"/>
			<result column="digest" property="digest"/>
			<result column="disk_directory" property="diskDirectory"/>
			<result column="create_time" property="createTime"/>
    	</collection>
    </resultMap>
    
    <resultMap type="com.bt.itscore.domain.vo.FacilityAttachVO" id="AttachMap">
		<id column="id" property="id"/>
		<result column="facility_no" property="facilityNo"/>
		<result column="file_name" property="fileName"/>
		<result column="disk_file_name" property="diskFileName"/>
		<result column="file_size" property="fileSize"/>
		<result column="content_type" property="contentType"/>
		<result column="digest" property="digest"/>
		<result column="disk_directory" property="diskDirectory"/>
		<result column="create_time" property="createTime"/>
	</resultMap>
	<select id="selectAttach" parameterType="com.bt.itsdevice.domain.dto.FacilityDTO" resultMap="AttachMap">
		SELECT * FROM facility_attach WHERE facility_no=#{facilityNo}	
	</select>

    <select id="selectList" resultMap="facilityTunnelMap" parameterType="com.bt.itsdevice.domain.dto.FacilityTunnelDTO">
        SELECT f.facility_name,f.road_no,f.facility_type_no,f.mile_post,f.up_facility_no,f.mp_value,f.sort,
        f.lng,f.lat,f.facility_code,r.road_alias,r.road_name,ft.*
        FROM facility f, facility_tunnel ft ,road r
        WHERE f.facility_type_no=2
        AND f.facility_no=ft.facility_no
        AND f.road_no=r.road_no
        AND f.facility_no IN (Facility-Permissions-Check)
        <if test="facilityName != null &amp;&amp; facilityName != '' "> AND f.facility_name like CONCAT('%', #{facilityName}, '%') </if>
        <if test="roadNo != null &amp;&amp; roadNo > 0 "> AND f.road_no=#{roadNo}</if>
        <if test="useFlag != null &amp;&amp; useFlag != '' "> AND ft.useFlag=#{useFlag}</if>
        ORDER BY f.FACILITY_NO ASC
    </select>

    <select id="selectAll" resultMap="facilityTunnelDetailMap" parameterType="com.bt.itsdevice.domain.dto.FacilityTunnelDTO">
        SELECT f.facility_name,f.road_no,f.facility_type_no,f.mile_post,f.up_facility_no,f.mp_value,f.sort,
        f.lng,f.lat,f.facility_code,r.road_alias,r.road_name,ft.*,
        fa.id,fa.file_name,fa.disk_file_name,fa.file_size,fa.content_type,fa.digest,fa.disk_directory,fa.create_time
        FROM facility_tunnel ft ,road r,facility f LEFT JOIN facility_attach fa ON f.facility_no=fa.facility_no
        WHERE f.facility_type_no=2
        AND f.facility_no=ft.facility_no
        AND f.road_no=r.road_no
        AND f.facility_no IN (Facility-Permissions-Check)
        <if test="facilityName != null &amp;&amp; facilityName != '' "> AND f.facility_name like CONCAT('%', #{facilityName}, '%') </if>
        <if test="roadNo != null &amp;&amp; roadNo > 0 "> AND f.road_no=#{roadNo}</if>
        <if test="useFlag != null &amp;&amp; useFlag != '' "> AND ft.useFlag=#{useFlag}</if>
        ORDER BY f.FACILITY_NO ASC
    </select>

    <select id="maxFacilityNo" resultMap="facilityTunnelMap">
		select IFNULL(max(facility_no),0) facility_no from facility_station
	</select>

    <insert id="add" parameterType="com.bt.itsdevice.domain.dto.FacilityTunnelDTO">
		insert into facility_tunnel (
			facility_no,before_facility_no,after_facility_no,start_mile_post,end_mile_post,address_name,
			region,across_mountain,length,type,height_limit,speed_limit,safe_require,p_clocations,fire_locations,
			one_lanes,two_lanes,img,images,start_mile_post_right,end_mile_post_right,length_right
		)
		values
		(
			#{facilityNo},
			#{beforeFacilityNo},
			#{afterFacilityNo},
			#{startMilePost},
			#{endMilePost},
			#{addressName},
			#{region},
			#{acrossMountain},
			#{length},
			#{type},
			#{heightLimit},
			#{speedLimit},
			#{safeRequire},
			#{pClocations},
			#{fireLocations},
			#{oneLanes},
			#{twoLanes},
			#{img},
			#{images},
			#{startMilePostRight},
			#{endMilePostRight},
			#{lengthRight}
			)
	</insert>

    <insert id="update" parameterType="com.bt.itsdevice.domain.dto.FacilityTunnelDTO">
		update facility_tunnel
		set
			before_facility_no=#{beforeFacilityNo},
			after_facility_no=#{afterFacilityNo},
			start_mile_post=#{startMilePost},
			end_mile_post=#{endMilePost},
			length=#{length},
			start_mile_post_right=#{startMilePostRight},
			end_mile_post_right=#{endMilePostRight},
			length_right=#{lengthRight},
			address_name=#{addressName},
			region=#{region},
			across_mountain=#{acrossMountain},
			type=#{type},
			height_limit=#{heightLimit},
			speed_limit=#{speedLimit},
			safe_require=#{safeRequire},
			p_clocations=#{pClocations},
			fire_locations=#{fireLocations},
			one_lanes=#{oneLanes},
			two_lanes=#{twoLanes},
			img=#{img},
			images=#{images}
		where facility_no=#{facilityNo}
	</insert>

    <delete id="delete" parameterType="com.bt.itsdevice.domain.dto.FacilityTunnelDTO">
		delete from facility_tunnel where facility_no=#{facilityNo}
	</delete>


    <delete id="batchDelete" parameterType="com.bt.itsdevice.domain.dto.FacilityTunnelDTO">
        delete from facility_tunnel where facility_no IN
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>


</mapper>