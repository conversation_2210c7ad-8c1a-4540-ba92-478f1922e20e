package com.bt.itsdevice.domain.dto;

/**
 * 设施-大桥
 * 
 * <AUTHOR>
 * @date 2021-03-17 09:54:03
 */
public class FacilityBridgeDTO extends FacilityDTO {

	/**
	 * 设施主键-no
	 */
//	private String facilityNo;
	/**
	 * 前端收费站NO
	 */
	private String beforeFacilityNo;
	/**
	 * 后端收费站NO
	 */
	private String afterFacilityNo;
	/**
	 * 所在地名
	 */
	private String addressName;
	/**
	 * 所属行政区
	 */
	private String region;
	/**
	 * 穿越河流
	 */
	private String acrossRiver;
	/**
	 * 桥梁长度
	 */
	private String length;
	/**
	 * 通航高度
	 */
	private String height;
	/**
	 * 主跨长度
	 */
	private String mainSpanLen;
	/**
	 * 引桥长度
	 */
	private String brApproachLen;
	/**
	 * 单向车道数
	 */
	private Integer oneLanes;
	/**
	 * 双向车道数
	 */
	private Integer twoLanes;
	/**
	 * 限速
	 */
	private String speedLimit;
	/**
	 * 限重
	 */
	private String weightLimit;
	/**
	 * 安全规定
	 */
	private String safeRequire;
	/**
	 * 收费情况
	 */
	private Integer isCharge;
	/**
	 * 起点桩号
	 */
	private String startMilePost;
	/**
	 * 终点桩号
	 */
	private String endMilePost;

	/**
	 * 大桥图片
	 */
	private String images;

	public String getImages() {
		return images;
	}

	public void setImages(String images) {
		this.images = images;
	}
	
	/**
	 * 设置：设施主键-no
	 */
//	public void setFacilityNo(String facilityNo) {
//		this.facilityNo = facilityNo;
//	}
	/**
	 * 获取：设施主键-no
	 */
//	public String getFacilityNo() {
//		return facilityNo;
//	}
	/**
	 * 设置：前端收费站NO
	 */
	public void setBeforeFacilityNo(String beforeFacilityNo) {
		this.beforeFacilityNo = beforeFacilityNo;
	}
	/**
	 * 获取：前端收费站NO
	 */
	public String getBeforeFacilityNo() {
		return beforeFacilityNo;
	}
	/**
	 * 设置：后端收费站NO
	 */
	public void setAfterFacilityNo(String afterFacilityNo) {
		this.afterFacilityNo = afterFacilityNo;
	}
	/**
	 * 获取：后端收费站NO
	 */
	public String getAfterFacilityNo() {
		return afterFacilityNo;
	}
	/**
	 * 设置：所在地名
	 */
	public void setAddressName(String addressName) {
		this.addressName = addressName;
	}
	/**
	 * 获取：所在地名
	 */
	public String getAddressName() {
		return addressName;
	}
	/**
	 * 设置：所属行政区
	 */
	public void setRegion(String region) {
		this.region = region;
	}
	/**
	 * 获取：所属行政区
	 */
	public String getRegion() {
		return region;
	}
	/**
	 * 设置：穿越河流
	 */
	public void setAcrossRiver(String acrossRiver) {
		this.acrossRiver = acrossRiver;
	}
	/**
	 * 获取：穿越河流
	 */
	public String getAcrossRiver() {
		return acrossRiver;
	}
	/**
	 * 设置：桥梁长度
	 */
	public void setLength(String length) {
		this.length = length;
	}
	/**
	 * 获取：桥梁长度
	 */
	public String getLength() {
		return length;
	}
	/**
	 * 设置：通航高度
	 */
	public void setHeight(String height) {
		this.height = height;
	}
	/**
	 * 获取：通航高度
	 */
	public String getHeight() {
		return height;
	}
	/**
	 * 设置：主跨长度
	 */
	public void setMainSpanLen(String mainSpanLen) {
		this.mainSpanLen = mainSpanLen;
	}
	/**
	 * 获取：主跨长度
	 */
	public String getMainSpanLen() {
		return mainSpanLen;
	}
	/**
	 * 设置：引桥长度
	 */
	public void setBrApproachLen(String brApproachLen) {
		this.brApproachLen = brApproachLen;
	}
	/**
	 * 获取：引桥长度
	 */
	public String getBrApproachLen() {
		return brApproachLen;
	}
	/**
	 * 设置：单向车道数
	 */
	public void setOneLanes(Integer oneLanes) {
		this.oneLanes = oneLanes;
	}
	/**
	 * 获取：单向车道数
	 */
	public Integer getOneLanes() {
		return oneLanes;
	}
	/**
	 * 设置：双向车道数
	 */
	public void setTwoLanes(Integer twoLanes) {
		this.twoLanes = twoLanes;
	}
	/**
	 * 获取：双向车道数
	 */
	public Integer getTwoLanes() {
		return twoLanes;
	}
	/**
	 * 设置：限速
	 */
	public void setSpeedLimit(String speedLimit) {
		this.speedLimit = speedLimit;
	}
	/**
	 * 获取：限速
	 */
	public String getSpeedLimit() {
		return speedLimit;
	}
	/**
	 * 设置：限重
	 */
	public void setWeightLimit(String weightLimit) {
		this.weightLimit = weightLimit;
	}
	/**
	 * 获取：限重
	 */
	public String getWeightLimit() {
		return weightLimit;
	}
	/**
	 * 设置：安全规定
	 */
	public void setSafeRequire(String safeRequire) {
		this.safeRequire = safeRequire;
	}
	/**
	 * 获取：安全规定
	 */
	public String getSafeRequire() {
		return safeRequire;
	}
	/**
	 * 设置：收费情况
	 */
	public void setIsCharge(Integer isCharge) {
		this.isCharge = isCharge;
	}
	/**
	 * 获取：收费情况
	 */
	public Integer getIsCharge() {
		return isCharge;
	}
	/**
	 * 设置：起点桩号
	 */
	public void setStartMilePost(String startMilePost) {
		this.startMilePost = startMilePost;
	}
	/**
	 * 获取：起点桩号
	 */
	public String getStartMilePost() {
		return startMilePost;
	}
	/**
	 * 设置：终点桩号
	 */
	public void setEndMilePost(String endMilePost) {
		this.endMilePost = endMilePost;
	}
	/**
	 * 获取：终点桩号
	 */
	public String getEndMilePost() {
		return endMilePost;
	}
}
