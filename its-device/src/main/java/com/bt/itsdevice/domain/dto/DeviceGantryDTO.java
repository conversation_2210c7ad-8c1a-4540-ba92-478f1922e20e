package com.bt.itsdevice.domain.dto;

import com.bt.itscore.domain.dto.DeviceDTO;

import javax.validation.constraints.NotBlank;

public class DeviceGantryDTO extends DeviceDTO {
//private String deviceId;
@NotBlank(message = "编号不能为空")
private String code;
private String type;

//public String getDeviceId() {
//    return deviceId;
//}
//
//public void setDeviceId(String deviceId) {
//    this.deviceId = deviceId;
//}

public String getCode() {
    return code;
}

public void setCode(String code) {
    this.code = code;
}

public String getType() {
    return type;
}

public void setType(String type) {
    this.type = type;
}
}
