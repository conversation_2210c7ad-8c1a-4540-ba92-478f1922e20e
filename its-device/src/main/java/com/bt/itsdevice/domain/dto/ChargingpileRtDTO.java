package com.bt.itsdevice.domain.dto;

import java.util.List;

import javax.validation.constraints.NotNull;

public class ChargingpileRtDTO {
	@NotNull(message = "开始时间不能为空")
	private Long startTime; //起始时间
	@NotNull(message = "结束时间不能为空")
	private Long endTime; // 终止时间
	private Integer roadNo; // 路段编号
	private String facilityNo; // 服务区编号
	private List<String> facilityNos; // 服务区编号
	private Integer top; //top几
	private String startTimeStr;
	private String endTimeStr;

	public Long getStartTime() {
		return startTime;
	}

	public void setStartTime(Long startTime) {
		this.startTime = startTime;
	}

	public Long getEndTime() {
		return endTime;
	}

	public void setEndTime(Long endTime) {
		this.endTime = endTime;
	}

	public Integer getRoadNo() {
		return roadNo;
	}

	public void setRoadNo(Integer roadNo) {
		this.roadNo = roadNo;
	}

	public String getFacilityNo() {
		return facilityNo;
	}

	public void setFacilityNo(String facilityNo) {
		this.facilityNo = facilityNo;
	}

	public List<String> getFacilityNos() {
		return facilityNos;
	}

	public void setFacilityNos(List<String> facilityNos) {
		this.facilityNos = facilityNos;
	}

	public Integer getTop() {
		return top;
	}

	public void setTop(Integer top) {
		this.top = top;
	}

	public String getStartTimeStr() {
		return startTimeStr;
	}

	public void setStartTimeStr(String startTimeStr) {
		this.startTimeStr = startTimeStr;
	}

	public String getEndTimeStr() {
		return endTimeStr;
	}

	public void setEndTimeStr(String endTimeStr) {
		this.endTimeStr = endTimeStr;
	}
}
