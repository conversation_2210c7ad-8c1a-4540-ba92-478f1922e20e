package com.bt.itsdevice.domain.dto;

/**
 * 设施-隧道
 * 
 * <AUTHOR>
 * @date 2021-03-16 14:23:06
 */
public class FacilityTunnelDTO extends FacilityDTO {

	/**
	 * 设施NO
	 */
//	private String facilityNo;
	/**
	 * 前端收费站
	 */
	private String beforeFacilityNo;
	/**
	 * 后端收费站
	 */
	private String afterFacilityNo;

	private String startMilePost; // 左洞起点桩号

	private String endMilePost;// 左洞终点桩号
	
	private String length;// 左洞隧道长度
	
	private String startMilePostRight; // 右洞起点桩号

	private String endMilePostRight;// 右洞终点桩号
	
	private String lengthRight;// 右洞隧道长度

	/**
	 * 所在地名
	 */
	private String addressName;
	/**
	 * 所属行政区
	 */
	private String region;
	/**
	 * 跨越山脉
	 */
	private String acrossMountain;

	/**
	 * 隧道类型 1单孔 2双孔
	 */
	private Integer type;
	/**
	 * 限高
	 */
	private String heightLimit;
	/**
	 * 限速
	 */
	private String speedLimit;
	/**
	 * 安全规定
	 */
	private String safeRequire;
	/**
	 * 人(车)行横道及其位置
	 */
	private String pClocations;
	/**
	 * 消防设施及其位置
	 */
	private String fireLocations;
	/**
	 * 
	 */
	private Integer isCharge;
	/**
	 * 单向车道数
	 */
	private Integer oneLanes;
	/**
	 * 双向车道数
	 */
	private Integer twoLanes;
	/**
	 * 隧道使用标志 10启用，20未启用
	 */
	private String useFlag;
	/**
	 * 隧道底图
	 */
	private String img;

	/**
	 * 隧道图片
	 */
	private String images;

	public String getImages() {
		return images;
	}

	public void setImages(String images) {
		this.images = images;
	}
	/**
	 * 设置：设施NO
	 */
//	public void setFacilityNo(String facilityNo) {
//		this.facilityNo = facilityNo;
//	}
	/**
	 * 获取：设施NO
	 */

//	public String getFacilityNo() {
//		return facilityNo;
//	}
	/**
	 * 设置：前端收费站
	 */
	public void setBeforeFacilityNo(String beforeFacilityNo) {
		this.beforeFacilityNo = beforeFacilityNo;
	}

	/**
		 * 获取：前端收费站
		 */
	public String getBeforeFacilityNo() {
		return beforeFacilityNo;
	}

	/**
		 * 设置：后端收费站
		 */
	public void setAfterFacilityNo(String afterFacilityNo) {
		this.afterFacilityNo = afterFacilityNo;
	}

	/**
	 * 获取：后端收费站
	 */
	public String getAfterFacilityNo() {
		return afterFacilityNo;
	}

	/**
	 * 设置：起点桩号
	 */
	public void setStartMilePost(String startMilePost) {
		this.startMilePost = startMilePost;
	}

	/**
	 * 获取：起点桩号
	 */
	public String getStartMilePost() {
		return startMilePost;
	}

	/**
	 * 设置：终点桩号
	 */
	public void setEndMilePost(String endMilePost) {
		this.endMilePost = endMilePost;
	}

	/**
	 * 获取：终点桩号
	 */
	public String getEndMilePost() {
		return endMilePost;
	}

	/**
	 * 设置：所在地名
	 */
	public void setAddressName(String addressName) {
		this.addressName = addressName;
	}

	/**
	 * 获取：所在地名
	 */
	public String getAddressName() {
		return addressName;
	}

	/**
	 * 设置：所属行政区
	 */
	public void setRegion(String region) {
		this.region = region;
	}

	/**
	 * 获取：所属行政区
	 */
	public String getRegion() {
		return region;
	}

	/**
	 * 设置：跨越山脉
	 */
	public void setAcrossMountain(String acrossMountain) {
		this.acrossMountain = acrossMountain;
	}

	/**
	 * 获取：跨越山脉
	 */
	public String getAcrossMountain() {
		return acrossMountain;
	}

	/**
	 * 设置：隧道长度
	 */
	public void setLength(String length) {
		this.length = length;
	}

	/**
	 * 获取：隧道长度
	 */
	public String getLength() {
		return length;
	}

	/**
	 * 设置：隧道类型 1单孔 2双孔
	 */
	public void setType(Integer type) {
		this.type = type;
	}

	/**
	 * 获取：隧道类型 1单孔 2双孔
	 */
	public Integer getType() {
		return type;
	}

	/**
	 * 设置：限高
	 */
	public void setHeightLimit(String heightLimit) {
		this.heightLimit = heightLimit;
	}

	/**
	 * 获取：限高
	 */
	public String getHeightLimit() {
		return heightLimit;
	}

	/**
	 * 设置：限速
	 */
	public void setSpeedLimit(String speedLimit) {
		this.speedLimit = speedLimit;
	}

	/**
	 * 获取：限速
	 */
	public String getSpeedLimit() {
		return speedLimit;
	}

	/**
	 * 设置：安全规定
	 */
	public void setSafeRequire(String safeRequire) {
		this.safeRequire = safeRequire;
	}

	/**
	 * 获取：安全规定
	 */
	public String getSafeRequire() {
		return safeRequire;
	}

	/**
	 * 设置：人(车)行横道及其位置
	 */
	public void setPClocations(String pClocations) {
		this.pClocations = pClocations;
	}

	/**
	 * 获取：人(车)行横道及其位置
	 */
	public String getPClocations() {
		return pClocations;
	}

	/**
	 * 设置：消防设施及其位置
	 */
	public void setFireLocations(String fireLocations) {
		this.fireLocations = fireLocations;
	}

	/**
	 * 获取：消防设施及其位置
	 */
	public String getFireLocations() {
		return fireLocations;
	}

	/**
	 * 设置：
	 */
	public void setIsCharge(Integer isCharge) {
		this.isCharge = isCharge;
	}

	/**
	 * 获取：
	 */
	public Integer getIsCharge() {
		return isCharge;
	}

	/**
	 * 设置：单向车道数
	 */
	public void setOneLanes(Integer oneLanes) {
		this.oneLanes = oneLanes;
	}

	/**
	 * 获取：单向车道数
	 */
	public Integer getOneLanes() {
		return oneLanes;
	}

	/**
	 * 设置：双向车道数
	 */
	public void setTwoLanes(Integer twoLanes) {
		this.twoLanes = twoLanes;
	}

	/**
	 * 获取：双向车道数
	 */
	public Integer getTwoLanes() {
		return twoLanes;
	}

	/**
	 * 设置：隧道使用标志 10启用，20未启用
	 */
	public void setUseFlag(String useFlag) {
		this.useFlag = useFlag;
	}

	/**
	 * 获取：隧道使用标志 10启用，20未启用
	 */
	public String getUseFlag() {
		return useFlag;
	}

	/**
	 * 设置：
	 */
	public void setImg(String img) {
		this.img = img;
	}

	/**
	 * 获取：
	 */
	public String getImg() {
		return img;
	}

	public String getStartMilePostRight() {
		return startMilePostRight;
	}

	public void setStartMilePostRight(String startMilePostRight) {
		this.startMilePostRight = startMilePostRight;
	}

	public String getEndMilePostRight() {
		return endMilePostRight;
	}

	public void setEndMilePostRight(String endMilePostRight) {
		this.endMilePostRight = endMilePostRight;
	}

	public String getLengthRight() {
		return lengthRight;
	}

	public void setLengthRight(String lengthRight) {
		this.lengthRight = lengthRight;
	}

	public String getpClocations() {
		return pClocations;
	}

	public void setpClocations(String pClocations) {
		this.pClocations = pClocations;
	}
	
	
}
