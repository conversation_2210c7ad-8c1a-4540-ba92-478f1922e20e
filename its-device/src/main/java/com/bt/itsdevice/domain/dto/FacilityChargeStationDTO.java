package com.bt.itsdevice.domain.dto;

/**
 * 
 * @Description: 接入恒信充电站基础信息
 * <AUTHOR>
 * @date 2024年4月17日 上午10:17:27
 *
 */
public class FacilityChargeStationDTO {
	private String stationID; // 充电站ID。运营商平台自定义的唯一编码
	private String stationName; // 站点名，由业主指定
	private String province; // 省份
	private String roadName; // 道路-服务区-方向名称（非道路名称，这个是对接恒信接口返回的设施位置描述的字段）
	private String dcChargingStandard; // 直流费率
	private String acChargingStandard; // 交流费率
	private String stationTel; // 充电站电话
	private Integer stationType; // 充电站类型：1-慢充 2-快充 3-超充
	private Integer stationStatus; // 充电站状态:0：未知,1：建设中,5：关闭下线,6：维护中,50：正常使用
	private Integer parkNum; // 停车数量
	private Integer supportOrder; // 是否支持预约，0-不支持，1-支持
	private String addTime; // 站点新增时间
	private String updateTime; // 数据更新时间

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getRoadName() {
		return roadName;
	}

	public void setRoadName(String roadName) {
		this.roadName = roadName;
	}

	public String getStationName() {
		return stationName;
	}

	public void setStationName(String stationName) {
		this.stationName = stationName;
	}

	public String getStationID() {
		return stationID;
	}

	public void setStationID(String stationID) {
		this.stationID = stationID;
	}

	public String getDcChargingStandard() {
		return dcChargingStandard;
	}

	public void setDcChargingStandard(String dcChargingStandard) {
		this.dcChargingStandard = dcChargingStandard;
	}

	public String getAcChargingStandard() {
		return acChargingStandard;
	}

	public void setAcChargingStandard(String acChargingStandard) {
		this.acChargingStandard = acChargingStandard;
	}

	public String getStationTel() {
		return stationTel;
	}

	public void setStationTel(String stationTel) {
		this.stationTel = stationTel;
	}

	public Integer getStationStatus() {
		return stationStatus;
	}

	public void setStationStatus(Integer stationStatus) {
		this.stationStatus = stationStatus;
	}

	public Integer getParkNum() {
		return parkNum;
	}

	public void setParkNum(Integer parkNum) {
		this.parkNum = parkNum;
	}

	public Integer getSupportOrder() {
		return supportOrder;
	}

	public void setSupportOrder(Integer supportOrder) {
		this.supportOrder = supportOrder;
	}

	public String getAddTime() {
		return addTime;
	}

	public void setAddTime(String addTime) {
		this.addTime = addTime;
	}

	public String getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}

	public Integer getStationType() {
		return stationType;
	}

	public void setStationType(Integer stationType) {
		this.stationType = stationType;
	}

}
