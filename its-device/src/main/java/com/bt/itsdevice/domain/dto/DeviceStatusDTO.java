package com.bt.itsdevice.domain.dto;

import java.util.List;

import javax.validation.constraints.NotBlank;

/**
 * 
 * @Description: 设备状态统计查询参数
 * <AUTHOR>
 * @date 2023年7月19日 上午9:03:27
 *
 */
public class DeviceStatusDTO {
	private String topOrgId;// 所属单位-根节点北投集团
	private String parentOrgId;// 所属单位-父节点上级分公司
	private String orgId;// 所属单位-所处查询单位-运营中心
	private Integer roadNo;// 所属路段
	private String facilityNo;// 所属设施
	@NotBlank(message = "设备查询类型不能为空")
	private String itemVale;// 设备数据字典设备查询分类值
	private String deviceTypeNo;// 设备类型
	private String deviceName;// 设备名称
	private Integer status;// 设备状态 0-离线，1-在线，2-故障
	// 外场全部类型查询
	private List<String> types;
	private List<String> roleIds; //用户具有的角色

	public String getTopOrgId() {
		return topOrgId;
	}

	public void setTopOrgId(String topOrgId) {
		this.topOrgId = topOrgId;
	}

	public String getParentOrgId() {
		return parentOrgId;
	}

	public void setParentOrgId(String parentOrgId) {
		this.parentOrgId = parentOrgId;
	}

	public String getOrgId() {
		return orgId;
	}

	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}

	public Integer getRoadNo() {
		return roadNo;
	}

	public void setRoadNo(Integer roadNo) {
		this.roadNo = roadNo;
	}

	public String getFacilityNo() {
		return facilityNo;
	}

	public void setFacilityNo(String facilityNo) {
		this.facilityNo = facilityNo;
	}

	public String getItemVale() {
		return itemVale;
	}

	public void setItemVale(String itemVale) {
		this.itemVale = itemVale;
	}

	public String getDeviceTypeNo() {
		return deviceTypeNo;
	}

	public void setDeviceTypeNo(String deviceTypeNo) {
		this.deviceTypeNo = deviceTypeNo;
	}

	public String getDeviceName() {
		return deviceName;
	}

	public void setDeviceName(String deviceName) {
		this.deviceName = deviceName;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public List<String> getTypes() {
		return types;
	}

	public void setTypes(List<String> types) {
		this.types = types;
	}

	public List<String> getRoleIds() {
		return roleIds;
	}

	public void setRoleIds(List<String> roleIds) {
		this.roleIds = roleIds;
	}

	

}
