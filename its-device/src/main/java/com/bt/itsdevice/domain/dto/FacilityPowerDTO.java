package com.bt.itsdevice.domain.dto;

import java.util.ArrayList;
import java.util.List;

/**
 * 
 * @Description: 电力监控设施变电房
 * <AUTHOR>
 * @date 2022年10月26日 上午10:00:38
 *
 */
public class FacilityPowerDTO extends FacilityDTO {
	private String type;// 设施类型
	private String transformers;// 变压器数量
	private String installCapacity;//装机容量
	private String reportCapacity;// 申报需量
	private String commands;//获取gis页面状态指令
	private List<String> voltageLevels = new ArrayList<>();//电压等级
	private List<String> backupPowers= new ArrayList<>();//备用电源类型
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	
	public String getTransformers() {
		return transformers;
	}
	public void setTransformers(String transformers) {
		this.transformers = transformers;
	}
	public String getInstallCapacity() {
		return installCapacity;
	}
	public void setInstallCapacity(String installCapacity) {
		this.installCapacity = installCapacity;
	}
	public String getReportCapacity() {
		return reportCapacity;
	}
	public void setReportCapacity(String reportCapacity) {
		this.reportCapacity = reportCapacity;
	}
	public List<String> getVoltageLevels() {
		return voltageLevels;
	}
	public void setVoltageLevels(List<String> voltageLevels) {
		this.voltageLevels = voltageLevels;
	}
	public List<String> getBackupPowers() {
		return backupPowers;
	}
	public void setBackupPowers(List<String> backupPowers) {
		this.backupPowers = backupPowers;
	}
	
	public String getCommands() {
		return commands;
	}

	public void setCommands(String commands) {
		this.commands = commands;
	}

}
