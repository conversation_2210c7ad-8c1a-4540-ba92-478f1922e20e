package com.bt.itsemer.domain.dto;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.bt.itsemer.domain.entity.Post;
import com.bt.itsemer.domain.entity.Road;
import com.bt.itsemer.domain.entity.User;

public class EmerGroupDTO {
	private String id;
	@NotBlank(message = "应急小组名称不能为空")
	private String groupName;//varchar(50)应急小组名称
	private String orgId;
	@NotNull(message = "管辖路段roads不能为空")
	private List<Road> roads;
	@NotBlank(message = "所属应急角色ID不能为空")
	private String roleId;//varchar(36)所属应急角色编号，emer_role主键
	private List<User> users;//varchar(36)小组人员用户ID
	private List<Post> posts;//人员岗位职责
	private String keyWord;//查询关键字
	private Integer startMile;//int(11)管辖范围：起始桩号
	private Integer sort;//排序
	private List<String> sysRoleIds;//系统角色
	private Integer infoReview;//信息审核标识
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getGroupName() {
		return groupName;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	public String getOrgId() {
		return orgId;
	}
	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}
	public List<Road> getRoads() {
		return roads;
	}
	public void setRoads(List<Road> roads) {
		this.roads = roads;
	}
	public String getRoleId() {
		return roleId;
	}
	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}
	public List<User> getUsers() {
		return users;
	}
	public void setUsers(List<User> users) {
		this.users = users;
	}
	public List<Post> getPosts() {
		return posts;
	}
	public void setPosts(List<Post> posts) {
		this.posts = posts;
	}
	public String getKeyWord() {
		return keyWord;
	}
	public void setKeyWord(String keyWord) {
		this.keyWord = keyWord;
	}
	
	public Integer getStartMile() {
		return startMile;
	}
	public void setStartMile(Integer startMile) {
		this.startMile = startMile;
	}
	public Integer getSort() {
		return sort;
	}
	public void setSort(Integer sort) {
		this.sort = sort;
	}
	public List<String> getSysRoleIds() {
		return sysRoleIds;
	}
	public void setSysRoleIds(List<String> sysRoleIds) {
		this.sysRoleIds = sysRoleIds;
	}
	public Integer getInfoReview() {
		return infoReview;
	}
	public void setInfoReview(Integer infoReview) {
		this.infoReview = infoReview;
	}

}
