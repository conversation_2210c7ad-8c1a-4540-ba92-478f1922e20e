package com.bt.itsemer.controller;

import java.util.Arrays;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bt.itscore.auth.Login;
import com.bt.itscore.domain.dto.IdStringDTO;
import com.bt.itscore.domain.dto.PageDTO;
import com.bt.itscore.domain.vo.PageVO;
import com.bt.itscore.domain.vo.ResponseVO;
import com.bt.itscore.exception.AuthzException;
import com.bt.itscore.utils.ValidUtils;
import com.bt.itsemer.domain.dto.DeleteEmerRoleDTO;
import com.bt.itsemer.domain.dto.EmerGroupDTO;
import com.bt.itsemer.domain.dto.EmerPersonMatchDTO;
import com.bt.itsemer.domain.dto.EmerRoleDTO;
import com.bt.itsemer.domain.dto.UpdateEmerRoleDTO;
import com.bt.itsemer.service.EmerPersonService;

/**
* <AUTHOR>
* @date 2021年9月29日 下午2:58:35
* @Description 应急人员管理控制类
 */
@RestController
@RequestMapping("emerPerson")
public class EmerPersonController {
	@Autowired
	private EmerPersonService emerPersonService;
	
	/**
	 * @api {POST} emerPerson/addRole 应急工作组-新增emerPerson/addRole
	 * @apiGroup 应急人员EmerPersonController
	 * @apiHeader {String} Authorization token
	 * @apiBody {String} roleName=应急工作组名称 应急小组名称
	 * @apiBody {String} [content=工作职责] 工作职责
	 * @apiBody {String[]} orgIds=【"4a5c49a9-8152-4d38-9666-e536725c6670"】 公司ID
	 * @apiBody {Number} [value=1] 值，1-领导组、2-排障组、3-客服组、4-养护组、5-路政组、6-交警组
	 * @apiBody {Number} [sort=1] 排序
	 * @apiBody {Number} delStatus=0 0-启用，1-禁用
	 * @apiSuccess (Success 200) {Number} code 状态码，1-成功，0-失败
	 * @apiSuccess (Success 200) {String} message 提示消息
	 * @apiSuccessExample {json} Success-Response:
			HTTP/1.1 200 OK
			{
				"message": "SUCCESS"
				"code": 1
			}
     * @apiErrorExample {json} Fail-Response:
     *     HTTP/1.1 200 OK
	 *     {
	 *       "message": "FAIL"
	 *       "code": 0
	 *     }
	 * @apiSampleRequest /emerPerson/addRole
	 */
	@Login
	@PostMapping("addRole")
	public Object addRole(@Valid @RequestBody EmerRoleDTO dto, BindingResult result) {
		ValidUtils.error(result);
		return new ResponseVO(emerPersonService.addRole(dto));
	}
	
	/**
	 * @api {POST} emerPerson/updateRole 应急工作组-修改emerPerson/updateRole
	 * @apiGroup 应急人员EmerPersonController
	 * @apiHeader {String} Authorization token
	 * @apiBody {String} id=9424578b-c7bf-4711-90a9-5d5bf8b4188f 应急小组ID
	 * @apiBody {String} roleName=应急工作组名称 应急小组名称
	 * @apiBody {String} [content=工作职责] 工作职责
	 * @apiBody {String[]} orgIds=【"4a5c49a9-8152-4d38-9666-e536725c6670"】 公司ID
	 * @apiBody {Number} [value=1] 值，1-领导组、2-排障组、3-客服组、4-养护组、5-路政组、6-交警组
	 * @apiBody {Number} sort=1 排序
	 * @apiBody {Number} delStatus=0 0-启用，1-禁用
	 * @apiSuccess (Success 200) {Number} code 状态码，1-成功，0-失败
	 * @apiSuccess (Success 200) {String} message 提示消息
	 * @apiSuccessExample {json} Success-Response:
			HTTP/1.1 200 OK
			{
				"message": "SUCCESS"
				"code": 1
			}
     * @apiErrorExample {json} Fail-Response:
     *     HTTP/1.1 200 OK
	 *     {
	 *       "message": "FAIL"
	 *       "code": 0
	 *     }
	 * @apiSampleRequest /emerPerson/updateRole
	 */
	@Login
	@PostMapping("updateRole")
	public Object updateRole(@Valid @RequestBody UpdateEmerRoleDTO dto, BindingResult result) {
		ValidUtils.error(result);
		return new ResponseVO(emerPersonService.updateRole(dto));
	}
	
	/**
	 * @api {POST} emerPerson/pageRole 应急工作组-分页查询emerPerson/pageRole
	 * @apiGroup 应急人员EmerPersonController
	 * @apiHeader {String} Authorization token
	 * @apiParam {Number} page=1 页码
	 * @apiParam {Number} limit=10 条数
	 * @apiBody {String} [keyword=名称] 关键字
	 * @apiBody {String} [orgId=4a5c49a9-8152-4d38-9666-e536725c6670] 公司
	 * @apiBody {Number} [delStatus=0] 0-未删除/启用，0-已删除/禁用
	 * @apiSuccess (Success 200) {String} id 应急工作组ID
	 * @apiSuccess (Success 200) {String} roleName 应急工作组名称
	 * @apiSuccess (Success 200) {String} content 工作职责
	 * @apiSuccess (Success 200) {Number} sort 排序
	 * @apiSuccess (Success 200) {Number} value 值，1-领导组、2-排障组、3-客服组、4-养护组、5-路政组、6-交警组
	 * @apiSuccess (Success 200) {Number} delStatus 0-启用，1-禁用
	 * @apiSuccess (Success 200) {Object[]} orgs 公司
	 * @apiSuccess (Success 200) {String} orgs.orgId 公司ID
	 * @apiSuccess (Success 200) {String} orgs.orgName 公司名称
	 * @apiSuccess (Success 200) {String} orgs.shortName 公司简称
	 * @apiSuccessExample {json} Success-Response:
			HTTP/1.1 200 OK
			{
				"total": 1,
				"items": [{
					"id":"9424578b-c7bf-4711-90a9-5d5bf8b4188f",
					"roleName": "应急工作组名称",
					"content": "工作职责",
					"sort":1,
					"value":1,
					"delStatus":0,
					"orgs":[{
						"orgIds":"4a5c49a9-8152-4d38-9666-e536725c6670",
						"orgNames":"钦州运营管理中心",
						"shortNames":"钦州运营管理中心"
					}]
				}]
			}
     * @apiErrorExample {json} Fail-Response:
			HTTP/1.1 200 OK
			[]
	 * @apiSampleRequest /emerPerson/pageRole
	 */
	@Login
	@PostMapping("pageRole")
	public Object pageRole(@Valid PageDTO pageDTO, @RequestBody EmerRoleDTO dto, BindingResult result, HttpServletRequest request) {
		ValidUtils.error(result);
		String role = (String)request.getAttribute("role");
	    if(role == null || role.length() < 1) {
	        throw new AuthzException("没有角色权限");
	    }
	    dto.setSysRoleIds(Arrays.asList(role.split(";")));
		return new PageVO(emerPersonService.pageRole(pageDTO, dto));
	}
	
	/**
	 * @api {POST} emerPerson/deleteRole 应急工作组-删除emerPerson/deleteRole
	 * @apiGroup 应急人员EmerPersonController
	 * @apiHeader {String} Authorization token
	 * @apiBody {String} id=9424578b-c7bf-4711-90a9-5d5bf8b4188f 应急小组ID
	 * @apiBody {Number} delStatus=0 删除标识，1-删除/禁用，0-未删除/启用
	 * @apiSuccess (Success 200) {Number} code 状态码，1-成功，0-失败
	 * @apiSuccess (Success 200) {String} message 提示消息
	 * @apiSuccessExample {json} Success-Response:
			HTTP/1.1 200 OK
			{
				"message": "SUCCESS"
				"code": 1
			}
     * @apiErrorExample {json} Fail-Response:
     *     HTTP/1.1 200 OK
	 *     {
	 *       "message": "FAIL"
	 *       "code": 0
	 *     }
	 * @apiSampleRequest /emerPerson/deleteRole
	 */
	@Login
	@PostMapping("deleteRole")
	public Object deleteRole(@Valid @RequestBody DeleteEmerRoleDTO dto, BindingResult result) {
		ValidUtils.error(result);
		return new ResponseVO(emerPersonService.deleteRole(dto));
	}
	
	/**
	 * @api {POST} emerPerson/addGroup 应急小组-新增emerPerson/addGroup
	 * @apiGroup 应急人员EmerPersonController
	 * @apiHeader {String} Authorization token
	 * @apiBody {String} groupName=应急小组名称 应急小组名称
	 * @apiBody {String} roleId=9424578b-c7bf-4711-90a9-5d5bf8b4188f 应急工作组ID
	 * @apiBody {Object[]} roads=【{"orgId":"4a5c49a9-8152-4d38-9666-e536725c6670","roadNo":174,"startMile":2055900,"endMile":2108400}】 管辖范围
	 * @apiBody {String} roads.orgId 所属公司
	 * @apiBody {Number} roads.roadNo 管辖路段
	 * @apiBody {Number} roads.startMile 起始桩号
	 * @apiBody {Number} roads.endMile 结束桩号
	 * @apiBody {Object[]} users=【{"userId":"e24bad6a-9215-4bcf-8fb0-f602fd5e1333","userName":"龙思颖"}】 应急人员
	 * @apiBody {String} users.userId 应急人员ID
	 * @apiBody {String} users.userName 应急人员姓名
	 * @apiBody {Object[]} [posts=【{"level":1,"post":1,"userIds":【"e24bad6a-9215-4bcf-8fb0-f602fd5e1333"】}】] 应急人员岗位职责
	 * @apiBody {Number} posts.level 事件等级，1、2、3、4
	 * @apiBody {Number} posts.post 岗位职级，1-组长，2-副组长
	 * @apiBody {String[]} posts.userIds 人员ID
	 * @apiBody {Number} sort=1 排序
	 * @apiSuccess (Success 200) {Number} code 状态码，1-成功，0-失败
	 * @apiSuccess (Success 200) {String} message 提示消息
	 * @apiSuccessExample {json} Success-Response:
			HTTP/1.1 200 OK
			{
				"message": "SUCCESS"
				"code": 1
			}
     * @apiErrorExample {json} Fail-Response:
     *     HTTP/1.1 200 OK
	 *     {
	 *       "message": "FAIL"
	 *       "code": 0
	 *     }
	 * @apiSampleRequest /emerPerson/addGroup
	 */
	@Login
	@PostMapping("addGroup")
	public Object addGroup(@Valid @RequestBody EmerGroupDTO dto, BindingResult result) {
		ValidUtils.error(result);
		return new ResponseVO(emerPersonService.addGroup(dto));
	}
	
	/**
	 * @api {POST} emerPerson/updateGroup 应急小组-修改emerPerson/updateGroup
	 * @apiGroup 应急人员EmerPersonController
	 * @apiHeader {String} Authorization token
	 * @apiBody {String} id=3c6549f9-6046-4171-bbb3-7f8e2fa650bb 应急小组ID
	 * @apiBody {String} roleId=9424578b-c7bf-4711-90a9-5d5bf8b4188f 应急工作组ID
	 * @apiBody {String} groupName=应急小组名称 应急小组名称
	 * @apiBody {Object[]} roads=【{"orgId":"4a5c49a9-8152-4d38-9666-e536725c6670","roadNo":174,"startMile":2055900,"endMile":2108400}】 管辖范围
	 * @apiBody {String} roads.orgId 所属公司
	 * @apiBody {Number} roads.roadNo 管辖路段
	 * @apiBody {Number} roads.startMile 起始桩号
	 * @apiBody {Number} roads.endMile 结束桩号
	 * @apiBody {Object[]} users=【{"userId":"e24bad6a-9215-4bcf-8fb0-f602fd5e1333","userName":"龙思颖"}】 应急人员
	 * @apiBody {String} users.userId 应急人员ID
	 * @apiBody {String} users.userName 应急人员姓名
	 * @apiBody {Object[]} [posts=【{"level":1,"post":1,"userIds":【"e24bad6a-9215-4bcf-8fb0-f602fd5e1333"】}】] 应急人员岗位职责
	 * @apiBody {Number} posts.level 事件等级，1、2、3、4
	 * @apiBody {Number} posts.post 岗位职级，1-组长，2-副组长
	 * @apiBody {String[]} posts.userIds 人员ID
	 * @apiBody {Number} sort=1 排序
	 * @apiSuccess (Success 200) {Number} code 状态码，1-成功，0-失败
	 * @apiSuccess (Success 200) {String} message 提示消息
	 * @apiSuccessExample {json} Success-Response:
			HTTP/1.1 200 OK
			{
				"message": "SUCCESS"
				"code": 1
			}
     * @apiErrorExample {json} Fail-Response:
			HTTP/1.1 200 OK
			{
				"message": "FAIL"
				"code": 0
			}
	 * @apiSampleRequest /emerPerson/updateGroup
	 */
	@Login
	@PostMapping("updateGroup")
	public Object updateGroup(@Valid @RequestBody EmerGroupDTO dto, BindingResult result) {
		ValidUtils.error(result);
		return new ResponseVO(emerPersonService.updateGroup(dto));
	}
	
	/**
	 * @api {POST} emerPerson/deleteGroup 应急小组-删除emerPerson/deleteGroup
	 * @apiGroup 应急人员EmerPersonController
	 * @apiHeader {String} Authorization token
	 * @apiBody {String} id=3c6549f9-6046-4171-bbb3-7f8e2fa650bb 应急小组ID
	 * @apiSuccess (Success 200) {Number} code 状态码，1-成功，0-失败
	 * @apiSuccess (Success 200) {String} message 提示消息
	 * @apiSuccessExample {json} Success-Response:
			HTTP/1.1 200 OK
			{
				"message": "SUCCESS"
				"code": 1
			}
     * @apiErrorExample {json} Fail-Response:
			HTTP/1.1 200 OK
			{
				"message": "FAIL"
				"code": 0
			}
	 * @apiSampleRequest /emerPerson/deleteGroup
	 */
	@Login
	@PostMapping("deleteGroup")
	public Object deleteGroup(@Valid @RequestBody IdStringDTO dto, BindingResult result) {
		ValidUtils.error(result);
		return new ResponseVO(emerPersonService.deleteGroup(dto));
	}
	
	/**
	 * @api {POST} emerPerson/pageGroup 应急小组-分页查询emerPerson/pageGroup
	 * @apiGroup 应急人员EmerPersonController
	 * @apiHeader {String} Authorization token
	 * @apiParam {Number} page=1 页码
	 * @apiParam {Number} limit=10 条数
	 * @apiBody {String} roleId=3c6549f9-6046-4171-bbb3-7f8e2fa650bb 应急工作组ID
	 * @apiBody {String} keyWord=关键字 关键字
	 * @apiSuccess (Success 200) {String} id 应急小组ID
	 * @apiSuccess (Success 200) {String} groupName 应急小组名称
	 * @apiSuccess (Success 200) {String} orgId 所属公司
	 * @apiSuccess (Success 200) {String} orgName 所属公司名称（表单回显）
	 * @apiSuccess (Success 200) {Object[]} roads 管辖范围
	 * @apiSuccess (Success 200) {String} roads.orgId 所属公司
	 * @apiSuccess (Success 200) {Number} roads.roadNo 管辖路段
	 * @apiSuccess (Success 200) {Number} roads.startMile 起始桩号
	 * @apiSuccess (Success 200) {Number} roads.endMile 结束桩号
	 * @apiSuccess (Success 200) {Object[]} users 应急人员
	 * @apiSuccess (Success 200) {String} users.userId 应急人员ID
	 * @apiSuccess (Success 200) {String} users.userName 应急人员姓名
	 * @apiSuccess (Success 200) {Object[]} posts 应急人员岗位职责
	 * @apiSuccess (Success 200) {Number} posts.level 事件等级，1、2、3、4
	 * @apiSuccess (Success 200) {Number} posts.post 岗位职级，1-组长，2-副组长
	 * @apiSuccess (Success 200) {String[]} posts.userIds 人员ID
	 * @apiSuccessExample {json} Success-Response:
			HTTP/1.1 200 OK
			{
				"total": 1,
				"items": [{
					"id":"9424578b-c7bf-4711-90a9-5d5bf8b4188f",
					"groupName": "应急小组名称",
					"orgId": "d9bffff7-4f6c-4768-94e8-2491ed592453",
					"orgName": "大化高速公路运营管理中心",
					"roads":[{
						"orgId":"9424578b-c7bf-4711-90a9-5d5bf8b4188f",
						"orgName": "运营中心",
						"rodeNo":174,
						"rodeName":"S7512",
						"mileScope":"K2055+900~K2108+400",
						"startMile":2055900,
						"endMile":2108400
					}],
					"users":[{
						"userId":"e24bad6a-9215-4bcf-8fb0-f602fd5e1333",
						"userName":"龙思颖"
					}],
					"posts":[{
			            "level": 1,
			            "post": 1,
			            "userIds": [
			                "e24bad6a-9215-4bcf-8fb0-f602fd5e1333"
			            ]
			        }]
				}]
			}
     * @apiErrorExample {json} Fail-Response:
			HTTP/1.1 200 OK
			[]
	 * @apiSampleRequest /emerPerson/pageGroup
	 */
	@Login
	@PostMapping("pageGroup")
	public Object pageGroup(@Valid PageDTO pageDTO, @RequestBody EmerGroupDTO dto, BindingResult result, HttpServletRequest request) {
		ValidUtils.error(result);
		String role = (String)request.getAttribute("role");
	    if(role == null || role.length() < 1) {
	        throw new AuthzException("没有角色权限");
	    }
	    dto.setSysRoleIds(Arrays.asList(role.split(";")));
		return new PageVO(emerPersonService.pageGroup(pageDTO, dto));
	}
	
	/**
	 * @api {POST} emerPerson/match 事件匹配应急人员emerPerson/match
     * @apiDescription 事件匹配应急人员；创建人：龙思颖，修改人：邓云钢，添加事件id条件，并且调整今日值班人员查询
	 * @apiGroup 应急人员EmerPersonController
	 * @apiHeader {String} Authorization token
	 * @apiBody {String} emerPlanLevelId=3c6549f9-6046-4171-bbb3-7f8e2fa650bb 应急预案等级id
	 * @apiBody {String} eventId 事件id
	 * @apiBody {Number} roadNo=174 路段编号
	 * @apiBody {Number} mile=2100000 整型桩号
	 * @apiBody {Number} level=1 预案等级值，普通-10，I级-1，II级-2，III级-3，IV级-4
	 * @apiSuccess (Success 200) {String} emerRoleId 应急工作组ID
	 * @apiSuccess (Success 200) {String} emerRoleName 应急工作组名称
	 * @apiSuccess (Success 200) {String} emerPlanFlag 1-已关联应急预案，0-未关联
	 * @apiSuccess (Success 200) {Object[]} emerGroups 应急小组
	 * @apiSuccess (Success 200) {String} emerGroups.emerGroupId 应急小组ID
	 * @apiSuccess (Success 200) {String} emerGroups.emerGroupName 应急小组名称
	 * @apiSuccess (Success 200) {Number} emerGroups.forceRemind 1-强提醒，0-不需要强提醒
	 * @apiSuccess (Success 200) {String} emerGroups.users 应急小组人员
	 * @apiSuccess (Success 200) {String} emerGroups.users.userId 应急小组人员ID
	 * @apiSuccess (Success 200) {String} emerGroups.users.userName 应急小组人员名称
	 * @apiSuccess (Success 200) {String} emerGroups.users.mobile 应急小组人员电话
	 * @apiSuccess (Success 200) {Number} emerGroups.users.post 应急小组人员职级，1-组长，2-副组长
	 * @apiSuccess (Success 200) {Number} emerGroups.users.dutyFlag 1-值班表，0-应急小组
	 * @apiSuccessExample {json} Success-Response:
			HTTP/1.1 200 OK
			[{
				"emerRoleId":"9424578b-c7bf-4711-90a9-5d5bf8b4188f",
				"emerRoleName": "应急工作组名称",
				"emerPlanFlag": 1,
				"emerGroups":[{
					"emerGroupId":"9424578b-c7bf-4711-90a9-5d5bf8b4188f",
					"emerGroupName": "应急小组名称",
					"forceRemind":1,
					"users":[{
						"userId":"e24bad6a-9215-4bcf-8fb0-f602fd5e1333",
						"userName":"龙思颖",
						"mobile":"18978868568",
						"dutyFlag":1,
						"post": 1
					}]
				}]
			}]
     * @apiErrorExample {json} Fail-Response:
			HTTP/1.1 200 OK
			[]
	 * @apiSampleRequest /emerPerson/match
	 */
	@Login
	@PostMapping("match")
	public Object match(@Valid @RequestBody EmerPersonMatchDTO dto, BindingResult result) {
		ValidUtils.error(result);
		return emerPersonService.match(dto);
	}
	
	//查询应急小组列表，应用：应急排班表查询下拉框
	@Login
	@PostMapping("selectGroup")
	public Object selectGroup(@RequestBody EmerGroupDTO dto, HttpServletRequest request) {
		String role = (String)request.getAttribute("role");
		if(role == null || role.length() < 1) {
	        throw new AuthzException("没有角色权限");
	    }
	    dto.setSysRoleIds(Arrays.asList(role.split(";")));
		return emerPersonService.selectGroup(dto);
	}
}
