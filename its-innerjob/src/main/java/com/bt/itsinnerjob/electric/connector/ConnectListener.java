package com.bt.itsinnerjob.electric.connector;

import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelFutureListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 描述：
 *
 * <AUTHOR>
 * @since 2023-12-19 15:39
 */
public class ConnectListener implements ChannelFutureListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(ConnectListener.class);
    private static final int CONN_TIME_OUT = 5 * 1000; //单次发起的最长连接超时时间，单位：s

    private String ip;

    private Integer port;

    private NettyClientManager nettyClientManager;

    private Integer status = 0; // 0：等待连接，1：连接成功


    public ConnectListener() {
    }

    public ConnectListener(String ip, Integer port) {
        this.ip = ip;
        this.port = port;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getIp() {
        return ip;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public NettyClientManager getNettyClientManager() {
        return nettyClientManager;
    }

    public void setNettyClientManager(NettyClientManager nettyClientManager) {
        this.nettyClientManager = nettyClientManager;
    }

    @Override
    public void operationComplete(ChannelFuture future) {
        String serverMsg = ip + ":" + port;
        boolean success = future.isSuccess();
        if (success) {
            LOGGER.info("发起连接,服务设备信息：{}", serverMsg);
            NettyManagerCache.removeFailServer(serverMsg);
            NettyManagerCache.addOnlineServer(serverMsg, this);
            return;
        }
        try {
            Thread.sleep(CONN_TIME_OUT);
        } catch (InterruptedException e) {
            LOGGER.error("发起连接，线程休眠被中断：" + e);
        }
        ChannelFuture close = future.channel().close();
        LOGGER.error("连接失败，关闭成功：{}，等待发起重连，服务设备信息：{}", close.isSuccess(), serverMsg);
        // 添加到服务设备连接失败缓存中
        NettyManagerCache.addFailServer(serverMsg, nettyClientManager);
        NettyManagerCache.removeOnlineServer(serverMsg);
    }
}
