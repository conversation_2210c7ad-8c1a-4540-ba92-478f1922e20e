package com.bt.itsinnerjob.controller;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.bt.itscore.domain.vo.ResponseVO;
import com.bt.itscore.utils.EncodeUtils;
import com.bt.itscore.utils.TimeUtils;
import com.bt.itsinnerjob.domain.dto.SysMetricsDTO;
@RequestMapping("heartbeat")
@RestController
public class HeartbeatController {
	@Value("${ms.url:msUrl}")
	private String msUrl;

    @Autowired
    RestTemplate restTemplate;

	@Value("${heart.beat.secret:u&jQ74kX45SFm3CD}")
	private String heartBeatSecret;

	@PostMapping("/send")
	public Object send(@RequestBody SysMetricsDTO dto) {
		ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		HttpServletRequest request = attributes.getRequest();
		String authorization = request.getHeader("Authorization");
		String validTime = request.getHeader("ykSolt");
		String token = EncodeUtils.encode(heartBeatSecret + validTime, "MD5");
		if(authorization == null || !authorization.equals(token)) {
			return new ResponseVO("Authorization校验未通过！", 401);
		}
		// HTTP（POST）请求阿里云接口（/its-ms/ms/serviceHeartBeat）
		// 入库服务名称，IP，来源（沿海、罗城、大化、昭平、灵山），时间，
		Map<String, Object> param = new HashMap<String, Object>();
		param.put("serviceName", dto.getServiceName());
		param.put("ip", dto.getIp());
		param.put("sourceName", dto.getSourceName());
		param.put("sourceTime", TimeUtils.getTimeString());
		return postBody(msUrl + "serviceHeartBeat", param);
	}

	private Object postBody(String url, Map<String, Object> param) {
		long timestamp = System.currentTimeMillis();
		String token = EncodeUtils.encode(heartBeatSecret + timestamp, "MD5");
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.add("Authorization", token);
		httpHeaders.add("ykSolt", "" + timestamp);
		HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(param, httpHeaders);
		Object o = null;
		try {
			o = restTemplate.postForEntity(url, httpEntity, Object.class);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return o;
	}
}
