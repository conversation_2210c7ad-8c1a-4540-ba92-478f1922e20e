package com.bt.itsinnerjob.detector.service;

import com.bt.itsinnerjob.detector.utils.DetectorUtils;
import com.bt.itsinnerjob.mapper.detector.DeviceVdMapper;
import com.bt.itsinnerjob.detector.utils.DetectorDBQueueUtils;
import com.bt.itsinnerjob.detector.codec.CommonEncoder;
import com.bt.itsinnerjob.detector.codec.WXRDDecoder;
import com.bt.itsinnerjob.detector.entity.*;

import io.netty.bootstrap.Bootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import io.netty.handler.timeout.IdleStateHandler;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.InetSocketAddress;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Service("wxrdService")
public class WXRDService {
    /**
     * 无锡润德车检器数据采集服务，作为Client端
     */
    private static final int TIME_OUT_CONNECT = 5;//连接超时，单位秒
    private static final int TIME_OUT_READ = 5*60;//读超时，单位秒
    private static final int CONNECT_COUNT_MAX = 3;//最大重连次数
    private static final int RETRY_COUNT_MAX = 3;//命令最大超时重传次数
    private boolean isActive = false;//服务是否可用
    private Bootstrap bootstrap;
    private EventLoopGroup worker;
    Logger logger = LogManager.getLogger(LogManager.ROOT_LOGGER_NAME);
    private final String cronValue = "0 */1 * * * ?";//不做修改时为每1分钟调度一次
    private List<DeviceVd> wxrdVdList;//无锡润德设备列表
    private ConcurrentHashMap<String,Boolean> vdConnectionInfo;//设备连接信息
    private ConcurrentHashMap<String,Boolean> vdNeedSyncInfo;//设备是否需要同步信息

    @Autowired
    private DeviceVdMapper deviceVdMapper;
    @Autowired
    private DeviceInfoService deviceInfoService;

    
    public boolean init() {
        try {
            DeviceVd tp = new DeviceVd();
            //统计无锡润德或江苏志华
            tp.setProtocol("润德志华");
            tp.setSource_id(deviceInfoService.getSourceId());
            wxrdVdList = deviceVdMapper.listByProtocol(tp);
            tp.setProtocol("江苏志华");
            List<DeviceVd> temp = deviceVdMapper.listByProtocol(tp);
            if(temp!=null&&temp.size()>0)
            {
                wxrdVdList.addAll(temp);
            }
            vdConnectionInfo = new ConcurrentHashMap<>();
            vdNeedSyncInfo = new ConcurrentHashMap<>();
            for (DeviceVd vd:wxrdVdList) {
                vdConnectionInfo.put(vd.getIp_address(),false);
            }
            isActive = true;
            setWorkGroup();
            logger.info("[润德志华]定时任务启动:"+wxrdVdList.size());
        }catch (Exception e)
        {
            logger.error("[润德志华]数据库错误:获取不到数据库中无锡润德设备" + e.getMessage());
            isActive = false;
        }
        return isActive;
    }

    public void pollDevice() {
        keedActive();
        if (isActive) {
            try {
                connect(wxrdVdList);
            } catch (Exception e) {
                logger.error("[润德志华]连接错误:" + e.getMessage());
            }
        }
    }

    public void keedActive() {
        if (!this.isActive)
            init();
    }


    
    public void stop() {
        //结束的处理:退出释放NIO线程组
        worker.shutdownGracefully();
        isActive = false;
        //detectorTimeTaskScheduler.cancelTask(this);
        logger.info("[润德志华]定时任务已取消");
    }
    
    public boolean isActive() {
        return isActive;
    }

    public void setWorkGroup() {
        worker = new NioEventLoopGroup();

        //辅助启动类
        bootstrap = new Bootstrap();

        //设置线程池
        bootstrap.group(worker);

        //设置socket工厂
        bootstrap.channel(NioSocketChannel.class);

        //设置管道
        bootstrap.handler(new ChannelInitializer<SocketChannel>() {
            @Override
            protected void initChannel(SocketChannel socketChannel) {
                //获取管道
                ChannelPipeline pipeline = socketChannel.pipeline();
                //添加心跳检测处理器
                pipeline.addLast(new IdleStateHandler(TIME_OUT_READ, 0, 0, TimeUnit.SECONDS));
                //编码器
                pipeline.addLast(new CommonEncoder());
                //解码器
                pipeline.addLast(new WXRDDecoder());
                //处理类
                pipeline.addLast(new ClientHandler());
            }
        });
    }

    public void connect(List<DeviceVd> vdList)
    {
        //连接及超时重连
        for (DeviceVd vd : vdList) {
            ConnectionProperty property = new ConnectionProperty();
            property.vd = vd;
            if(!vdConnectionInfo.get(vd.getIp_address()))
                property.doConnect();//如果设备没有连接上，则开始连接
        }
    }

    private class ConnectionProperty
    {
        DeviceVd vd;
        int connectCount = 0;
        void doConnect() {
            //发起异步连接操作
            String ip = vd.getIp_address();
            int port = vd.getPort();
            try {
                ChannelFuture channelFuture = bootstrap.connect(new InetSocketAddress(ip,port));
                //添加ChannelFutur监听器
                channelFuture.addListener(new ChannelFutureListener() {
                    @Override
                    public void operationComplete(ChannelFuture future) throws Exception {
                        //连接不成功
                        if (!future.isSuccess()) {
                            connectCount++;
                            logger.debug("[润德志华]到" + ip + "连接超时次数:" + connectCount + " " + future.cause());
                            //超时次数不大于最大次数时进行重连
                            if (connectCount < CONNECT_COUNT_MAX) {
                                future.channel().eventLoop().schedule(new Runnable() {
                                    @Override
                                    public void run() {
                                        doConnect();
                                    }
                                }, TIME_OUT_CONNECT, TimeUnit.SECONDS);

                                return;
                            } else {
                                //达到最大超时次数，关闭连接
                                logger.debug("[润德志华]已达到" + ip + "的最大连接次数，将断开连接");
                                future.channel().close();
                            }
                        }
                        else
                        {
                            vdConnectionInfo.put(ip,true);
                            return;
                        }
                    }
                });
            } catch (Exception e) {
                logger.warn("[润德志华]抛出异常:" + e.getMessage());
            }
            vdConnectionInfo.put(ip,false);
        }
    }

    private void dataProcess(WXRDPacket packet) {
        logger.debug("数据处理");
        //根据IP反查设备信息
        DeviceVd vd = deviceInfoService.getVdIPMap().get(packet.getIp());
        if(vd==null)
            return;
        WXRDData data = new WXRDData();
        if(packet.getType()==44&&data.eventAnalysis(packet.getData()))
        {
            if(data.getEventDirection()==1)//如果车辆逆行
            {
                logger.info("[润德志华]设备"+vd.getIp_address()+"检测到车道"+data.getEventLane()+"有"+data.getEventTime()+"有车辆逆行，车速"+data.getEventCarSpeed()+"km/h");
            }
            return;
        }
        if(packet.getType()!=41)
        {
            logger.info("[润德志华]设备"+vd.getIp_address()+"发来的不是车流量数据包，过滤");
            return;
        }
        List<VdFluxRecord> records = new ArrayList<>();
        //车流量数据解析
        data.dataAnalysis(packet.getData());
        //时间同步
        if(DetectorUtils.needTimeSync(data.getWriteDate()))
        {
            vdNeedSyncInfo.put(packet.getIp(),true);
        }else
        {
            vdNeedSyncInfo.put(packet.getIp(),false);
        }
        //写入车流量表对象
        HashMap<Integer, String> VDStatTitleMap = data.getVDStatTitleMap();
        for (int i = 0; i < VDStatTitleMap.size(); i++) {
            VdFluxRecord vdFluxRecord = new VdFluxRecord();
            vdFluxRecord.setDevice_id(vd.getDevice_id());
            vdFluxRecord.setVd_name(vd.getDevice_name());
            vdFluxRecord.setStat_title(VDStatTitleMap.get(i + 1));
            vdFluxRecord.setStat_title_id(i + 1);
            for (int j = 0; j < data.getTotalLane(); j++) {
                switch (i + 1) {
                    case 1: {
                        //车流量
                        int totalFlow = data.getTraffic().get(j);
                        vdFluxRecord.batchSetRoad(j + 1, totalFlow);

                        if (j == data.getTotalLane() - 1) {
                            logger.info("[润德志华]" + vd.getDevice_name() + "的车流量数据:" + vdFluxRecord.getRoad1() + "," + vdFluxRecord.getRoad2() + "," + vdFluxRecord.getRoad3() + "," + vdFluxRecord.getRoad4() + " " + data.getWriteDate());
                        }
                        break;
                    }
                    case 2: {
                        //车速度
                        double averageSpeed = data.getSpeed().get(j);
                        vdFluxRecord.batchSetRoad(j + 1, averageSpeed);

                        if (j == data.getTotalLane() - 1)
                            logger.info("[润德志华]" + vd.getDevice_name() + "的车速度数据:" + vdFluxRecord.getRoad1() + "," + vdFluxRecord.getRoad2() + "," + vdFluxRecord.getRoad3() + "," + vdFluxRecord.getRoad4() + " " + data.getWriteDate());
                        break;
                    }
                    case 5: {
                        //车道占有率
                        vdFluxRecord.batchSetRoad(j + 1, data.getOccupancy().get(j));
                        break;
                    }
                }
            }
            vdFluxRecord.setVd_type("车辆检测器");
            vdFluxRecord.setWrite_date(data.getWriteDate());
            vdFluxRecord.setDate_stamp(data.getDateStamp());
            records.add(vdFluxRecord);
        }

        //写入历史记录队列
        //DetectorDBQueueUtils.offerVdFluxHistory(records);
        //写入每日记录
        //DetectorDBQueueUtils.offerVdFlux(VdFluxRecord.convertToVdFlux(records));
        DetectorDBQueueUtils.offerVdFluxMap(vd,VdFluxRecord.convertToVdFlux(records));
    }

    private class ClientHandler extends ChannelInboundHandlerAdapter {
        //每个信息入站都会调用
        @Override
        public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
            InetSocketAddress clientInetSocketAddress = (InetSocketAddress) ctx.channel().remoteAddress();
            String deviceIP = clientInetSocketAddress.getAddress().getHostAddress();

            if(msg==null)
                return;

            WXRDPacket packet = (WXRDPacket) msg;
            packet.setIp(deviceIP);
            //首先进行校验
            if (packet.isCheckRight()) {
                logger.debug("[润德志华]校验正确");
                //若校验正确，则处理数据
                dataProcess(packet);
                //断开到车检器的连接
                //ctx.channel().close();
            }
            logger.debug("[润德志华]数据处理结束");
        }

        //通知处理器最后的channelread()是当前批处理中的最后一条消息时调用
        @Override
        public void channelReadComplete(ChannelHandlerContext ctx) throws Exception {
            ctx.flush();
        }

        //与服务器建立连接
        @Override
        public void channelActive(ChannelHandlerContext ctx) throws Exception {
            InetSocketAddress inetSocketAddress = (InetSocketAddress) ctx.channel().remoteAddress();
            String deviceIP = inetSocketAddress.getAddress().getHostAddress();

            logger.info(Thread.currentThread().getName() + "[润德志华]已连接到设备" + deviceIP);
//            //检查是否需要时间同步
//            if(vdNeedSyncInfo.get(deviceIP)!=null&&vdNeedSyncInfo.get(deviceIP)==true)
//            {
//                ctx.channel().writeAndFlush(WXRDPacket.getTimeSynchronizionCommand());
//            }
            String timeStr = WXRDPacket.getTimeSynchronizionCommand();
            ctx.channel().writeAndFlush(timeStr);
            vdConnectionInfo.put(deviceIP,true);

            //给服务器发消息
            /*String command = WXRDPacket.getTrafficDataCommand();
            ctx.channel().writeAndFlush(command);
            logger.debug("[润德志华]向设备" + deviceIP + "发送取交通量数据信息");*/
        }

        //与服务器断开连接
        @Override
        public void channelInactive(ChannelHandlerContext ctx) throws Exception {
            InetSocketAddress inetSocketAddress = (InetSocketAddress) ctx.channel().remoteAddress();
            String deviceIP = inetSocketAddress.getAddress().getHostAddress();
            logger.debug("[润德志华]已断开到设备" + deviceIP + "的连接");
            vdConnectionInfo.put(deviceIP,false);
        }

        //读超时处理
        @Override
        public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
            InetSocketAddress inetSocketAddress = (InetSocketAddress) ctx.channel().remoteAddress();
            String deviceIP = inetSocketAddress.getAddress().getHostAddress();
            logger.warn("[润德志华]历时5分钟未收到" + deviceIP + "的数据");
            if (evt instanceof IdleStateEvent) {
                IdleStateEvent event = (IdleStateEvent) evt;
                if (event.state() == IdleState.READER_IDLE) {
                    logger.warn("[润德志华]到" + deviceIP + "的连接将关闭");
                    ctx.channel().close();
                }
            } else {
                super.userEventTriggered(ctx, evt);
            }
        }

        //异常
        @Override
        public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
            //关闭管道
            ctx.channel().close();
            //打印异常信息
            //cause.printStackTrace();
            logger.warn("[润德志华]抛出异常，关闭管道，原因:"+cause.getMessage());
            vdConnectionInfo.put(((InetSocketAddress) ctx.channel().remoteAddress()).getAddress().getHostAddress(),false);
        }

    }
}
