package com.bt.itsinnerjob.detector.service;

import com.bt.itsinnerjob.detector.entity.*;
import com.bt.itsinnerjob.detector.utils.ConvertToNewVersionUtils;
import com.bt.itsinnerjob.detector.utils.DetectorDBQueueUtils;
import com.bt.itsinnerjob.detector.utils.UniqueCacheUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Service("trafficRepostService")
public class TrafficRepostService {
    Logger logger = LogManager.getLogger(LogManager.ROOT_LOGGER_NAME);
    @Autowired
    private DeviceInfoService deviceInfoService;

    //交调数据转发服务
    public boolean repost(TrafficTCPEntity tcp)
    {
        boolean flag = false;
        //拿到报文和ip后调用交调站的解析服务
        TrafficPacket packet = new TrafficPacket();
        packet.setFrame(tcp.getPacket());
        TrafficPacket.FrameSplit(packet);
        packet.setIp(tcp.getIp());
        dataProcess(packet);
        return flag;
    }

    public TrafficData dataProcess(TrafficPacket packet) {
        //根据IP反查设备信息
        DeviceVd vd = deviceInfoService.getVdIPMap().get(packet.getIp());
        if (vd == null)
            return null;
        String protocol = vd.getProtocol();
        //获取交调站类别信息：1类or2类
        int vdType = vd.getVd_type();
        int type = 0;
        TrafficData trafficData = new TrafficData();
        boolean flag = false;
        if (vdType == 7) {
            //一类交调站数据解析
            flag = trafficData.Type1DataAnalysis(packet.getData());
            type = 1;
        } else if (vdType == 8) {
            //二类交调站数据解析
            flag = trafficData.Type2DataAnalysis(packet.getData());
            type = 2;
        }else if("辽宁金洋车检器".equals(protocol))
        {
            if("1级设备".equals(vd.getVd_model()))
            {
                flag = trafficData.Type1DataAnalysis(packet.getData());
                type = 1;
            }
            else
            {
                flag = trafficData.Type2DataAnalysis(packet.getData());
                type = 2;
            }
        }
        else {
            logger.warn("[交调站转发]IP为" + packet.getIp() + "的设备在数据库中的类型不为交调站，不进行数据处理");
        }
        if (!flag) {
            if (trafficData.getType() != 1)
                logger.info("[交调站转发]设备" + packet.getIp() + "发来的信息不为实时数据包:\r\n" + packet.getFrame());
            else {
                //数据解析不成功，返回
                logger.warn("[交调站转发]"+packet.getIp()+"数据解析失败，请检查交调站类别是否正确");
            }
            return null;
        }

        if(UniqueCacheUtils.putAndCheck(vd.getDevice_id()+""+trafficData.getTimeStamp()))
        {
            logger.info("[交调站转发]过滤设备" + packet.getIp() + "发来的重复数据");
            return trafficData;
        }

        List<VdFluxRecord> records = new ArrayList<>();
        //VdFluxRecord数据处理
        HashMap<Integer, String> VDStatTitleMap = trafficData.getVDStatTitleMap();
        for (int i = 0; i < VDStatTitleMap.size(); i++) {
            VdFluxRecord vdFluxRecord = new VdFluxRecord();
            vdFluxRecord.setDevice_id(vd.getDevice_id());
            vdFluxRecord.setVd_name(vd.getDevice_name());
            vdFluxRecord.setStat_title(VDStatTitleMap.get(i + 1));
            vdFluxRecord.setStat_title_id(i + 1);
            for (int j = 0; j < trafficData.getTotalLane(); j++) {
                switch (i + 1) {
                    case 1: {
                        //将所有车型的车流量加起来
                        int totalFlow = trafficData.getTotalFlow(j, type);
                        vdFluxRecord.batchSetRoad(j + 1, totalFlow);

                        if (j == trafficData.getTotalLane() - 1) {
                            logger.info("[交调站转发]" + vd.getDevice_name() + "的车流量数据:" + vdFluxRecord.getRoad1() + ", "
                                    + vdFluxRecord.getRoad2() + ", " + vdFluxRecord.getRoad3() + ", " + vdFluxRecord.getRoad4() + ", "
                                    + vdFluxRecord.getRoad5() + ", " + vdFluxRecord.getRoad6() + ", " + vdFluxRecord.getRoad7() + ", "
                                    + vdFluxRecord.getRoad8() + " " + trafficData.getWriteDate());
                        }
                        break;
                    }
                    case 2: {
                        //平均所有车型的车速度
                        double averageSpeed = trafficData.getAverageSpeed(j, type);
                        vdFluxRecord.batchSetRoad(j + 1, averageSpeed);
                        //如果车速度过快或过慢，超过设定的阈值，则报警

                        if (j == trafficData.getTotalLane() - 1)
                            logger.info("[交调站转发]" + vd.getDevice_name() + "的车速度数据:" + vdFluxRecord.getRoad1() + ", "
                                    + vdFluxRecord.getRoad2() + ", " + vdFluxRecord.getRoad3() + ", " + vdFluxRecord.getRoad4() +", "
                                    + vdFluxRecord.getRoad5()+ ", " + vdFluxRecord.getRoad6() + ", " + vdFluxRecord.getRoad7() +", "
                                    + vdFluxRecord.getRoad8() + " " + trafficData.getWriteDate());
                        break;
                    }
                    case 3: {
                        //跟车百分比
                        vdFluxRecord.batchSetRoad(j + 1, trafficData.getFollowingCar().get(j));
                        break;
                    }
                    case 4: {
                        //车间距
                        vdFluxRecord.batchSetRoad(j + 1, trafficData.getCarSpacing().get(j));
                        break;
                    }
                    case 5: {
                        int occupancy = trafficData.getTimeOccupancy().get(j);
                        //车道占有率
                        vdFluxRecord.batchSetRoad(j + 1, trafficData.getTimeOccupancy().get(j));

                        break;
                    }
                }
            }
            vdFluxRecord.setVd_type("交调站");
            vdFluxRecord.setWrite_date(trafficData.getWriteDate());
            vdFluxRecord.setDate_stamp(trafficData.getTimeStamp());
            records.add(vdFluxRecord);
        }

        //vd_traffic_carType数据处理
        List<VdTrafficCarType> carTypes = new ArrayList<>();
        for (int i = 0; i < trafficData.getTotalLane(); i++) {
            VdTrafficCarType vdTrafficCarType = new VdTrafficCarType();
            vdTrafficCarType.setDevice_id(vd.getDevice_id());
            vdTrafficCarType.setVd_name(vd.getDevice_name());
            vdTrafficCarType.setStat_title("车道" + (i + 1));
            int lane = vd.getLane_num();
            if(i<lane/2)
            {
                vdTrafficCarType.setVd_direction("1");
            }
            else
            {
                vdTrafficCarType.setVd_direction("2");
            }
            vdTrafficCarType.setWrite_date(trafficData.getWriteDate());
            vdTrafficCarType.setDataFromTrafficData(type, i, trafficData);
            carTypes.add(vdTrafficCarType);
        }

        //写入每日记录队列
        DetectorDBQueueUtils.offerVdFluxes(ConvertToNewVersionUtils.tvsConvertVdFluxRecord(records,vd));
        //写入车型记录队列
        DetectorDBQueueUtils.offerVdTrafficCarType(carTypes);
        return trafficData;
    }
}
