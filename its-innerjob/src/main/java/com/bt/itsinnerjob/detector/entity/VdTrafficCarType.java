package com.bt.itsinnerjob.detector.entity;

import java.util.List;

public class VdTrafficCarType {
    /**
     * vd_traffic_cartype表实体
     */
    private String id;
    private List<String> idList;
    private String device_id;
    private String vd_name;
    private String vd_direction;
    private String stat_title;
    private int zx_car;
    private double zx_car_speed;
    private int x_truck;
    private double x_truck_speed;
    private int d_car;
    private double d_car_speed;
    private int z_truck;
    private double z_truck_speed;
    private int d_truck;
    private double d_truck_speed;
    private int td_truck;
    private double td_truck_speed;
    private int jzx_car;
    private double jzx_car_speed;
    private int tlj_car;
    private double tlj_car_speed;
    private int moto_car;
    private double moto_car_speed;
    private String write_date;
    private int date_stamp;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<String> getIdList() {
        return idList;
    }

    public void setIdList(List<String> idList) {
        this.idList = idList;
    }

    public void setDataFromTrafficData(int type, int road, TrafficData data)
    {
        if(type==1)
        {
            zx_car = data.getSmallBusTraffic().get(road);
            zx_car_speed = data.getSmallBusSpeed().get(road);
            x_truck = data.getSmallTruckTraffic().get(road);
            x_truck_speed = data.getSmallTruckSpeed().get(road);
            d_car = data.getBigBusTraffic().get(road);
            d_car_speed = data.getBigBusSpeed().get(road);
            z_truck = data.getMiddleTruckTraffic().get(road);
            z_truck_speed = data.getMiddleTruckSpeed().get(road);
            d_truck = data.getBigTruckTraffic().get(road);
            d_truck_speed = data.getBigTruckSpeed().get(road);
            td_truck = data.getHugeTruckTraffic().get(road);
            td_truck_speed = data.getHugeTruckSpeed().get(road);
            jzx_car = data.getContainerTruckTraffic().get(road);
            jzx_car_speed = data.getContainerTruckSpeed().get(road);
        }
        else
        {
            x_truck = data.getSmallCarTraffic().get(road);
            x_truck_speed = data.getSmallCarSpeed().get(road);
            z_truck = data.getMiddleCarTraffic().get(road);
            z_truck_speed = data.getMiddleCarSpeed().get(road);
            d_truck = data.getBigCarTraffic().get(road);
            d_truck_speed = data.getBigCarSpeed().get(road);
            td_truck = data.getHugeCarTraffic().get(road);
            td_truck_speed = data.getHugeCarSpeed().get(road);
        }
        tlj_car = data.getTractorTraffic().get(road);
        tlj_car_speed = data.getTractorSpeed().get(road);
        moto_car = data.getMotoTraffic().get(road);
        moto_car_speed = data.getMotoSpeed().get(road);
    }
    
    //导航者车检器协议车型
    public void setDataFromRadarData(int type, int road, RadarData data)
    {
    	x_truck = data.getSflow().get(road);
        z_truck = data.getMflow().get(road);
        d_truck = data.getBflow().get(road);
        moto_car = data.getNflow().get(road);
    }
    
    //陕西瑞亚车检器协议车型
    public void setDataFromRuiyaData(RuiyaData data)
    {
    	x_truck = data.getSflow();
        z_truck = data.getMflow();
        d_truck = data.getBflow();
        td_truck = data.getHflow();
    }

    public String getDevice_id() {
        return device_id;
    }

    public void setDevice_id(String device_id) {
        this.device_id = device_id;
    }

    public String getVd_name() {
        return vd_name;
    }

    public void setVd_name(String vd_name) {
        this.vd_name = vd_name;
    }

    public String getVd_direction() {
        return vd_direction;
    }

    public void setVd_direction(String vd_direction) {
        this.vd_direction = vd_direction;
    }

    public String getStat_title() {
        return stat_title;
    }

    public void setStat_title(String stat_title) {
        this.stat_title = stat_title;
    }

    public int getZx_car() {
        return zx_car;
    }

    public void setZx_car(int zx_car) {
        this.zx_car = zx_car;
    }

    public double getZx_car_speed() {
        return zx_car_speed;
    }

    public void setZx_car_speed(double zx_car_speed) {
        this.zx_car_speed = zx_car_speed;
    }

    public int getX_truck() {
        return x_truck;
    }

    public void setX_truck(int x_truck) {
        this.x_truck = x_truck;
    }

    public double getX_truck_speed() {
        return x_truck_speed;
    }

    public void setX_truck_speed(double x_truck_speed) {
        this.x_truck_speed = x_truck_speed;
    }

    public int getD_car() {
        return d_car;
    }

    public void setD_car(int d_car) {
        this.d_car = d_car;
    }

    public double getD_car_speed() {
        return d_car_speed;
    }

    public void setD_car_speed(double d_car_speed) {
        this.d_car_speed = d_car_speed;
    }

    public int getZ_truck() {
        return z_truck;
    }

    public void setZ_truck(int z_truck) {
        this.z_truck = z_truck;
    }

    public double getZ_truck_speed() {
        return z_truck_speed;
    }

    public void setZ_truck_speed(double z_truck_speed) {
        this.z_truck_speed = z_truck_speed;
    }

    public int getD_truck() {
        return d_truck;
    }

    public void setD_truck(int d_truck) {
        this.d_truck = d_truck;
    }

    public double getD_truck_speed() {
        return d_truck_speed;
    }

    public void setD_truck_speed(double d_truck_speed) {
        this.d_truck_speed = d_truck_speed;
    }

    public int getTd_truck() {
        return td_truck;
    }

    public void setTd_truck(int td_truck) {
        this.td_truck = td_truck;
    }

    public double getTd_truck_speed() {
        return td_truck_speed;
    }

    public void setTd_truck_speed(double td_truck_speed) {
        this.td_truck_speed = td_truck_speed;
    }

    public int getJzx_car() {
        return jzx_car;
    }

    public void setJzx_car(int jzx_car) {
        this.jzx_car = jzx_car;
    }

    public double getJzx_car_speed() {
        return jzx_car_speed;
    }

    public void setJzx_car_speed(double jzx_car_speed) {
        this.jzx_car_speed = jzx_car_speed;
    }

    public int getTlj_car() {
        return tlj_car;
    }

    public void setTlj_car(int tlj_car) {
        this.tlj_car = tlj_car;
    }

    public double getTlj_car_speed() {
        return tlj_car_speed;
    }

    public void setTlj_car_speed(double tlj_car_speed) {
        this.tlj_car_speed = tlj_car_speed;
    }

    public int getMoto_car() {
        return moto_car;
    }

    public void setMoto_car(int moto_car) {
        this.moto_car = moto_car;
    }

    public double getMoto_car_speed() {
        return moto_car_speed;
    }

    public void setMoto_car_speed(double moto_car_speed) {
        this.moto_car_speed = moto_car_speed;
    }

    public String getWrite_date() {
        return write_date;
    }

    public void setWrite_date(String write_date) {
        this.write_date = write_date;
    }

    public int getDate_stamp() {
        return date_stamp;
    }

    public void setDate_stamp(int date_stamp) {
        this.date_stamp = date_stamp;
    }
}
