package com.bt.itsinnerjob.detector.service;

import com.bt.itsinnerjob.detector.entity.CmsRtRepertoryDTO;
import com.bt.itsinnerjob.detector.entity.DeviceCms;
import com.bt.itsinnerjob.detector.utils.DetectorUtils;
import com.bt.itsinnerjob.detector.utils.TimeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.*;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;

@Service("cmsRtRepertoryService")
public class CmsRtRepertoryService {
    /**
     * 情报板实时节目单刷新服务
     */
    private static final String CMD_BEGIN_FRAME = "02";//帧起始符，固定
    private static final String CMD_END_FRAME = "03";//帧结束符，固定
    //情报支持的单次传输最大字节量
    private static final int CMD_MAX_LENGTH = 4096;
    private static final int CMD_TIME_OUT = 5000;//单指令超时设定5秒
    private static final int CMD_TIME_OUT_READ = 5000;//单指令超时设定5秒
    private static final String CHARSET = "UTF-8";
    private static final String CHARSET_GBK = "GBK";

    Log logger = LogFactory.getLog(CmsRtRepertoryService.class);
    private ConcurrentLinkedQueue<String> syncIdList = new ConcurrentLinkedQueue<>();
    private ConcurrentLinkedQueue<CmsRtRepertoryDTO> syncCmsRtList = new ConcurrentLinkedQueue<>();


    public boolean getDmRtRepertory(DeviceCms cms) {
        Socket socket = new Socket();
        DataOutputStream out = null;
        DataInputStream in = null;
        StringBuffer recvSb = new StringBuffer();
        try {
            socket.setSoTimeout(CMD_TIME_OUT);
            socket.connect(new InetSocketAddress(cms.getIp_address(), cms.getPort()), CMD_TIME_OUT);
            in = new DataInputStream(socket.getInputStream());
            out = new DataOutputStream(socket.getOutputStream());
//            String listNumerStr = sendCmdDm(in, out, "3439", "");
//            if (listNumerStr == null || listNumerStr.length() == 0) {
//                XxlJobLogger.log(cms+" 获取当前播放列表序号失败");
//                return false;
//            }
//            listNumerStr = DetectorUtils.hexStringToString(listNumerStr, "GBK");
//            String listName = DetectorUtils.stringToHexString("play" + listNumerStr + ".lst");
            String listName = DetectorUtils.stringToHexString("play00.lst");
            int offset = 0;
            String recv = "test";
            final int maxLen = 2048;
            int length = maxLen;
            while (recv != null && recv.length() > 0 && length >= maxLen/2) {
                String offsetStr = DetectorUtils.stringToHexString(String.format("%08d", offset));
                if("V1".equals(cms.getVersion().toUpperCase())) {
                    recv = sendCmdDm(in, out, "3537", offsetStr + listName);
                }else {
                    recv = sendCmdDm(in, out, "3037", "706C61796C6973745C" + listName + "2B" + offsetStr);//旧版，同海威格式
                }
                if (recv == null || recv.length() < (36 + listName.length())) {
                    break;
                }
                String content = recv.substring(36 + listName.length());
                recvSb.append(content);
                offset += content.length() / 2;
                length = content.length() / 2;
            }
            if (recvSb.length() == 0) {
                return false;
            }
        } catch (IOException e) {
            //logger.info("[电明]读取实时节目单失败，原因:" + e.getMessage());
            return false;
        } finally {
            closeIOStream(in, out);
        }

        String recvStr = recvSb.toString();
        //System.out.println("电明节目单:"+recvStr);
        String list = DetectorUtils.hexStringToString(recvStr, "GBK");
        String[] items = list.split("(?i)ITEM\\d+=");
        ArrayList<CmsRtRepertoryDTO> rtList = new ArrayList<CmsRtRepertoryDTO>();
        for (int i = 1; i < items.length; i++) {
            String item = items[i];
            int startFlag = item.indexOf("\\");
            if (startFlag < 0) {
                continue;
            }
            String paramStr = item.substring(0, startFlag);
            String[] params = paramStr.split(",");
            int delay = Integer.parseInt(params[0].replace(" ", "")) / 100;
            int inMode = Integer.parseInt(params[1].replace(" ", ""));
            int outMode = Integer.parseInt(params[3].replace(" ", ""));
            int speed = Integer.parseInt(params[4].replace(" ", ""));
            String word = item.substring(startFlag);
            word = word.replace("\r", "");
            word = word.replace("\n", "");
            int fontSize = 16;
            String fontType = "SimSun";
            //修正：非本系统情报板内容下发错误的修正方法
            if (word.indexOf("\\W")>0)
            {
                int first = word.indexOf("\\W");
                if(word.substring(first+2).indexOf("\\W")>=0)
                {
                    word = word.substring(0,first+2)+word.substring(first+2).replaceAll("\\\\C[0-9]{6}\\\\W","\\\\A");
                    word = word.replaceAll("(\\\\W){2,}","\\\\W");
                }
            }
            //如果有字体
            if (word.indexOf("\\F") >= 0) {
                fontSize = Integer.parseInt(word.substring(word.indexOf("\\F") + 3, word.indexOf("\\F") + 5));
                fontType = word.substring(word.indexOf("\\F") + 2, word.indexOf("\\F") + 3);
                if (fontType.equals("h")) {
                    fontType = "SimHei";
                } else if (fontType.equals("s")) {
                    fontType = "SimSun";
                } else if (fontType.equals("k")) {
                    fontType = "KaiTi";
                } else if (fontType.equals("f")) {
                    fontType = "Fangsong";
                } else {
                    fontType = "SimSun";
                }
            }
            //如果有字符颜色
            String fontColor = "FF0000";
            if (word.indexOf("\\T") >= 0) {
                if (word.substring(word.indexOf("\\T") + 2, word.indexOf("\\T") + 3).equalsIgnoreCase("t")) {
                    fontColor = "000000";
                    word = word.replace("\\Tt", "\\T000000000000");
                } else {
                    fontColor = word.substring(word.indexOf("\\T") + 2, word.indexOf("\\T") + 11);
                    fontColor = addZero(Integer.toHexString(Integer.parseInt(fontColor.substring(0, 3))), 2) +
                            addZero(Integer.toHexString(Integer.parseInt(fontColor.substring(3, 6))), 2) +
                            addZero(Integer.toHexString(Integer.parseInt(fontColor.substring(6, 9))), 2);
                }
            }
            //如果有背景颜色
            String bg_color = "000000";
            if (word.indexOf("\\K") >= 0) {
                if (word.substring(word.indexOf("\\K") + 2, word.indexOf("\\K") + 3).equals("t")) {
                    bg_color = "000000";
                    word = word.replace("\\Kt", "\\K000000000000");
                } else {
                    bg_color = word.substring(word.indexOf("\\K") + 2, word.indexOf("\\K") + 11);
                    bg_color = addZero(Integer.toHexString(Integer.parseInt(bg_color.substring(0, 3))), 2) +
                            addZero(Integer.toHexString(Integer.parseInt(bg_color.substring(3, 6))), 2) +
                            addZero(Integer.toHexString(Integer.parseInt(bg_color.substring(6, 9))), 2);
                }
            }
            int img_width = 0;
            String imgName = null;
            int imgX = 0, imgY = 0;
            //如果有图片
            if (word.indexOf("\\B") >= 0) {
                imgName = word.substring(word.indexOf("\\B") + 2, word.indexOf("\\B") + 5) + ".bmp";
                //清空接收文件夹
                //如果有图片，图片坐标必然紧挨在\B之前
                int img_loc_C = word.substring(0, word.indexOf("\\B")).indexOf("\\C");
                if (img_loc_C >= 0) {
                    imgX = Integer.parseInt(word.substring(img_loc_C + 2, img_loc_C + 5));
                    imgY = Integer.parseInt(word.substring(img_loc_C + 5, img_loc_C + 8));
                    //取完所有数据后，删除图片的坐标\CXXXXXX和\BXXX以免混淆
                    word = (img_loc_C > 0) ? word.substring(0, img_loc_C) : "" + word.substring(word.indexOf("\\B") + 5);
                }
            }

            String txtInfo = "";
            int labelX = 0;
            int labelY = 0;
            //如果有文字
            if (word.indexOf("\\W") >= 0) {
                int txt_loc_C = word.indexOf("\\C");
                labelX = Integer.parseInt(word.substring(txt_loc_C + 2, txt_loc_C + 5));
                labelY = Integer.parseInt(word.substring(txt_loc_C + 5, txt_loc_C + 8));
                txtInfo = word.substring(word.indexOf("\\W") + 2);
            }

            CmsRtRepertoryDTO rt = new CmsRtRepertoryDTO();
            rt.setName("电明实时节目单" + i);
            rt.setLabelX(labelX);
            rt.setLabelY(labelY);
            rt.setFontType(fontType);
            rt.setFontSize(fontSize);
            rt.setFontColor("#" + fontColor.toUpperCase());
            rt.setBackColor("#" + bg_color.toUpperCase());
            //新版的messagebody需转换
            txtInfo = txtInfo.replace("\\A", "<br>");//替换换行符
            txtInfo = txtInfo.replaceAll("\\s+", "&nbsp;");//替换空格
            rt.setMessageBody(txtInfo);
            rt.setImageX(imgX);
            rt.setImageY(imgY);
            rt.setImageSize(img_width);
            rt.setImageBody(imgName);
            if(delay==0)
            {
                delay = 5;
            }
            if(speed<=0)
            {
                speed =1 ;
            }
            rt.setHoldTime(5);
            rt.setSpeed(speed);

            rt.setInputMode(inMode + "");
            rt.setOutputMode(outMode + "");
            rt.setDeviceId(cms.getDevice_id());
            rt.setDeviceName(cms.getDevice_name());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String currentTime = sdf.format(new Date());
            rt.setReleaseTime(currentTime);
            rtList.add(rt);
        }
        return updateRtRepertory(cms.getDevice_id(),rtList);
    }

    public String sendCmdDm(DataInputStream in, DataOutputStream out, String cmd, String content) {
        String CMD_SOURCE_ADDRESS = "3030";//范围01~99，源地址，用户自定义
        String CMD_DEST_ADDRESS = "3030";//范围01~99，目的地址，用户自定义
        try {
            //分步传输字节流
            String controlstr = CMD_DEST_ADDRESS + CMD_SOURCE_ADDRESS + cmd + content;
            //校验位
            String crcstr = getVerifyCRC(controlstr, 4);
            controlstr += crcstr;
            //转义字符
            String convertstr = escape(controlstr);
            //指令的完整形式
            convertstr = CMD_BEGIN_FRAME + convertstr + CMD_END_FRAME;
            //传输
            byte[] senddata = DetectorUtils.hexStringToBytes(convertstr);
            out.write(senddata);
            int readLenth = 2048;
            byte[] respondata = new byte[readLenth];
            String hexResult = "";
            readLenth = in.read(respondata);
            while (readLenth > 0) {
                respondata = Arrays.copyOfRange(respondata, 0, readLenth);
                hexResult += DetectorUtils.bytesToHexString(respondata);
                if(hexResult.endsWith("03"))
                {
                    break;
                }
                respondata = new byte[readLenth];
                Thread.sleep(100);
                readLenth = in.read(respondata);
                System.out.println();
            }
            //反转义
            //System.out.println("电明反转义前：\r\n"+hexResult);
            hexResult = deEscape(hexResult);
            //去除帧头、地址、指令头、校验位和帧尾
            hexResult = hexResult.substring(14, hexResult.length() - 6);
            return hexResult;
        } catch (Exception e) {
            //logger.info("[电明]发送指令失败，原因:" + e.getMessage());
        }
        return null;
    }

    //------------------------------------------------------------------------------------------------------------------

    public boolean getXkRtRepertory(DeviceCms cms) {
        boolean flag = false;
        //先获取当前播放列表
        String list = downloadCurrentListXk(cms);
        if (StringUtils.isBlank(list)) {
            logger.info( cms.getDevice_name() + " 显科获取实时节目单为空 : " + TimeUtils.getTimeString("yyyy-MM-dd HH:mm:ss"));
            return false;
        }
        //logger.info( cms.getDevice_name() + "实时节目单:\r\n "+list);
        list = DetectorUtils.hexStringToString(list, CHARSET_GBK);
        //播放列表解析
        String[] items = list.split("Item\\d{2}=");
        ArrayList<CmsRtRepertoryDTO> rtListNew = new ArrayList<>();
        for (int i = 1; i < items.length; i++) {
            String item = items[i];
            int startFlag = item.indexOf("\\");
            if (startFlag < 0)
                continue;
            String paramStr = item.substring(0, startFlag);
            String[] params = paramStr.split(",");
            int delay = Integer.parseInt(params[0]);
            int inMode = Integer.parseInt(params[1]);
            int showMode = Integer.parseInt(params[2]);
            int outMode = Integer.parseInt(params[3]);
            int speed = Integer.parseInt(params[4]);
            String word = item.substring(startFlag);
            word = word.replace("\\r", "");
            word = word.replace("\\n", "");
            int fontSize = 16;
            String fontType = "SimSun";
            //如果有字体
            if (word.indexOf("\\F") >= 0) {
                fontSize = Integer.parseInt(word.substring(word.indexOf("\\F") + 3, word.indexOf("\\F") + 5));
                fontType = word.substring(word.indexOf("\\F") + 2, word.indexOf("\\F") + 3);
                if (fontType.equals("h")) {
                    fontType = "SimHei";
                } else if (fontType.equals("s")) {
                    fontType = "SimSun";
                } else if (fontType.equals("k")) {
                    fontType = "KaiTi";
                } else if (fontType.equals("f")) {
                    fontType = "Fangsong";
                } else {
                    fontType = "SimSun";
                }
            }
            //如果有字符颜色
            String fontColor = "FF0000";
            if (word.indexOf("\\T") >= 0) {
                fontColor = word.substring(word.indexOf("\\T") + 2, word.indexOf("\\T") + 11);
                fontColor = addZero(Integer.toHexString(Integer.parseInt(fontColor.substring(0, 3))), 2) +
                        addZero(Integer.toHexString(Integer.parseInt(fontColor.substring(3, 6))), 2) +
                        addZero(Integer.toHexString(Integer.parseInt(fontColor.substring(6, 9))), 2);
            }
            //如果有背景颜色
            String bg_color = "000000";
            if (word.indexOf("\\B") >= 0) {
                bg_color = word.substring(word.indexOf("\\B") + 2, word.indexOf("\\B") + 11);
                bg_color = addZero(Integer.toHexString(Integer.parseInt(bg_color.substring(0, 3))), 2) +
                        addZero(Integer.toHexString(Integer.parseInt(bg_color.substring(3, 6))), 2) +
                        addZero(Integer.toHexString(Integer.parseInt(bg_color.substring(6, 9))), 2);
            }
            int img_width = 0;
            String imgName = null;
            int imgX = 0;
            int imgY = 0;
            //如果有图片
            if (word.indexOf("\\I") >= 0) {
                imgName = word.substring(word.indexOf("\\I") + 2, word.indexOf("\\I") + 5) + ".bmp";
                //如果有图片，图片坐标必然紧挨在\I之前
                int img_loc_C = word.substring(0, word.indexOf("\\I")).indexOf("\\C");
                if (img_loc_C >= 0) {
                    imgX = Integer.parseInt(word.substring(img_loc_C + 2, img_loc_C + 5));
                    imgY = Integer.parseInt(word.substring(img_loc_C + 5, img_loc_C + 8));
                    //取完所有数据后，删除图片的坐标\CXXXXXX和\IXXX以免混淆
                    //word = word.substring(0, img_loc_C) + word.substring(word.indexOf("\\I" + 5));
                    word = (img_loc_C > 0) ? word.substring(0, img_loc_C) : "" + word.substring(word.indexOf("\\I") + 5);
                }
            }
            word = word.replace("\n", "");
            word = word.replaceAll("\r", "");
            String txtInfo = "";
            int labelX = 0;
            int labelY = 0;
            //如果有文字
            if (word.indexOf("\\U") >= 0) {
                int txt_loc_C = word.indexOf("\\C");
                labelX = Integer.parseInt(word.substring(txt_loc_C + 2, txt_loc_C + 5));
                labelY = Integer.parseInt(word.substring(txt_loc_C + 5, txt_loc_C + 8));
                txtInfo = word.substring(word.indexOf("\\U") + 2);
            }

            CmsRtRepertoryDTO rt = new CmsRtRepertoryDTO();
            rt.setName("显科实时节目单" + i);
            rt.setLabelX(labelX);
            rt.setLabelY(labelY);
            rt.setFontSize(fontSize);
            rt.setFontType(fontType);
            rt.setFontColor("#" + fontColor.toUpperCase());
            rt.setBackColor("#" + bg_color.toUpperCase());
            //新版的messagebody需转换
            txtInfo = txtInfo.replace("\\N", "<br>");//替换换行符
            txtInfo = txtInfo.replaceAll("\\s+", "&nbsp;");//替换空格
            rt.setMessageBody(txtInfo);
            rt.setImageX(imgX);
            rt.setImageY(imgY);
            rt.setImageSize(img_width);
            rt.setImageBody(imgName);
            if(delay==0)
            {
                delay = 5;
            }
            if(speed<=0)
            {
                speed =1 ;
            }
            rt.setHoldTime(5);
            rt.setSpeed(speed);
            //出入屏方式需转换
            int[] convertMode = {15, 0, 2, 1, 3, 4, 5, 6, 7, 8, 9, 10, 14, 11, 12, 13};
            rt.setInputMode(convertMode[inMode] + "");
            rt.setOutputMode(convertMode[outMode] + "");
            rt.setDeviceId(cms.getDevice_id());
            rt.setDeviceName(cms.getDevice_name());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String currentTime = sdf.format(new Date());
            rt.setReleaseTime(currentTime);
            rtListNew.add(rt);
        }
        return updateRtRepertory(cms.getDevice_id(),rtListNew);

    }

    //获取当前播放列表
    public String downloadCurrentListXk(DeviceCms cms) {
        String CMD_DEVICE_ADDRESS = "3030";//范围01~99，设备地址，用户自定义
        String CMD_DEST_ADDRESS = "3232";//范围01~99，目的地址，用户自定义
        String CMD_LIST_GET = "3233";//获取当前显示列表
        Socket socket = null;
        DataOutputStream out = null;
        DataInputStream in = null;
        boolean flag = false;
        int offsetIndex = 0;//偏移量
        try {
            //1.创建传输套接字
            socket = new Socket();
            socket.connect(new InetSocketAddress(cms.getIp_address(), cms.getPort()), CMD_TIME_OUT);
            socket.setSoTimeout(CMD_TIME_OUT);
            out = new DataOutputStream(socket.getOutputStream());
            in = new DataInputStream(socket.getInputStream());
            //先获取当前播放列表
            String data = CMD_LIST_GET + CMD_DEVICE_ADDRESS;
            String recvHex = sendCmdXk(in, out, data);
            if (recvHex == null || recvHex.length() < 2) {
                return null;
            }
            int result = Integer.parseInt(recvHex.substring(0, 2), 16);
            if (result == 0) {
                return null;
            }
            String listName = DetectorUtils.hexStringToString(recvHex.substring(2), CHARSET_GBK);
            //再下载当前播放列表
            return getFileStringFromXkCms(in, out, "list\\" + listName);
        } catch (IOException e) {
            logger.info("显科获取当前播放列表失败:" + e.getMessage());
        } finally {
            closeIOStream(in, out);
        }
        return null;
    }

    private String getFileStringFromXkCms(DataInputStream in, DataOutputStream out, String fileName) {
        String CMD_FILE_GET = "3231";//文件读取指令
        String CMD_DEVICE_ADDRESS = "3030";//范围01~99，设备地址，用户自定义
        // "09"：2 字节帧类型
        String cmd = CMD_FILE_GET;
        //目的地址
        String deviceAddress = CMD_DEVICE_ADDRESS;
        //文件名：不定长 ASCII 码字符串
        String fileNameHex = DetectorUtils.stringToHexString(fileName);
        String fileNameLenHex = String.format("%03d", fileNameHex.length() / 2);
        fileNameLenHex = DetectorUtils.stringToHexString(fileNameLenHex);
        //帧序列，即文件偏移地址为帧序列x 2048个字节
        int offset = 0;
        int dataLenGot = 2048;
        StringBuffer fileData = new StringBuffer();
        while (dataLenGot >= 2048) {
            String frameSerial = String.format("%04d", offset);
            frameSerial = DetectorUtils.stringToHexString(frameSerial);
            String data = cmd + deviceAddress + fileNameLenHex + fileNameHex + frameSerial;
            String recv = sendCmdXk(in, out, data);
            if (recv == null) {
                //接收不到这一段的数据
                if (fileData.length() == 0) {
                    return null;
                } else {
                    break;
                }
            } else {
                //剔除指令执行情况和文件名和帧序列
                recv = recv.substring((4+fileName.length()+4)*2);
                fileData.append(recv);
                dataLenGot = recv.length();
                offset++;
            }
        }
        String trueFileData = fileData.toString();
        return trueFileData;
    }

    public String sendCmdXk(DataInputStream in, DataOutputStream out, String sendHexStr) {
        boolean flag = false;
        int offsetIndex = 0;//偏移量
        try {
            //计算校验位
            String crcstr = getVerifyCRC(sendHexStr, 4);
            sendHexStr = sendHexStr + crcstr;
            //转义字符
            String convertstr = CmsStatusService.escape(sendHexStr);
            //指令的完整形式
            convertstr = CMD_BEGIN_FRAME + convertstr + CMD_END_FRAME;
            //logger.info("显科通用发送指令:\r\n" + convertstr);
            //传输
            byte[] sendData = DetectorUtils.hexStringToBytes(convertstr);
            out.write(sendData);
            out.flush();
            int readLenth = 2048;
            byte[] respondata = new byte[readLenth];
            String hexResult = "";
            readLenth = in.read(respondata);
            while (readLenth > 0) {
                respondata = Arrays.copyOfRange(respondata, 0, readLenth);
                hexResult += DetectorUtils.bytesToHexString(respondata);
                if(hexResult.endsWith("03"))
                {
                    break;
                }
                respondata = new byte[readLenth];
                Thread.sleep(100);
                readLenth = in.read(respondata);
            }
            //反转义
            //System.out.println("显科反转义前：\r\n"+hexResult);
            hexResult = deEscape(hexResult);
            //去除帧头、地址、指令头、校验位和帧尾
            hexResult = hexResult.substring(10, hexResult.length() - 6);
            return hexResult;
        } catch (Exception e) {
            //logger.debug("显科通用查询失败:" + e.getMessage());
        }
        return null;
    }

    //------------------------------------------------------------------------------------------------------------------
    public boolean getKXBRtRepertory(DeviceCms cms) {
        String CMD_DOWNLOAD_FILE = "3039";//下载文件
        String FILENAME_SEPARATOR = "2B";//分隔符"+"的ASCII码
        //1.先下载play.lst文件
        //2 字节帧类型
        String cmd = CMD_DOWNLOAD_FILE;
        //文件名：不定长 ASCII 码字符串
        String fileName = DetectorUtils.stringToHexString("play.lst");
        //文件指针偏移：4 字节十六进制数，先发高字节，后发低字节
        int offset = 0;
        int lengthMax = 2048;
        int dataLenGot = lengthMax;
        StringBuffer fileData = new StringBuffer();
        while (dataLenGot >= lengthMax) {
            String addressOffset = String.format("%08x", lengthMax / 2 * offset);
            String data = fileName + FILENAME_SEPARATOR + addressOffset;
            String recv = sendCmdKXB(cms, cmd, data);
            if (recv == null || recv.length() == 0) {
                //接收不到这一段的数据
                return false;
            } else {
                if (!(recv.substring(0, 2).equals("30") && recv.indexOf(FILENAME_SEPARATOR) > 0 && recv.indexOf(FILENAME_SEPARATOR) + 10 <= recv.length())) {
                    return false;
                }
                String recvData = recv.substring(recv.indexOf(FILENAME_SEPARATOR) + 10);
                fileData.append(recvData);
                dataLenGot = recvData.length();
                offset++;
            }
        }
        if(StringUtils.isBlank(fileData.toString()))
        {
            logger.info( cms.getDevice_name() + " 丰海获取实时节目单为空 : " + TimeUtils.getTimeString("yyyy-MM-dd HH:mm:ss"));
            return false;
        }
        String recvStr = DetectorUtils.hexStringToString(fileData.toString(), CHARSET_GBK);

        //logger.debug("[丰海]读取当前显示的播放列表回复：" + recvStr);

        //2.将获得的play.lst文件解析
        String[] items = recvStr.split("item\\d+=");
        ArrayList<CmsRtRepertoryDTO> rtList = new ArrayList<CmsRtRepertoryDTO>();
        if(items.length<1)
        {
            return false;
        }
        for (int i = 1; i < items.length; i++) {
            String item = items[i];
            //logger.debug("[丰海]第" + i + "屏节目解析:" + item);
            int startFlag = item.indexOf("\\");
            if (startFlag < 0)
                continue;
            String paramStr = item.substring(0, startFlag);
            String[] params = paramStr.split(",");
            int delay = Integer.parseInt(params[0]) / 100;
            int inMode = Integer.parseInt(params[1]);
            int outMode = 0;
            int speed = Integer.parseInt(params[2]);
            String word = item.substring(startFlag);
            word = word.replace("\r", "");
            word = word.replace("\n", "");
            word = word.replace("\\F", "\\D");
            word = word.replace("\\f", "\\F");
            word = word.replace("\\c", "\\T");
            word = word.replace("\\b", "\\K");
            int fontSize = 16;
            String fontType = "SimSun";
            //即使有动画文件也不解析，为了方便定位正文信息，故删去
            if (word.indexOf("\\D") >= 0) {
                word = word.replaceAll("\\\\D\\d{5}", "");
            }
            //如果有字体
            if (word.indexOf("\\F") >= 0) {
                fontSize = Integer.parseInt(word.substring(word.indexOf("\\F") + 3, word.indexOf("\\F") + 5));
                fontType = word.substring(word.indexOf("\\F") + 2, word.indexOf("\\F") + 3);
                if (fontType.equals("h")) {
                    fontType = "SimHei";
                } else if (fontType.equals("s")) {
                    fontType = "SimSun";
                } else if (fontType.equals("k")) {
                    fontType = "KaiTi";
                } else if (fontType.equals("f")) {
                    fontType = "Fangsong";
                } else {
                    fontType = "SimSun";
                }
            }
            //如果有字符颜色
            String fontColor = "FF0000";
            if (word.indexOf("\\T") >= 0) {
                if (word.substring(word.indexOf("\\T") + 2, word.indexOf("\\T") + 3).equals("t")) {
                    fontColor = "000000";
                    word = word.replace("\\Tt", "\\T000000000000");
                } else {
                    fontColor = word.substring(word.indexOf("\\T") + 2, word.indexOf("\\T") + 11);
                    fontColor = addZero(Integer.toHexString(Integer.parseInt(fontColor.substring(0, 3))), 2) +
                            addZero(Integer.toHexString(Integer.parseInt(fontColor.substring(3, 6))), 2) +
                            addZero(Integer.toHexString(Integer.parseInt(fontColor.substring(6, 9))), 2);
                }
            }
            //如果有背景颜色
            String bg_color = "000000";
            if (word.indexOf("\\K") >= 0) {
                if (word.substring(word.indexOf("\\K") + 2, word.indexOf("\\K") + 3).equals("t")) {
                    bg_color = "000000";
                    word = word.replace("\\Kt", "\\K000000000000");
                } else {
                    bg_color = word.substring(word.indexOf("\\K") + 2, word.indexOf("\\K") + 11);
                    bg_color = addZero(Integer.toHexString(Integer.parseInt(bg_color.substring(0, 3))), 2) +
                            addZero(Integer.toHexString(Integer.parseInt(bg_color.substring(3, 6))), 2) +
                            addZero(Integer.toHexString(Integer.parseInt(bg_color.substring(6, 9))), 2);
                }
            }
            int img_width = 0;
            String imgName = null;
            int imgX = 0, imgY = 0;
            //如果有图片
            if (word.indexOf("\\B") >= 0) {
                //\B前的\C是图片坐标
                int img_loc_C = word.substring(0, word.indexOf("\\B")).indexOf("\\C");
                imgName = word.substring(word.indexOf("\\B") + 2, word.indexOf("\\B") + 5) + ".bmp";
                if (img_loc_C >= 0) {
                    imgX = Integer.parseInt(word.substring(img_loc_C + 2, img_loc_C + 5));
                    imgY = Integer.parseInt(word.substring(img_loc_C + 5, img_loc_C + 8));
                }
                //取完所有数据后，删除图片的坐标\CXXXXXX和\BXXX以免混淆
                word = (img_loc_C > 0) ? word.substring(0, img_loc_C) : "" + word.substring(word.indexOf("\\B") + 5);
            }
            int txtX = 0, txtY = 0;
            int txt_locC = word.indexOf("\\C");
            if (txt_locC >= 0 && word.length() > txt_locC + 8) {
                txtX = Integer.parseInt(word.substring(txt_locC + 2, txt_locC + 5));
                txtY = Integer.parseInt(word.substring(txt_locC + 5, txt_locC + 8));
            }

            word = word.replace("\\n", "\\A");
            //寻找最后的非\\A的转义符
            int loc_firstLineBreakA = (word.indexOf("\\A") >= 0) ? word.indexOf("\\A") : word.length();
            int loc_plusWord = word.substring(0, loc_firstLineBreakA).lastIndexOf("\\");
            //判断跟随在"\"之后的字符
            char temp_afterPlusWord = word.charAt(loc_plusWord + 1);
            int loc_plusWordEnd = loc_plusWord;
            if (temp_afterPlusWord == 'T' || temp_afterPlusWord == 'K') {
                loc_plusWordEnd = loc_plusWord + 14;
            } else if (temp_afterPlusWord == 'C') {
                loc_plusWordEnd = loc_plusWord + 8;
            } else if (temp_afterPlusWord == 'F') {
                loc_plusWordEnd = loc_plusWord + 7;
            }
            String txtInfo = "";
            //如果最后非\A的转义符不是\B（说明有文字）
            if (temp_afterPlusWord != 'B') {
                String plusWord = word.substring(loc_plusWord, loc_plusWordEnd);
                word = word.substring(0, loc_plusWordEnd) + "\\W" + word.substring(loc_plusWordEnd);
                txtInfo = word.substring(word.indexOf("\\W") + 2);
            }
            CmsRtRepertoryDTO rt = new CmsRtRepertoryDTO();
            rt.setName("丰海实时节目单" + i);
            rt.setLabelX(txtX);
            rt.setLabelY(txtY);
            rt.setFontSize(fontSize);
            rt.setFontType(fontType);
            rt.setFontColor("#" + fontColor.toUpperCase());
            rt.setBackColor("#" + bg_color.toUpperCase());
            //新版的messagebody需转换
            txtInfo = txtInfo.replace("\\A", "<br>");//替换换行符
            txtInfo = txtInfo.replaceAll("\\s", "&nbsp;");//替换空格
            rt.setMessageBody(txtInfo);
            rt.setImageX(imgX);
            rt.setImageY(imgY);
            rt.setImageSize(img_width);
            rt.setImageBody(imgName);
            if(delay==0)
            {
                delay = 5;
            }
            if(speed<=0)
            {
                speed =1 ;
            }
            rt.setHoldTime(5);
            rt.setSpeed(speed);
            //出入屏方式需转换
            int[] convertMode = {0, 0, 2, 1, 3, 4, 12, 13, 10, 11, 14, 9, 0, 0, 0, 0};
            rt.setInputMode(convertMode[inMode] + "");
            rt.setOutputMode(convertMode[outMode] + "");
            rt.setDeviceId(cms.getDevice_id());
            rt.setDeviceName(cms.getDevice_name());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String currentTime = sdf.format(new Date());
            rt.setReleaseTime(currentTime);
            rtList.add(rt);
        }
        return updateRtRepertory(cms.getDevice_id(),rtList);
    }

    public String sendCmdKXB(DeviceCms cms, String command, String data) {
        String CMD_DEST_ADDRESS = "3030";//范围01~99，目的地址，用户自定义
        //通用查询指令
        String ip = cms.getIp_address();
        int port = cms.getPort();
        Socket socket = new Socket();
        DataOutputStream out = null;
        DataInputStream in = null;
        String result = null;
        try {
            //1.基本命令，此处先不加帧头，为了计算CRC方便
            String controlStr = CMD_DEST_ADDRESS + command + data;
            //2.校验位
            String crc = getVerifyCRC(controlStr, 4);
            controlStr += crc;
            //3.转义字符
            String convertStr = escape(controlStr);
            //4.指令的完整形式
            convertStr = CMD_BEGIN_FRAME + convertStr + CMD_END_FRAME;
            //5.创建传输套接字
            InetSocketAddress socketAddress = new InetSocketAddress(ip, port);
            socket.setSoTimeout(CMD_TIME_OUT);//设置 超时时间
            socket.connect(socketAddress, CMD_TIME_OUT);//开始连接ip
            //6.传输
            byte[] sendData = DetectorUtils.hexStringToBytes(convertStr);
            out = new DataOutputStream(socket.getOutputStream());
            in = new DataInputStream(socket.getInputStream());
            out.write(sendData);
            out.flush();
            //7.读取设备回复信息
            int readLenth = CMD_MAX_LENGTH;
            byte[] responData = new byte[readLenth];
            readLenth = in.read(responData);
            if (readLenth >= 0) {
                responData = Arrays.copyOfRange(responData, 0, readLenth);
                String hexResult = DetectorUtils.bytesToHexString(responData);
                //反转义
                hexResult = deEscape(hexResult);
                //去除帧头、地址、帧类型、校验位和帧尾
                hexResult = hexResult.substring(10, hexResult.length() - 6);
                return hexResult;
            }
        } catch (Exception e) {
            //logger.info("[丰海]发送指令出错!原因:" + e.getMessage());
        } finally {
            closeIOStream(in, out);
        }
        return null;
    }

    //------------------------------------------------------------------------------------------------------------------
    public boolean getSsRtRepertory(DeviceCms cms) {
        String CMD_DOWNLOAD = "3039";//从可变信息标志下载文件
        //2 字节帧类型
        String cmd = CMD_DOWNLOAD;
        //文件名：不定长 ASCII 码字符串
        String fileName = DetectorUtils.stringToHexString("play.lst");
        //文件指针偏移：4 字节十六进制数，先发高字节，后发低字节
        int offset = 0;
        int dataLenGot = CMD_MAX_LENGTH / 2;//2048 BYTE
        StringBuffer fileData = new StringBuffer();
        while (dataLenGot >= CMD_MAX_LENGTH / 2) {
            String addressOffset = String.format("%08x", CMD_MAX_LENGTH / 2 * offset);
            String data = fileName + addressOffset;
            String recv = sendCmdSs(cms, cmd, data);
            if (recv == null || recv.length() == 0) {
                //接收不到这一段的数据
                return false;
            } else {
                fileData.append(recv);
                dataLenGot = recv.length() / 2;
                offset++;
            }
        }
        if(StringUtils.isBlank(fileData.toString()))
        {
            logger.info( cms.getDevice_name() + " 三思获取实时节目单为空 : " + TimeUtils.getTimeString("yyyy-MM-dd HH:mm:ss"));
            return false;
        }
        String recvStr = DetectorUtils.hexStringToString(fileData.toString(), CHARSET_GBK);

        String[] items = recvStr.split("Item\\d+=");
        ArrayList<CmsRtRepertoryDTO> rtList = new ArrayList<>();
        for (int i = 1; i < items.length; i++) {
            StringBuffer sb = new StringBuffer();
            String item = items[i];
            int startFlag = item.indexOf("\\");
            if (startFlag < 0)
                continue;
            String paramStr = item.substring(0, startFlag);
            String[] params = paramStr.split(",");
            int delay = Integer.parseInt(params[0].replace(" ", "")) / 100;
            int inMode = Integer.parseInt(params[1].replace(" ", ""));
            int outMode = 0;
            int speed = Integer.parseInt(params[2].replace(" ", ""));
            sb.append(delay + "," + inMode + "," + outMode + ",0," + speed + ",");
            String word = item.substring(startFlag);
            word = word.replace("\r", "");
            word = word.replace("\n", "");
            word = word.replace("\\f", "\\F");
            word = word.replace("\\c", "\\T");
            word = word.replace("\\b", "\\K");
            int fontSize = 16;
            String fontType = "SimSun";
            int imgX = 0;
            int imgY = 0;
            int txtX = 0;
            int txtY = 0;
            //如果有字体
            if (word.indexOf("\\F") >= 0) {
                fontSize = Integer.parseInt(word.substring(word.indexOf("\\F") + 3, word.indexOf("\\F") + 5));
                fontType = word.substring(word.indexOf("\\F") + 2, word.indexOf("\\F") + 3);
                if (fontType.equals("h"))
                    fontType = "SimHei";
                else if (fontType.equals("s"))
                    fontType = "SimSun";
                else if (fontType.equals("k"))
                    fontType = "KaiTi";
                else if (fontType.equals("f"))
                    fontType = "Fangsong";
                else
                    fontType = "SimSun";
            }
            //如果有字符颜色
            String fontColor = "FF0000";
            if (word.indexOf("\\T") >= 0) {
                if (word.substring(word.indexOf("\\T") + 2, word.indexOf("\\T") + 3).equals("t")) {
                    fontColor = "000000";
                    word = word.replace("\\Tt", "\\T000000000000");
                } else {
                    fontColor = word.substring(word.indexOf("\\T") + 2, word.indexOf("\\T") + 11);
                    fontColor = addZero(Integer.toHexString(Integer.parseInt(fontColor.substring(0, 3))), 2) +
                            addZero(Integer.toHexString(Integer.parseInt(fontColor.substring(3, 6))), 2) +
                            addZero(Integer.toHexString(Integer.parseInt(fontColor.substring(6, 9))), 2);
                }
            }
            //如果有背景颜色
            String bg_color = "000000";
            if (word.indexOf("\\K") >= 0) {
                if (word.substring(word.indexOf("\\K") + 2, word.indexOf("\\K") + 3).equals("t")) {
                    bg_color = "000000";
                    word = word.replace("\\Kt", "\\K000000000000");
                } else {
                    bg_color = word.substring(word.indexOf("\\K") + 2, word.indexOf("\\K") + 11);
                    bg_color = addZero(Integer.toHexString(Integer.parseInt(bg_color.substring(0, 3))), 2) +
                            addZero(Integer.toHexString(Integer.parseInt(bg_color.substring(3, 6))), 2) +
                            addZero(Integer.toHexString(Integer.parseInt(bg_color.substring(6, 9))), 2);
                }
            }
            int img_width = 0;
            String imgName = null;
            //如果有图片
            if (word.indexOf("\\B") >= 0) {
                //\B前的\C是图片坐标
                int img_loc_C = word.substring(0, word.indexOf("\\B")).indexOf("\\C");
                imgName = word.substring(word.indexOf("\\B") + 2, word.indexOf("\\B") + 5) + ".bmp";
                if (img_loc_C >= 0) {
                    imgX = Integer.parseInt(word.substring(img_loc_C + 2, img_loc_C + 5));
                    imgY = Integer.parseInt(word.substring(img_loc_C + 5, img_loc_C + 8));
                }
                //取完所有数据后，删除图片的坐标\CXXXXXX和\BXXX以免混淆
                word = (img_loc_C > 0) ? word.substring(0, img_loc_C) : "" + word.substring(word.indexOf("\\B") + 5);
            }

            int txt_locC = word.indexOf("\\C");
            if (txt_locC >= 0 && word.length() > txt_locC + 8) {
                txtX = Integer.parseInt(word.substring(txt_locC + 2, txt_locC + 5));
                txtY = Integer.parseInt(word.substring(txt_locC + 5, txt_locC + 8));
            }

            word = word.replace("\\n", "\\A");
            //寻找最后的非\\A的转义符
            int loc_firstLineBreakA = (word.indexOf("\\A") >= 0) ? word.indexOf("\\A") : word.length();
            int loc_plusWord = word.substring(0, loc_firstLineBreakA).lastIndexOf("\\");
            //判断跟随在"\"之后的字符
            char temp_afterPlusWord = word.charAt(loc_plusWord + 1);
            int loc_plusWordEnd = loc_plusWord;
            if (temp_afterPlusWord == 'T' || temp_afterPlusWord == 'K') {
                loc_plusWordEnd = loc_plusWord + 14;
            } else if (temp_afterPlusWord == 'C')
                loc_plusWordEnd = loc_plusWord + 8;
            else if (temp_afterPlusWord == 'F')
                loc_plusWordEnd = loc_plusWord + 7;
            String txtInfo = "";
            //如果最后非\A的转义符不是\B（说明有文字）
            if (temp_afterPlusWord != 'B') {
                String plusWord = word.substring(loc_plusWord, loc_plusWordEnd);
                word = word.substring(0, loc_plusWordEnd) + "\\W" + word.substring(loc_plusWordEnd);
                txtInfo = word.substring(word.indexOf("\\W") + 2);
            }
            sb.append(word);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String currentTime = sdf.format(new Date());

            //新版的messagebody需转换
            txtInfo = txtInfo.replaceAll("\\s++\\\\A", "\\\\A");
            txtInfo = txtInfo.replace("\\A", "<br>");//替换换行符
            txtInfo = txtInfo.replaceAll("\\s+", "&nbsp;");//替换空格
            CmsRtRepertoryDTO rt = new CmsRtRepertoryDTO();
            rt.setFontSize(fontSize);
            rt.setFontType(fontType);
            rt.setFontSize(fontSize);
            rt.setFontColor("#" + fontColor.toUpperCase());
            rt.setBackColor("#" + bg_color.toUpperCase());
            if(delay==0)
            {
                delay = 5;
            }
            if(speed<=0)
            {
                speed =1 ;
            }
            rt.setHoldTime(5);
            //出入屏方式需转换
            int[] convertMode = {1, 12, 10, 11, 13, 2, 3, 4, 5, 14, 9, 15, 6, 7, 8, 0};
            rt.setInputMode(convertMode[inMode] + "");
            rt.setOutputMode(convertMode[outMode] + "");
            rt.setSpeed(speed);
            if (imgName != null && imgName.length() > 4) {
                rt.setImageSize(img_width);
                rt.setImageName(imgName.substring(0, imgName.length() - 4));
                rt.setImageBody(imgName);
            }
            rt.setImageX(imgX);
            rt.setImageY(imgY);
            rt.setLabelX(txtX);
            rt.setLabelY(txtY);
            rt.setMessageBody(txtInfo);
            rt.setDeviceId(cms.getDevice_id());
            rt.setDeviceName(cms.getDevice_name());
            rt.setReleaseTime(currentTime);
            rt.setName("三思实时节目单"+i);
            rtList.add(rt);

        }
        return updateRtRepertory(cms.getDevice_id(),rtList);
    }

    public String sendCmdSs(DeviceCms cms, String command, String data) {
        String CMD_DEVICE_ADDRESS = "3030";//范围01~99，设备地址，厂家定义，00为广播地址
        //通用查询指令
        String ip = cms.getIp_address();
        int port = cms.getPort();
        if (ip == null || ip.length() == 0) {
            return null;
        }
        Socket socket = null;
        DataOutputStream out = null;
        DataInputStream in = null;
        String result = null;
        try {
            //基本命令，此处先不加帧头，为了计算CRC方便
            String controlstr = CMD_DEVICE_ADDRESS + command + data;
            //校验位
            String crcstr = getVerifyCRC(controlstr, 4).toUpperCase();
            controlstr += crcstr;
            //转义字符
            String convertstr = escape(controlstr);
            //指令的完整形式
            convertstr = CMD_BEGIN_FRAME + convertstr + CMD_END_FRAME;
            //创建传输套接字
            socket = new Socket();
            socket.connect(new InetSocketAddress(ip, port), CMD_TIME_OUT);
            socket.setSoTimeout(CMD_TIME_OUT);
            //传输
            byte[] sendData = DetectorUtils.hexStringToBytes(convertstr.toUpperCase());
            out = new DataOutputStream(socket.getOutputStream());
            in = new DataInputStream(socket.getInputStream());
            out.write(sendData);
            int readLenth = 2048;
            byte[] respondata = new byte[readLenth];
            String hexResult = "";
            readLenth = in.read(respondata);
            while (readLenth > 0) {
                respondata = Arrays.copyOfRange(respondata, 0, readLenth);
                hexResult += DetectorUtils.bytesToHexString(respondata);
                if(hexResult.endsWith("03"))
                {
                    break;
                }
                respondata = new byte[readLenth];
                Thread.sleep(100);
                readLenth = in.read(respondata);
            }
            //反转义
            //System.out.println("三思反转义前：\r\n"+hexResult);
            hexResult = deEscape(hexResult);
            //去除帧头、地址、指令头、校验位和帧尾
            hexResult = hexResult.substring(6, hexResult.length() - 6);
            return hexResult;
        } catch (Exception e) {
            //logger.debug("[三思]通用发送命令失败:" + e.getMessage());
        } finally {
            closeIOStream(in, out);
        }
        return null;
    }


    //------------------------------------------------------------------------------------------------------------------
    public boolean getDnRtRepertory(DeviceCms cms) {
        String CMD_GET_LIST = "3937";//获取当前显示内容

        String command = CMD_GET_LIST;
        String hexResult = sendCmdDn(cms, command, "");
        String result = DetectorUtils.hexStringToString(hexResult, CHARSET_GBK);
        StringBuffer sb = new StringBuffer();
        if(StringUtils.isBlank(result))
        {
            logger.info( cms.getDevice_name() + " 鼎恩获取实时节目单为空 : " + TimeUtils.getTimeString("yyyy-MM-dd HH:mm:ss"));
            return false;
        }
        int delay = Integer.parseInt(result.substring(3, 8)) / 100;
        int inMode = Integer.parseInt(result.substring(8, 10));
        int speed = Integer.parseInt(result.substring(10, 15));
        sb.append(delay + "," + inMode + ",1,0," + speed + ",");
        String word = result.substring(15);
        word = word.replace("\r", "");
        word = word.replace("\n", "");
        word = word.replace("\\f", "\\F");
        word = word.replace("\\c", "\\T");
        word = word.replace("\\b", "\\K");
        int fontSize = 16;
        String fontType = "SimSun";
        String wordReplace = word + "";
        int imgX = 0;
        int imgY = 0;
        int txtX = 0;
        int txtY = 0;
        //如果有字体
        if (word.indexOf("\\F") >= 0) {
            fontSize = Integer.parseInt(word.substring(word.indexOf("\\F") + 3, word.indexOf("\\F") + 5));
            fontType = word.substring(word.indexOf("\\F") + 2, word.indexOf("\\F") + 3);
            if (fontType.equals("h"))
                fontType = "SimHei";
            else if (fontType.equals("s"))
                fontType = "SimSun";
            else if (fontType.equals("k"))
                fontType = "KaiTi";
            else if (fontType.equals("f"))
                fontType = "Fangsong";
            else
                fontType = "SimSun";
            wordReplace = wordReplace.replaceAll("\\\\F\\w\\d{4}", getStars(7));
        }
        //如果有字符颜色
        String fontColor = "FF0000";
        if (word.indexOf("\\T") >= 0) {
            if (word.substring(word.indexOf("\\T") + 2, word.indexOf("\\T") + 3).equals("t")) {
                fontColor = "000000";
                word = word.replace("\\Tt", "\\T000000000000");
            } else {
                fontColor = word.substring(word.indexOf("\\T") + 2, word.indexOf("\\T") + 11);
                fontColor = addZero(Integer.toHexString(Integer.parseInt(fontColor.substring(0, 3))), 2) +
                        addZero(Integer.toHexString(Integer.parseInt(fontColor.substring(3, 6))), 2) +
                        addZero(Integer.toHexString(Integer.parseInt(fontColor.substring(6, 9))), 2);
            }
            wordReplace = wordReplace.replaceAll("\\\\T\\d{12}", getStars(14));
        }
        //如果有背景颜色
        String bg_color = "000000";
        if (word.indexOf("\\K") >= 0) {
            if (word.substring(word.indexOf("\\K") + 2, word.indexOf("\\K") + 3).equals("t")) {
                bg_color = "000000";
                word = word.replace("\\Kt", "\\K000000000000");
            } else {
                bg_color = word.substring(word.indexOf("\\K") + 2, word.indexOf("\\K") + 11);
                bg_color = addZero(Integer.toHexString(Integer.parseInt(bg_color.substring(0, 3))), 2) +
                        addZero(Integer.toHexString(Integer.parseInt(bg_color.substring(3, 6))), 2) +
                        addZero(Integer.toHexString(Integer.parseInt(bg_color.substring(6, 9))), 2);
            }
            wordReplace = wordReplace.replaceAll("\\\\K\\d{12}", getStars(14));
        }
        int img_width = 0;
        String imgName = null;
        //如果有图片
        if (word.indexOf("\\B") >= 0) {
            //\B前的\C是图片坐标
            int img_loc_C = word.substring(0, word.indexOf("\\B")).indexOf("\\C");
            imgName = word.substring(word.indexOf("\\B") + 2, word.indexOf("\\B") + 5) + ".bmp";
            if (img_loc_C >= 0) {
                imgX = Integer.parseInt(word.substring(img_loc_C + 2, img_loc_C + 5));
                imgY = Integer.parseInt(word.substring(img_loc_C + 5, img_loc_C + 8));
            }
            //无法通过下载图片确定图片大小，只好假定图片是32*32像素
            img_width = 32;
            wordReplace = wordReplace.replaceAll("\\\\B\\d{3}\\\\C{6}", getStars(13));
        }
        //寻找最后的正式文字的位置
        int loc_plusWord = wordReplace.replaceFirst("\\*[^\\*\\\\]", "%locPlusWord%").indexOf("%locPlusWord%") + 1;
        //后面的\C要替换成换行符号
        String word_modify = word.substring(loc_plusWord);
        while (word_modify.indexOf("\\C") >= 0) {
            word_modify = word_modify.replaceFirst("\\\\C\\d{6}", "\\\\A");
        }
        String txtInfo = word_modify;
        //把修改后的word_modify替换回来
        word = word.substring(0, loc_plusWord) + word_modify;
        //提取文字坐标需要区分是否有图片(以免混淆图片坐标)
        int C_offset = 0;
        if (word.indexOf("\\B") > 0) {
            C_offset = word.indexOf("\\B");
            //\B后的\C才是文字坐标
        }
        //没有图片，第一个\C就是文字坐标
        int txt_locC = word.indexOf("\\C", C_offset);
        if (txt_locC > 0 && word.length() > txt_locC + 8) {
            txtX = Integer.parseInt(word.substring(txt_locC + 2, txt_locC + 5));
            txtY = Integer.parseInt(word.substring(txt_locC + 5, txt_locC + 8));
        }
        sb.append(word);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String currentTime = sdf.format(new Date());

        //新版的messagebody需转换
        txtInfo = txtInfo.replace("\\A", "<br>");//替换换行符
        txtInfo = txtInfo.replaceAll("\\s+", "&nbsp;");//替换空格
        CmsRtRepertoryDTO rt = new CmsRtRepertoryDTO();
        if(delay==0)
        {
            delay = 5;
        }
        if(speed<=0)
        {
            speed =1 ;
        }
        rt.setFontType(fontType);
        rt.setFontSize(fontSize);
        rt.setFontColor("#" + fontColor.toUpperCase());
        rt.setBackColor("#" + bg_color.toUpperCase());
        rt.setHoldTime(5);
        rt.setInputMode(inMode + "");
        rt.setSpeed(speed);
        rt.setOutputMode("0");
        if (imgName != null && imgName.length() > 4) {
            rt.setImageSize(img_width);
            rt.setImageName(imgName.substring(0, imgName.length() - 4));
            rt.setImageBody(imgName);
        }
        rt.setMessageBody(txtInfo);
        rt.setImageX(imgX);
        rt.setImageY(imgY);
        rt.setLabelX(txtX);
        rt.setLabelY(txtY);
        rt.setDeviceId(cms.getDevice_id());
        rt.setDeviceName(cms.getDevice_name());
        rt.setName("鼎恩实时节目单");
        rt.setReleaseTime(currentTime);
        //删除原来的实时节目单

        List<CmsRtRepertoryDTO> list = new ArrayList<>();
        list.add(rt);
        //生成新的实时节目单

        return true;
    }

    public String sendCmdDn(DeviceCms deviceCms, String command, String data) {
        String CMD_DEVICE_ADDRESS = "3030";//范围01~99，设备地址，厂家定义，00为广播地址
        //地址+指令+内容
        String sendstr = CMD_DEVICE_ADDRESS + command + data;
        //校验位
        String crcstr = getVerifyCRC(sendstr, 4);
        sendstr += crcstr;
        //转义字符
        String convertstr = escape(sendstr.toUpperCase());
        //添加帧头帧尾
        sendstr = CMD_BEGIN_FRAME + convertstr + CMD_END_FRAME;
        //1.构造UDP DatagramSocket对象
        DatagramSocket ds = null;
        byte[] buffer = DetectorUtils.hexStringToBytes(sendstr);//2位转成1个字节
        DatagramPacket dpSendData = null;
        int recvBufLen = 1024;
        byte[] recvBuf = new byte[recvBufLen];
        //创建传输套接字
        try {
            ds = new DatagramSocket();
            ds.setSoTimeout(CMD_TIME_OUT);
            dpSendData = new DatagramPacket(buffer, 0, buffer.length, InetAddress.getByName(deviceCms.getIp_address()), deviceCms.getPort());
            ds.send(dpSendData);
            DatagramPacket dpRecvData = new DatagramPacket(recvBuf, recvBufLen);//字节数组，长度
            ds.receive(dpRecvData);
            byte[] rec = dpRecvData.getData();
            String recvStr = DetectorUtils.bytesToHexString(rec);//02303130c55203
            Integer relength = recvStr.length();
            if (recvStr == null || recvStr.length() <= 12)
                return null;
            else {
                //一个字节为单位寻找帧尾
                int endFlag = -1;
                for (int i = 0; i < recvStr.length(); i += 2) {
                    if (recvStr.substring(i, i + 2).equals("03")) {
                        endFlag = i;
                        break;
                    }
                }
                if (endFlag < 0) {
                    //logger.debug("找不到帧尾");
                    return null;
                }
                recvStr = recvStr.substring(0, endFlag + 2);
                //反转义
                recvStr = deEscape(recvStr);
                //去除帧头、地址、帧尾和校验位
                recvStr = recvStr.substring(6, recvStr.length() - 6);
                return recvStr;
            }

        } catch (IOException e) {
//            logger.debug("[鼎恩]通用命令发送失败，原因：" + e.getMessage());
        } finally {
            if (ds != null)
                ds.close();
        }
        return null;
    }


    //------------------------------------------------------------------------------------------------------------------
    public boolean getTxRtRepertory(DeviceCms cms) {
        if ("V1".equalsIgnoreCase(cms.getVersion())|| ("V11").equalsIgnoreCase(cms.getVersion())) {
            return readCurrentPlaylistV1(cms);
        } else if (("V3").equalsIgnoreCase(cms.getVersion())) {
            return readCurrentPlaylistV3(cms);
        } else {
            return false;
        }
    }

    public boolean readCurrentPlaylistV1(DeviceCms cms) {
        //ArrayList<CmsRtRepertory> rtListNew = TxV1Utils.readCurrentPlaylist(cms);
        ArrayList<CmsRtRepertoryDTO> rtListNew = null;
        if (rtListNew==null||rtListNew.size() == 0) {
//            logger.debug("[同鑫V1]读取不到情报板内容，原因：获取的节目列表大小为0");
            return false;
        }
        return updateRtRepertory(cms.getDevice_id(),rtListNew);
    }

    public boolean readCurrentPlaylistV3(DeviceCms cms) {
        Socket socket = null;
        DataOutputStream out = null;
        DataInputStream in = null;
        String recvHex = null;
        try {
            socket = new Socket();
            socket.connect(new InetSocketAddress(cms.getIp_address(), cms.getPort()), CMD_TIME_OUT);
            socket.setSoTimeout(CMD_TIME_OUT_READ);
            in = new DataInputStream(socket.getInputStream());
            out = new DataOutputStream(socket.getOutputStream());
            String cmd = "3A";
            //身份验证
            sendSafeCheck(in, out,cms);
            Thread.sleep(500);
            recvHex = sendCmdTxV3(in, out, cmd, "",cms);
        } catch (Exception e) {
            logger.info("[同鑫V3]读取当前播放内容失败,原因：" + e.getMessage());
            return false;
        } finally {
            closeIOStream(in, out);
        }
        if (recvHex == null || recvHex.length() < 2) {
            logger.info( cms.getDevice_name() + " 同鑫V3获取实时节目单为空 : " + recvHex);
            return false;
        }
        recvHex = recvHex.substring(2);
        String recvStr = DetectorUtils.hexStringToString(recvHex, CHARSET);
        recvStr=recvStr.replace("\\,","*#");
        recvStr=recvStr.replace("\\、","、");
        String[] items = recvStr.split("\\[(?i)item");
        ArrayList<CmsRtRepertoryDTO> rtListNew = new ArrayList<>();
        for (int i = 1; i < items.length; i++) {
            int img_x = 0;
            int img_y = 0;
            int txt_x = 0;
            int txt_y = 0;
            String item = items[i];
            String tmpStr = item.substring(item.indexOf("param=") + "param=".length());
            String[] params = tmpStr.split(",");
            int delay = (params[0] != null && params[0].length() > 0) ? Integer.parseInt(params[0]) / 10 : 50;
            int inMode = (params[1] != null && params[1].length() > 0) ? Integer.parseInt(params[1]) : 1;
            if (inMode == 255) {
                inMode = 0;
            }
            int outMode = 0;
            try {
                outMode = (params[2] != null && params[2].length() > 0) ? Integer.parseInt(params[2]) : 1;
                if (outMode == 255) {
                    outMode = 0;
                }
            }catch (Exception e)
            {
                StackTraceElement[] elements = e.getStackTrace();
                StringBuffer buffer = new StringBuffer();
                for (StackTraceElement el:elements) {
                    buffer.append(el.toString()+"\n");
                }
                System.out.println(cms + " :出错 "+e.getCause()+"\n" + buffer.toString());
                return false;
            }
            int speed = (params[3] != null && params[3].length() > 0) ? Integer.parseInt(params[3]) : 1;
            StringBuffer sb = new StringBuffer();
            sb.append(delay + "," + inMode + ",0," + outMode + "," + speed + ",");
            //图片的处理只考虑一个图片的情况
            int img_width = 0;
            String img_name = null;
            if (tmpStr.indexOf("img") >= 0) {
                String imgStr = tmpStr.substring(tmpStr.indexOf("img") + 5, tmpStr.indexOf("imgparam"));
                imgStr = imgStr.replace("\\n", "");
                imgStr = imgStr.replace("\\r", "");
                String[] img_params = imgStr.split(",");
                for (int k = 0; k < img_params.length; k++) {
                    img_x = Integer.parseInt(img_params[0]);
                    img_y = Integer.parseInt(img_params[1]);
                    img_name = img_params[2].substring(0, img_params[2].indexOf(".bmp"));
                    if (img_name.length() > 3) {
                        img_name = img_name.substring(0, 3);
                    }
                    img_width = Integer.parseInt(img_params[4]);
                    //开始写图片参数
                    sb.append("\\B" + img_name + "\\C" + String.format("%03d", img_x + String.format("%03d", img_y)));
                }
            }
            tmpStr = tmpStr.substring(tmpStr.indexOf("txt"));
            String[] txts = tmpStr.split("txt\\d+=");
            String txt_fontType = null;
            String txt_fontSize = null;
            String txt_color = null;
            String bg_color = null;
            String txt_color_tmp = null;
            StringBuffer txtInfo = new StringBuffer();
            int origin_x = 0;
            int origin_y = 0;
            int min_x = 0;
            for (int j = 0; j < txts.length; j++) {
                String txt = txts[j];
                if (txt.length() == 0 || txt == null) {
                    continue;
                }
                String[] txt_params = txt.split(",");
                txt_x = (txt_params[0] != null && txt_params[0].length() > 0) ? Integer.parseInt(txt_params[0]) : 0;
                txt_y = (txt_params[1] != null && txt_params[1].length() > 0) ? Integer.parseInt(txt_params[1]) : 0;
                if (j == 0) {
                    origin_x = txt_x;
                    origin_y = txt_y;
                    min_x = txt_x;
                }
                else
                {
                    //不为首行且xy坐标不等于首行坐标的情况下
                    if(min_x>txt_x)
                    {

                    }
                }
                if (txt_fontType == null) {
                    txt_fontType = txt_params[2];
                    if (txt_fontType == null || txt_fontType.length() == 0) {
                        txt_fontType = "s";
                    }
                    if (txt_fontType.equals("1")) {
                        txt_fontType = "h";
                    } else if (txt_fontType.equals("2")) {
                        txt_fontType = "k";
                    } else if (txt_fontType.equals("3")) {
                        txt_fontType = "s";
                    } else if (txt_fontType.equals("4")) {
                        txt_fontType = "f";
                    } else {
                        txt_fontType = "s";
                    }
                }
                if (txt_fontSize == null) {
                    txt_fontSize = (txt_params[3] != null && txt_params[3].length() > 0) ? txt_params[3] : "1616";
                }
                if (txt_color == null) {
                    txt_color = (txt_params[4] != null && txt_params[4].length() > 0) ? txt_params[4] : "1";
                    if (txt_color.equals("1")) {
                        txt_color = "FF0000";
                    } else if (txt_color.equals("2")) {
                        txt_color = "00FF00";
                    } else if (txt_color.equals("3")) {
                        txt_color = "0000FF";
                    } else if (txt_color.equals("4")) {
                        txt_color = "FFFF00";
                    } else if (txt_color.equals("5")) {
                        txt_color = "FF00FF";
                    } else if (txt_color.equals("6")) {
                        txt_color = "00FFFF";
                    } else if (txt_color.equals("7")) {
                        txt_color = "FFFFFF";
                    } else if (txt_color.equals("8")) {
                        txt_color = "000000";
                    } else if (txt_color.length() < 6) {
                        txt_color = "FFFF00";
                    }
                }
                if (txt_color_tmp == null) {
                    txt_color_tmp = String.format("%03d", Integer.parseInt(txt_color.substring(0, 2), 16))
                            + String.format("%03d", Integer.parseInt(txt_color.substring(2, 4), 16)) +
                            String.format("%03d", Integer.parseInt(txt_color.substring(4, 6), 16));
                }
                String bg_color_tmp = "000000000";
                String txt_word = txt_params[7];
                txt_word = txt_word.replace("\\r", "");
                //txt_word = txt_word.replace("\\n", "\\A");
                txt_word = txt_word.replace("\\n", "");
                int fontSize = Integer.parseInt(txt_fontSize.substring(0, 2));
                //开始写文字参数
                if (txtInfo.length() == 0) {
                    int width = txt_params[8].matches("\\d+")?Integer.parseInt(txt_params[8]):0;
                    int height = txt_params[9].matches("\\d+")?Integer.parseInt(txt_params[9]):0;
                    if(width==cms.getWidth()&&height==cms.getHeight())
                    {
                        //文字框等于宽高尺寸，需要手动对其分行
                        char[] cs = txt_word.toCharArray();
                        double lineLength = 0.0;
                        StringBuffer tempword = new StringBuffer();
                        for (int k = 0; k <cs.length ; k++) {
                            char c = cs[k];
                            double count = (c+"").matches("[^\\x00-\\xff]")?1.0:0.5;
                            if(lineLength+fontSize*count>cms.getWidth())
                            {
                                tempword.append("\\A"+c);
                                lineLength = fontSize*count;
                            }
                            else
                            {
                                lineLength+=fontSize*count;
                                tempword.append(c);
                            }
                        }
                        txt_word = tempword.toString();
                    }
                    sb.append("\\C" + String.format("%03d", txt_x) + String.format("%03d", txt_y));
                    sb.append("\\F" + txt_fontType + txt_fontSize + "\\T" + txt_color_tmp + "\\K" + bg_color_tmp);
                    sb.append("\\W" + txt_word);
                    txtInfo.append(txt_word);
                } else {
                    sb.append("\\A" + txt_word);
                    txtInfo.append("\\A" + txt_word);
                }
            }
            //字体转换
            String right_fontName = "";
            if (txt_fontType.equals("h")) {
                right_fontName = "SimHei";
            } else if (txt_fontType.equals("s")) {
                right_fontName = "SimSun";
            } else if (txt_fontType.equals("k")) {
                right_fontName = "KaiTi";
            } else if (txt_fontType.equals("f")) {
                right_fontName = "Fangsong";
            } else {
                right_fontName = "SimSun";
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String currentTime = sdf.format(new Date());

            //新版的messagebody需转换
            String txt = txtInfo.toString();
            txt = txt.replace("\\A", "<br>");//替换换行符
            txt = txt.replaceAll("\\s+", "&nbsp;");//替换空格
            txt = txt.replace("*#",",");
            CmsRtRepertoryDTO rt = new CmsRtRepertoryDTO();
            rt.setName("同鑫V3实时节目单"+i);
            if (!StringUtils.isBlank(img_name)) {
                rt.setImageName(img_name);
                rt.setImageSize(img_width);
                rt.setImageBody(img_name + ".bmp");
            }
            rt.setImageX(img_x);
            rt.setImageY(img_y);
            rt.setLabelX(origin_x);
            rt.setLabelY(origin_y);
            rt.setFontSize(Integer.parseInt(txt_fontSize.substring(0, 2)));
            rt.setFontType(right_fontName);
            rt.setFontColor("#" + txt_color.toUpperCase());
            rt.setBackColor("#000000");
            rt.setMessageBody(txt);
            if(delay<=0)
            {
                delay = 5;
            }
            if(speed<=0)
            {
                speed =1 ;
            }
            rt.setHoldTime(5);
            rt.setSpeed(speed);
            rt.setInputMode(inMode + "");
            rt.setOutputMode(outMode + "");
            rt.setDeviceId(cms.getDevice_id());
            rt.setDeviceName(cms.getDevice_name());
            rt.setReleaseTime(currentTime);
            rtListNew.add(rt);
        }
        return updateRtRepertory(cms.getDevice_id(),rtListNew);
    }

    //V3身份验证
    public boolean sendSafeCheck(DataInputStream in, DataOutputStream out,DeviceCms cms) {
        String SAFE_CHECK_PASSWORD = "tx8888";
        //0表示不启用，1表示启用
        String sastr = "";
        String sastrtem = "700000" + DetectorUtils.stringToHexString(SAFE_CHECK_PASSWORD);
        String recv = sendCmdTxV3(in, out, "", sastrtem,cms);
        if (recv != null&&recv.length()>=4&&recv.substring(2, 4).equals("01")) {
            return true;
        } else {
            return false;
        }

    }

    public String sendCmdTxV3(DataInputStream in, DataOutputStream out, String cmd, String data,DeviceCms cms) {
        String CMD_BEGIN_V3 = "AA";
        String CMD_DEVICE_ADDRESS_V3 = "0000";//广播地址
        String CMD_END_V3 = "CC";
        String result = null;
        try {
            //2.基本命令
            String content = (CMD_DEVICE_ADDRESS_V3 + cmd + data).toUpperCase();
            //转义
            content = escapeTxV3(content);
            String controlStr = CMD_BEGIN_V3 + content + CMD_END_V3;
            //3.校验位
            byte[] bytedata = DetectorUtils.hexStringToBytes(controlStr);
            String crcstr = Integer.toHexString(PubCalcCRC16(bytedata, bytedata.length)).toUpperCase();
            for (int i = crcstr.length(); i < 4; i++) {//补齐2字节CRC校验位
                crcstr = "0" + crcstr;
            }
            //校验位要低位为先
            controlStr += crcstr;
            //测试 生产环境删掉
            logger.info("[同鑫V3]"+cms.getDevice_name()+"发送："+controlStr);
            //传输
            byte[] sendData = DetectorUtils.hexStringToBytes(controlStr);
            out.write(sendData);
            out.flush();
            int readLenth = 2048;
            byte[] respondata = new byte[readLenth];
            String hexResult = "";
            readLenth = in.read(respondata);
            while (readLenth > 0) {
                respondata = Arrays.copyOfRange(respondata, 0, readLenth);
                hexResult += DetectorUtils.bytesToHexString(respondata);
                //问题出在这里；并不是以CC结尾的
                if(hexResult.substring(0,hexResult.length()-4).endsWith(CMD_END_V3))
                {
                    break;
                }
                respondata = new byte[readLenth];
                Thread.sleep(100);
                readLenth = in.read(respondata);
            }
            //反转义
            //System.out.println("同鑫V3反转义前：\r\n"+hexResult);


            if (hexResult.length() > 6) {
                //如果不是以CCXXXX结尾，打印出来
                if(!hexResult.substring(hexResult.length()-6).startsWith("CC"))
                {
                    logger.info("hexResult=\n"+hexResult);
                }
                //首先转义
                hexResult = deEscapeTxV3(hexResult);
                //去除帧头、地址、校验位和帧尾(注意：没有去除指令码，需要手动去除)
                return hexResult.substring(6, hexResult.length() - 6).toUpperCase();
            }
        } catch (Exception e) {
            logger.info("[同鑫V3]"+cms.getDevice_name()+"获取信息失败:"+e.getMessage());
        }
        return null;
    }
    //------------------------------------------------------------------------------------------------------------------

    public List<String> pollAllId() {
        List<String> result = new ArrayList<>();
        synchronized (syncIdList) {
            while (!syncIdList.isEmpty()) {
                result.add(syncIdList.poll());
            }
        }
        return result;
    }

    public List<CmsRtRepertoryDTO> pollAllCmsRt() {
        List<CmsRtRepertoryDTO> result = new ArrayList<>();
        synchronized (syncCmsRtList) {
            while (!syncCmsRtList.isEmpty()) {
                result.add(syncCmsRtList.poll());
            }
        }
        return result;
    }

    private boolean updateRtRepertory(String deviceID,List<CmsRtRepertoryDTO> list)
    {
        if(list.size()>0) {
            //删除原来的实时节目单
            syncIdList.offer(deviceID);
            //插入实时节目
            for (CmsRtRepertoryDTO rt:list) {
                syncCmsRtList.offer(rt);
            }
            return true;
        }
        else
        {
            return false;
        }
    }


    private boolean closeIOStream(InputStream in, OutputStream out) {
        boolean flag = true;
        if (in != null) {
            try {
                in.close();
            } catch (IOException e) {
                flag = false;
            }
        }
        if (out != null) {
            try {
                out.close();
            } catch (IOException e) {
                flag = false;
            }
        }
        return flag;
    }

    /**
     * 按照协议文件转义特殊的命令符，要求传入的长度为2字节的参数
     *
     * @param cmd 待转义的字符，长度为2字节
     * @return 转义结果字符
     */
    private static String escape(String cmd) {
        return CmsStatusService.escape(cmd);
    }

    private static String escapeOld(String cmd) {
        String convend = "";
        if (cmd.equals("02")) {
            convend = "1BE7";
        } else if (cmd.equals("03")) {
            convend = "1BE8";
        } else if (cmd.equals("1B")) {
            convend = "1B00";
        } else {
            convend = cmd.toUpperCase();
        }
        return convend;
    }

    private static String deEscape(String str) {
        StringBuffer sb = new StringBuffer(str.toUpperCase());
        StringBuffer result = new StringBuffer();
        while (sb.length() >= 2) {
            String temp = sb.substring(0, 2);
            if (temp.equals("1B")) {
                if (sb.length() >= 4) {
                    String temp2 = sb.substring(2, 4);
                    if (temp2.equals("E7")) {
                        result.append("02");
                    } else if (temp2.equals("E8")) {
                        result.append("03");
                    } else if (temp2.equals("00")) {
                        result.append("1B");
                    } else {
                        result.append(temp);
                        sb.delete(0, 2);
                        continue;
                    }
                    sb.delete(0, 4);
                } else {
                    result.append(temp);
                    sb.delete(0, 2);
                }
            } else {
                result.append(temp);
                sb.delete(0, 2);
            }
        }
        if (sb.length() > 0) {
            result.append(sb.toString());
        }
        return result.toString();
    }

    public static String addZero(String str, int totalLength) {
        String result = str;
        for (int i = str.length(); i < totalLength; i++) {
            result = "0" + result;
        }
        return result;
    }

    private static String getVerifyCRC(String hex, int totalLen) {
        byte[] bytes = DetectorUtils.hexStringToBytes(hex);
        int crc = 0x00;
        for (int i = 0; i < bytes.length; i++) {
            byte b = bytes[i];
            for (int j = 0; j < 8; j++) {
                boolean bit = ((b >> (7 - j) & 1) == 1);
                boolean c15 = ((crc >> 15 & 1) == 1);
                crc <<= 1;
                if (c15 ^ bit) {
                    crc ^= 0x1021;
                }
            }
        }
        crc &= 0xffff;
        String hexCRC = addZero(Integer.toHexString(crc), totalLen);
        return hexCRC;
    }

    private static String getStars(int num) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < num; i++) {
            sb.append("*");
        }
        return sb.toString();
    }

    //转义
    public static final String escapeTxV3(String str) {
        if (str == null || str.length() == 0) {
            return null;
        }
        StringBuffer temp = new StringBuffer();
        StringBuffer sb = new StringBuffer(str);
        while (sb.length() >= 2) {
            if (sb.substring(0, 2).equals("AA")) //Delphi copy(crc_temp1,1,2)
            {
                temp.append("EE0A");
            } else if (sb.substring(0, 2).equals("CC")) {
                temp.append("EE0C");
            } else if (sb.substring(0, 2).equals("EE")) {
                temp.append("EE0E");
            } else {
                temp.append(sb.substring(0, 2));
            }
            sb.delete(0, 2);
        }
        if (sb.length() > 0) {
            temp.append(sb.toString());
        }
        return temp.toString().toUpperCase();
    }

    //反转义
    public static final String deEscapeTxV3(String str) {
        StringBuffer sb = new StringBuffer(str.toUpperCase());
        StringBuffer result = new StringBuffer();
        while (sb.length() >= 2) {
            String temp = sb.substring(0, 2);
            if (temp.equals("EE")) {
                if (sb.length() >= 4) {
                    String temp2 = sb.substring(2, 4);
                    if (temp2.equals("0A")) {
                        result.append("AA");
                    } else if (temp2.equals("0C")) {
                        result.append("CC");
                    } else if (temp2.equals("0E")) {
                        result.append("EE");
                    } else {
                        result.append(temp);
                        sb.delete(0, 2);
                        continue;
                    }
                    sb.delete(0, 4);
                } else {
                    result.append(temp);
                    sb.delete(0, 2);
                }
            } else {
                result.append(temp);
                sb.delete(0, 2);
            }
        }
        if (sb.length() > 0) {
            result.append(sb.toString());
        }
        return result.toString().toUpperCase();
    }

    //CRC校验2
    public static int PubCalcCRC16(byte[] puchMsg, int usDataLen) {
        int fcs = 0xFFFF;

        int uIndex;
        for (int i = 0; i < usDataLen; i++) {
            uIndex = (fcs ^ puchMsg[i]) & 0xff;
            fcs = ((fcs >> 8) ^ CRC_MATRIX[uIndex]) & 0xffff;
        }
        return ((((int) fcs) << 8 | (((int) fcs) >> 8 & 0xff))) & 0xffff;//低位在前，高位在后
    }

    private static int[] CRC_MATRIX = {
            0x0000, 0x1189, 0x2312, 0x329b, 0x4624, 0x57ad, 0x6536, 0x74bf, 0x8c48, 0x9dc1,
            0xaf5a, 0xbed3, 0xca6c, 0xdbe5, 0xe97e, 0xf8f7, 0x1081, 0x0108, 0x3393, 0x221a,
            0x56a5, 0x472c, 0x75b7, 0x643e, 0x9cc9, 0x8d40, 0xbfdb, 0xae52, 0xdaed, 0xcb64,
            0xf9ff, 0xe876, 0x2102, 0x308b, 0x0210, 0x1399, 0x6726, 0x76af, 0x4434, 0x55bd,
            0xad4a, 0xbcc3, 0x8e58, 0x9fd1, 0xeb6e, 0xfae7, 0xc87c, 0xd9f5, 0x3183, 0x200a,
            0x1291, 0x0318, 0x77a7, 0x662e, 0x54b5, 0x453c, 0xbdcb, 0xac42, 0x9ed9, 0x8f50,
            0xfbef, 0xea66, 0xd8fd, 0xc974, 0x4204, 0x538d, 0x6116, 0x709f, 0x0420, 0x15a9,
            0x2732, 0x36bb, 0xce4c, 0xdfc5, 0xed5e, 0xfcd7, 0x8868, 0x99e1, 0xab7a, 0xbaf3,
            0x5285, 0x430c, 0x7197, 0x601e, 0x14a1, 0x0528, 0x37b3, 0x263a, 0xdecd, 0xcf44,
            0xfddf, 0xec56, 0x98e9, 0x8960, 0xbbfb, 0xaa72, 0x6306, 0x728f, 0x4014, 0x519d,
            0x2522, 0x34ab, 0x0630, 0x17b9, 0xef4e, 0xfec7, 0xcc5c, 0xddd5, 0xa96a, 0xb8e3,
            0x8a78, 0x9bf1, 0x7387, 0x620e, 0x5095, 0x411c, 0x35a3, 0x242a, 0x16b1, 0x0738,
            0xffcf, 0xee46, 0xdcdd, 0xcd54, 0xb9eb, 0xa862, 0x9af9, 0x8b70, 0x8408, 0x9581,
            0xa71a, 0xb693, 0xc22c, 0xd3a5, 0xe13e, 0xf0b7, 0x0840, 0x19c, 0x2b52, 0x3adb,
            0x4e64, 0x5fed, 0x6d76, 0x7cff, 0x9489, 0x8500, 0xb79b, 0xa612, 0xd2ad, 0xc324,
            0xf1bf, 0xe036, 0x18c1, 0x0948, 0x3bd3, 0x2a5a, 0x5ee5, 0x4f6c, 0x7df7, 0x6c7e,
            0xa50a, 0xb483, 0x8618, 0x9791, 0xe32e, 0xf2a7, 0xc03c, 0xd1b5, 0x2942, 0x38cb,
            0x0a50, 0x1bd9, 0x6f66, 0x7eef, 0x4c74, 0x5dfd, 0xb58b, 0xa402, 0x9699, 0x8710,
            0xf3af, 0xe226, 0xd0bd, 0xc134, 0x39c3, 0x284a, 0x1ad1, 0x0b58, 0x7fe7, 0x6e6e,
            0x5cf5, 0x4d7c, 0xc60c, 0xd785, 0xe51e, 0xf497, 0x8028, 0x91a1, 0xa33a, 0xb2b3,
            0x4a44, 0x5bcd, 0x6956, 0x78df, 0x0c60, 0x1de9, 0x2f72, 0x3efb, 0xd68d, 0xc704,
            0xf59f, 0xe416, 0x90a9, 0x8120, 0xb3bb, 0xa232, 0x5ac5, 0x4b4c, 0x79d7, 0x685e,
            0x1ce1, 0x0d68, 0x3ff3, 0x2e7a, 0xe70e, 0xf687, 0xc41c, 0xd595, 0xa12a, 0xb0a3,
            0x8238, 0x93b1, 0x6b46, 0x7acf, 0x4854, 0x59dd, 0x2d62, 0x3ceb, 0x0e70, 0x1ff9,
            0xf78f, 0xe606, 0xd49d, 0xc514, 0xb1ab, 0xa022, 0x92b9, 0x8330, 0x7bc7, 0x6a4e,
            0x58d5, 0x495c, 0x3de3, 0x2c6a, 0x1ef1, 0x0f78
    };
}
