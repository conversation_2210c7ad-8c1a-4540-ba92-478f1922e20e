package com.bt.itsinnerjob.detector.service;

import com.bt.itscore.domain.dto.FogInductStatusDTO;
import com.bt.itsinnerjob.detector.codec.CdDecoder;
import com.bt.itsinnerjob.detector.codec.CommonEncoder;
import com.bt.itsinnerjob.detector.entity.CdFogPacket;
import com.bt.itsinnerjob.detector.entity.DeviceFogInduct;
import com.bt.itsinnerjob.detector.entity.FogInductPlanContent;
import com.bt.itsinnerjob.detector.entity.OutfieldLinkage;
import com.bt.itsinnerjob.detector.utils.DetectorDBQueueUtils;
import com.bt.itsinnerjob.detector.utils.DetectorUtils;
import com.bt.itsinnerjob.mapper.detector.DeviceFogInductMapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioDatagramChannel;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.DatagramPacket;
import java.net.InetSocketAddress;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.bt.itscore.utils.TimeUtils.getNowTime;

//畅电雾区协议
@Service("CdFogService")
public class CdFogService{

    private static final int TIME_OUT_CONNECT = 5;//连接超时，单位秒
    private static final int TIME_OUT_READ = 5 * 60;//读超时，单位秒
    private static final int CONNECT_COUNT_MAX = 3;//最大重连次数
    private static final int RETRY_COUNT_MAX = 3;//命令最大超时重传次数
    private boolean isActive = false;//服务是否可用
    private Bootstrap bootstrap;
    private EventLoopGroup worker;
    public static final int CDFOGPORT=30000; //Server端监听端口

    Logger logger = LogManager.getLogger(LogManager.ROOT_LOGGER_NAME);
    private ThreadPoolExecutor executor;
  //  private ConcurrentHashMap<String, TCPConnection> connectMap;
    private ConcurrentHashMap<String, UDPConnection> UDPconnectMap;   //UDP连接
    private HashMap<String, String> ipAndPortToDeviceIDMap;
    private List<DeviceFogInduct> deviceList = new ArrayList<>();
   // private UDPClientHandler ClientHandler;
    private UDPServerHandler UDPServerHandler; //UDP的ServerHandel
    private ConcurrentHashMap<String,String> visibilityIDMap = new ConcurrentHashMap<>();

    @Autowired
    private DeviceFogInductMapper deviceFogInductMapper;
    @Autowired
    private DeviceInfoService deviceInfoService;

    public void keepActive() { //入口
        if (!isActive) {
            init();
        }
    }

    //初始化
    public void init() {
       // setWorkGroup();
        setWorkGroup2();
        logger.info("setWorkGroup2()执行完毕！");
        UDPconnectMap = new ConcurrentHashMap<>();
        ipAndPortToDeviceIDMap = new HashMap<>();
        DeviceFogInduct sample = new DeviceFogInduct();
        sample.setProtocol("畅电");
        sample.setSourceId(Integer.parseInt(deviceInfoService.getSourceId()));
        deviceList = deviceFogInductMapper.selectList(sample);
        for (DeviceFogInduct d : deviceList) {
            if(d.getVisibilityId()!=null) {
                visibilityIDMap.put(d.getDeviceId(), d.getVisibilityId());
            }
            ipAndPortToDeviceIDMap.put(d.getIpAddress() + "[" + d.getPort() + "]", d.getDeviceId());
            UDPConnection connection = new UDPConnection();
            logger.info(d.getIpAddress()+"UDP连接完成！");
            connection.deviceID = d.getDeviceId();
            connection.ip = d.getIpAddress();
            connection.port = d.getPort();
            connection.isConnected = false;
            UDPconnectMap.put(d.getDeviceId(), connection);
        }
        isActive = true;
    }


  //UDP连接
    public void setWorkGroup2() {
        /**Bootstrap 与 ServerBootstrap都继承(extends)于 AbstractBootstrap
         * 创建服务端辅助启动类,并对其配置,与服务器稍微不同，这里的 Channel 设置为NioSocketChannel
         * 然后为其添加 Handler，这里直接使用匿名内部类，实现 initChannel 方法
         * 作用是当创建 NioSocketChannel 成功后，
         在进行初始化时,将它的ChannelHandler设置到ChannelPipeline中，
         * 用于处理网络I/O事件*/
        logger.info("changdian创建服务端辅助启动类");
        try {
        worker = new NioEventLoopGroup(); //配置服务端NIO 线程组/线程池
       //辅助启动类
       bootstrap = new Bootstrap();

       //设置线程池
       bootstrap.group(worker);

       //设置socket工厂
       bootstrap.channel(NioDatagramChannel.class); ///3、设置NIO UDP连接通道:channel(NioDatagramChannel.class)

       //设置管道
       bootstrap.handler(new ChannelInitializer<NioDatagramChannel>() {  //.handler(new ChannelInitializer<NioDatagramChannel>()
           @Override
           protected void initChannel(NioDatagramChannel ch) { //initChannel(NioDatagramChannel ch)
               //获取管道
               ChannelPipeline pipeline = ch.pipeline(); //handelInboundHandle的容器
               //添加心跳检测处理器
               //  pipeline.addLast(new IdleStateHandler(TIME_OUT_READ, 0, 0, TimeUnit.SECONDS)); //UDP没有长连接
               //编码器
               pipeline.addLast(new CommonEncoder());
               //解码器
               pipeline.addLast(new CdDecoder());
               //处理类
               UDPServerHandler = new UDPServerHandler(); //new UDPServerHandler()
               pipeline.addLast(UDPServerHandler);
           }
       });
           logger.info("Server端绑定端口");
           //4.bind到指定端口，并返回一个channel，该端口就是监听UDP报文的端口
           ChannelFuture channelFutrue = bootstrap.bind(CDFOGPORT).sync();
            if(channelFutrue.isSuccess()){
               logger.info(CDFOGPORT+"端口绑定成功，netty服务器启动成功！");
            }
            else {
                logger.info(CDFOGPORT+"端口绑定失败,netty服务器启动失败！");
            }
           //5.阻塞等待channel的close
       //    channelFutrue.channel().closeFuture().sync();
       }
       catch (InterruptedException e) {
           e.printStackTrace();
           logger.warn("运行出错"+e.getMessage());
       }
       finally {
     //      worker.shutdownGracefully();
     //      logger.info("websocket服务器已关闭连接！");
       }

    }


    public void connect() {
        keepActive();
        for (DeviceFogInduct d : deviceList) {
            connect(d);
        }
    }

    public void connect(DeviceFogInduct d) {
        keepActive();
        //连接及超时重连
        if (UDPconnectMap.get(d.getDeviceId()) == null) {
            UDPConnection connection = new UDPConnection();
            connection.deviceID = d.getDeviceId();
            connection.ip = d.getIpAddress();
            connection.port = d.getPort();
            connection.isConnected = false;
            UDPconnectMap.put(d.getDeviceId(), connection);
            connection.doConnect();
        } else if (!UDPconnectMap.get(d.getDeviceId()).isConnected) {
            UDPConnection connection = UDPconnectMap.get(d.getDeviceId());
            connection.doConnect();
        }

        int outTime = TIME_OUT_CONNECT * 10;//超时 秒*10 100ms一次计时
        while (!UDPconnectMap.get(d.getDeviceId()).isConnected && outTime > 0) {
            outTime--;
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

    }

    public void stop() {
        if(worker!=null) {
            //结束的处理:退出释放NIO线程组
            worker.shutdownGracefully();
            isActive = false;
        }
    }

    //需设置为每5分钟一次
    public void timer() //入口2
    {
        Calendar cal = Calendar.getInstance();
        int minute = cal.get(Calendar.MINUTE);
        //每五分钟检测在线状态
        checkStatus();
        //每30分钟检查雾灯离线数目
        /*if(minute%30==0)
        {
            checkLightNumber();
        }*/
        //每一小时检查预案执行状态
        if(minute%60==0)
        {
            keepPlanFromLinkage();
        }
    }

    //状态回读，每10分钟一次
    public void checkStatus() {
        logger.info("[畅电畅电]检查状态--------------------");
        if(deviceList==null||deviceList.size()==0)
            return;
        //如果处于连接状态则尝试发送消息
        for (DeviceFogInduct d : deviceList) {
            connect(d);
            UDPConnection c = UDPconnectMap.get(d.getDeviceId());
            if (c.channel != null && c.channel.isWritable()) {
                try {
                    //发送获取能见度信息
                    //String getVisibilityStr = CdFogPacket.getVisibilityCmd();
                    //总状态检查
                    String totalStatusStr = CdFogPacket.getTotalStatusCmd(); //畅电状态查询指令
                    c.channel.writeAndFlush(totalStatusStr);  //放入缓存数组,channel.writeAndFlush(new DatagramPacket(byteBuf, address)).sync();
                    logger.info("[畅电] " + DetectorUtils.getNowTime() + " 正检查雾区主机[" + d.getDeviceName() + "|" + d.getIpAddress()
                            + "]的雾灯主状态和参数信息"+"指令cmd:"+totalStatusStr);
                    Thread.sleep(1000);
                    //c.channel.writeAndFlush(getVisibilityStr);
                } catch (Exception e) {
                    logger.error("[畅电]错误:" + e.getMessage());
                }
            }
        }
    }



    //普通手动执行/预案执行
    public void modifyWorkMode(DeviceFogInduct fog, FogInductPlanContent planContent) {
        boolean flag = false;
        boolean needModifyAlarmDistance = false;
        connect(fog);  //连接雾区主机
        UDPConnection c = UDPconnectMap.get(fog.getDeviceId());
        if (c.channel != null && c.channel.isWritable()) {
            try {
                //开控制器/畅电无需开控制器
           //     String openCmd = CdFogPacket.openController();
           //     c.channel.writeAndFlush(openCmd);
           //     Thread.sleep(1000);

                //畅电的修改指令
                int cmdId = 1; //第一条指令
                // 要发送的指令数据HEX
                int lightlevelPWM = ligthcdTolightPWM(planContent.getLightLevel()); //cd值转为PWM值
                int workmode=workModeMatch(planContent.getWorkMode());  //预案工作模式转为畅电雾灯工作模式
                String cmd = CdFogPacket.getModifyWorkModeCmd(cmdId,workmode, planContent.getFlashFrequency(),
                        lightlevelPWM);
                c.channel.writeAndFlush(cmd);
                logger.info("[畅电] " + getNowTime() + " 向雾区主机[" + fog.getDeviceName() + "|" + fog.getIpAddress() + "]发送修改工作模式指令:\r\n" + cmd);
                Thread.sleep(1000);

              //     if (needModifyAlarmDistance) { //防追尾距离及超时时间设置******
              //        String cmd2 = CdFogPacket.getModifyAlarmDistanceAndTime(1, planContent.getTailLength(), 3);
              //       c.channel.writeAndFlush(cmd2);
              //   }
                logger.info("[畅电] " + DetectorUtils.getNowTime() + " 正向雾区主机[" + fog.getDeviceName() + "|" + fog.getIpAddress()
                        + "]发送修改工作模式指令,工作模式：" + planContent.getWorkMode() + " 频率:" + planContent.getFlashFrequency()
                        + " 亮度等级:" + planContent.getLightLevel() + " 占空比:" + planContent.getDutyCycle() + " 警示距离:" + planContent.getTailLength());
            } catch (Exception e) {
                e.printStackTrace();
                logger.info("[畅电] " + DetectorUtils.getNowTime() + " 向雾区主机[" + fog.getDeviceName() + "|" + fog.getIpAddress() + "]发送修改工作模式指令出现错误:" + e.getMessage());
            }
        } else {
            logger.info("[畅电] " + DetectorUtils.getNowTime() + " 向雾区主机[" + fog.getDeviceName() + "|" + fog.getIpAddress() + "]发送修改工作模式指令出现错误:连接失败，尝试重连");

        }
    }

    //雾灯预案，每一小时检查一次预案状态
    //@Scheduled(cron = "0 */60 * * * ?")
    public void keepPlanFromLinkage() {
        //更新设备列表信息
        DeviceFogInduct sample = new DeviceFogInduct();
        sample.setProtocol("畅电");
        sample.setSourceId(Integer.parseInt(deviceInfoService.getSourceId()));
        deviceList = deviceFogInductMapper.selectList(sample);

        for (DeviceFogInduct d : deviceList) {
            logger.info("[畅电]" + DetectorUtils.getNowTime() + " 检查雾区主机[" + d.getDeviceName() + "|" + d.getIpAddress() + "]预案执行情况");
            keepPlanFromLinkage(d);
        }
    }

    private List<Integer> timeRangeConvert(String time)
    {
        List<Integer> timeList = new ArrayList<>();
        if(time!=null&&time.length()>0) {
            time = time.replace("{", "");
            time = time.replace("}", "");
            String[] ss = time.split(",");
            for (String s : ss) {
                timeList.add(Integer.parseInt(s));
            }
        }
        return timeList;
    }

    private List<Integer> visibilityRangeConvert(String visibility)
    {
        List<Integer> list = new ArrayList<>();
        if(visibility!=null&&visibility.length()>0) {
            visibility = visibility.replace("[", "");
            visibility = visibility.replace("]", "");
            String[] ss = visibility.split(",");
            for (String s : ss) {
                list.add(Integer.parseInt(s));
            }
        }
        return list;
    }


    //从关联表查询并获取已储存的预案并保持执行
    public void keepPlanFromLinkage(DeviceFogInduct d) {
        //获取主机当前设置预案
        OutfieldLinkage linkage = new OutfieldLinkage();
        linkage.setDeviceId(d.getDeviceId());
        linkage.setType(1);
        String planContentStr = null;
        OutfieldLinkage result = deviceFogInductMapper.queryLinkageContent(linkage);
        if(result!=null)
        {
            planContentStr = result.getContent();
        }
        //转换为实体
        List<FogInductPlanContent> contentList = new Gson().fromJson(planContentStr,new TypeToken<List<FogInductPlanContent>>(){}.getType());
        excutePlan(d,contentList); //执行预案
    }

    //执行预案
    public void excutePlan(DeviceFogInduct d,List<FogInductPlanContent> contentList) {
        FogInductStatusDTO result = new FogInductStatusDTO();
        result.setDeviceId(d.getDeviceId());
        result.setStatus(0);
        result.setTime(DetectorUtils.getNowTime());
        if (contentList==null||contentList.size()==0) {
            return;
        }
        //依次判断预案条件是否满足
        for (FogInductPlanContent content:contentList) {
            //获取此时时间
            Calendar cal = Calendar.getInstance();
            int hour = cal.get(Calendar.HOUR_OF_DAY);//小时
            String timeRange = content.getTime();
            List<Integer> timeList = timeRangeConvert(timeRange);
            //无时间条件或满足时间条件，继续判断能见度条件
            if(timeList.size()==0||timeList.contains(hour))
            {
                //获取能见度条件
                String visiThr = content.getVisibility();
                List<Integer> visiList = visibilityRangeConvert(visiThr);
                //如果有能见度条件
                if(visiList.size()>0) {
                    //获取关联能见度设备
                    String visiId = d.getVisibilityId();
                    //如果不为空则查关联表，如果为空则认为不满足条件，跳过
                    if(visiId==null)
                    {
                        continue;
                    }
                    OutfieldLinkage visiLink = new OutfieldLinkage();
                    visiLink.setDeviceId(visiId);
                    visiLink.setType(1);
                    String visi = null;
                    OutfieldLinkage visiResult = deviceFogInductMapper.queryLinkageContent(visiLink);
                    if(visiResult!=null)
                    {
                        visi = visiResult.getContent();
                    }
                    //如果能查到
                    if(visi!=null)
                    {
                        Integer visiInt = Integer.parseInt(visi);
                        if(!(visiList.size()==1&&visiInt>visiList.get(0))&&!(visiList.size()==2&&visiInt<visiList.get(1)&&visiInt>=visiList.get(0)))
                        {
                            //如果都不满足能见度阈值条件
                            continue;
                        }
                    }else
                    {
                        //如果查不到，跳过
                        continue;
                    }
                }

                //如果没有能见度条件,或者满足能见度条件，则直接执行下面的预案
                modifyWorkMode(d,content);
            }
        }

    }

   //畅电根据回文进行数据处理
    private void dataProcess(String ip, int port, String msg) {
        logger.info("[畅电][" + ip + "]数据处理开始:" + msg);
        //判别信息类型
        msg = msg.toUpperCase();
        String type = msg.substring(0,2); //提取帧头A1
        //提取长度
      //  int len = Integer.parseInt(msg.substring(12,14),16);
        DeviceFogInduct f = new DeviceFogInduct();
        f.setIpAddress(ip);
        f.setPort(port);
        FogInductStatusDTO status = new FogInductStatusDTO();
        String deviceId = ipAndPortToDeviceIDMap.get(f.getIpAddress() + "[" + f.getPort() + "]");
        status.setDeviceId(deviceId);
        status.setTime(DetectorUtils.getNowTime());
        logger.info("[畅电][" + ip + "]type:" + type);
        //根据回文
        switch (type) {
            case "A1":
                //畅电****，是指令发送回应包，返回工作模式、频率、亮度、占空比
                CdFogPacket.getStatusEntityFromResponse(msg, status); //返回结果解析
                logger.info("[畅电]收到主机[" + ip + "]返回的总状态检测响应:" + msg);
                status.setStatus(1);
                //推送到外网
                if(status.getWorkMode()!=null) {
                    DetectorDBQueueUtils.offerFog(status);
                }
                break;
            default:
                logger.info("[畅电]收到主机[" + ip + "]返回的其它响应:" + msg);
                break;
        }

    }

    /*
    private class TCPConnection {
        ChannelHandlerContext ctx;
        Channel channel;
        String ip;
        int port;
        String deviceID;
        int connectCount = 0;
        boolean isConnected = false;
        int status = 0;//0-未连接 1-已连接 -1-连接失败

        void doConnect() {
            //发起异步连接操作
            try {
                ChannelFuture channelFuture = bootstrap.connect(new InetSocketAddress(ip, port)); //发起异步连接操作
                //添加ChannelFutur监听器,监听客户端连接服务端的结果反馈，异步非阻塞式断开
                channelFuture.addListener(new ChannelFutureListener() {
                    @Override
                    public void operationComplete(ChannelFuture future) throws Exception { //NioEventLoop线程帮你调用的，也即处理connect()方法的线程
                        //连接不成功
                        if (!future.isSuccess()) {
                            connectCount++;
                            logger.debug("[畅电]到" + ip + "[" + port + "]连接超时次数:" + connectCount + " " + future.cause());
                            //超时次数不大于最大次数时进行重连
                            if (connectCount < CONNECT_COUNT_MAX) {
                                future.channel().eventLoop().schedule(new Runnable() {
                                    @Override
                                    public void run() {
                                        doConnect();
                                    }
                                }, TIME_OUT_CONNECT, TimeUnit.SECONDS);

                                return;
                            } else {
                                //达到最大超时次数，关闭连接
                                logger.info("[畅电]已达到" + ip + "[" + port + "]的最大连接次数，将断开连接");
                                future.channel().close();

                                //更新设备在线状态为离线
                                logger.info("[畅电]更新设备" + ip + "[" + port + "]的状态为离线");
                                connectCount = 0;
                                isConnected = false;
                                status = -1;
                                FogInductStatusDTO status = new FogInductStatusDTO();
                                status.setDeviceId(deviceID);
                                status.setStatus(0);
                                status.setTime(DetectorUtils.getNowTime());
                                deviceFogInductMapper.updateStatus(status);
                            }
                        } else {
                            channel = future.channel();
                            //连接成功最大次数清0
                            connectCount = 0;
                            isConnected = true;
                            status = 1;
                        }
                        connectMap.put(deviceID, CdFogService.TCPConnection.this);
                    }
                });
            } catch (Exception e) {
                logger.warn("[畅电]抛出异常:" + e.getMessage());
            }
        }
    }
    */

//实现UDP连接
    private class UDPConnection {
        ChannelHandlerContext ctx;
        Channel channel;
        String ip;
        int port;
        String deviceID;
        int connectCount = 0;
        boolean isConnected = false;
        int status = 0;//0-未连接 1-已连接 -1-连接失败

        void doConnect() {
            //发起异步连接操作
            try {
                ChannelFuture channelFuture = bootstrap.connect(new InetSocketAddress(ip, port)); //发起异步连接操作
                //添加ChannelFutur监听器,监听服务端连接客户端的结果反馈，异步非阻塞式断开
                // 当客户端与服务器连接成功后，EventLoop会自动调用ChannelFutureListener对象中的operationComplete()方法。
                channelFuture.addListener(new ChannelFutureListener() {
                    @Override
                    public void operationComplete(ChannelFuture future) throws Exception { //NioEventLoop线程帮你调用的，也即处理connect()方法的线程
                        //连接不成功
                        if (!future.isSuccess()) {
                            connectCount++;
                            logger.debug("[畅电]到" + ip + "[" + port + "]连接超时次数:" + connectCount + " " + future.cause());
                            //超时次数不大于最大次数时进行重连
                            if (connectCount < CONNECT_COUNT_MAX) {
                                future.channel().eventLoop().schedule(new Runnable() {
                                    @Override
                                    public void run() {
                                        doConnect();
                                    }
                                }, TIME_OUT_CONNECT, TimeUnit.SECONDS);

                                return;
                            } else {
                                //达到最大超时次数，关闭连接
                                logger.info("[畅电]已达到" + ip + "[" + port + "]的最大连接次数，将断开连接");
                                future.channel().close();

                                //更新设备在线状态为离线
                                logger.info("[畅电]更新设备" + ip + "[" + port + "]的状态为离线");
                                connectCount = 0;
                                isConnected = false;
                                status = -1;
                                FogInductStatusDTO status = new FogInductStatusDTO();
                                status.setDeviceId(deviceID);
                                status.setStatus(0);
                                status.setTime(DetectorUtils.getNowTime());
                                deviceFogInductMapper.updateStatus(status);
                            }
                        } else { //连接成功
                            logger.info("[畅电畅电UDPConnection]:"+ ip + "[\" "+ port +" \"]连接成功！");
                            channel = future.channel();  //获取channel，发送数据
                            //连接成功最大次数清0
                            connectCount = 0;
                            isConnected = true;
                            status = 1;
                        }
                        UDPconnectMap.put(deviceID, CdFogService.UDPConnection.this); //connetMap2
                    }
                });
            } catch (Exception e) {
                logger.warn("[畅电]抛出异常:" + e.getMessage());
            }
        }
    }

/*
    //用于接收，处理入站数据
    private class ClientHandler extends ChannelInboundHandlerAdapter { //extends SimpleChannelInboundHandler<DatagramPacket>
        private int retryCount = 0;
        private ChannelHandlerContext ctx;
        private ChannelPromise channelPromise = null;

        //每个信息入站都会调用，当服务器返回应答消息时被调用
        @Override
        public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception { //DatagramPacket msg
            InetSocketAddress clientInetSocketAddress = (InetSocketAddress) ctx.channel().remoteAddress();
            String deviceIP = clientInetSocketAddress.getAddress().getHostAddress();
            int port = clientInetSocketAddress.getPort();
            CdFogPacket packet = (CdFogPacket) msg; //msg转为畅电的包
            String hex = packet.getFrame();
            logger.info("[畅电]接收到了设备" + deviceIP + "[" + port + "]的信息:" + hex);
            //处理数据
            dataProcess(deviceIP, port, hex);


        }

        //通知处理器最后的channelread()是当前批处理中的最后一条消息时调用,flux后全部写入到SocketChannel中
        @Override
        public void channelReadComplete(ChannelHandlerContext ctx) throws Exception {
            ctx.flush();
        }

        //与服务器建立连接
        @Override
        public void channelActive(ChannelHandlerContext ctx) throws Exception {
            this.ctx = ctx;
            InetSocketAddress inetSocketAddress = (InetSocketAddress) ctx.channel().remoteAddress();
            String deviceIP = inetSocketAddress.getAddress().getHostAddress();
            int port = inetSocketAddress.getPort();
            logger.info("[畅电]已连接到设备" + deviceIP + "[" + port + "]");
            UDPConnection connection = new UDPConnection(); //UDP连接
            connection.ctx = ctx;
            connection.ip = deviceIP;
            String deviceID = ipAndPortToDeviceIDMap.get(deviceIP + "[" + port + "]");

            if (deviceID != null) {
                UDPConnection c = UDPconnectMap.get(deviceID);
                c.isConnected = true;
                UDPconnectMap.put(deviceID, c);
            }
        }


        //与服务器断开连接
        @Override
        public void channelInactive(ChannelHandlerContext ctx) throws Exception {
            InetSocketAddress inetSocketAddress = (InetSocketAddress) ctx.channel().remoteAddress();
            String deviceIP = inetSocketAddress.getAddress().getHostAddress();
            int port = inetSocketAddress.getPort();
            logger.info("[畅电]关闭" + deviceIP + "[" + port + "]的控制器");
            ctx.channel().writeAndFlush(CdFogPacket.closeController());
            logger.info("[畅电]已断开到设备" + deviceIP + "[" + port + "]的连接");
            String deviceID = ipAndPortToDeviceIDMap.get(deviceIP + "[" + port + "]");

            if (deviceID != null) {
                UDPConnection c = UDPconnectMap.get(deviceID);
                c.isConnected = false;
                UDPconnectMap.put(deviceID, c);
            }
        }

        //读超时处理
        @Override
        public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
            InetSocketAddress inetSocketAddress = (InetSocketAddress) ctx.channel().remoteAddress();
            String deviceIP = inetSocketAddress.getAddress().getHostAddress();
            int port = inetSocketAddress.getPort();
            logger.info("[畅电]超时未收到" + deviceIP + "[" + port + "]的数据，将关闭连接");
            super.userEventTriggered(ctx, evt);
            ctx.channel().close();
            //super.userEventTriggered(ctx,evt);
        }

        //异常
        @Override
        public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
            InetSocketAddress inetSocketAddress = (InetSocketAddress) ctx.channel().remoteAddress();
            String deviceIP = inetSocketAddress.getAddress().getHostAddress();
            int port = inetSocketAddress.getPort();
            //关闭管道
            ctx.channel().close();
            String deviceID = ipAndPortToDeviceIDMap.get(deviceIP + "[" + port + "]");
            if (deviceID != null) {
                UDPConnection c = UDPconnectMap.get(deviceID);
                c.isConnected = false;
                UDPconnectMap.put(deviceID, c);
            }
            //打印异常信息
            logger.warn("[畅电]到设备" + deviceIP + "[" + port + "]的连接抛出异常，关闭管道，原因:" + cause.getMessage());
        }

    }
*/

    private class UDPServerHandler extends SimpleChannelInboundHandler<DatagramPacket> { //UDP服务端Handle
        //在客户端的业务Handler继承的是SimpleChannelInboundHandler，而在服务器端继承的是ChannelInboundHandlerAdapter。
        private int retryCount = 0;
        private ChannelHandlerContext ctx;
        private ChannelPromise channelPromise = null;
/*
 protected void channelRead0(ChannelHandlerContext ctx, DatagramPacket packet) {
        System.out.println("客户端接收到消息：" + packet.content().toString(StandardCharsets.UTF_8));
        // 向服务端回复消息
        ByteBuf byteBuf = Unpooled.copiedBuffer("已经接收到消息!".getBytes(StandardCharsets.UTF_8));
        ctx.writeAndFlush(new DatagramPacket(byteBuf, packet.sender()));
    }
 */


//每个信息入站都会调用
@Override
public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
    logger.info("channelread()方法被执行");
    logger.info("[畅电畅电]channelRead开始读取回传的状态信息"+msg.toString());
    if (msg instanceof io.netty.channel.socket.DatagramPacket) {
        DatagramPacket p=(DatagramPacket)msg;
        //获取发送端的 IP 地址
        String sendIP = p.getAddress().getHostAddress();
        //获取发送端的端口号
        int sendPort = p.getPort();
        byte[] rePacket = p.getData();
        String recv0 =bytesToHexString(rePacket); //byte[]转为hex
        logger.info("Received message: [" + recv0 + "]from[" + sendIP + ":" + sendPort + "]");
    } else {
        // 处理其他类型的消息
        logger.warn("Received an unexpected message type: " + msg.getClass().getName());
    }
}

        @Override
        protected void channelRead0(ChannelHandlerContext ctx, DatagramPacket msg) throws Exception { //**必要，传入Datagrampacket??
            logger.info("[畅电畅电]channelRead0开始读取回传的状态信息");
            String deviceIP = msg.getAddress().getHostAddress();
            int port = msg.getPort();
            byte[] rePacket = msg.getData();
            rePacket = Arrays.copyOfRange(rePacket,0,msg.getLength()); //获取被更新的部分
            String  hex = bytesToHexString(rePacket); //byte[]转为hex
            //CdFogPacket packet = (CdFogPacket) msg; //msg转为畅电的包
            //String hex = packet.getFrame();
            logger.info("[畅电]接收到了设备" + deviceIP + "[" + port + "]的信息:" + hex);
            //处理数据
            dataProcess(deviceIP, port, hex);
        }


        //通知处理器最后的channelread()是当前批处理中的最后一条消息时调用,flux后全部写入到SocketChannel中
        @Override
        public void channelReadComplete(ChannelHandlerContext ctx) throws Exception {
            ctx.flush();
        }

        //与服务器建立连接
        @Override
        public void channelActive(ChannelHandlerContext ctx) throws Exception {
            logger.info("[畅电]开启与客户端建立连接");
            this.ctx = ctx;
            InetSocketAddress inetSocketAddress = (InetSocketAddress) ctx.channel().remoteAddress();
            String deviceIP = inetSocketAddress.getAddress().getHostAddress();
            int port = inetSocketAddress.getPort();
            UDPConnection connection = new UDPConnection(); //UDP连接
            connection.ctx = ctx;
            connection.ip = deviceIP;
            String deviceID = ipAndPortToDeviceIDMap.get(deviceIP + "[" + port + "]");

            if (deviceID != null) {
                UDPConnection c = UDPconnectMap.get(deviceID);
                c.isConnected = true;
                UDPconnectMap.put(deviceID, c);
            }
            logger.info("[畅电]已连接到设备" + deviceIP + "[" + port + "]");
        }

        //与服务器断开连接
        @Override
        public void channelInactive(ChannelHandlerContext ctx) throws Exception {
            InetSocketAddress inetSocketAddress = (InetSocketAddress) ctx.channel().remoteAddress();
            String deviceIP = inetSocketAddress.getAddress().getHostAddress();
            int port = inetSocketAddress.getPort();
            //写出端口和广播掩码地址
            /*
            channelFuture.channel().writeAndFlush(new DatagramPacket(Unpooled.copiedBuffer("你好啊", CharsetUtil.UTF_8),
                    new InetSocketAddress("***************",1111))).sync();
            */
            /*
             InetSocketAddress address = new InetSocketAddress("localhost", 8088);
            ByteBuf byteBuf = Unpooled.copiedBuffer("你好".getBytes(StandardCharsets.UTF_8));
            channel.writeAndFlush(new DatagramPacket(byteBuf, address)).sync();
             */
         //   String closecmd = CdFogPacket.closeController();
         //   byte[] reqMsgByte = closecmd.getBytes("UTF-8");
            //writeBytes：将指定的源数组的数据传输到缓冲区,调用ChannelHandlerContext的writeAndFlush 方法将消息发送给服务器
       //     ByteBuf reqByteBuf = Unpooled.copiedBuffer(reqMsgByte);
            //    ctx.channel().writeAndFlush(new DatagramPacket(reqMsgByte,reqMsgByte.length)); //输出缓冲*********????

            logger.info("[畅电]已断开到设备" + deviceIP + "[" + port + "]的连接");
            String deviceID = ipAndPortToDeviceIDMap.get(deviceIP + "[" + port + "]");

            if (deviceID != null) {
                UDPConnection c = UDPconnectMap.get(deviceID);
                c.isConnected = false;
                UDPconnectMap.put(deviceID, c);
            }
        }

        //读超时处理
        @Override
        public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
            InetSocketAddress inetSocketAddress = (InetSocketAddress) ctx.channel().remoteAddress();
            String deviceIP = inetSocketAddress.getAddress().getHostAddress();
            int port = inetSocketAddress.getPort();
            logger.info("[畅电]超时未收到" + deviceIP + "[" + port + "]的数据，将关闭连接");
            super.userEventTriggered(ctx, evt);
            ctx.channel().close();
            //super.userEventTriggered(ctx,evt);
        }

        //异常
        @Override
        public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
            InetSocketAddress inetSocketAddress = (InetSocketAddress) ctx.channel().remoteAddress();
            String deviceIP = inetSocketAddress.getAddress().getHostAddress();
            int port = inetSocketAddress.getPort();
            //关闭管道
            ctx.channel().close();
            String deviceID = ipAndPortToDeviceIDMap.get(deviceIP + "[" + port + "]");
            if (deviceID != null) {
                UDPConnection c = UDPconnectMap.get(deviceID);
                c.isConnected = false;
                UDPconnectMap.put(deviceID, c);
            }
            //打印异常信息
            logger.warn("[畅电]到设备" + deviceIP + "[" + port + "]的连接抛出异常，关闭管道，原因:" + cause.getMessage());
            cause.printStackTrace();
        }

    }

    /**
     * 将byte[]数组转换为16进制字符串
     *
     * @param data byte[] data
     * @return hex string
     */
    public static String bytesToHexString(byte[] data) {
        StringBuilder hexString = new StringBuilder();
        if (data == null || data.length <= 0) {
            return null;
        }
        for (int i = 0; i < data.length; i++) {
            int d = data[i] & 0xFF;
            String hex = Integer.toHexString(d);
            if (hex.length() < 2) {
                hexString.append(0);
            }
            hexString.append(hex);
        }
        return hexString.toString().toUpperCase();
    }

    public  int ligthcdTolightPWM(int lightcd)
    {
        //亮度cd值转为PWM
        int lightPWM=4750; //默认设置为4700,1500cd
        switch (lightcd){
            case 500:
                lightPWM=1400;
                break;
            case 1000:
                lightPWM=2900;
                break;
            case 1500:
                lightPWM=4750;
                break;
            case 2500:
                lightPWM=8200;
                break;
            case 3500:
                lightPWM=11500;
                break;
            case 4500:
                lightPWM=15200;
                break;
            case 5700:
                lightPWM=21000;
                break;
            case 7000:
                lightPWM=28000;
                break;
        }
        return lightPWM;
    }

    //预案工作模式转换为畅电工作模式
    public  int  workModeMatch(int originWorkMode) {
        //亮度cd值转为PWM
        int result=2; //默认设置为黄闪
        switch (originWorkMode){
            case 8: //黄闪
                result=2;
                break;
            case 5: //雾灯关闭
                result=0;
                break;
            case 6: //黄灯常亮
                result=1;
                break;
            case 9: //红灯闪烁
                result=7;
                break;
        }
        return result;
    }


}
