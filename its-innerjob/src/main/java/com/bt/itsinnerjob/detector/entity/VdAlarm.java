package com.bt.itsinnerjob.detector.entity;

public class VdAlarm {
    private String device_id;
    private String device_name;//设备名称
    private String time;//报警时间
    private int alarm_type;//报警类别：1逆行报警，2占有率报警，3速度过快报警，4速度过慢报警
    private String alarm_title;//报警标题
    private int alarm_lane;//报警车道(1-上行，2-下行)
    private String details;//报警详情
    private double alarm_value;//触发报警的值
    private int alarm_level;//报警级别(严重程度 1-黄色 2-橙色 3-红色)
    private int check_status;//确认标志  0:未确认 1、确认  2:误报',
    private String facility_no;//所属设施id
    private String facility_name;//所属设施
    private String direction_name;//方向
    private String mile_post;//桩号

    public String getDevice_id() {
        return device_id;
    }

    public void setDevice_id(String device_id) {
        this.device_id = device_id;
    }

    public String getDevice_name() {
        return device_name;
    }

    public void setDevice_name(String device_name) {
        this.device_name = device_name;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public Integer getAlarm_type() {
        return alarm_type;
    }

    public void setAlarm_type(Integer alarm_type) {
        this.alarm_type = alarm_type;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public int getCheck_status() {
        return check_status;
    }

    public void setCheck_status(int check_status) {
        this.check_status = check_status;
    }

    public String getFacility_name() {
        return facility_name;
    }

    public void setFacility_name(String facility_name) {
        this.facility_name = facility_name;
    }

    public String getDirection_name() {
        return direction_name;
    }

    public void setDirection_name(String direction_name) {
        this.direction_name = direction_name;
    }

    public String getMile_post() {
        return mile_post;
    }

    public void setMile_post(String mile_post) {
        this.mile_post = mile_post;
    }

    public void setAlarm_type(int alarm_type) {
        this.alarm_type = alarm_type;
    }

    public int getAlarm_lane() {
        return alarm_lane;
    }

    public void setAlarm_lane(int alarm_lane) {
        this.alarm_lane = alarm_lane;
    }

    public String getFacility_no() {
        return facility_no;
    }

    public void setFacility_no(String facility_no) {
        this.facility_no = facility_no;
    }

    public String getAlarm_title() {
        return alarm_title;
    }

    public void setAlarm_title(String alarm_title) {
        this.alarm_title = alarm_title;
    }

    public double getAlarm_value() {
        return alarm_value;
    }

    public void setAlarm_value(double alarm_value) {
        this.alarm_value = alarm_value;
    }

    public int getAlarm_level() {
        return alarm_level;
    }

    public void setAlarm_level(int alarm_level) {
        this.alarm_level = alarm_level;
    }
}
