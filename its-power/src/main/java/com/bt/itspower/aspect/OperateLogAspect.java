package com.bt.itspower.aspect;

import com.bt.itscore.domain.dto.DevicePowerDTO;
import com.bt.itscore.domain.dto.FacilityCommonDTO;
import com.bt.itscore.domain.dto.LogOperatePowerDTO;
import com.bt.itscore.domain.vo.DevicePowerVO;
import com.bt.itscore.domain.vo.FacilityVO;
import com.bt.itscore.enums.LogModuleEnum;
import com.bt.itscore.utils.HttpClientUtils;
import com.bt.itscore.utils.IpAddressUtils;
import com.bt.itscore.utils.ServiceUtils;
import com.bt.itspower.constants.PowerMonitorEnums;
import com.bt.itspower.domain.dto.SwitchControlDTO;
import com.bt.itspower.domain.vo.PMControlRespVO;
import com.bt.itspower.domain.vo.SwitchControlVO;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 电力监控操作日志记录类
 * @Author: QinShiheng
 * @Date: 2022年11月08日10:32
 * @Description:
 **/
@Component
@Aspect
public class OperateLogAspect {
    private final static Logger LOGGER = LoggerFactory.getLogger(OperateLogAspect.class);
    private static final String SWITCH_CONTROL = "开关控制";
    private static final String SUCCESS = "成功";
    private static final String FAIL = "失败";
    private static final String SWITCH_CONTROL_OK = "RESPONSE_OK";

    @Value("${yk.domain:https://yktest.gxits.cn:8763/s}")
    private String ykDomain;

    @Pointcut("execution(* com.bt.itspower.controller.PowerMonitorController.switchControl(..))")
    public void PowerSwitchPointCut() {
    }

    @Around("PowerSwitchPointCut()")
    public Object switchControl(ProceedingJoinPoint proceedingJoinPoint) {
        String targetName = proceedingJoinPoint.getTarget().getClass().getName();
        String signatureName = proceedingJoinPoint.getSignature().getName() + "()";
//		String filterMethod = "#add()#update()#delete()#";

        return addLog(LogModuleEnum.TYPE_6, targetName, signatureName, proceedingJoinPoint);
    }


    public Object addLog(LogModuleEnum logModuleEnum, String targetName, String signatureName, ProceedingJoinPoint proceedingJoinPoint) {
        LogOperatePowerDTO logDTO = new LogOperatePowerDTO();
        SwitchControlDTO paramDTO = null;
        SwitchControlVO res = new SwitchControlVO();

        try {
            Object[] args = proceedingJoinPoint.getArgs();
            for (Object arg : args) {
                if (arg instanceof SwitchControlDTO) {
                    paramDTO = (SwitchControlDTO) arg;
                }
            }
            if (null == paramDTO) {
                LOGGER.warn("电力监控日志，入参获取失败！");
                return res;
            }

            Object result = proceedingJoinPoint.proceed();

            if (result instanceof SwitchControlVO) {
                res = (SwitchControlVO) result;
            }

            // 处理log_operate表数据
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            String account = (String) request.getAttribute("account");
            logDTO.setAccount(account);
            logDTO.setParams(new Gson().toJson(paramDTO));
            logDTO.setMethod(targetName + "." + signatureName);
            logDTO.setModule(logModuleEnum.getIndex());
            logDTO.setModuleName(logModuleEnum.getName());
            logDTO.setIpAddress(IpAddressUtils.getClientIp(request));
            LOGGER.info(IpAddressUtils.getClientIp(request));
            setRemark(logDTO, res); // 设置操作结果
            long currentTime = System.currentTimeMillis() / 1000;
            logDTO.setCreateTime(currentTime);
            String logJson = new Gson().toJson(logDTO);

            // 处理log_operate_power表数据
            String facilityNo = paramDTO.getFacilityNo();
            logDTO.setFacilityNo(facilityNo);
            // 查询设施信息
            FacilityCommonDTO fcDto = new FacilityCommonDTO();
            fcDto.setFacilityNo(facilityNo);
            // List<FacilityVO> facilityVOList = itsDeviceFeignClient.selectFacilityById(fcDto);
            Map<String, String> headParam = ServiceUtils.getInnerLoginHead();
            String facStr = HttpClientUtils.post(ykDomain + "/its-device/facility/selectFacilityById", headParam, new Gson().toJson(fcDto));
            List<FacilityVO> facilityVOList = new Gson().fromJson(facStr, new TypeToken<List<FacilityVO>>() {}.getType());

            if (null == facilityVOList || facilityVOList.size() == 0) {
                LOGGER.warn("根据facilityNo={}查询不到设施信息！", facilityNo);
                return res;
            } else if (facilityVOList.size() > 1) {
                LOGGER.warn("facilityNo={}查询到多个设施！", facilityNo);
                return res;
            }
            logDTO.setFacilityName(facilityVOList.get(0).getFacilityName());
            logDTO.setRoadNo(String.valueOf(facilityVOList.get(0).getRoadNo()));
            logDTO.setUpFacilityTypeNo(String.valueOf(facilityVOList.get(0).getUpFacilityTypeNo()));
            logDTO.setRoadName(facilityVOList.get(0).getRoadName());
            DevicePowerDTO dpDto = new DevicePowerDTO();
            dpDto.setDeviceId(paramDTO.getDeviceId());
            // List<DevicePowerVO> devicePowerVOList = itsDeviceFeignClient.selectByDeviceId(dpDto);
            String dpStr = HttpClientUtils.post(ykDomain + "/its-device/device/selectByDeviceId", headParam, new Gson().toJson(dpDto));
            List<DevicePowerVO> devicePowerVOList = new Gson().fromJson(dpStr, new TypeToken<List<DevicePowerVO>>() {}.getType());

            if (null == devicePowerVOList || devicePowerVOList.size() == 0) {
                LOGGER.warn("根据facilityNo={}查询不到设施信息！", facilityNo);
                return res;
            } else if (devicePowerVOList.size() > 1) {
                LOGGER.warn("facilityNo={}查询到多个设施！", facilityNo);
                return res;
            }
            logDTO.setPageType(devicePowerVOList.get(0).getPageTypeCode());
            logDTO.setPageTypeName(devicePowerVOList.get(0).getPageTypeName());
            logDTO.setDeviceName(devicePowerVOList.get(0).getDeviceName());
            String signalId = paramDTO.getSignalId();
            Integer value = paramDTO.getValue();
            String signal = signalId.substring(signalId.length()-4, signalId.length());
            String afterValueStr = PowerMonitorEnums.getDisplayName(signal, String.valueOf(value));
            String beforeValueStr = PowerMonitorEnums.getDisplayName(signal, String.valueOf(getReverseValue(value)));
            logDTO.setBeforeState(beforeValueStr);
            logDTO.setAfterState(afterValueStr);


            // 存储
            LOGGER.info("日志对象:{}", logJson);
            String addResult = HttpClientUtils.post(ykDomain + "/its-user/user/addOperateLogPower", headParam, new Gson().toJson(logDTO));
            LOGGER.info("操作日志入库:{},{}", addResult, logJson);

        } catch (Throwable e) {
            LOGGER.error(e.getMessage(), e);
        }
        return res;
    }

    private Integer getReverseValue(Integer value) {
        if (null == value) {
            return null;
        } else if (value == 0) {
            return 1;
        } else {
            return 0;
        }
    }

    private void setRemark(LogOperatePowerDTO logDTO, SwitchControlVO res) {
        logDTO.setRemark(logDTO.getModuleName() + SWITCH_CONTROL + FAIL);
        logDTO.setSuccess(0);
        if (res != null && StringUtils.isNotEmpty(res.getUuid())) {
            logDTO.setUuid(res.getUuid());
        }
    }

    public static void updateLog(String uuid, PMControlRespVO respVO, String ykDomain) {
        LOGGER.info("即将更新uuid={}的控制返回结果：{}", uuid, new Gson().toJson(respVO));
        if (StringUtils.isEmpty(uuid)) {
            return;
        }
        if (respVO != null && respVO.getRetValue()!=null && SWITCH_CONTROL_OK.equals(respVO.getRetValue())) {
            // 操作成功
            LogOperatePowerDTO logDTO = new LogOperatePowerDTO();
            logDTO.setUuid(uuid);
            logDTO.setSuccess(1);
            logDTO.setRemark(LogModuleEnum.TYPE_6.getName()+ SWITCH_CONTROL + SUCCESS);
            // 调用its-user方法更新
            Map<String, String> headParam = ServiceUtils.getInnerLoginHead();
            String addResult = HttpClientUtils.post(ykDomain + "/its-user/user/updateOperateLogPower", headParam, new Gson().toJson(logDTO));
            // itsUserFeignClient.updateOperateLogPower(logDTO);
        }

        // 写入时默认失败，仅控制成功时需要更新
    }
}
