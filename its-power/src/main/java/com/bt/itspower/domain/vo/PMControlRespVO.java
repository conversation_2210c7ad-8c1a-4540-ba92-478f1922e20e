package com.bt.itspower.domain.vo;

import java.io.Serializable;

/**
 * @Author: QinShiheng
 * @Date: 2022年10月25日15:24
 * @Description:
 **/
public class PMControlRespVO implements Serializable {
    private String mqCid; // 随机数，MQ消息唯一编号，不能有负数
    private Integer cmdType; // 指令码，暂时只开放 GET_BY_ID(4) GET_BY_LOAD(3) SET_CONTROL(5) PUSH_NOTIFY(13)
    private String destUnit; // 目标电力监控单元，即隧道/设施的唯一编码
    private String retValue; // 错误码。RESPONSE_OK为成功，其余值为错误码
    private String signalId;
    private Integer value;
    private String uuid;

    public String getMqCid() {
        return mqCid;
    }

    public void setMqCid(String mqCid) {
        this.mqCid = mqCid;
    }

    public Integer getCmdType() {
        return cmdType;
    }

    public void setCmdType(Integer cmdType) {
        this.cmdType = cmdType;
    }

    public String getDestUnit() {
        return destUnit;
    }

    public void setDestUnit(String destUnit) {
        this.destUnit = destUnit;
    }

    public String getRetValue() {
        return retValue;
    }

    public void setRetValue(String retValue) {
        this.retValue = retValue;
    }

    public String getSignalId() {
        return signalId;
    }

    public void setSignalId(String signalId) {
        this.signalId = signalId;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
}
