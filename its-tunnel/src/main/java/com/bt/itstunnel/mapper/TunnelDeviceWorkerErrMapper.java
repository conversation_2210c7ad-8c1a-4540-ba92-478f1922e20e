package com.bt.itstunnel.mapper;

import com.bt.itstunnel.domain.dto.TunnelDeviceWorkerErrDTO;
import com.bt.itstunnel.domain.vo.TunnelDeviceWorkerErrVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TunnelDeviceWorkerErrMapper {

	List<TunnelDeviceWorkerErrVO> selectList(TunnelDeviceWorkerErrDTO tunnelDeviceWorkerErrDTO);

	int add(TunnelDeviceWorkerErrDTO tunnelDeviceWorkerErrDTO);

	int update(TunnelDeviceWorkerErrDTO tunnelDeviceWorkerErrDTO);

	int batchModify(List<TunnelDeviceWorkerErrDTO> list);

	int batchUpdate(List<TunnelDeviceWorkerErrDTO> list);

	int batchAdd(List<TunnelDeviceWorkerErrDTO> list);
	
	List<String> selectUpdateDeviceId();

    List<String> selectCleanDeviceWorkerErr(@Param("lastCleanTime") Long lastCleanTime);

	int cleanDeviceWorkerErr(List<String> delList);

	void moveDeviceWorkerErr(@Param("startTime") Long startTime, @Param("endTime") Long endTime);
}
