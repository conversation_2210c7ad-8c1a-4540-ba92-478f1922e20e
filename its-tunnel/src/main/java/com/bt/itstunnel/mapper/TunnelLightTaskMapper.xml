<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itstunnel.mapper.TunnelLightTaskMapper">
    <resultMap type="com.bt.itstunnel.domain.vo.TunnelLightTaskVO" id="resultMap">
        <result column="id" property="id"/>
        <result column="road_no" property="roadNo"/>
        <result column="road_name" property="roadName"/>
        <result column="tunnel_id" property="tunnelId"/>
        <result column="tunnel_name" property="tunnelName"/>
        <result column="light_type_array" property="lightTypeArray"
                typeHandler="com.bt.itstunnel.common.ListToStringHandler"/>
        <result column="line_type" property="lineType"/>
        <result column="duration" property="duration"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="is_next_day" property="isNextDay"/>
        <result column="start_time_order" property="startTimeOrder"/>
        <result column="end_time_order" property="endTimeOrder"/>
        <result column="status" property="status"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_start_open" property="isStartOpen"/>
        <result column="is_end_close" property="isEndClose"/>
    </resultMap>

    <select id="selectLightTypeByFacilityAndLineAndType" resultType="java.lang.Integer">
        SELECT t1.light_type FROM tunnel_plcdeviceinfo t1
        LEFT JOIN device t2 ON t2.device_id=t1.device_id
        WHERE
        1=1
        <if test="facilityNo != null &amp;&amp; facilityNo != '' ">AND t2.facility_no=#{facilityNo}</if>
        <if test="deviceType != null &amp;&amp; deviceType != '' ">AND t1.type_id=#{deviceType}</if>
        <if test="lineType != null &amp;&amp; lineType != '' ">AND t1.line=#{lineType}</if>
        GROUP BY t1.light_type
    </select>

    <select id="selectTaskByFacilityNoAndLine" resultMap="resultMap">
        SELECT t1.id,t1.road_no,t1.facility_no AS tunnel_id,t1.light_type_array,t1.line_type,
               t1.duration,t1.start_time,t1.end_time,t1.is_next_day,t1.status,t1.create_time,t1.update_time,
               t2.facility_name AS tunnel_name, t3.road_name
        FROM tunnel_light_task t1
        LEFT JOIN facility t2 ON t1.facility_no=t2.facility_no
        LEFT JOIN road t3 ON t2.road_no=t3.road_no
        WHERE 1=1
        <if test="facilityNo != null &amp;&amp; facilityNo != '' ">AND t1.facility_no=#{facilityNo}</if>
        <if test="lineType != null &amp;&amp; lineType != '' ">AND t1.line_type=#{lineType}</if>
        <if test="status != null &amp;&amp; status != '' ">AND t1.status=#{status}</if>
        ORDER BY t1.create_time DESC
    </select>

    <insert id="insert" parameterType="com.bt.itstunnel.domain.dto.TunnelLightTaskDTO">
        insert into tunnel_light_task
        (id,
         road_no,
         facility_no,
         light_type_array,
         line_type,
         duration,
         start_time,
         end_time,
         is_next_day,
         start_time_order,
         end_time_order,
         status,
         creator,
         create_time,
         update_time)
        values (#{id},
                #{roadNo},
                #{facilityNo},
                #{lightTypeArray,typeHandler=com.bt.itstunnel.common.ListToStringHandler},
                #{lineType},
                #{duration},
                #{startTime},
                #{endTime},
                #{isNextDay},
                #{startTimeOrder},
                #{endTimeOrder},
                #{status},
                #{creator},
                #{createTime},
                #{updateTime})
    </insert>

    <select id="selectTaskById" parameterType="java.lang.String"
            resultType="com.bt.itstunnel.domain.vo.TunnelLightTaskVO">
        SELECT *
        FROM tunnel_light_task
        WHERE id = #{id}
    </select>

    <update id="update" parameterType="com.bt.itstunnel.domain.dto.TunnelLightTaskDTO">
        UPDATE tunnel_light_task
        SET status=#{status},
            creator=#{creator},
            update_time=#{updateTime},
            is_start_open=#{isStartOpen},
            is_end_close=#{isEndClose}
        where id=#{id}
    </update>

    <delete id="delete" parameterType="java.lang.String">
        DELETE
        FROM tunnel_light_task
        WHERE id = #{id}
    </delete>

    <select id="countTask" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT count(id)
        FROM tunnel_light_task
        WHERE status = #{status}
    </select>

    <select id="selectTaskByStatus" parameterType="java.util.HashMap" resultMap="resultMap">
        SELECT id,road_no,facility_no AS tunnel_id,light_type_array,line_type,
               duration,start_time,end_time,is_next_day,status,create_time,update_time,is_start_open,is_end_close
        FROM tunnel_light_task
        WHERE status = #{taskOpenStatus}
        LIMIT #{offSet}, #{size}
    </select>

    <select id="selectPlcIdsByFacilityNoAndLineAndType" parameterType="java.util.HashMap" resultType="java.lang.String">
        SELECT t1.id AS plcId
        FROM tunnel_plcdeviceinfo t1
        LEFT JOIN device t2 ON t2.device_id = t1.device_id
        WHERE
        1=1
        <if test="facilityNo != null &amp;&amp; facilityNo != '' ">AND t2.facility_no = #{facilityNo}</if>
        <if test="deviceType != null &amp;&amp; deviceType != '' ">AND t1.type_id = #{deviceType}</if>
        <if test="lineType != null &amp;&amp; lineType != '' ">AND t1.line = #{lineType}</if>
        <if test="lightTypeList != null &amp;&amp; lightTypeList.size>0 ">
            AND t1.light_type IN (
            <foreach collection="lightTypeList" item="item" index="index" separator=",">
                #{item}
            </foreach>
            )
        </if>
    </select>
</mapper>