<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itstunnel.mapper.TunnelSystemConfigMapper">
 <resultMap type="com.bt.itstunnel.domain.vo.TunnelSystemConfigVO" id="tunnelSystemConfigMap">
        <result property="id" column="id"/>
        <result column="code" property="code" ></result>
        <result column="param_type" property="paramType" ></result>
        <result column="param_name" property="paramName" ></result>
        <result column="param_value" property="paramValue" ></result>
        <result column="use_flag" property="useFlag" ></result>
        <result column="remark" property="remark" ></result>
    </resultMap>

    <select id="selectList" resultMap="tunnelSystemConfigMap" parameterType="com.bt.itstunnel.domain.dto.TunnelSystemConfigDTO">
    	select id,code,param_type,param_name,param_value,use_flag,remark
        from tunnel_system_config  where 1=1 and use_flag='1'	
		<if test="paramName != null &amp;&amp; paramName != '' "> AND param_name like CONCAT('%', #{paramName}, '%') </if>
	    <if test="paramValue != null &amp;&amp; paramValue != '' "> AND param_value like CONCAT('%', #{paramValue}, '%') </if>
		order by id
    </select>
    
    <select id="selectParamValue" resultType="java.lang.String" parameterType="java.lang.String">
    	select param_value  from tunnel_system_config  where param_value=#{paramValue} and use_flag='1' limit 1	 
    </select>

   <select id="list" resultMap="tunnelSystemConfigMap">
    	select id,code,param_type,param_name,param_value,use_flag,remark
        from tunnel_system_config  where 1=1 and use_flag='1' order by id	 
    </select>

    <insert id="add" parameterType="com.bt.itstunnel.domain.dto.TunnelSystemConfigDTO">
		insert into tunnel_system_config (id,code,param_type,param_name,param_value,use_flag,remark) 
		 values (#{id},#{code},#{paramType},#{paramName},#{paramValue},#{useFlag},#{remark})
	</insert>

    <update id="update" parameterType="com.bt.itstunnel.domain.dto.TunnelSystemConfigDTO">
		update tunnel_system_config set code=#{code},param_type=#{paramType},param_name=#{paramName},
		      param_value=#{paramValue},use_flag=#{useFlag},remark=#{remark}
		where id=#{id}
	</update>

    <delete id="delete" parameterType="com.bt.itstunnel.domain.dto.TunnelSystemConfigDTO">
		delete from tunnel_system_config where id = #{id}
	</delete>

   

</mapper>