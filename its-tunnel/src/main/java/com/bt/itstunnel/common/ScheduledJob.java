package com.bt.itstunnel.common;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.bt.itstunnel.domain.dto.TunnelMqMessageDTO;
import com.bt.itstunnel.service.DealMessageService;
import com.bt.itstunnel.service.EmergencyTelService;
import com.bt.itstunnel.service.RadarService;
import com.bt.itstunnel.service.TunnelDeviceWorkerErrService;
import com.bt.itstunnel.service.TunnelMqMessageService;

/**
 * 
 * @Description: 隧道定时处理任务公共类
 * @author: NingYiQiang
 * @date: 2022年1月10日 上午11:08:58
 */

@Component
public class ScheduledJob {
	public static List<TunnelMqMessageDTO> mqMessageQueue = new ArrayList<TunnelMqMessageDTO>(); // 接收MQ消息
	@Autowired
	TunnelDeviceWorkerErrService tunnelDeviceWorkerErrService;
	@Autowired
	DealMessageService dealMessageService;
	@Autowired
	TunnelMqMessageService tunnelMqMessageService;
	@Autowired
	EmergencyTelService emergencyTelService;
	@Autowired
	RadarService radarService;

	@Value("${tunnel.server.mqmessage:false}")
	private boolean insertDBFlag;

	@Scheduled(cron = "0 0/2 * * * ?") // 定时处理隧道故障信息
	public void schedulerDealDeviceWorkErrTask() {
		tunnelDeviceWorkerErrService.dealDeviceWorkErr();
	}

	@Scheduled(cron = "0 0/5 * * * ?")
	public void schedulerDealFanAlarm() {
		dealMessageService.checkFanAlarm(); // 定时检测风机
	}

	@Scheduled(cron = "0 0/10 * * * ?")
	public void schedulerDealDeviceStatus() {
		dealMessageService.checkDeviceStatus();// 定时检测情报板、摄像机、车检器状态进行更新,页面更新
		dealMessageService.checkOuntTimeState();// 定时设备超时
		Map<String, Long> webTunnelMap = CommonCacheAction.WEB_TUNNEL_MAP;
		if (!CollectionUtils.isEmpty(webTunnelMap)) {
			Long nowTime = System.currentTimeMillis() / 1000;
			for (String key : webTunnelMap.keySet()) {
				if (nowTime - webTunnelMap.get(key) > 1800) {// 半个小时
					webTunnelMap.remove(key);
				}
			}
		}
	}

	@Scheduled(cron = "0 0/3 * * * ?") // 定时处理MQ消息
	public void schedulerDealMqMessageTask() {
		if (!mqMessageQueue.isEmpty() && insertDBFlag) { // 插入MQ接收消息
			List<TunnelMqMessageDTO> dealList = new ArrayList<TunnelMqMessageDTO>();
			dealList.addAll(mqMessageQueue);
			mqMessageQueue.clear();
			tunnelMqMessageService.insertData(dealList);
		}
	}

	/**
	 * @描述 定时处理雷达轨迹
	 */
	@Async(value = "asyncServiceExecutor")
	@Scheduled(fixedRate = 90000, initialDelay = 3000)
	public void schedulerRadarTask() {
		radarService.addRadarTrack();
	}

	/**
	 * @描述 定时处理雷达车辆明细
	 * fixedRate：毫秒为单位
	 */
	@Async(value = "asyncServiceExecutor")
	@Scheduled(fixedRate = 30000, initialDelay = 2000)
	public void schedulerRadarCarListTask() {
		radarService.dealRadarCarList();
	}
	
	/**
	 * @描述 定时清理雷达轨迹，提取告警轨迹信息
	 */
	@Async(value = "asyncServiceExecutor")
	@Scheduled(fixedRate = 60000, initialDelay = 5000)
	public void schedulerAlarmRadarTrackTask() {
		radarService.dealAlarmRadarTrack();
	}

	/**
	 * @描述 定时删除时间超过6小时的轨迹信息
	 */
	@Async(value = "asyncServiceExecutor")
	@Scheduled(cron = "0 0/30 * * * ?")
	public void schedulerDeleteRadarTrackTask() {
		radarService.dealOverTimeRadarTrack();
	}


}
