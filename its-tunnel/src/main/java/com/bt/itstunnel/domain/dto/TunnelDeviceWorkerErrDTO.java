/**
 *
 */
package com.bt.itstunnel.domain.dto;

import java.util.List;

public class TunnelDeviceWorkerErrDTO {

    private String id;
    private String deviceId;//设备id
    private String errState;//故障代码
    private String errName;//故障名称
    private Long errTime;//故障发生时间
    private Long recoveryTime;//故障恢复时间
    private String isNomal;//恢复标志 0：未恢复,1：已经恢复
    private String roadNo;//路段ID
    private String roadName;// 路段名称
    private String facilityNo;// 隧道Id
    private String facilityName;// 隧道名称
    private String typeId;  //设备类型Id
    private String typeName;  //设备类型名称
    private String deviceName;  //设备名称
    private String milePost;//设备桩号
    //时间查询条件
    private List<String> roleIds; //用户具有的角色
    private List<String> queryTime;//查询时间
    private Long startTime;//开始时间
    private Long endTime;//结束时间

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getErrState() {
        return errState;
    }

    public void setErrState(String errState) {
        this.errState = errState;
    }

    public String getErrName() {
        return errName;
    }

    public void setErrName(String errName) {
        this.errName = errName;
    }

    public Long getErrTime() {
        return errTime;
    }

    public void setErrTime(Long errTime) {
        this.errTime = errTime;
    }

    public Long getRecoveryTime() {
        return recoveryTime;
    }

    public void setRecoveryTime(Long recoveryTime) {
        this.recoveryTime = recoveryTime;
    }

    public String getIsNomal() {
        return isNomal;
    }

    public void setIsNomal(String isNomal) {
        this.isNomal = isNomal;
    }

    public String getTypeId() {
        return typeId;
    }

    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getMilePost() {
        return milePost;
    }

    public void setMilePost(String milePost) {
        this.milePost = milePost;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getRoadName() {
        return roadName;
    }

    public void setRoadName(String roadName) {
        this.roadName = roadName;
    }

    public List<String> getQueryTime() {
        return queryTime;
    }

    public void setQueryTime(List<String> queryTime) {
        this.queryTime = queryTime;
    }

    public String getRoadNo() {
        return roadNo;
    }

    public void setRoadNo(String roadNo) {
        this.roadNo = roadNo;
    }

    public String getFacilityNo() {
        return facilityNo;
    }

    public void setFacilityNo(String facilityNo) {
        this.facilityNo = facilityNo;
    }

    public String getFacilityName() {
        return facilityName;
    }

    public void setFacilityName(String facilityName) {
        this.facilityName = facilityName;
    }

    public List<String> getRoleIds() {
        return roleIds;
    }

    public void setRoleIds(List<String> roleIds) {
        this.roleIds = roleIds;
    }


}
