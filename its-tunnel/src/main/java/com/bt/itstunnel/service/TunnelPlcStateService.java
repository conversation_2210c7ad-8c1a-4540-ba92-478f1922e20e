package com.bt.itstunnel.service;

import java.util.List;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bt.itscore.domain.dto.PageDTO;
import com.bt.itstunnel.domain.dto.TunnelPlcStateDTO;
import com.bt.itstunnel.domain.vo.TunnelPlcStateVO;
import com.bt.itstunnel.mapper.TunnelPlcStateMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

@Service("tunnelPlcStateService")
public class TunnelPlcStateService {
	@Autowired
	private TunnelPlcStateMapper tunnelPlcStateMapper;

	public PageInfo<TunnelPlcStateVO> page(TunnelPlcStateDTO tunnelPlcStateDTO, PageDTO pageDTO) {
		PageHelper.startPage(pageDTO.getPage(), pageDTO.getLimit());
		List<TunnelPlcStateVO> list = tunnelPlcStateMapper.selectList(tunnelPlcStateDTO);
		return new PageInfo<>(list);
	}

	public boolean add(TunnelPlcStateDTO tunnelPlcStateDTO) {
		tunnelPlcStateDTO.setId(UUID.randomUUID().toString());
		return tunnelPlcStateMapper.add(tunnelPlcStateDTO) > 0;
	}

	public boolean update(TunnelPlcStateDTO tunnelPlcStateDTO) {
		return tunnelPlcStateMapper.update(tunnelPlcStateDTO) > 0;
	}

	public boolean delete(TunnelPlcStateDTO tunnelPlcStateDTO) {
		return tunnelPlcStateMapper.delete(tunnelPlcStateDTO) > 0;
	}

	public boolean batchDelete(TunnelPlcStateDTO tunnelPlcStateDTO) {
		return tunnelPlcStateMapper.batchDelete(tunnelPlcStateDTO) > 0;
	}
	
	public List<TunnelPlcStateVO> list() {
		return tunnelPlcStateMapper.list();
	}

}
