package com.bt.itstunnel.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bt.itscore.domain.dto.PageDTO;
import com.bt.itscore.exception.ArgumentException;
import com.bt.itstunnel.common.CommonCacheAction;
import com.bt.itstunnel.domain.dto.CommonParamsDTO;
import com.bt.itstunnel.domain.dto.TunnelPreplanDTO;
import com.bt.itstunnel.domain.dto.TunnelPreplanDetailDTO;
import com.bt.itstunnel.domain.entity.TunnePlcBean;
import com.bt.itstunnel.domain.entity.TunnelResPoseBean;
import com.bt.itstunnel.domain.vo.FacilityTunnelVO;
import com.bt.itstunnel.domain.vo.TunnelDeviceTypeVO;
import com.bt.itstunnel.domain.vo.TunnelPlcDeviceInfoVO;
import com.bt.itstunnel.domain.vo.TunnelPreplanDetailVO;
import com.bt.itstunnel.domain.vo.TunnelPreplanVO;
import com.bt.itstunnel.mapper.TunnelDeviceTypeMapper;
import com.bt.itstunnel.mapper.TunnelPlcDeviceInfoMapper;
import com.bt.itstunnel.mapper.TunnelPreplanDetailMapper;
import com.bt.itstunnel.mapper.TunnelPreplanMapper;
import com.bt.itstunnel.utils.TimeUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

@Service("tunnelPreplanService")
public class TunnelPreplanService {
	@Autowired
	private TunnelPreplanMapper tunnelPreplanMapper;
	@Autowired
	private TunnelDeviceTypeMapper tunnelDeviceTypeMapper;
	@Autowired
	private TunnelPlcDeviceInfoMapper tunnelPlcDeviceInfoMapper;
	@Autowired
	TunnelPreplanDetailMapper tunnelPreplanDetailMapper;
	@Autowired
	FacilityTunnelService facilityTunnelService;
	@Autowired
	TunnelRecoveryService tunnelRecoveryService;
	@Autowired
	DeviceService deviceService;

	public PageInfo<TunnelPreplanVO> page(TunnelPreplanDTO tunnelPreplanDTO, PageDTO pageDTO) {
		PageHelper.startPage(pageDTO.getPage(), pageDTO.getLimit());
		List<TunnelPreplanVO> list = tunnelPreplanMapper.selectList(tunnelPreplanDTO);
		return new PageInfo<>(list);
	}

	public boolean add(TunnelPreplanDTO tunnelPreplanDTO, HttpServletRequest request) {
		boolean success = false;
		String operationList = tunnelPreplanDTO.getOperationList();
		String operater = "";
		String userId = (String) request.getAttribute("userId");
		if (userId != null) {
			operater = deviceService.getUserNameByUserId(userId);
		}
		tunnelPreplanDTO.setCreater(operater);
		String id = tunnelPreplanDTO.getId();// 当前预案Id
		if (StringUtils.isNotBlank(operationList)) {
			String planId = "";
			if (StringUtils.isBlank(id)) {
				tunnelPreplanDTO.setId(UUID.randomUUID().toString());
				tunnelPreplanDTO.setUseFlag("10");
				tunnelPreplanDTO.setUseNum(0);
				tunnelPreplanDTO
						.setCreateTime(TimeUtils.getTimeString("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis()));
				success = tunnelPreplanMapper.add(tunnelPreplanDTO) > 0;
				planId = tunnelPreplanMapper.getSiglnTunnelPreplanVO(tunnelPreplanDTO).getId();
			} else {
				planId = id;
				success = tunnelPreplanMapper.update(tunnelPreplanDTO) > 0;
				tunnelPreplanDetailMapper.deleteByPlanId(planId);// 删除已经存在的操作子表信息
			}
			if (success) {
				String[] temp = operationList.split(",");
				for (int i = 0; i < temp.length; i++) {
					String[] tm = temp[i].split(":");
					if (tm.length >= 2) {
						TunnelPreplanDetailDTO preplanDetai = new TunnelPreplanDetailDTO();
						preplanDetai.setId(UUID.randomUUID().toString());
						preplanDetai.setPlanId(planId);
						preplanDetai.setDeviceId(tm[0]);
						preplanDetai.setState(tm[1]);
						preplanDetai.setCmdCode("Modify");
						tunnelPreplanDetailMapper.add(preplanDetai);
					}
				}
			}
		}
		return success;
	}

	public boolean update(TunnelPreplanDTO tunnelPreplanDTO) {
		return tunnelPreplanMapper.update(tunnelPreplanDTO) > 0;
	}

	public boolean delete(TunnelPreplanDTO tunnelPreplanDTO) {
		if (tunnelPreplanDTO == null || tunnelPreplanDTO.getId() == null) {
			throw new ArgumentException("删除信息不能为空！");
		}
		boolean ret = tunnelPreplanMapper.delete(tunnelPreplanDTO) > 0;
		if (ret) {
			String preplanId = tunnelPreplanDTO.getId();
			tunnelPreplanDetailMapper.deleteByPlanId(preplanId);// 删除已经存在的操作子表信息
			tunnelRecoveryService.deleteTunnelRecoveryByPreplanId(preplanId);// 删除当前预案恢复信息
		}
		return ret;
	}

	public boolean batchDelete(TunnelPreplanDTO tunnelPreplanDTO) {
		return tunnelPreplanMapper.batchDelete(tunnelPreplanDTO) > 0;
	}

	public List<TunnelPreplanVO> list(TunnelPreplanDTO tunnelPreplanDTO) {
		return tunnelPreplanMapper.selectList(tunnelPreplanDTO);
	}

	public TunnelPreplanVO getSiglnTunnelPreplanVO(TunnelPreplanDTO tunnelPreplanDTO) {
		return tunnelPreplanMapper.getSiglnTunnelPreplanVO(tunnelPreplanDTO);
	}

	public TunnelPreplanVO getTunnelPreplanVO(String id) {
		return tunnelPreplanMapper.getTunnelPreplanVO(id);
	}

	public boolean checkPreplanExit(TunnelPreplanDTO tunnelPreplanDTO) {
		String id = tunnelPreplanDTO.getId();
		int count = tunnelPreplanMapper.checkPreplanExit(tunnelPreplanDTO);
		if (count == 0) {
			return true;
		} else {
			if (id == null) {
				return false;
			} else {
				return true;
			}
		}

	}

	public TunnePlcBean getOperateImg(CommonParamsDTO commonParamsDTO) {
		TunnePlcBean plcBean = new TunnePlcBean();
		String plcId = commonParamsDTO.getPlcId();
		String operationName = commonParamsDTO.getOperationName();
		if (StringUtils.isNotBlank(operationName) && StringUtils.isNotBlank(plcId)) {
			TunnelPlcDeviceInfoVO info = this.getTunnelPlcDeviceInfoVO(plcId);
			if (info != null) {
				String deviceState = operationName.split("-")[1];
				plcBean.setId(info.getId());
				plcBean.setFacilityNo(info.getFacilityNo());
				plcBean.setFacilityName(info.getFacilityName());
				plcBean.setDeviceCode(info.getCode());
				plcBean.setDeviceId(info.getDeviceId());
				plcBean.setRoadNo(info.getRoadNo());
				plcBean.setRoadName(info.getRoadName());
				plcBean.setTypeCode(info.getTypeCode());
				plcBean.setTypeId(info.getTypeId());
				plcBean.setDeviceName(info.getDeviceName());
				plcBean.setTypeName(info.getTypeName());
				plcBean.setMilePost(info.getMilePost());
				plcBean.setIpAddress(info.getIpAddress());
				plcBean.setPlcCode(info.getPlcCode());
				plcBean.setDirectionNo(info.getDirectionNo());
				plcBean.setDirectionName(info.getDirectionName());
				plcBean.setLightType(info.getLightType());// 根据调整参数设置显示
				plcBean.setLine(info.getLine());
				plcBean.setLaneType(info.getLaneType());
				plcBean.setxRatio(info.getxRatio());
				plcBean.setyRatio(info.getyRatio());
				plcBean.setImgArea(info.getImgArea());
				plcBean.setDeviceState(deviceState);
				String imgUrl = CommonCacheAction.findTunnelPlcStateByPlcIdState(info.getTypeId(), deviceState,
						info.getTypeCode(), info.getLine());
				plcBean.setImgUrl(imgUrl);

			} else {
				throw new ArgumentException("操作设备plcId不正确，找不到对应的设备，请核对！");
			}
		} else {
			throw new ArgumentException("提交参数不正确，请核对！");
		}
		return plcBean;
	}

	public List<TunnelResPoseBean> getTunnelPlcDeviceInfoByPreplanId(String tunnelId, String preplanId) {
		List<TunnelDeviceTypeVO> typeList = tunnelDeviceTypeMapper.selectPreplanList();
		List<String> idList = new ArrayList<String>();
		if (typeList.size() > 0) {
			for (TunnelDeviceTypeVO type : typeList) {
				idList.add(type.getId());
			}
		}
		CommonParamsDTO commonParamsDTO = new CommonParamsDTO();
		commonParamsDTO.setIds(idList);
		commonParamsDTO.setTunnelId(tunnelId);
		List<TunnelPlcDeviceInfoVO> list = tunnelPlcDeviceInfoMapper.selectPlcDeviceInfo(commonParamsDTO);
		List<TunnelResPoseBean> listJson = new ArrayList<TunnelResPoseBean>();
		List<TunnelPreplanDetailVO> listDetailVO = new ArrayList<TunnelPreplanDetailVO>();
		// 获取预案中设备状态
		if (preplanId != null) {
			listDetailVO = tunnelPreplanDetailMapper.selectTunnelPreplanDetail(preplanId);
		}
		if (list.size() > 0) {
			for (TunnelPlcDeviceInfoVO info : list) {
				TunnelResPoseBean tunnelResPoseBean = CommonCacheAction.getTunnelResPoseBeanProtity(info);
				if (listDetailVO.size() > 0) {
					for (TunnelPreplanDetailVO entry : listDetailVO) {
						String deviceId = entry.getDeviceId();
						String deviceState = entry.getState();
						if (info.getId().equals(deviceId)) {
							tunnelResPoseBean.setDeviceState(deviceState);
							tunnelResPoseBean.setImgUrl(CommonCacheAction.findTunnelPlcStateByPlcIdState(
									info.getTypeId(), deviceState, info.getTypeCode(), info.getLine()));
							tunnelResPoseBean.setOtherState(deviceState);
						}
					}
				}
				listJson.add(tunnelResPoseBean);
			}
		}
		return listJson;
	}

	public Map<Object, Object> getPreplan(CommonParamsDTO commonParamsDTO) {
		String tunnelId = commonParamsDTO.getTunnelId();
		String preplanId = commonParamsDTO.getPreplanId();
		if (StringUtils.isBlank(tunnelId)) {
			throw new ArgumentException("选择预案的隧道不能为空！");
		}
		Map<Object, Object> retuMap = new HashMap<Object, Object>();
		FacilityTunnelVO tunnelVO = facilityTunnelService.getFacilityTunnel(tunnelId);
		retuMap.put("tunnelId", tunnelId);
		retuMap.put("tunnelName", tunnelVO.getTunnelName());
		retuMap.put("roadId", tunnelVO.getRoadId());
		retuMap.put("tunnel_length", tunnelVO.getLength());
		if (StringUtils.isNotBlank(preplanId)) {
			retuMap.put("preplanId", preplanId);
		} else {
			TunnelPreplanDTO tunnelPreplanDTO = new TunnelPreplanDTO();
			tunnelPreplanDTO.setTunnelId(tunnelId);
			List<TunnelPreplanVO> list = tunnelPreplanMapper.selectList(tunnelPreplanDTO);
			if (list.size() > 0) {
				retuMap.put("preplanId", list.get(0).getId());
			} else {
				retuMap.put("preplanId", null);
			}
		}
		return retuMap;
	}

	public Map<Object, Object> preplanConfig(CommonParamsDTO commonParamsDTO) {
		Map<Object, Object> retuMap = new HashMap<Object, Object>();
		String tunnelId = commonParamsDTO.getTunnelId();
		String preplanId = commonParamsDTO.getPreplanId();
		// 获取当前隧道所有预案信息
		TunnelPreplanDTO tunnelPreplanDTO = new TunnelPreplanDTO();
		tunnelPreplanDTO.setTunnelId(tunnelId);
		List<TunnelPreplanVO> preplanList = tunnelPreplanMapper.selectList(tunnelPreplanDTO);
		retuMap.put("preplanList", preplanList);
		// 获取当前隧道信息
		FacilityTunnelVO tunnelVO = facilityTunnelService.getFacilityTunnel(tunnelId);
		retuMap.put("facilityTunnel", tunnelVO);
		List<TunnelResPoseBean> resPoseBeanList = getTunnelPlcDeviceInfoByPreplanId(tunnelId, preplanId);
		// 获取隧道设备信息
		retuMap.put("resPoseBeanList", resPoseBeanList);
		if (preplanId != null) {
			TunnelPreplanVO tunnelPreplan = tunnelPreplanMapper.getTunnelPreplanVO(preplanId);
			retuMap.put("tunnelPreplan", tunnelPreplan);
			List<TunnelPreplanDetailVO> listDetailVO = tunnelPreplanDetailMapper.selectTunnelPreplanDetail(preplanId);
			String operationList = "";
			if (listDetailVO.size() > 0) {
				for (TunnelPreplanDetailVO detail : listDetailVO) {
					operationList += detail.getDeviceId() + ":" + detail.getState() + ",";
				}
			}
			retuMap.put("operationList", operationList);
		}
		return retuMap;
	}

	public TunnelPlcDeviceInfoVO getTunnelPlcDeviceInfoVO(String id) {
		TunnelPlcDeviceInfoVO deviceInfoVO = null;
		List<TunnelPlcDeviceInfoVO> listVO = tunnelPlcDeviceInfoMapper.getPlcDeviceInfo(id);
		if (listVO.size() > 0) {
			deviceInfoVO = listVO.get(0);
		}
		return deviceInfoVO;
	}
}
