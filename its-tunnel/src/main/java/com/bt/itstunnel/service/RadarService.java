package com.bt.itstunnel.service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.bt.itscore.domain.dto.RadarTwinBean;
import com.bt.itscore.domain.vo.ResponseVO;
import com.bt.itscore.utils.Base64Utils;
import com.bt.itscore.utils.GsonUtils;
import com.bt.itscore.utils.HttpClientUtils;
import com.bt.itscore.utils.MilePostUtils;
import com.bt.itscore.utils.NumbersUtils;
import com.bt.itscore.utils.ServiceUtils;
import com.bt.itscore.utils.TimeUtils;
import com.bt.itstunnel.common.CommonFunction;
import com.bt.itstunnel.domain.dto.AlarmDTO;
import com.bt.itstunnel.domain.dto.AlarmRadarEventDTO;
import com.bt.itstunnel.domain.dto.RadarAlarmDTO;
import com.bt.itstunnel.domain.dto.RadarAlarmPointDTO;
import com.bt.itstunnel.domain.dto.RadarCarDTO;
import com.bt.itstunnel.domain.entity.Radar1Bean;
import com.bt.itstunnel.domain.entity.Radar2Bean;
import com.bt.itstunnel.domain.vo.FacilityRadarPointVO;
import com.bt.itstunnel.domain.vo.RadarAlarmPointVO;
import com.bt.itstunnel.domain.vo.RadarAlarmTrackVO;
import com.bt.itstunnel.domain.vo.RadarCarStatVO;
import com.bt.itstunnel.domain.vo.RadarCarTotalVO;
import com.bt.itstunnel.domain.vo.RadarDeviceVO;
import com.bt.itstunnel.domain.vo.RadarFuseDataVO;
import com.bt.itstunnel.domain.vo.RadarPointVO;
import com.bt.itstunnel.domain.vo.TunnelRadarVO;
import com.bt.itstunnel.mapper.RadarTwinMapper;
import com.bt.itstunnel.mapper.TunnelRadarMapper;
import com.bt.itstunnel.sharding.mapper.TunnelAlarmMapper;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

/**
 * <AUTHOR>
 * @date 2023年12月26日 上午11:58:47
 * @Description 雷达处理类
 */
@Service("radarService")
public class RadarService {
	private final static Logger LOGGER = LoggerFactory.getLogger(RadarService.class);
	/**
	 * 雷达事件解析开关
	 */
	@Value("${radar.alarm.switchits:false}")
	private boolean radarAlarmSwitchits;
	@Autowired
	TunnelRadarMapper tunnelRadarMapper;
	@Autowired
	WebSocketService webSocketService;
	@Autowired
	private RadarTwinMapper radarTwinMapper;
	@Autowired
	private TunnelAlarmMapper tunnelAlarmMapper;
	@SuppressWarnings("rawtypes")
	@Autowired
	private RedisTemplate redisTemplate;
	@Value("${yk.domain:https://yktest.gxits.cn:8763/s}")
	private String ykDomain;
	public static List<RadarPointVO> RADAR_POINT_LIST = new ArrayList<>();
	public static Map<String, Integer> RADAR_DEVICE_MAP = new HashMap<String, Integer>();// 雷达设备基础信息

	/**
	 * @描述 进行版本判断，轨迹接收
	 * @param message
	 * @param isPush
	 * @param parseEvent
	 */
	public void consume(String message, boolean isPush, boolean parseEvent) {
		List<RadarTwinBean> radarTwinList = new ArrayList<RadarTwinBean>();
		// 1、解析雷达数据
		boolean success = parse(message, radarTwinList, parseEvent);
		// boolean success = parseRadar(message, radarTwinList, parseEvent);
		// 2、推送消息到WEB前端
		if (isPush && success) {
			pushWebsocketMsg(radarTwinList);
		}
	}

	/**
	 * @描述 解析雷达数据
	 * 协议版本号 v1.0 信梧 钦北 来都 2.0 思防
	 */
	public void parseRadar(String message, boolean isPush, boolean parseEvent) {
		try {
			boolean isSzVersion = false;
			JsonObject dataPacket = GsonUtils.String2Object(message);
			String version = dataPacket.has("szProtoVersion") ? dataPacket.get("szProtoVersion").getAsString() : "";
			if (StringUtils.isNotBlank(version) && version.equals("v2.0")) {
				isSzVersion = true;
			}
			int tid = 0;
			int line = 0;
			long sTime = System.currentTimeMillis() / 1000;// 雷达服务启动时间
			double timeStamp = 0;
			JsonArray lstFuseData = new JsonArray();
			if (isSzVersion) { // 新版本2.0
				tid = dataPacket.get("nTid").getAsInt();
				line = dataPacket.get("nDirection").getAsInt();
				String time = dataPacket.get("szTime").getAsString();
				if (time.trim().length() > 19) {
					sTime = TimeUtils.getMsTimeStamp(time);
				} else {
					sTime = TimeUtils.getMsTimeStamp(time + " 000");
				}
				lstFuseData = dataPacket.has("lstFuseData") ? dataPacket.get("lstFuseData").getAsJsonArray() : null;
			} else {
				tid = dataPacket.get("tid").getAsInt();
				line = dataPacket.get("lineid").getAsInt();
				String time = dataPacket.get("time").getAsString();
				if (time.trim().length() > 19) {
					sTime = TimeUtils.getMsTimeStamp(time);
				} else {
					sTime = TimeUtils.getMsTimeStamp(time + " 000");
				}
				lstFuseData = dataPacket.has("fuse_data") ? dataPacket.get("fuse_data").getAsJsonArray() : null;
				timeStamp = dataPacket.get("timeStamp").getAsDouble();
			}
			RadarFuseDataVO fuseDataVO = getRadarFuseData(tid, line);// 通过对应关系查询facilityNo(隧道设施编号)
			if (fuseDataVO == null) {
				return;
			}
			if (lstFuseData.isJsonNull() || lstFuseData.size() == 0) {
				return;
			}
			Integer statFlag = fuseDataVO.getStatFlag();// 流量统计标志0-不存储，1-存储
			boolean tidFlag = RadarTwinService.tids.contains(tid);
			if (!parseEvent && !tidFlag && statFlag == 0) {
				return;
			}
			List<RadarTwinBean> radarTwinList = this.parseRadarVersion(lstFuseData, isSzVersion, line, sTime,
					fuseDataVO, timeStamp, parseEvent, tidFlag);
			if (statFlag == 1) { // 存储雷达轨迹和车辆统计明细
				this.saveRadarTrack(radarTwinList);
			}
			if (tidFlag && isPush) { // 有人登录才推送页面
				pushWebsocketMsg(radarTwinList);
			}
		} catch (Exception e) {
			e.printStackTrace();
			LOGGER.error("雷达数据parse出错{}:{}", message, e.getMessage());
		}

	}

	private List<RadarTwinBean> parseRadarVersion(JsonArray lstFuseData, boolean isSzVersion, int line, long sTime,
			RadarFuseDataVO fuseDataVO, double timeStamp, boolean parseEvent, boolean tidFlag) {
		List<RadarTwinBean> radarTwinList = new ArrayList<RadarTwinBean>();
		for (Object object : lstFuseData) {
			RadarTwinBean bean = new RadarTwinBean();
			if (isSzVersion) { // 新版本2.0
				Radar2Bean radar2Bean = GsonUtils.jsonToBean(object.toString(), Radar2Bean.class);
				bean.setFuseDataId(radar2Bean.getSzDeviceID());
				bean.setLine(line);
				bean.setFacilityNo(fuseDataVO.getFacilityNo());
				bean.setId(radar2Bean.getnId());
				bean.setsTime(sTime);
				bean.setDetectTime(radar2Bean.getSzDetectTime());
				bean.setSpeed(radar2Bean.getnSpeed());
				bean.setSzPlate(radar2Bean.getSzPlate());
				bean.setnPlateColor(radar2Bean.getnPlateColor());
				bean.setnAxleNum(radar2Bean.getnAxleNum());
				bean.setnVehicleType(radar2Bean.getnVehicleType());
				bean.setSzCarPic(radar2Bean.getSzCarPic());
				bean.setSzPlatePic(radar2Bean.getSzPlatePic());
				String szSidePic = radar2Bean.getSzSidePic();
				if (StringUtils.isNotBlank(szSidePic)) {
					if (szSidePic.length() > 18000) { // 大于18000字节，base64编码进行压缩
						bean.setSzSidePic(Base64Utils.resizeImage(szSidePic, 19));
					} else {
						bean.setSzSidePic(szSidePic);
					}
				} else {
					bean.setSzSidePic("");
				}
				String cls = radar2Bean.getnCls();
				if (StringUtils.isBlank(cls)) {
					cls = "-1";
				}
				bean.setCls(cls);
				Double nFuseFlag = radar2Bean.getnFuseFlag();
				if (nFuseFlag != null) {
					bean.setFuseFlag(Double.parseDouble(String.format("%.6f", nFuseFlag)));
				}
				Integer nIdLane = radar2Bean.getnIdLane();// 车道号
				if (nIdLane == null || nIdLane == -1) {
					nIdLane = 1;
				}
				String idLane = String.valueOf(nIdLane);
				// 计算距离桩号
				bean.setMilePost(this.getRadarMilePostY(radar2Bean.getSzDeviceID(), radar2Bean.getnRelativeY(), line));
				bean.setIdLane(idLane);
				Double nProgressBar = radar2Bean.getnProgressBar();
				bean.setProgressBar(nProgressBar);
				if (tidFlag) {// 需要推送点表
					String showILane = idLane; // 人行道类型，重新定义
					if ("2".equals(cls)) { // 行人类型,车道号
						switch (line) {
						case 1:
							if ("1".equals(idLane)) {// 超车道，改为左线超车道行人
								showILane = "11";
							} else {
								showILane = "12";
							}
							break;
						case 2:
							if ("2".equals(idLane)) {// 超车道，改为右线超车道行人
								showILane = "21";
							} else {
								showILane = "22";
							}
							break;
						}
					}
					bean.setxRatioYk(this.getNewXRatioYk(nProgressBar, line, fuseDataVO.getFacilityLength()));// 计算云控X轴
					RadarPointVO point = this.getRadarPointVersion(nProgressBar, fuseDataVO, showILane);
					if (point != null) {
						bean.setxRatio(point.getxRatio());
						bean.setyRatio(point.getyRatio());
						bean.setFinishFlag(point.getFinishFlag());
					} else {
						bean.setxRatio(0.0);
						bean.setyRatio(0.0);
						bean.setFinishFlag("0");
					}
				}

			} else {
				Radar1Bean radar1Bean = GsonUtils.jsonToBean(object.toString(), Radar1Bean.class);
				bean.setSzPlate("默A00000");
				bean.setnPlateColor(9);
				bean.setnAxleNum(0);
				bean.setnVehicleType(0);
				bean.setSzPlatePic("");
				bean.setSzSidePic("");
				bean.setSzCarPic("");
				bean.setFuseDataId(radar1Bean.getDeviceID());
				String facilityNo = fuseDataVO.getFacilityNo();
				bean.setLine(line);
				bean.setFacilityNo(facilityNo);
				bean.setId(radar1Bean.getId());
				bean.setsTime(sTime);
				bean.setDetectTime(radar1Bean.getDetect_time());
				bean.setSpeed(radar1Bean.getSpeed());
				String cls = radar1Bean.getCls();
				bean.setCls(cls);
				if (StringUtils.isBlank(cls)) {
					bean.setCls("-1");
				}
				Double fuseFlag = radar1Bean.getFuse_flag();
				if (fuseFlag != null) {
					String fuseFlagStr = String.format("%.6f", fuseFlag);
					fuseFlag = Double.parseDouble(fuseFlagStr);
					bean.setFuseFlag(fuseFlag);
				}
				Integer nIdLane = radar1Bean.getIdLane();// 车道号
				if (nIdLane == null || nIdLane == -1) {
					nIdLane = 1;
				}
				String idLane = String.valueOf(nIdLane);
				bean.setIdLane(idLane);
				if (tidFlag || parseEvent) {// 需要推送点表或者事件检测
					Double coo_x = radar1Bean.getCoo_x();
					Double coo_y = radar1Bean.getCoo_y();
					String showILane = idLane;
					if ("2".equals(cls)) { // 行人类型
						switch (line) {
						case 1:
							if ("1".equals(showILane)) {// 超车道，改为左线超车道行人
								showILane = "11";
							} else {
								showILane = "12";
							}
							break;
						case 2:
							if ("2".equals(showILane)) {// 超车道，改为右线超车道行人
								showILane = "21";
							} else {
								showILane = "22";
							}
							break;
						}
					}
					bean.setxRatioYk(getXRatioYk(coo_x, coo_y, line, facilityNo));// 计算云控X轴
					RadarPointVO point = this.getRadarPoint(coo_x, coo_y, line, showILane, facilityNo);
					if (point != null) {
						bean.setxRatio(point.getxRatio());
						bean.setyRatio(point.getyRatio());
						bean.setFinishFlag(point.getFinishFlag());
					} else {
						bean.setxRatio(0.0);
						bean.setyRatio(0.0);
						bean.setFinishFlag("0");
					}
				}

				// 雷达事件解析
				if (radarAlarmSwitchits && parseEvent) {
					Integer event = radar1Bean.getEvent();
					if (event != null && event > 0) {
						parseEvent(fuseDataVO.getFactory(), radar1Bean.getDeviceID(), timeStamp, bean.getxRatio(),
								bean.getyRatio(), event);
					}
				}
			}
			radarTwinList.add(bean);
		}
		return radarTwinList;
	}

	/**
	 * @描述 解释雷达数据
	 */
	private boolean parse(String message, List<RadarTwinBean> radarTwinList, boolean parseEvent) {
		try {
			boolean isSzVersion = false;
			JsonObject dataPacket = GsonUtils.String2Object(message);
			String version = dataPacket.has("szProtoVersion") ? dataPacket.get("szProtoVersion").getAsString() : "";
			// 协议版本号 v1.0 信梧 钦北 来都 2.0 思防
			if (StringUtils.isNotBlank(version) && version.equals("v2.0")) {
				isSzVersion = true;
			}
			if (isSzVersion) { // 新版本2.0
				// LOGGER.info("radarTwin接收消息:{}", message);
				int tid = dataPacket.has("nTid") ? dataPacket.get("nTid").getAsInt() : 0;
				int line = dataPacket.has("nDirection") ? dataPacket.get("nDirection").getAsInt() : 0;// 1左洞，2右洞 //
																										// 上下行方向
				RadarFuseDataVO vo = getRadarFuseData(tid, line);// 通过对应关系查询facilityNo(隧道设施编号)
				if (vo == null) {
					return false;
				}
				Integer statFlag = vo.getStatFlag();// 流量统计标志0-不存储，1-存储
				if (!parseEvent && !RadarTwinService.tids.contains(tid) && statFlag == 0) {
					return false;
				}
				JsonElement sTimeElement = dataPacket.get("szTime");// 当前帧的发送时间戳
				long sTime = System.currentTimeMillis() / 1000;
				if (sTimeElement != null) {
					sTime = TimeUtils.getTimeStamp(sTimeElement.getAsString());// 雷达服务启动时间
				}
				JsonArray lstFuseData = dataPacket.has("lstFuseData") ? dataPacket.get("lstFuseData").getAsJsonArray()
						: null;
				if (lstFuseData.isJsonNull() || lstFuseData.size() == 0) {
					return false;
				}
				for (Object object : lstFuseData) {
					Radar2Bean radar2Bean = GsonUtils.jsonToBean(object.toString(), Radar2Bean.class);
					RadarTwinBean bean = new RadarTwinBean();
					bean.setFuseDataId(radar2Bean.getSzDeviceID());
					bean.setLine(line);
					bean.setFacilityNo(vo.getFacilityNo());
					bean.setId(radar2Bean.getnId());
					bean.setsTime(sTime);
					bean.setDetectTime(radar2Bean.getSzDetectTime());
					bean.setSpeed(radar2Bean.getnSpeed());
					bean.setSzPlate(radar2Bean.getSzPlate());
					bean.setnPlateColor(radar2Bean.getnPlateColor());
					bean.setnAxleNum(radar2Bean.getnAxleNum());
					bean.setnVehicleType(radar2Bean.getnVehicleType());
					bean.setSzCarPic(radar2Bean.getSzCarPic());
					bean.setSzPlatePic(radar2Bean.getSzPlatePic());
					String szSidePic = radar2Bean.getSzSidePic();
					if (StringUtils.isNotBlank(szSidePic)) {
						// LOGGER.info("压缩前SidePic长度：" + szSidePic.length());
						if (szSidePic.length() > 18000) { // 大于18000字节，base64编码进行压缩
							bean.setSzSidePic(Base64Utils.resizeImage(szSidePic, 19));
						} else {
							bean.setSzSidePic(szSidePic);
						}
						// LOGGER.info("压缩后SidePic长度：" + bean.getSzSidePic().length());
					} else {
						bean.setSzSidePic("");
					}
					String cls = radar2Bean.getnCls();
					if (StringUtils.isBlank(cls)) {
						cls = "-1";
					}
					bean.setCls(cls);
					Double nFuseFlag = radar2Bean.getnFuseFlag();
					if (nFuseFlag != null) {
						bean.setFuseFlag(Double.parseDouble(String.format("%.6f", nFuseFlag)));
					}
					Integer nIdLane = radar2Bean.getnIdLane();// 车道号
					if (nIdLane == null || nIdLane == -1) {
						nIdLane = 1;
					}
					String idLane = String.valueOf(nIdLane);
					bean.setIdLane(idLane);
					Double nProgressBar = radar2Bean.getnProgressBar();
					String showILane = idLane; // 人行道类型，重新定义
					if ("2".equals(cls)) { // 行人类型,车道号
						switch (line) {
						case 1:
							if ("1".equals(idLane)) {// 超车道，改为左线超车道行人
								showILane = "11";
							} else {
								showILane = "12";
							}
							break;
						case 2:
							if ("2".equals(idLane)) {// 超车道，改为右线超车道行人
								showILane = "21";
							} else {
								showILane = "22";
							}
							break;
						}
					}
					bean.setxRatioYk(0.00);// 计算云控X轴
					RadarPointVO point = this.getRadarPointVersion(nProgressBar, vo, showILane);
					if (point != null) {
						bean.setxRatio(point.getxRatio());
						bean.setyRatio(point.getyRatio());
						bean.setFinishFlag(point.getFinishFlag());
					} else {
						bean.setxRatio(0.0);
						bean.setyRatio(0.0);
						bean.setFinishFlag("0");
					}
					radarTwinList.add(bean);
				}
			} else {// 旧版本1.0
				int tid = dataPacket.get("tid").getAsInt();// 隧道id
				if (!parseEvent && !RadarTwinService.tids.contains(tid)) {
					return false;
				}

				int lineId = dataPacket.get("lineid").getAsInt();// 1左洞，2右洞
				RadarFuseDataVO vo = getRadarFuseData(tid, lineId);// 通过对应关系查询facilityNo(隧道设施编号)
				if (vo == null) {
					return false;
				}
				JsonElement sTimeElement = dataPacket.get("time");// 当前帧的发送时间戳
				long sTime = System.currentTimeMillis() / 1000;
				if (sTimeElement != null) {
					sTime = TimeUtils.getTimeStamp(sTimeElement.getAsString());// 雷达服务启动时间
				}
				JsonArray fuseData = dataPacket.get("fuse_data").getAsJsonArray();
				int size = fuseData.size();
				if (size == 0) {
					return false;
				}
				double timeStamp = dataPacket.get("timeStamp").getAsDouble();
				for (Object object : fuseData) {
					JsonObject jsonObject2 = GsonUtils.String2Object(object.toString());
					RadarTwinBean bean = new RadarTwinBean();
					// 设置默认值
					bean.setSzPlate("默A00000");
					bean.setnPlateColor(9);
					bean.setnAxleNum(0);
					bean.setnVehicleType(0);
					bean.setSzPlatePic("");
					bean.setSzSidePic("");
					bean.setSzCarPic("");
					String deviceId = jsonObject2.get("DeviceID").getAsString();// 雷达设备id
					bean.setFuseDataId(deviceId);
					String facilityNo = vo.getFacilityNo();
					bean.setLine(lineId);
					bean.setFacilityNo(facilityNo);
					bean.setId(jsonObject2.get("id").getAsString());
					bean.setsTime(sTime);
					bean.setDetectTime(jsonObject2.get("detect_time").getAsString());
					JsonElement speedElement = jsonObject2.get("speed");
					if (speedElement != null) {
						bean.setSpeed(speedElement.getAsDouble());
					}
					String cls = jsonObject2.get("cls").getAsString();
					bean.setCls(cls);
					if (StringUtils.isBlank(cls)) {
						bean.setCls("-1");
					}
					JsonElement fuseFlagElement = jsonObject2.get("fuse_flag");
					if (fuseFlagElement != null) {
						Double fuseFlag = fuseFlagElement.getAsDouble();
						String fuseFlagStr = String.format("%.6f", fuseFlag);
						fuseFlag = Double.parseDouble(fuseFlagStr);
						bean.setFuseFlag(fuseFlag);
					}
					String idLane = jsonObject2.get("idLane").getAsString();
					bean.setIdLane(idLane);
					if (StringUtils.isNotBlank(idLane)) {
						bean.setIdLane(idLane.equals("-1") ? "1" : idLane);
					} else {
						bean.setIdLane("1");
					}
					Double coo_x = jsonObject2.get("coo_x").getAsDouble();
					Double coo_y = jsonObject2.get("coo_y").getAsDouble();
					int line = bean.getLine();
					String idLane1 = bean.getIdLane();
					if ("2".equals(cls)) { // 行人类型
						switch (line) {
						case 1:
							if ("1".equals(idLane1)) {// 超车道，改为左线超车道行人
								idLane1 = "11";
							} else {
								idLane1 = "12";
							}
							break;
						case 2:
							if ("2".equals(idLane1)) {// 超车道，改为右线超车道行人
								idLane1 = "21";
							} else {
								idLane1 = "22";
							}
							break;
						}
					}
					bean.setxRatioYk(getXRatioYk(coo_x, coo_y, line, facilityNo));// 计算云控X轴
					RadarPointVO point = this.getRadarPoint(coo_x, coo_y, line, idLane1, facilityNo);
					if (point != null) {
						bean.setxRatio(point.getxRatio());
						bean.setyRatio(point.getyRatio());
						bean.setFinishFlag(point.getFinishFlag());
					} else {
						bean.setxRatio(0.0);
						bean.setyRatio(0.0);
						bean.setFinishFlag("0");
					}
					radarTwinList.add(bean);
					// 雷达事件解析
					if (radarAlarmSwitchits && parseEvent) {
						JsonElement eventElement = jsonObject2.get("event");
						int event = 0;
						if (eventElement != null && (event = eventElement.getAsInt()) > 0) {
							parseEvent(vo.getFactory(), deviceId, timeStamp, bean.getxRatio(), bean.getyRatio(), event);
						}
					}

				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			LOGGER.error("雷达数据parse出错{}:{}", message, e.getMessage());
			return false;
		}
		return true;
	}

	/**
	 * @描述 2.0版本坐标计算
	 * @param nProgressBar：车辆在路段的行驶进度条范围0~1（0-开始，1-结束）
	 * @param fuseData：设施数据（平面区分上下行）
	 * @param showILane:车道号
	 * @return
	 */
	public RadarPointVO getRadarPointVersion(Double nProgressBar, RadarFuseDataVO fuseData, String showILane) {
		int line = fuseData.getLine(); // 1左线-下行 ，2右线-上行
		String facilityNo = fuseData.getFacilityNo();// 设施ID
		RadarPointVO point = null;
		if (nProgressBar != null) {
			List<FacilityRadarPointVO> facilityRadarPoints = this.getFacilityRadarPoints();
			List<RadarPointVO> radarPoints = null;
			for (FacilityRadarPointVO vo : facilityRadarPoints) {
				if (vo.getLine() == line && vo.getIdLand().equals(showILane) && vo.getFacilityNo().equals(facilityNo)) {
					radarPoints = vo.getPoints();
					break;
				}
			}
			if (!CollectionUtils.isEmpty(radarPoints)) {
				Double nodeStart = fuseData.getNodeStart(); // 开始像素点
				Double nodeLength = fuseData.getNodeLength(); // 像素点长度
				Double progressBar = line == 1 ? (1 - nProgressBar) : nProgressBar;// 1：左洞-下行-大桩号往小桩号，2右线-上行-小桩号往大桩号
				Double xRatio = progressBar * nodeLength + nodeStart;
				radarPoints.forEach(ra -> {
					ra.setXcRatio(Math.abs(xRatio - ra.getxRatio()));
				});
				point = radarPoints.stream().min(Comparator.comparing(RadarPointVO::getXcRatio)).get();
			}
		}
		return point;
	}

	/** 解析雷达检测的事件告警，并入库 **/
	private void parseEvent(Integer factory, String deviceId, double timeStamp, Double xRatio, Double yRatio,
			int event) {
		LOGGER.info("有雷达告警事件,event:{},factory:{}", event, factory);
		TunnelRadarVO radar = TunnelRadarService.radarMap.get(deviceId);
		if (radar != null) {
			long createTime = (long) timeStamp / 1000;
			String roadName = radar.getRoadName();
			String milePost = radar.getMilePost();
			AlarmDTO alarmDTO = new AlarmDTO();
			String alarmId = UUID.randomUUID().toString();
			alarmDTO.setId(alarmId);
			alarmDTO.setDeviceId(radar.getDeviceId());
			alarmDTO.setCameraId(radar.getCameraId());
			alarmDTO.setDetail(roadName);
			alarmDTO.setFacilityNo(radar.getFacilityNo());
			alarmDTO.setFacilityName(radar.getFacilityName());
			alarmDTO.setCreateTime(createTime);
			alarmDTO.setRoadNo(radar.getRoadNo());
			alarmDTO.setMilePost(milePost);
			if (factory == null) {
				// 任何事不做，备用
				return;
			} else if (factory == 1) {
				itsEvent(event, alarmDTO);
			} else if (factory == 2) {
				sunyanV1Event(event, alarmDTO);
			}
			if (xRatio != null && yRatio != null) {
				AlarmRadarEventDTO alarmRadarEventDTO = new AlarmRadarEventDTO();
				alarmRadarEventDTO.setAlarmId(alarmId);
				alarmRadarEventDTO.setCreateTime(TimeUtils.getTimeString(TimeUtils.FULL_TIME, createTime * 1000));
				alarmRadarEventDTO.setxRatio(xRatio + "");
				alarmRadarEventDTO.setyRatio(yRatio + "");
				radarTwinMapper.insertAlarmRadarEvent(alarmRadarEventDTO);
				LOGGER.info("{}雷达告警{}，且有转化后的坐标", roadName, alarmId);
			}
		}
	}

	/** its雷视融合事件 **/
	void itsEvent(int event, AlarmDTO alarmDTO) {
		// Bit计算 雷达检测到事件（1逆行，2停车，4超速，8行人）
		String bitCount = bitCount(event);
		if (bitCount.contains("Bit1;")) {
			// 1逆行
			alarmDTO.setDetail("检测到疑似逆行车辆事件");
			alarmDTO.setType("707");
			addRadarAlarm(alarmDTO);
		} else if (bitCount.contains("Bit2;")) {
			// 2停车
			alarmDTO.setDetail("检测到疑似畅通停车事件");
			alarmDTO.setType("701");
			addRadarAlarm(alarmDTO);
		} else if (bitCount.contains("Bit3;")) {
			// 4超速
			alarmDTO.setDetail("检测到疑似车辆超速事件");
			alarmDTO.setType("703");
//			addRadarAlarm(alarmDTO);
		} else if (bitCount.contains("Bit4;")) {
			// 8行人，关闭
			alarmDTO.setDetail("检测到疑似行人事件");
			alarmDTO.setType("706");
			addRadarAlarm(alarmDTO);
		}
	}

	/**
	 * 雷达事件厂家：隼眼科技
	 */
	void sunyanV1Event(int event, AlarmDTO alarmDTO) {
		// 0-无事件，1-逆行，2-大车超高速，3-小车超高速，4-大车超低速，5-小车超低速，6-停车，7-占用应急车道行驶，
		// 8-压线，9-变道，10-占用应急车道停车，11-占用应急车道逆行
		if (event == 1 || event == 11) {
			// 1逆行
			alarmDTO.setDetail("检测到疑似逆行车辆事件");
			alarmDTO.setType("707");
			addRadarAlarm(alarmDTO);
		} else if (event == 6 || event == 10) {
			// 2停车
			alarmDTO.setDetail("检测到疑似停车事件");
			alarmDTO.setType("701");
			addRadarAlarm(alarmDTO);
		} else if (event == 2 || event == 3) {
			// 4超速
			alarmDTO.setDetail("检测到疑似车辆超速事件");
			alarmDTO.setType("703");
//			addRadarAlarm(alarmDTO);
		}
	}

	public void addRadarAlarm(AlarmDTO alarmDTO) {
		Map<String, String> innerLoginHead = ServiceUtils.getInnerLoginHead();
		HttpClientUtils.post(ykDomain + "/its-road/alarm/add", innerLoginHead, new Gson().toJson(alarmDTO));
		tunnelRadarMapper.addRadarAlarmSync(alarmDTO);
	}

	private String bitCount(int n) {
		String eventBit = "";
		int bit = 1;
		while (n != 0) {
			int one = n & 1;
			if (one == 1) {
				eventBit += ("Bit" + bit + ";");
			}
			bit++;
			n >>>= 1;
		}
		return eventBit;
	}

	@SuppressWarnings("unchecked")
	private void saveRadarTrack(List<RadarTwinBean> radarTwinList) {
		if (!CollectionUtils.isEmpty(radarTwinList)) {
			List<RadarCarDTO> RADAR_TRACK_lIST = new ArrayList<RadarCarDTO>(); // 雷达车辆轨迹
			Map<String, RadarCarDTO> RADAR_CAR_MAP = new ConcurrentHashMap<String, RadarCarDTO>();
			radarTwinList.stream().forEach(x -> {
				String carId = x.getId();
				RadarCarDTO dto = new RadarCarDTO();
				dto.setId(UUID.randomUUID().toString());
				dto.setCarId(carId);
				dto.setCls(x.getCls());
				dto.setDetectTime(x.getsTime());
				dto.setFacilityNo(x.getFacilityNo());
				dto.setIdLane(x.getIdLane());
				dto.setLine(x.getLine());
				dto.setPlate(x.getSzPlate());
				dto.setPlateColor(x.getnPlateColor());
				dto.setSpeed(x.getSpeed());
				dto.setVehicleType(x.getnVehicleType());
				dto.setProgressBar(x.getProgressBar());
				dto.setCreateTime(TimeUtils.getTimeString());
				RADAR_TRACK_lIST.add(dto);
				RADAR_CAR_MAP.put(carId, dto);
			});
			if (!CollectionUtils.isEmpty(RADAR_TRACK_lIST)) {
				redisTemplate.opsForList().rightPushAll(CommonFunction.RADAR_TRACK_lIST, RADAR_TRACK_lIST);// 缓存雷达轨迹
				redisTemplate.opsForHash().putAll(CommonFunction.RADAR_CAR_MAP, RADAR_CAR_MAP);// 缓存雷达明细
			}
		}

	}

	/**
	 * @描述 推送雷达轨迹到前端，缓存报警轨迹、刷选车辆统计明细
	 * @param radarTwinList
	 */
	@SuppressWarnings("unchecked")
	private void pushWebsocketMsg(List<RadarTwinBean> radarTwinList) {
		// LOGGER.info("推送雷达数据：" + radarTwinList.size());
		if (CollectionUtils.isEmpty(radarTwinList)) {
			return;
		}
		// 新增页面推送时间
		String pushTime = TimeUtils.getTimeString();
		radarTwinList.stream().forEach(x -> {
			x.setPushTime(pushTime);
		});
		String cacheKey = CommonFunction.RADAR_USER_ID;// 隧道雷达用户缓存
		List<String> userIdList = (List<String>) redisTemplate.opsForValue().get(cacheKey);
		if (CollectionUtils.isEmpty(userIdList)) {
			userIdList = radarTwinMapper.getRadarUsers();
			redisTemplate.opsForValue().set(cacheKey, userIdList, CommonFunction.TIMEOUT, TimeUnit.HOURS);// 默认缓存2小时
		}
		webSocketService.pushRadarTwinMessage(radarTwinList, userIdList);

	}

	private final double COS_RADIAN = -0.3420201433256694; // 250
	private final double SIN_RADIAN = -0.9396926207859082;// 250
	private final double COS_RADIAN_352 = 0.9902680687415703; // 352
	private final double SIN_RADIAN_352 = -0.13917310096006588;// 352
	private final double COS_RADIAN_337 = 0.9218631515885004; // 337.2
	private final double SIN_RADIAN_337 = -0.38751558645210327;// 337.2
	private final double COS_RADIAN_201 = -0.9323238012155122; // 21.2
	private final double SIN_RADIAN_201 = -0.36162457008209226; // 21.2

	/**
	 * 云控X坐标轴轨迹计算
	 * 1、爽冲隧道： 
	 * 左线公式：xRatio=(coo_x*cos(250°)+coo_y*sin(250°))*1.213+252
	 * 右线公式：xRatio=(coo_x*cos(250°)+coo_y*sin(250°))*1.213+252
	 * 2、阳爽隧道：
	 * 左线公式：xRatio=(coo_x*cos(196°)+coo_y*sin(196°))*1.065+252
	 * 右线公式：xRatio=(coo_x*cos(196°)+coo_y*sin(196°))*1.065+252
	 * 3、陈岭顶隧道
	 * 左线公式：xRatio=(coo_x*cos(233.7°)+coo_y*sin(233.7°))*1.167+252
	 * 右线公式：xRatio=(coo_x*cos(233.7°)+coo_y*sin(233.7°))*1.167+252
	 * 4、铁山港跨海大桥
	 * 左线公式：xRatio=(coo_x*cos(352°)+coo_y*sin(352°))*0.99+774
	 * 右线公式：xRatio=(coo_x*cos(352°)+coo_y*sin(352°))*0.99+774
	 * @param coo_x
	 * @param coo_y
	 * @param line
	 * @param facilityNo
	 */
	public Double getXRatioYk(Double coo_x, Double coo_y, int line, String facilityNo) {
		Double xRatioYk = 0.00;
		Double radian = 0.00;// 角度转弧度
		switch (facilityNo) {
		case "5e564e25-d4e7-4939-9c53-43e565973863":// 爽冲隧道
			// radian = 250 * Math.PI / 180;// 角度转弧度
			xRatioYk = (coo_x * COS_RADIAN + coo_y * SIN_RADIAN) * 1.213 + 252;
			break;
		case "bbe08af6-f60d-41bc-9e5b-f9e39ef96d7e":// 阳爽隧道
			/*
			 * if (line == 1) { xRatioYk = (coo_x * COS_RADIAN + coo_y * SIN_RADIAN) * 1.03
			 * + 252; } else { xRatioYk = (coo_x * COS_RADIAN + coo_y * SIN_RADIAN) * 1.065
			 * + 252; }
			 */
			radian = 196 * Math.PI / 180;
			xRatioYk = (coo_x * Math.cos(radian) + coo_y * Math.sin(radian)) * 1.065 + 252;
			break;
		case "32d21560-7362-42c5-9a43-b9748cc2b306":// 陈岭顶隧道
			/*
			 * if (line == 1) { xRatioYk = (coo_x * COS_RADIAN + coo_y * SIN_RADIAN) * 1.07
			 * + 252; } else { xRatioYk = (coo_x * COS_RADIAN + coo_y * SIN_RADIAN) * 1.167
			 * + 252; }
			 */
			radian = 233.7 * Math.PI / 180;
			xRatioYk = (coo_x * Math.cos(radian) + coo_y * Math.sin(radian)) * 1.167 + 252;
			break;
		/*
		 * case "37718127-78c9-4d47-ba89-2bc9cefe4620"://铁山港跨海大桥 radian = 352 * Math.PI
		 * / 180; xRatioYk = (coo_x * Math.cos(radian) + coo_y * Math.sin(radian)) *
		 * 0.99 + 774; break;
		 */

		}
		return NumbersUtils.round(xRatioYk, 2);
	}

	/**
	 * @描述 计算横隧道页面横坐标
	 */
	public Double getNewXRatioYk(Double nProgressBar, int line, Double facilityLength) {
		Double xRatioYk = 0.00;
		if (nProgressBar != null) {
			Double progressBar = line == 1 ? (1 - nProgressBar) : nProgressBar;
			if (facilityLength != null && facilityLength <= 1420) {
				xRatioYk = progressBar * 1420;
			} else {
				xRatioYk = progressBar * facilityLength;
			}
		}
		return xRatioYk;
	}

	/**
	 * 左线1公式：xRatio=(coo_x*cos(250°)+coo_y*sin(250°))*3.224+270
	 * 右线2公式：xRatio=(coo_x*cos(250°)+coo_y*sin(250°))*3.224+270
	 * 
	 * @param coo_x:雷达x轴
	 * @param coo_y:雷达y轴
	 * @param line:左右线            1-左下 2-右上
	 * @param idLane:1-超车         2-行车 3-应急车道 21-右线超车行人 22-右线行车行人 11-左线超车行人
	 *                            12-左线行车行人
	 * @param facilityNo:所属设施（隧道）,目前之前爽冲隧道
	 * @param cls:车类型             0-小车 1-大车 3-行人
	 * @return
	 */
	public RadarPointVO getRadarPoint(Double coo_x, Double coo_y, int line, String idLane, String facilityNo) {
		RadarPointVO point = null;
		if (coo_x != null && coo_y != null) {
			List<FacilityRadarPointVO> facilityRadarPoints = this.getFacilityRadarPoints();
			List<RadarPointVO> radarPoints = null;
			for (FacilityRadarPointVO vo : facilityRadarPoints) {
				if (vo.getLine() == line && vo.getIdLand().equals(idLane) && vo.getFacilityNo().equals(facilityNo)) {
					radarPoints = vo.getPoints();
					break;
				}
			}
			if (!CollectionUtils.isEmpty(radarPoints)) {
				switch (facilityNo) {
				case "5e564e25-d4e7-4939-9c53-43e565973863":// 爽冲隧道
					// 大屏左右洞公式一样
					Double xRatio = (coo_x * COS_RADIAN + coo_y * SIN_RADIAN) * 3.224 + 270;
					// 左洞最大小值 344.00,3478.68
					// 右洞最大小值 290.00,4049.83
					if (line == 1) {
						if (xRatio >= 344.00 && xRatio <= 3478.68) {
							radarPoints.forEach(ra -> {
								ra.setXcRatio(Math.abs(xRatio - ra.getxRatio()));
							});
							point = radarPoints.stream().min(Comparator.comparing(RadarPointVO::getXcRatio)).get();
						}
					} else {
						if (xRatio >= 290.00 && xRatio <= 4049.83) {
							radarPoints.forEach(ra -> {
								ra.setXcRatio(Math.abs(xRatio - ra.getxRatio()));
							});
							point = radarPoints.stream().min(Comparator.comparing(RadarPointVO::getXcRatio)).get();
						}
					}
					break;
				case "37718127-78c9-4d47-ba89-2bc9cefe4620":// 铁山港大桥
					// 跨海桥坐标转换公式：xRatio=(coo_x*cos(352°)+coo_y*sin(352°))*0.99+774
					Double xRatio_352 = (coo_x * COS_RADIAN_352 + coo_y * SIN_RADIAN_352) * 0.99 + 774;
					radarPoints.forEach(ra -> {
						ra.setXcRatio(Math.abs(xRatio_352 - ra.getxRatio()));
					});
					point = radarPoints.stream().min(Comparator.comparing(RadarPointVO::getXcRatio)).get();
					break;
				case "2dc56897-3a4e-421c-b833-111864e06dcc":// 南流江大桥
					// 南流江大桥坐标转换公式：xRatio=(coo_x*cos(337.2°)+coo_y*sin(337.2°))*4.598+722+31*4.598
					Double xRatio_337 = (coo_x * COS_RADIAN_337 + coo_y * SIN_RADIAN_337) * 4.598 + 722 + 31 * 4.598;
					radarPoints.forEach(ra -> {
						ra.setXcRatio(Math.abs(xRatio_337 - ra.getxRatio()));
					});
					point = radarPoints.stream().min(Comparator.comparing(RadarPointVO::getXcRatio)).get();
					break;
				case "aa01abbe-5167-7dfd-f939-424f6ff87940":// 金钗一号隧道
					// 金钗一号隧道：xRatio=(coo_x*cos(201.2°)+coo_y*sin(201.2°))*1.32+128
					Double xRatio_201 = (coo_x * COS_RADIAN_201 + coo_y * SIN_RADIAN_201) * 1.32 + 128;
					radarPoints.forEach(ra -> {
						ra.setXcRatio(Math.abs(xRatio_201 - ra.getxRatio()));
					});
					point = radarPoints.stream().min(Comparator.comparing(RadarPointVO::getXcRatio)).get();
					break;
				case "36f1bf46-94b9-3ea4-7586-e4ba374e14c2":// 思防路百宝隧道
					// 思防路百宝隧道：xRatio=(coo_x*cos(201.2°)+coo_y*sin(201.2°))*1.32+128
					Double xRatio_bb = (coo_x * COS_RADIAN_201 + coo_y * SIN_RADIAN_201) * 1.32 + 128;
					radarPoints.forEach(ra -> {
						ra.setXcRatio(Math.abs(xRatio_bb - ra.getxRatio()));
					});
					point = radarPoints.stream().min(Comparator.comparing(RadarPointVO::getXcRatio)).get();
					break;
				}

			}
		}
		return point;
	}

	/** 设施-雷达点位数据（在前端底图的位置数据集） **/
	public static List<FacilityRadarPointVO> FACILITY_RADAR_POINTS = new ArrayList<>();

	public List<FacilityRadarPointVO> getFacilityRadarPoints() {
		if (RADAR_POINT_LIST.size() == 0) {
			RADAR_POINT_LIST = radarTwinMapper.selectRadarPointList();
			RADAR_POINT_LIST.stream()
					.collect(
							Collectors.groupingBy(vo -> vo.getFacilityNo() + "#" + vo.getLine() + "#" + vo.getIdLane()))
					.forEach((k, v) -> {
						FacilityRadarPointVO vo = new FacilityRadarPointVO();
						if (k.contains("#")) {
							String[] keys = k.split("#");
							if (keys.length >= 3) {
								vo.setFacilityNo(keys[0]);
								vo.setLine(Integer.valueOf(keys[1]));
								vo.setIdLand(keys[2]);
								vo.setPoints(v);
								FACILITY_RADAR_POINTS.add(vo);
							}
						}
					});
		}
		return FACILITY_RADAR_POINTS;
	}

	public RadarFuseDataVO getRadarFuseData(Integer tid, Integer line) {
		RadarFuseDataVO vo = null;
		if (tid != null && line != null) {
			updateRadarFuseData();// 更新雷达设施对应关系
			for (RadarFuseDataVO data : RadarTwinService.RADAR_FUSE_DATA) {
				if (data.getTid().equals(tid) && data.getLine().equals(line)) {
					vo = data;
					break;
				}
			}

		}
		return vo;
	}

	/**
	 * 根据雷达设备id获取当前雷达桩号值
	 * @param zDeviceID
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public String getRadarMilePostY(String szDeviceID, Double nRelativeY, Integer line) {
		String milePost = "";
		if (StringUtils.isNotBlank(szDeviceID)) {
			if (RADAR_DEVICE_MAP.isEmpty()) {
				String cacheKey = CommonFunction.RADAR_DEVICE_LIST;// 雷达设施对应关系
				List<RadarDeviceVO> list = (List<RadarDeviceVO>) redisTemplate.opsForValue().get(cacheKey);
				if (CollectionUtils.isEmpty(list)) {
					list = radarTwinMapper.selectRadarDevice();
					redisTemplate.opsForValue().set(cacheKey, list, CommonFunction.TIMEOUT, TimeUnit.HOURS);
				}
				list.forEach(x -> {
					RADAR_DEVICE_MAP.put(x.getDeviceCode(), x.getMpValue());
				});

			}
			Integer mpValue = RADAR_DEVICE_MAP.get(szDeviceID);
			if (mpValue != null) {
				int intY = (int) Math.floor(nRelativeY);
				if (line == 1) {
					mpValue = mpValue + intY;
				} else {
					mpValue = mpValue - intY;
				}
				milePost = this.valueToPileNo(mpValue);
			}
		}
		return milePost;
	}

	/**
	 * 桩号值转为桩号K+000格式
	 * @param pileNumber
	 * @return
	 */
	public String valueToPileNo(Integer pileNumber) {
		int kilometers = pileNumber / 1000;
		int meters = pileNumber % 1000;
		return "K" + kilometers + "+" + String.format("%03d", meters);
	}

	@SuppressWarnings("unchecked")
	private void updateRadarFuseData() {
		if (CollectionUtils.isEmpty(RadarTwinService.RADAR_FUSE_DATA)) {
			String cacheKey = CommonFunction.RADAR_FUSE_DATA_lIST;// 雷达设施对应关系
			List<RadarFuseDataVO> list = (List<RadarFuseDataVO>) redisTemplate.opsForValue().get(cacheKey);
			if (CollectionUtils.isEmpty(list)) {
				list = radarTwinMapper.selectRadarFuseData();
				redisTemplate.opsForValue().set(cacheKey, list, CommonFunction.TIMEOUT, TimeUnit.HOURS);
			}
			RadarTwinService.RADAR_FUSE_DATA = list;
		}
	}

	public void testRadarTrail(String id) {
		String carId = "\"id\":" + id + ",";
		List<String> list = radarTwinMapper.selectCarMeassageById(carId);
		for (String message : list) {
			try {
				Thread.sleep(100);
				this.consume(message, true, false);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		}
	}

	public Integer alarmPointAdd(RadarAlarmPointDTO dto) {
		Integer tid = dto.getTid();
		Integer lineId = dto.getLineId();
		String cls = dto.getCls();
		String idLane = dto.getIdLane();
		RadarFuseDataVO vo = getRadarFuseData(tid, lineId);// 通过对应关系查询facilityNo(隧道设施编号)
		if (vo == null) {
			return 0;
		}
		if (StringUtils.isBlank(cls)) {
			dto.setCls("-1");
		}
		if (StringUtils.isNotBlank(idLane)) {
			dto.setIdLane(idLane.equals("-1") ? "1" : idLane);
		} else {
			dto.setIdLane("1");
		}
		String idLane1 = dto.getIdLane();
		if ("2".equals(cls)) { // 行人类型
			switch (lineId) {
			case 1:
				if ("1".equals(dto.getIdLane())) {// 超车道，改为左线超车道行人
					idLane1 = "11";
				} else {
					idLane1 = "12";
				}
				break;
			case 2:
				if ("2".equals(idLane1)) {// 超车道，改为右线超车道行人
					idLane1 = "21";
				} else {
					idLane1 = "22";
				}
				break;
			}
		}
		RadarPointVO point = this.getRadarPoint(dto.getCooX(), dto.getCooY(), lineId, idLane1, vo.getFacilityNo());
		if (point == null) {
			return 0;
		}
		Double xRatio = point.getxRatio();
		Double yRatio = point.getyRatio();

		if (xRatio != null && yRatio != null) {
			AlarmRadarEventDTO alarmRadarEventDTO = new AlarmRadarEventDTO();
			alarmRadarEventDTO.setAlarmId(dto.getAlarmId());
			alarmRadarEventDTO.setCreateTime(TimeUtils.getTimeString(TimeUtils.FULL_TIME, System.currentTimeMillis()));
			alarmRadarEventDTO.setxRatio(xRatio + "");
			alarmRadarEventDTO.setyRatio(yRatio + "");
			alarmRadarEventDTO.setTargetId(dto.getTargetId());
			radarTwinMapper.insertAlarmRadarEvent(alarmRadarEventDTO);
			return 1;
		}
		return 0;
	}

	/**
	 * @描述 定时处理雷达轨迹以及雷达明细任务 一小时内不重复存储
	 */
	@SuppressWarnings("unchecked")
	public void dealRadarCarList() {
		String cacheKey = CommonFunction.RADAR_CAR_MAP;
		if (redisTemplate.hasKey(cacheKey)) {
			List<RadarCarDTO> allList = redisTemplate.opsForHash().values(cacheKey);
			Set<String> carIds = this.getCarIds();
			if (!allList.isEmpty()) {
				this.deleteAllFieldsFromHash(cacheKey);
				List<RadarCarDTO> addList = allList.stream().filter(x -> !carIds.contains(x.getCarId()))
						.collect(Collectors.toList());
				List<List<RadarCarDTO>> radarList = CommonFunction.splitList(addList, 100);
				for (List<RadarCarDTO> list : radarList) {
					tunnelRadarMapper.batchAddRadarCarList(list);
				}
				// 将保存的值批量存入缓存
				Set<String> addIds = addList.stream().map(RadarCarDTO::getCarId).collect(Collectors.toSet());
				if (addIds.size() > 0) {
					Map<String, Long> idMaps = new ConcurrentHashMap<String, Long>();
					Long time = System.currentTimeMillis();
					addIds.forEach(x -> {
						idMaps.put(x, time);
					});
					redisTemplate.opsForHash().putAll(CommonFunction.RADAR_STORE_MAP, idMaps);
				}
			}
		}
	}

	@SuppressWarnings("unchecked")
	private Set<String> getCarIds() {
		String storeKey = CommonFunction.RADAR_STORE_MAP;
		Set<String> carIds = new HashSet<String>();
		Map<Object, Object> idMaps = redisTemplate.opsForHash().entries(storeKey);
		long currentTime = System.currentTimeMillis();
		if (idMaps == null || idMaps.isEmpty()) {
			Long compTime = currentTime - 1 * 60 * 60 * 1000; // 默认缓存1小时
			carIds = tunnelRadarMapper.getRadarCarIds(compTime);
			if (carIds.isEmpty()) {
				idMaps.put("10", currentTime);
				carIds.add("10");
			} else {
				carIds.forEach(x -> {
					idMaps.put(x, currentTime);
				});
			}
			redisTemplate.opsForHash().putAll(storeKey, idMaps);
		} else { // 删除超出1小时的比较ID
			for (Map.Entry<Object, Object> entry : idMaps.entrySet()) {
				Long expireTime = (Long) entry.getValue();
				String carKey = String.valueOf(entry.getKey());
				if (currentTime - expireTime > 1 * 60 * 60 * 1000) {
					redisTemplate.opsForHash().delete(storeKey, carKey);
				} else {
					carIds.add(carKey);
				}
			}
		}
		return carIds;
	}

	private void deleteAllFieldsFromHash(String hashKey) {
		@SuppressWarnings("unchecked")
		HashOperations<String, Object, Object> opsForHash = redisTemplate.opsForHash();
		Set<Object> fields = opsForHash.keys(hashKey);
		if (fields != null && !fields.isEmpty()) {
			opsForHash.delete(hashKey, fields.toArray());
		}
	}

	/**
	 * @描述 处理保存雷达车辆轨迹
	 */
	@SuppressWarnings("unchecked")
	public void addRadarTrack() {
		String cacheKey = CommonFunction.RADAR_TRACK_lIST;// 雷达轨迹缓存信息
		if (redisTemplate.hasKey(cacheKey)) {
			List<RadarCarDTO> allList = redisTemplate.opsForList().range(cacheKey, 0, -1);
			if (!allList.isEmpty()) {
				redisTemplate.opsForList().trim(cacheKey, 1, 0);// 清空指定键对应的列表而不删除键
				List<List<RadarCarDTO>> radarList = CommonFunction.splitList(allList, 100);
				for (List<RadarCarDTO> list : radarList) {
					tunnelRadarMapper.batchAddRadarTrack(list);
				}
			}
		}
	}

	/**
	 * @描述 清理车辆轨迹，处理告警雷达车辆轨迹
	 * 1、提取告警的车辆轨迹，提取后清理轨迹表对应信息
	 */
	public void dealAlarmRadarTrack() {
		// 1小时内告警车辆
		String compTime = TimeUtils.getTimeString(TimeUtils.FULL_TIME_8,
				System.currentTimeMillis() - 1 * 60 * 60 * 1000);
		List<String> carIds = tunnelRadarMapper.getAlarmRadarCarIds(compTime);
		if (!carIds.isEmpty()) {
			// 当前告警轨迹信息
			List<RadarCarDTO> radarAlarmCarList = tunnelRadarMapper.selectRadarAlarmCarList(carIds);
			if (!radarAlarmCarList.isEmpty()) {
				List<List<RadarCarDTO>> addList = CommonFunction.splitList(radarAlarmCarList, 100);
				for (List<RadarCarDTO> list : addList) {
					tunnelRadarMapper.batchAddRadarAlarmTrack(list);
				}
				List<String> ids = radarAlarmCarList.stream().map(RadarCarDTO::getId).collect(Collectors.toList());
				// 删除以提取的告警轨迹
				List<List<String>> delIds = CommonFunction.splitList(ids, 200);
				for (List<String> dels : delIds) {
					tunnelRadarMapper.batchDeleteRadarTrack(dels);
				}
			}
		}
	}

	/**
	 * @描述 轨迹表数据只存储2个小时，超时将清除
	 * 1、正常产生告警信息个轨迹，会提取到雷达告警轨迹表
	 */
	public void dealOverTimeRadarTrack() {
		Long compTime = System.currentTimeMillis() - 2 * 60 * 60 * 1000; // 默认缓存2小时
		tunnelRadarMapper.deleteRadarTrack(compTime);
		// 清理车辆明细信息
		Long compTime2 = System.currentTimeMillis() - 8 * 24 * 60 * 60 * 1000; // 超车8天的数据
		tunnelRadarMapper.deleteRadarCar(compTime2);
	}

	public ResponseVO checkIsExitRadarCar(RadarAlarmDTO dto) {
		String carId = tunnelRadarMapper.getRadarCarIdByAlarmId(dto.getAlarmId());
		if (StringUtils.isBlank(carId)) {
			return new ResponseVO("不存在车辆信息", 0, null);
		} else {
			return new ResponseVO("存在车辆信息", 1, carId);
		}

	}

	public Object selectRadarAlarmTrack(RadarAlarmDTO dto) {
		String alarmId = dto.getAlarmId();
		String facilityNo = dto.getFacilityNo();
		String carId = tunnelRadarMapper.getRadarCarIdByAlarmId(alarmId);
		if (StringUtils.isBlank(carId)) {
			return new ResponseVO("当前雷达告警事件暂无轨迹数据!", 400);
		} else {
			List<RadarCarDTO> list = tunnelRadarMapper.selectRadarAlarmTrackList(carId);
			if (list.isEmpty()) {
				return new ResponseVO("当前雷达告警事件暂无轨迹数据！", 400);
			} else {
				RadarAlarmTrackVO result = new RadarAlarmTrackVO();
				result.setAlarmId(alarmId);
				result.setList(list);
				result.setFacilityNo(facilityNo);
				List<RadarFuseDataVO> statList = this.getRadarFuseDataByFacilityNo(facilityNo);
				if (!statList.isEmpty()) {
					statList.stream().forEach(x -> {
						Integer line = x.getLine();
						if (line == 1) {// 左线
							result.setOneLanes(x.getLanes());
							result.setFacilityName(x.getFacilityName());
						}
						if (line == 2) {// 右线
							result.setTwoLanes(x.getLanes());
						}
					});
				}
				return result;
			}
		}
	}

	public List<RadarFuseDataVO> getRadarFuseDataByFacilityNo(String facilityNo) {
		List<RadarFuseDataVO> list = new ArrayList<>();
		if (StringUtils.isNotBlank(facilityNo)) {
			updateRadarFuseData();// 更新雷达设施对应关系
			list = RadarTwinService.RADAR_FUSE_DATA.stream().filter(data -> data.getFacilityNo().equals(facilityNo))
					.collect(Collectors.toList());
		}
		return list;
	}

	/**
	 * @描述 统计12小时内车流量
	 * @param dto
	 * @return
	 */
	public Object statHourTraffic(RadarAlarmDTO dto) {
		LocalDateTime now = LocalDateTime.now();// 当前时间
		LocalDateTime twelveHoursAgo = now.minusHours(12);// 推前时间12小时
		List<Integer> hoursList = this.getTwelveHoursAgoList(now, twelveHoursAgo);
		// 往前推12小时间，要以整点开始 9:00:00
		String startDay = twelveHoursAgo.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:00:00 000"));
		dto.setStartTime(TimeUtils.getMsTimeStamp(startDay));
		dto.setEndTime(System.currentTimeMillis());// 当前时间戳
		List<RadarCarStatVO> statList = tunnelRadarMapper.statHourTraffic(dto);
		// 车类型统计： 0-普通客车 1-普通货车 两客一危（ 2-大客车 3-面包车 4-危化品车）
		if (!CollectionUtils.isEmpty(statList)) {
			List<Integer> types = Arrays.asList(2, 3, 4);
			statList.stream().forEach(x -> {
				Integer cls = x.getCls();
				if (types.contains(cls)) {// 两客一危类型
					x.setCls(2);
				}
			});
		}
		Map<String, Map<Integer, Integer>> groupResult = statList.stream()
				.collect(Collectors.groupingBy(RadarCarStatVO::getStatName,
						Collectors.groupingBy(RadarCarStatVO::getCls, Collectors.summingInt(RadarCarStatVO::getCars))));
		List<RadarCarStatVO> groupList = new ArrayList<RadarCarStatVO>();
		groupResult.forEach((statName, typeMap) -> {
			typeMap.forEach((cls, sum) -> {
				RadarCarStatVO vo = new RadarCarStatVO();
				vo.setStatName(statName);
				vo.setCls(cls);
				vo.setCars(sum);
				groupList.add(vo);
			});
		});
		List<RadarCarTotalVO> totalList = new ArrayList<RadarCarTotalVO>();
		for (int i = 0; i < hoursList.size(); i++) {
			Integer hour = hoursList.get(i);
			RadarCarTotalVO carTotal = new RadarCarTotalVO();
			carTotal.setStatName(String.valueOf(hour));
			carTotal.setTruckCars(0);
			carTotal.setBusCars(0);// 初始值
			carTotal.setDangerCars(0);
			if (!groupList.isEmpty()) {
				groupList.forEach(data -> {
					String statName = data.getStatName();
					Integer cls = data.getCls();
					Integer cars = data.getCars();
					if (String.valueOf(hour - 1).equals(statName)) {
						if (cls == 0) {
							carTotal.setBusCars(cars);
						}
						if (cls == 1) {
							carTotal.setTruckCars(cars);
						}
						if (cls == 2) {
							carTotal.setDangerCars(cars);
						}
					}
				});
			}
			totalList.add(carTotal);
		}
		return totalList;
	}

	private List<Integer> getTwelveHoursAgoList(LocalDateTime now, LocalDateTime twelveHoursAgo) {
		List<Integer> hours = new ArrayList<>();
		LocalDateTime currentTime = twelveHoursAgo;
		while (currentTime.isBefore(now) || currentTime.equals(now)) {
			hours.add(currentTime.getHour() + 1);
			currentTime = currentTime.plusHours(1); // 每次增加一小时
		}
		return hours;
	}

	/**
	 * @描述 统计7日内车流量
	 * @param dto
	 * @return
	 */
	public Object statDayTraffic(RadarAlarmDTO dto) {
		LocalDate now = LocalDate.now();
		LocalDate startDate = now.minusDays(7);// 往前推7天
		List<String> daysList = this.getDatesBetween(startDate, now);
		dto.setStartTime(TimeUtils.getMsTimeStamp(startDate + " 00:00:00 000"));
		dto.setEndTime(System.currentTimeMillis());// 当前时间戳
		List<RadarCarStatVO> statList = tunnelRadarMapper.statDayTraffic(dto);
		// 车类型统计： 0-普通客车 1-普通货车 两客一危（ 2-大客车 3-面包车 4-危化品车）
		if (!CollectionUtils.isEmpty(statList)) {
			List<Integer> types = Arrays.asList(2, 3, 4);
			statList.stream().forEach(x -> {				
				Integer cls = x.getCls();
				if (types.contains(cls)) {// 两客一危类型
					x.setCls(2);
				}
			});
		}
		Map<String, Map<Integer, Integer>> groupResult = statList.stream().collect(Collectors.groupingBy(
				RadarCarStatVO::getStatName,
				Collectors.groupingBy(RadarCarStatVO::getCls, Collectors.summingInt(RadarCarStatVO::getCars))));
		List<RadarCarStatVO> groupList = new ArrayList<RadarCarStatVO>(); // 根据客车、货车进行分组
		groupResult.forEach((statName, typeMap) -> {
			typeMap.forEach((cls, sum) -> {
				RadarCarStatVO vo = new RadarCarStatVO();
				vo.setStatName(statName);
				vo.setCls(cls);
				vo.setCars(sum);
				groupList.add(vo);
			});
		});
		List<RadarCarTotalVO> totalList = new ArrayList<RadarCarTotalVO>();
		for (int i = 0; i < daysList.size(); i++) {
			String day = daysList.get(i);
			RadarCarTotalVO carTotal = new RadarCarTotalVO();
			carTotal.setStatName(day.substring(5));
			carTotal.setTruckCars(0);
			carTotal.setBusCars(0);// 初始值
			carTotal.setDangerCars(0);
			if (!groupList.isEmpty()) {
				groupList.forEach(data -> {
					String statName = data.getStatName();
					Integer cls = data.getCls();
					Integer cars = data.getCars();
					if (day.equals(statName)) {
						if (cls == 0) {
							carTotal.setBusCars(cars);
						}
						if (cls == 1) {
							carTotal.setTruckCars(cars);
						}
						if (cls == 2) {
							carTotal.setDangerCars(cars);
						}
					}
				});
			}
			totalList.add(carTotal);
		}
		return totalList;
	}
	
	public List<String> getDatesBetween(LocalDate startDate, LocalDate endDate) {
		List<String> dates = new ArrayList<>();
		LocalDate currentDate = startDate;
		while (!currentDate.isAfter(endDate)) {
			dates.add(currentDate.toString());
			currentDate = currentDate.plusDays(1);
		}
		return dates;
	}

	public Object selectRadarAlarmPoint(RadarAlarmDTO dto) {
		Long nowTime = System.currentTimeMillis() / 1000; // 当前时间
		if (dto.getStartTime() == null) {
			dto.setStartTime(nowTime - 1 * 3600);// 往前1小时
		}
		dto.setEndTime(nowTime);// 当前时间戳
		List<RadarAlarmPointVO> pointList = tunnelAlarmMapper.selectRadarAlarmPoint(dto);
		Integer code = 0;// 1成功，0失败
		if (!pointList.isEmpty()) {
			code = 1;
			pointList = this.trantPointList(pointList);
		}
		Gson gson = new Gson();
		JsonObject result = new JsonObject();
		result.addProperty("code", code);
		result.add("result", gson.toJsonTree(pointList));
		return gson.toJson(result);
	}

	/**
	 * @描述 查询单个雷达告警的坐标id
	 * @param dto
	 * @return
	 */
	public Object selectPointByAlarmId(RadarAlarmDTO dto) {
		List<RadarAlarmPointVO> pointList = tunnelAlarmMapper.selectRadarAlarmPoint(dto);
		Integer code = 0;// 1成功，0失败
		if (!pointList.isEmpty()) {
			code = 1;
			pointList = this.trantPointList(pointList);
		}
		Gson gson = new Gson();
		JsonObject result = new JsonObject();
		result.addProperty("code", code);
		result.add("result", gson.toJsonTree(pointList));
		return gson.toJson(result);
	}

	/**
	 * @获取计算告警轨迹坐标
	 * @param pointList
	 * @return
	 */
	private List<RadarAlarmPointVO> trantPointList(List<RadarAlarmPointVO> pointList) {
		if (RADAR_POINT_LIST.size() == 0) {
			RADAR_POINT_LIST = radarTwinMapper.selectRadarPointList();
		}
		pointList.stream().forEach(point -> {
			point.setxRatioYk(null);
			point.setIdLane(null);
			point.setLine(null);
			String facilityNo = point.getFacilityNo();
			Double yRatio = StringUtils.isBlank(point.getyRatio()) ? 0.00 : Double.valueOf(point.getyRatio());// 大屏Y轴坐标点
			Double xRatio = StringUtils.isBlank(point.getxRatio()) ? 0.00 : Double.valueOf(point.getxRatio());// 大屏X轴坐标点
			Optional<RadarPointVO> radarPoints = RADAR_POINT_LIST.stream()
					.filter(x -> x.getFacilityNo().equals(facilityNo) && x.getyRatio().equals(yRatio)
							&& x.getxRatio().equals(xRatio))
					.findFirst();
			if (radarPoints.isPresent()) {
				RadarPointVO radarPoint = radarPoints.get();
				if (radarPoint != null) {
					point.setIdLane(radarPoint.getIdLane());
					Integer line = radarPoint.getLine();
					point.setLine(line);
					RadarFuseDataVO fuseData = this.getRadarFuseDataTs(facilityNo, line);
					if (fuseData != null) { // 根据当前设施基础信息反推算云控X轴坐标点
						Double facilityLength = fuseData.getFacilityLength(); // 当前设施长度
						Double nodeStart = fuseData.getNodeStart(); // 开始像素点
						Double nodeLength = fuseData.getNodeLength(); // 像素点长度
						Double progressBar = 0.00; // 当前坐标轴在大屏显示比例
						if (xRatio - nodeStart > 0.00) {
							progressBar = (xRatio - nodeStart) / nodeLength;
						}
						if (progressBar > 0.00) { //
							if (facilityLength <= 1420) {
								point.setxRatioYk(progressBar * 1420);
							} else {
								point.setxRatioYk(progressBar * facilityLength);
							}
						} else {// 根据桩号计算云控坐标
							String milePost = point.getMilePost();// 事件桩号
							if (StringUtils.isNotBlank(milePost)) {
								Integer val1 = MilePostUtils.pileno2IntValue(milePost);
								Integer val2 = MilePostUtils.pileno2IntValue(fuseData.getStartMilePost());
								if (val1 > val2) {
									progressBar = (val1 - val2) / facilityLength;
									if (facilityLength <= 1420) {
										point.setxRatioYk(progressBar * 1420);
									} else {
										point.setxRatioYk(progressBar * facilityLength);
									}
								}
							}
						}
					}
				}
			}
		});
		return pointList;
	}

	public RadarFuseDataVO getRadarFuseDataTs(String facilityNo, Integer line) {
		RadarFuseDataVO vo = null;
		if (StringUtils.isNotBlank(facilityNo) && line != null) {
			updateRadarFuseData();// 更新雷达设施对应关系
			for (RadarFuseDataVO data : RadarTwinService.RADAR_FUSE_DATA) {
				if (data.getFacilityNo().equals(facilityNo) && data.getLine().equals(line)) {
					vo = data;
					break;
				}
			}
		}
		return vo;
	}
}
