package com.bt.itstunnel.service;

import com.bt.itscore.exception.ArgumentException;
import com.bt.itstunnel.domain.dto.CommonParamsDTO;
import com.bt.itstunnel.domain.dto.TunnelLightTaskDTO;
import com.bt.itstunnel.domain.vo.TunnelLightTaskVO;
import com.bt.itstunnel.domain.vo.TunnelLightTypeVO;
import com.bt.itstunnel.mapper.TunnelLightTaskMapper;
import com.bt.itstunnel.utils.RegexUtils;
import com.bt.itstunnel.utils.TimeUtils;
import com.bt.itstunnel.utils.TunnelDeviceConstant;
import com.google.gson.Gson;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 描述：隧道定时照明灯光任务业务层
 *
 * <AUTHOR>
 * @since 2022-12-05
 */
@Service("tunnelLightTaskService")
public class TunnelLightTaskService {
    private static final Log LOGGER = LogFactory.getLog(TunnelLightTaskService.class);

    private static final int MAX_LENGTH = 1000;

    @Autowired
    private TunnelLightTaskMapper tunnelLightTaskMapper;

    @Autowired
    private TunnelDeviceOptionService tunnelDeviceOptionService;

    public TunnelLightTypeVO selectLightTypeInfo(String tunnelId) {
        if (StringUtils.isEmpty(tunnelId) || tunnelId.length() > MAX_LENGTH) {
            throw new ArgumentException("隧道ID为空或者参数超限异常");
        }
        TunnelLightTypeVO typeVo = new TunnelLightTypeVO();
        // 查询左边路线的类型并去重
        List<Integer> leftLightTypeList = tunnelLightTaskMapper.selectLightTypeByFacilityAndLineAndType(tunnelId,
                TunnelDeviceConstant.DEVICE_TYPE, TunnelDeviceConstant.LEFT_LINE_TYPE);
        typeVo.setLeftLightType(leftLightTypeList);
        // 查询右边路线的类型并去重
        List<Integer> rightLightTypeList = tunnelLightTaskMapper.selectLightTypeByFacilityAndLineAndType(tunnelId,
                TunnelDeviceConstant.DEVICE_TYPE, TunnelDeviceConstant.RIGHT_LINE_TYPE);
        typeVo.setRightLightType(rightLightTypeList);
        return typeVo;
    }

    public List<TunnelLightTaskVO> selectTaskList(String facilityNo) {
        if (StringUtils.isEmpty(facilityNo) || facilityNo.length() > MAX_LENGTH) {
            throw new ArgumentException("隧道ID为空或者参数超长异常");
        }
        return tunnelLightTaskMapper.selectTaskByFacilityNoAndLine(facilityNo, null, null);
    }

    public boolean addTask(TunnelLightTaskDTO taskDTO, String userName) {
        // 校验时间范围
        int startTime = TimeUtils.parseHourStrToMin(taskDTO.getStartTime());
        int endTime = TimeUtils.parseHourStrToMin(taskDTO.getEndTime());
        boolean isNormal = TimeUtils.validMaxDayMinute(startTime);
        boolean isNormalTwo = TimeUtils.validMaxDayMinute(endTime);

        Integer isNextDay = taskDTO.getIsNextDay(); // 跨天时间段计算补偿
        if (endTime < startTime && isNextDay == 1) {
            endTime = endTime + TunnelDeviceConstant.NEXT_DAY_MINUTE_PATCH;
        }
        if (!isNormal || !isNormalTwo) {
            throw new ArgumentException("设置时间参数范围超过最大限定值");
        }
        String facilityNo = taskDTO.getFacilityNo();
        Integer lineType = taskDTO.getLineType();
        // 需要检查当前的隧道定时任务是否存在重合，有则报错
        List<TunnelLightTaskVO> lightTaskDtoList = tunnelLightTaskMapper.selectTaskByFacilityNoAndLine(facilityNo,
                lineType, null);
        // 检查时间重合冲突
        if (checkTimeConflict(lightTaskDtoList, startTime, endTime, taskDTO.getLightTypeArray())) {
            throw new ArgumentException("当前任务时间设置与其他任务时间存在冲突，请重新设置时间");
        }
        // 直接插入
        taskDTO.setId(UUID.randomUUID().toString());
        taskDTO.setStatus(TunnelDeviceConstant.TASK_OPEN_STATUS);
        taskDTO.setStartTimeOrder(TunnelDeviceConstant.LIGHT_OPEN_PLC_CONTROL);
        taskDTO.setEndTimeOrder(TunnelDeviceConstant.LIGHT_CLOSE_PLC_CONTROL);
        taskDTO.setCreator(userName);
        taskDTO.setCreateTime(new Date());
        return tunnelLightTaskMapper.insert(taskDTO) > 0;
    }

    /**
     * 时间冲突校验规则：
     * ①相同道路，线路，照明类型（有多个的情况，判定是否包含，又包含的也算），三个条件同时满足才会进行时间冲突校验
     * ②A时间段包含B，B时间段包含A都认为是冲突的
     *
     * @param lightTaskDtoList 查询返回的结果
     * @param startTime        当前传入开始时间
     * @param endTime          当前传入结束时间
     * @param lightTypeArray   当前传入的照明类型数组
     * @return 布尔值，true-冲突，false-不冲突
     */
    private boolean checkTimeConflict(List<TunnelLightTaskVO> lightTaskDtoList, int startTime, int endTime,
                                      List<String> lightTypeArray) {
        if (CollectionUtils.isEmpty(lightTaskDtoList)) {
            return false;
        }
        for (TunnelLightTaskVO lightTaskVO : lightTaskDtoList) {
            // 校验照明类型是否被包含
            Set<String> containsSet = new HashSet<>(lightTaskVO.getLightTypeArray());
            boolean isNotContains = true; // 默认不包含
            for (String type : lightTypeArray) {
                if (containsSet.contains(type)) {
                    LOGGER.warn("The light type has bean contains.");
                    isNotContains = false;
                    break;
                }
            }
            if (isNotContains) {
                return false;
            }
            int startTimeOld = TimeUtils.parseHourStrToMin(lightTaskVO.getStartTime());
            int endTimeOld = TimeUtils.parseHourStrToMin(lightTaskVO.getEndTime());
            if (endTimeOld < startTimeOld && lightTaskVO.getIsNextDay() == 1) {
                endTimeOld = endTimeOld + TunnelDeviceConstant.NEXT_DAY_MINUTE_PATCH; // 补偿跨天的时间段
            }
            // 不重合的情况取反，得到存在重合冲突的结果
            boolean isNotConflict = startTime > endTimeOld || startTimeOld > endTime;
            if (!isNotConflict) {
                LOGGER.error("The time part is conflicted.startTimeOld: " + lightTaskVO.getStartTime() +
                        ",endTimeOld: " + lightTaskVO.getEndTime());
                return true;
            }
        }
        return false;
    }

    public boolean updateTask(TunnelLightTaskDTO taskDTO, String userName) {
        String id = taskDTO.getId();
        Integer status = taskDTO.getStatus();
        boolean isOK = RegexUtils.regexStr(TunnelDeviceConstant.REGEX_STATUS, status.toString());
        if (StringUtils.isEmpty(id) || id.length() > MAX_LENGTH || !isOK) {
            throw new ArgumentException("传入的参数ID或状态值不能为空");
        }
        TunnelLightTaskVO lightTaskVO = tunnelLightTaskMapper.selectTaskById(id);
        if (lightTaskVO == null) {
            throw new ArgumentException("参数ID异常，请重新传入正确的值");
        }
        lightTaskVO.setStatus(status);
        lightTaskVO.setCreator(userName);
        lightTaskVO.setUpdateTime(new Date());
        return tunnelLightTaskMapper.update(lightTaskVO) > 0;
    }

    public boolean deleteTask(TunnelLightTaskDTO taskDTO, String userName) {
        String id = taskDTO.getId();
        if (StringUtils.isEmpty(id) || id.length() > MAX_LENGTH) {
            throw new ArgumentException("传入的参数ID或状态值不能为空");
        }
        TunnelLightTaskVO lightTaskVO = tunnelLightTaskMapper.selectTaskById(id);
        if (lightTaskVO == null) {
            // 幂等处理，不存在的数据按照删除成功处理
            LOGGER.warn("The light task is not exist,id: " + id);
            return true;
        }
        return tunnelLightTaskMapper.delete(id) > 0;
    }

    public List<Map<String, String>> operateLightPlcSwitch() {
        LOGGER.info("定时任务被拉起");
        // 结果集列表
        List<Map<String, String>> resultList = new ArrayList<>();
        // 查询并分批处理，
        int total = tunnelLightTaskMapper.countTask(TunnelDeviceConstant.TASK_OPEN_STATUS);
        if (total == 0) {
            LOGGER.info("The count start tunnel task is " + total);
            return resultList;
        }
        // 每批次最大处理1000条,基于照明设备是10倍估算，也会进行1W次操作请求了
        int maxSize = TunnelDeviceConstant.MAX_SIZE;
        int circle = total / maxSize;
        int reminder = total % maxSize;


        // 获取当天时间的分钟值对比
        int currentTime = TimeUtils.getTotalMinuteOfDay();
        for (int i = 0; i < circle; i++) {
            this.handleScheduleOperateLight(maxSize, currentTime, i, resultList);
        }
        // 处理剩余量
        if (reminder != 0) {
            this.handleScheduleOperateLight(reminder, currentTime, circle, resultList);
        }
        LOGGER.info("tunnel light operate result：" + new Gson().toJson(resultList));
        return resultList;
    }

    private void handleScheduleOperateLight(int maxSize, int currentTime, int currentCount,
                                            List<Map<String, String>> resultList) {
        // 定时任务拉起间隔是10min，考虑服务交互存在延迟，设定正向波动范围是5min
        final int timeRange = 5;
        List<TunnelLightTaskVO> tunnelLightTaskList =
                tunnelLightTaskMapper.selectTaskByStatus(TunnelDeviceConstant.TASK_OPEN_STATUS,
                        currentCount * maxSize, maxSize);
        for (TunnelLightTaskVO lightTaskVO : tunnelLightTaskList) {
            int startTime = TimeUtils.parseHourStrToMin(lightTaskVO.getStartTime());
            int isStartOpen = lightTaskVO.getIsStartOpen();
            int isEndClose = lightTaskVO.getIsEndClose();
            // 处理任务的开启
            if (currentTime >= startTime && currentTime - startTime <= timeRange) {
                if (isStartOpen == 0 || (isStartOpen == 1 && isEndClose == 1)) {
                    LOGGER.info("The light task startTime: " + lightTaskVO.getStartTime());
                    resultList.addAll(this.batchOperateLightPlc(lightTaskVO,
                            TunnelDeviceConstant.LIGHT_OPEN_PLC_CONTROL));
                }
            }
            // 处理任务的关闭
            int endTime = TimeUtils.parseHourStrToMin(lightTaskVO.getEndTime());
            if (currentTime >= endTime && currentTime - endTime <= timeRange && isEndClose == 0) {
                resultList.addAll(this.batchOperateLightPlc(lightTaskVO, TunnelDeviceConstant.LIGHT_CLOSE_PLC_CONTROL));
            }
        }
    }

    private List<Map<String, String>> batchOperateLightPlc(TunnelLightTaskVO lightTaskVO, int operateCode) {
        // 照明灯操作者，默认系统管理员
        final String operator = "系统管理员";
        // 查询该隧道涉及的照明灯设备列表并启动PLC控制开关
        List<String> plcIdList =
                tunnelLightTaskMapper.selectPlcIdsByFacilityNoAndLineAndType(lightTaskVO.getTunnelId(),
                        TunnelDeviceConstant.DEVICE_TYPE, lightTaskVO.getLineType(), lightTaskVO.getLightTypeArray());
        // 装载PLC设备执行操作的结果
        List<Map<String, String>> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(plcIdList)) {
            LOGGER.warn("Could not find any plc of device,the facility number: " + lightTaskVO.getTunnelId());
            return list;
        }
        for (String plcId : plcIdList) {
            CommonParamsDTO commonParamsDTO = new CommonParamsDTO();
            commonParamsDTO.setPlcId(plcId);
            commonParamsDTO.setOperationName(TunnelDeviceConstant.LIGHT_SWITCH_MANAGE + "-" + operateCode);
            list.add(tunnelDeviceOptionService.operationPlcDevice(commonParamsDTO, operator));
        }
        // 更新数据isStartOpen为开启（1），关闭是（2）--不保证全部成功，只是标记已经操作过了
        if (operateCode == 1) {
            lightTaskVO.setIsStartOpen(1);
            lightTaskVO.setIsEndClose(0);
        }
        if (operateCode == 2) {
            lightTaskVO.setIsEndClose(1);
        }
        tunnelLightTaskMapper.update(lightTaskVO);
        // 记录下日志，方便查看过程(详细操作日志查看tunnel_operatelog表记录)
        int plcSize = plcIdList.size();
        int operateSize = list.size();
        if (plcSize == operateSize) {
            LOGGER.info("Operate light switch is succeeded, the size: " + operateSize + ", operateCode: " + operateCode);
        } else {
            LOGGER.error("Operate light switch is failed, the failed size: " + (plcSize - operateSize));
        }
        return list;
    }
}
