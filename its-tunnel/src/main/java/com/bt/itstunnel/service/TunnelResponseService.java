package com.bt.itstunnel.service;

import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bt.itstunnel.domain.dto.TunnelResponseDTO;
import com.bt.itstunnel.mapper.TunnelResponseMapper;

@Service("tunnelResponseService")
public class TunnelResponseService {
	@Autowired
	TunnelResponseMapper tunnelResponseMapper;

	public boolean add(TunnelResponseDTO tunnelResponseDTO) {
		tunnelResponseDTO.setId(UUID.randomUUID().toString());
		return tunnelResponseMapper.add(tunnelResponseDTO) > 0;
	}

	public boolean update(TunnelResponseDTO tunnelResponseDTO) {
		return tunnelResponseMapper.update(tunnelResponseDTO) > 0;
	}
}
