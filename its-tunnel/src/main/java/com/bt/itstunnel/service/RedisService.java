package com.bt.itstunnel.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.bt.itscore.domain.vo.DevicePbxCacheVO;
import com.bt.itstunnel.common.CommonCacheAction;
import com.bt.itstunnel.common.CommonFunction;
import com.bt.itstunnel.domain.dto.TunnelResponseDTO;
import com.bt.itstunnel.domain.entity.LightingResPoseBean;
import com.bt.itstunnel.domain.entity.TunnelResPoseBean;
import com.bt.itstunnel.domain.vo.TunnelPlcDeviceInfoVO;
import com.bt.itstunnel.mapper.TunnelPlcDeviceInfoMapper;

@Service("redisService")
public class RedisService {
	@Autowired
	private RedisTemplate<String, Object> redisTemplate;
	@Autowired
	private TunnelPlcDeviceInfoMapper tunnelPlcDeviceInfoMapper;

	public boolean hashKeyBoolean(String cacheKey, String hashKey) {
		return !redisTemplate.opsForHash().hasKey(cacheKey, hashKey);
	}

	/**
	 * 更新缓存设备状态
	 * @param deviceCode
	 * @param tunnelResPoseBean
	 */

	public void setDeviceStateMap(String deviceCode, TunnelResPoseBean tunnelResPoseBean) {
		redisTemplate.opsForHash().put(CommonFunction.TUNNEL_PLC_DEVICE_STATE_MAP, deviceCode, tunnelResPoseBean);
	}

	/**
	 * 获取缓存隧道设备状态
	 * @param deviceCode
	 * @return
	 */
	public TunnelResPoseBean getTunnelResPoseBean(String deviceCode) {
		return (TunnelResPoseBean) redisTemplate.opsForHash().get(CommonFunction.TUNNEL_PLC_DEVICE_STATE_MAP,
				deviceCode);
	}

	/**
	 * 获取缓存设备状态
	 * @return
	 */
	public Map<String, String> selectDeviceStatus() {
		Map<String, String> deviceStatusMap = new HashMap<String, String>();
		List<Object> list = redisTemplate.opsForHash().values(CommonFunction.TUNNEL_PLC_DEVICE_STATE_MAP);
		if (!CollectionUtils.isEmpty(list)) {
			list.stream().forEach(obj -> {
				TunnelResPoseBean bean = (TunnelResPoseBean) obj;
				deviceStatusMap.put(bean.getDeviceCode(), bean.getDeviceState());
			});
		}
		return deviceStatusMap;
	}

	/**
	 * 获取缓存设备离线状态时间
	 * @return
	 */
	public Map<String, Long> selectDeviceOfflineTime() {
		Map<String, Long> deviceStatusMap = new HashMap<String, Long>();
		List<Object> list = redisTemplate.opsForHash().values(CommonFunction.TUNNEL_PLC_DEVICE_STATE_MAP);
		if (!CollectionUtils.isEmpty(list)) {
			list.stream().forEach(obj -> {
				TunnelResPoseBean bean = (TunnelResPoseBean) obj;
				String deviceState = bean.getDeviceState();
				if (StringUtils.isBlank(deviceState)) {
					deviceStatusMap.put(bean.getDeviceCode(), bean.getOfflineTime());
				}
				if ("LightingControl".equals(bean.getTypeCode()) && deviceState.indexOf("网络不通") > 0) {
					deviceStatusMap.put(bean.getDeviceCode(), bean.getOfflineTime());
				}
			});
		}
		return deviceStatusMap;
	}

	/**
	 * @描述 获取缓存设备集合
	 */
	public List<TunnelResPoseBean> getTunnelResPoseBeanList() {
		List<TunnelResPoseBean> result = new ArrayList<TunnelResPoseBean>();
		List<Object> list = redisTemplate.opsForHash().values(CommonFunction.TUNNEL_PLC_DEVICE_STATE_MAP);
		if (!CollectionUtils.isEmpty(list)) {
			list.stream().forEach(obj -> {
				result.add((TunnelResPoseBean) obj);
			});
		}
		return result;
	}

	/**
	 * 获取设备缓存实时状态
	 * @return
	 */
	public TunnelResPoseBean getInitCacheTunnelResPoseBean(String deviceCode, int type) {
		TunnelResPoseBean bean = null;
		if (type == 0) {
			CommonCacheAction.PLC_DEVICE_INFO_LIST = tunnelPlcDeviceInfoMapper.list();// 刷新隧道设备信息缓存
			TunnelPlcDeviceInfoVO info = CommonCacheAction.PLC_DEVICE_INFO_LIST.stream()
					.filter(x -> x.getCode().equals(deviceCode)).findFirst().orElse(null);
			if (info != null) {
				bean = CommonCacheAction.getTunnelResPoseBeanProtity(info);
			}

		} else {
			String cacheKey = CommonFunction.TUNNEL_PLC_DEVICE_STATE_MAP;// 初始化设备列表和设备状态
			List<TunnelPlcDeviceInfoVO> list = CommonCacheAction.PLC_DEVICE_INFO_LIST;
			if (!CollectionUtils.isEmpty(list)) {
				Map<String, TunnelResPoseBean> PLC_DEVICE_STATE_MAP = new ConcurrentHashMap<String, TunnelResPoseBean>();
				list.forEach(a -> {
					PLC_DEVICE_STATE_MAP.put(a.getCode(), CommonCacheAction.getTunnelResPoseBeanProtity(a));
				});
				bean = PLC_DEVICE_STATE_MAP.get(deviceCode);
				redisTemplate.delete(cacheKey);
				redisTemplate.opsForHash().putAll(cacheKey, PLC_DEVICE_STATE_MAP);
			}
		}
		return bean;
	}

	public boolean hasKey(String key) {
		return redisTemplate.hasKey(key);
	}

	/**
	 * @param msgSeq 检查指定发送的消息是否已经收到返回值
	 * @return
	 */
	public boolean isResposeMsgSeq(String msgSeq) {
		boolean flag = false;
		String cacheKey = CommonFunction.TUNNEL_PLC_MODIFY_lIST;// 状态反馈
		List<Object> modifyList = redisTemplate.opsForList().range(cacheKey, 0, -1);
		if (!CollectionUtils.isEmpty(modifyList)) {
			for (int i = 0; i < modifyList.size(); i++) {
				TunnelResponseDTO response = (TunnelResponseDTO) modifyList.get(i);
				if (response.getCmdSeq().equals(msgSeq)) {
					redisTemplate.opsForList().remove(cacheKey, i, response);
					if (response.getCmdState().equalsIgnoreCase("Err")) {
						throw new RuntimeException("服务应答提示出错(" + response.toString() + ")");
					}
					flag = true;
				} else if ((System.currentTimeMillis() - response.getReceivTimes()) > 4000) {
					redisTemplate.opsForList().remove(cacheKey, i, response);
				}
			}
		}
		return flag;
	}

	/**
	 * 保存更新缓存设备控制反馈状态
	 * @param msgSeq
	 * @param response
	 */
	public void setTunnelResponseList(TunnelResponseDTO response) {
		redisTemplate.opsForList().leftPush(CommonFunction.TUNNEL_PLC_MODIFY_lIST, response);
	}

	/**
	 * 调光缓存操作
	 * @param msgSeq
	 * @return
	 */
	public LightingResPoseBean getLightingResult(String msgSeq) {
		String cacheKey = CommonFunction.TUNNEL_LIGHTING_RESULT_lIST;// 缓存信息key
		LightingResPoseBean lightingResPoseBean = new LightingResPoseBean();
		List<Object> result = redisTemplate.opsForList().range(cacheKey, 0, -1);
		if (!CollectionUtils.isEmpty(result)) {
			for (int i = 0; i < result.size(); i++) {
				LightingResPoseBean resPoseBean = (LightingResPoseBean) result.get(i);
				if (resPoseBean.getCmdSeq().equals(msgSeq)) {
					lightingResPoseBean = resPoseBean;
					redisTemplate.opsForList().remove(cacheKey, i, resPoseBean);
				} else if ((System.currentTimeMillis() - resPoseBean.getReceivTimes()) > 3000) {
					redisTemplate.opsForList().remove(cacheKey, i, resPoseBean);
				}
			}
		}
		return lightingResPoseBean;
	}

	/**
	 * 保存更新缓存设备控制反馈状态
	 * @param msgSeq
	 * @param response
	 */
	public void setTunnelLightingResult(LightingResPoseBean resBean) {
		redisTemplate.opsForList().leftPush(CommonFunction.TUNNEL_LIGHTING_RESULT_lIST, resBean);
	}

	/**
	 * 更新风机操作时间
	 * @param deviceCode
	 * @param time
	 */
	public void setTunnelFanTime(String deviceCode, long time) {
		redisTemplate.opsForHash().put(CommonFunction.TUNNEL_FAN_OPERATE_MAP, deviceCode, time);
	}

	/**
	 * 获取风机操作时间
	 * @param deviceCode
	 * @return
	 */
	public long getTunnelFanTime(String deviceCode) {
		Object time = redisTemplate.opsForHash().get(CommonFunction.TUNNEL_FAN_OPERATE_MAP, deviceCode);
		return time == null ? 0L : Long.valueOf(time.toString());
	}

	/**
	 * 更新设备保存存储时间
	 * @param deviceCode
	 * @param time
	 */
	public void setDeviceAlarmTime(String deviceCode, long time) {
		redisTemplate.opsForHash().put(CommonFunction.TUNNEL_DEVICE_ALARM_MAP, deviceCode, time);
	}

	/**
	 * 获取设备报警存储时间
	 * @param deviceCode
	 * @return
	 */
	public long getDeviceAlarmTime(String deviceCode) {
		Object time = redisTemplate.opsForHash().get(CommonFunction.TUNNEL_DEVICE_ALARM_MAP, deviceCode);
		return time == null ? 0L : Long.valueOf(time.toString());
	}

	/**
	 * 获取缓存sip电话配置信息
	 * @param deviceCode
	 * @return
	 */
	public DevicePbxCacheVO getDevicePbxCache(String deviceCode) {
		Object object = redisTemplate.opsForHash().get(CommonFunction.PBX_DEVICE, deviceCode);
		if (object != null) {
			return (DevicePbxCacheVO) object;
		} else {
			return null;
		}
	}

	/**
	 * @描述 存储故障数据
	 * @param type:1-新增 2-修改最新记录 3-更新所有其他记录
	 */
	// 存储故障数据
	public void setDeviceWorkErrMap(String deviceCode, TunnelResPoseBean tunnelResPoseBean, Integer type) {
		String cacheKey = getDeviceErrCache(type);
		redisTemplate.opsForHash().put(cacheKey, deviceCode, tunnelResPoseBean);
	}

	/**
	 * @描述 获取故障集合
	 * @param  type:1-新增 2-修改最新记录 3-更新所有其他记录
	 * @return
	 */
	public List<TunnelResPoseBean> getDeviceWorkErrList(Integer type) {
		String cacheKey = getDeviceErrCache(type);
		List<Object> list = redisTemplate.opsForHash().values(cacheKey);
		List<TunnelResPoseBean> result = new ArrayList<TunnelResPoseBean>();
		if (!CollectionUtils.isEmpty(list)) {
			list.stream().forEach(obj -> {
				result.add((TunnelResPoseBean) obj);
			});
		}
		return result;
	}

	/**
	 * @描述 清理缓存
	 * @param type
	 */
	public void deleteDeviceWorkErrCache(Integer type) {
		String cacheKey = getDeviceErrCache(type);
		if (hasKey(cacheKey)) { // 清理缓存状态
			redisTemplate.delete(cacheKey);
		}
	}

	private String getDeviceErrCache(Integer type) {
		String cacheKey = "";// 缓存键值
		switch (type) {
		case 1:
			cacheKey = CommonFunction.TUNNEL_ERR_SAVE_MAP;
			break;
		case 2:
			cacheKey = CommonFunction.TUNNEL_ERR_MODIFY_MAP;
			break;
		case 3:
			cacheKey = CommonFunction.TUNNEL_ERR_UPDATE_MAP;
			break;
		}
		return cacheKey;
	}

	/**
	 * @描述 存储上次报警时间
	 */
	public void setAlarmRecordTime(String keyAlarm) {
		redisTemplate.opsForHash().put(CommonFunction.TUNNEL_ALARM_RECORD_MAP, keyAlarm, System.currentTimeMillis());
	}

	/**
	 * @描述 获取上次报警时间
	 */
	public long getAlarmRecordTime(String keyAlarm) {
		Object time = redisTemplate.opsForHash().get(CommonFunction.TUNNEL_ALARM_RECORD_MAP, keyAlarm);
		return time == null ? 0L : Long.valueOf(time.toString());
	}

	/**
	 * @描述 删除指定缓存信息
	 */
	public void deleteAlarmRecordTime(String keyAlarm) {
		redisTemplate.opsForHash().delete(CommonFunction.TUNNEL_ALARM_RECORD_MAP, keyAlarm);
	}

	/**
	 * @描述 删除缓存List中所有对象
	 */
	public void trimListToEmpty(String listKey) {
		redisTemplate.opsForList().trim(listKey, 1, 0);// 清空指定键对应的列表而不删除键
	}

	/**
	 * @描述 删除缓存Map中所有对象
	 */
	public void deleteAllFieldsFromHash(String hashKey) {
		// 获取哈希操作对象
		HashOperations<String, Object, Object> opsForHash = redisTemplate.opsForHash();
		// 获取哈希中所有的字段名称
		Set<Object> fields = opsForHash.keys(hashKey);
		// 如果字段列表不为空，则删除所有字段
		if (fields != null && !fields.isEmpty()) {
			opsForHash.delete(hashKey, fields.toArray());
		} else {
			System.out.println("No fields to delete in the hash: " + hashKey);
		}
	}

}
