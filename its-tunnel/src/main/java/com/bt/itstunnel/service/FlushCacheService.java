package com.bt.itstunnel.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bt.itstunnel.common.CommonCacheAction;
import com.bt.itstunnel.domain.entity.RabbitmqMapBean;
import com.bt.itstunnel.domain.vo.TunnelSystemConfigVO;
import com.bt.itstunnel.mapper.TunnelLightingControlMapper;
import com.bt.itstunnel.mapper.TunnelPlcStateMapper;
import com.bt.itstunnel.mapper.TunnelRabbitmqMapper;
import com.bt.itstunnel.mapper.TunnelSystemConfigMapper;

@Service("flushCacheService")
public class FlushCacheService {
	@Autowired
	TunnelSystemConfigMapper tunnelSystemConfigMapper;
	@Autowired
	private TunnelPlcStateMapper tunnelPlcStateMapper;
	@Autowired
	private TunnelRabbitmqMapper tunnelRabbitmqMapper;
	@Autowired
	private TunnelLightingControlMapper tunnelLightingControlMapper;
	/**
	 * 刷新JVM缓存信息
	 */
	public void flushInitParams() {
		// 1、初始化设备图标列表
		CommonCacheAction.PLC_STATE_LIST = tunnelPlcStateMapper.list();
		CommonCacheAction.LIGHTING_CONTROL_LIST=tunnelLightingControlMapper.list();
		// 2、初始化系统配置参数
		List<TunnelSystemConfigVO> list = tunnelSystemConfigMapper.list();
		if (list.size() > 0) {
			for (TunnelSystemConfigVO sys : list) {
				CommonCacheAction.SYS_PARAMS_MAP.put(sys.getParamName(), sys.getParamValue());
			}
		}
		// 3、初始化设备对应提交指令MQ服务器
		List<RabbitmqMapBean> rabbitmqMapList = tunnelRabbitmqMapper.listRabbitmqMapBean();
		if (rabbitmqMapList.size() > 0) {
			for (RabbitmqMapBean bean : rabbitmqMapList) {
				CommonCacheAction.RABBIT_MQ_MAP.put(bean.getDeviceCode(), bean);
			}
		}
	}

}
