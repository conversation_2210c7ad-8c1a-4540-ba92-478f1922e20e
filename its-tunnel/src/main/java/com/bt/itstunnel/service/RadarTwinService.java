package com.bt.itstunnel.service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.TreeSet;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.bt.itscore.domain.dto.IdStringDTO;
import com.bt.itscore.domain.dto.PageDTO;
import com.bt.itscore.domain.vo.ResponseVO;
import com.bt.itscore.utils.MilePostUtils;
import com.bt.itscore.utils.NumbersUtils;
import com.bt.itstunnel.common.CommonFunction;
import com.bt.itstunnel.domain.dto.CommonParamsDTO;
import com.bt.itstunnel.domain.dto.RadarAlarmDTO;
import com.bt.itstunnel.domain.dto.RadarPointDTO;
import com.bt.itstunnel.domain.dto.RadarUserDTO;
import com.bt.itstunnel.domain.vo.EventVO;
import com.bt.itstunnel.domain.vo.RadarCameraVO;
import com.bt.itstunnel.domain.vo.RadarDevice2VO;
import com.bt.itstunnel.domain.vo.RadarFacilityLineVO;
import com.bt.itstunnel.domain.vo.RadarFacilityVO;
import com.bt.itstunnel.domain.vo.RadarFuseDataVO;
import com.bt.itstunnel.domain.vo.RadarPointVO;
import com.bt.itstunnel.domain.vo.RadarUserVO;
import com.bt.itstunnel.domain.vo.TunnelPlcDeviceInfoVO;
import com.bt.itstunnel.mapper.RadarTwinMapper;
import com.bt.itstunnel.mapper.TunnelPlcDeviceInfoMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

@EnableScheduling // 开启定时任务
@Service("radarTwinService")
public class RadarTwinService {
	private final static Logger LOGGER = LoggerFactory.getLogger(RadarTwinService.class);
	@Autowired
	private TunnelPlcDeviceInfoMapper tunnelPlcDeviceInfoMapper;
	@Autowired
	private RadarTwinMapper radarTwinMapper;

	@SuppressWarnings("rawtypes")
	@Autowired
	private RedisTemplate redisTemplate;
	@Autowired
	private StringRedisTemplate stringRedisTemplate;
	public final static List<Integer> tids = new ArrayList<>();
	/** 与雷达对接方的关联数据 **/
	protected static List<RadarFuseDataVO> RADAR_FUSE_DATA = new ArrayList<>();

	public List<TunnelPlcDeviceInfoVO> selectRadarList(CommonParamsDTO commonParamsDTO) {
		commonParamsDTO.setTypeId("62");// 雷达设备类型
		return tunnelPlcDeviceInfoMapper.selectPlcDeviceInfo(commonParamsDTO);
	}

	@SuppressWarnings("unchecked")
	public boolean updateCache() {
		RADAR_FUSE_DATA = radarTwinMapper.selectRadarFuseData();
		redisTemplate.opsForValue().set(CommonFunction.RADAR_USER_ID, radarTwinMapper.getRadarUsers(),
				CommonFunction.TIMEOUT, TimeUnit.HOURS);// 默认缓存2小时
		return true;
	}

	@SuppressWarnings("unchecked")
	public List<String> getRadarUserIds() {
		String cacheKey = CommonFunction.RADAR_USER_ID;// 隧道雷达用户缓存
		List<String> list = (List<String>) redisTemplate.opsForValue().get(cacheKey);
		if (CollectionUtils.isEmpty(list)) {
			list = radarTwinMapper.getRadarUsers();
			redisTemplate.opsForValue().set(cacheKey, list, CommonFunction.TIMEOUT, TimeUnit.HOURS);// 默认缓存2小时
		}
		return list;
	}

	/**
	 * 清除缓存数据，重新加载
	 */
	@SuppressWarnings("unchecked")
	public void fashRadarCache() {
		// 隧道雷达用户缓存
		String cache_useIds = CommonFunction.RADAR_USER_ID;
		redisTemplate.delete(cache_useIds);
		redisTemplate.opsForValue().set(cache_useIds, radarTwinMapper.getRadarUsers(), CommonFunction.TIMEOUT,
				TimeUnit.HOURS);// 默认缓存2小时
		// 隧道雷达坐标缓存
		String cache_radarPointList = CommonFunction.RADAR_POINT_lIST;
		redisTemplate.delete(cache_radarPointList);
		redisTemplate.opsForValue().set(cache_radarPointList, radarTwinMapper.selectRadarPointList(),
				CommonFunction.TIMEOUT, TimeUnit.HOURS);// 默认缓存2小时
		// 隧道雷达基础缓存
		String cache_radarFuseDataList = CommonFunction.RADAR_FUSE_DATA_lIST;
		redisTemplate.delete(cache_radarFuseDataList);
		redisTemplate.opsForValue().set(cache_radarFuseDataList, radarTwinMapper.selectRadarFuseData(),
				CommonFunction.TIMEOUT, TimeUnit.HOURS);// 默认缓存2小时
		// 隧道雷达设备缓存
		String cache_radarDeviceList = CommonFunction.RADAR_DEVICE_LIST;
		redisTemplate.delete(cache_radarDeviceList);
		redisTemplate.opsForValue().set(cache_radarDeviceList, radarTwinMapper.selectRadarDevice(),
				CommonFunction.TIMEOUT, TimeUnit.HOURS);// 默认缓存2小时

	}

	public List<RadarCameraVO> selectRadarCamera(String deviceId) {
		return radarTwinMapper.selectRadarCamera(deviceId);
	}

	public List<RadarPointVO> selectRadarPoint(RadarPointDTO dto) {
		return radarTwinMapper.selectRadarPointsList(dto);
	}

	public void tunnelHeartbeat(IdStringDTO dto) {
		String id = dto.getId();
		Integer tid = 0;
		for (RadarFuseDataVO vo : RadarTwinService.RADAR_FUSE_DATA) {
			String facilityNo = vo.getFacilityNo();
			if (facilityNo.equals(id)) {
				tid = vo.getTid();
				break;
			}
		}
		stringRedisTemplate.opsForValue().set("tunnel:radartwin-tid-" + tid, System.currentTimeMillis() + "");
	}

	@Scheduled(cron = "0/20 * * * * ?")
	public void checkTunnelHeartbeat() {
		// LOGGER.info("tunnels:{}", tunnels.size());
		if (CollectionUtils.isEmpty(tids)) {
			return;
		}
		for (Integer tid : tids) {
			String key = "tunnel:radartwin-tid-" + tid;
			String hearbeatTime = stringRedisTemplate.opsForValue().get(key);
			if (hearbeatTime != null) {
				long hearbeatTimeL = NumberUtils.toLong(hearbeatTime);
				if (System.currentTimeMillis() - hearbeatTimeL > 60000) {// 超过60秒，停止解析隧道雷达数据坐标
					if (tid != null && RadarTwinService.tids.contains(tid)) {
						RadarTwinService.tids.remove(tid);
					}
					LOGGER.info("关闭隧道：{}", tid);
					stringRedisTemplate.delete("tunnel:radartwin-tid-" + tid);
				}
			}
		}
		LOGGER.info("执行定时任务checkTunnelHeartbeat-{}", LocalDateTime.now());
	}

	public void openTunnel(IdStringDTO dto) {
		String id = dto.getId();
		Integer tid = 0;
		if (CollectionUtils.isEmpty(RadarTwinService.RADAR_FUSE_DATA)) {
			updateCache();
		}
		for (RadarFuseDataVO vo : RadarTwinService.RADAR_FUSE_DATA) {
			String facilityNo = vo.getFacilityNo();
			if (facilityNo.equals(id)) {
				tid = vo.getTid();
				break;
			}
		}
		if (tid > 0 && !RadarTwinService.tids.contains(tid)) {
			LOGGER.info("openTunnel-{}", tid);
			RadarTwinService.tids.add(tid);
		}
	}

	public List<RadarFuseDataVO> selectTunnelFacilityNoTid() {
		if (CollectionUtils.isEmpty(RADAR_FUSE_DATA)) {
			RADAR_FUSE_DATA = radarTwinMapper.selectRadarFuseData();
		}
		return RADAR_FUSE_DATA;
	}

	public PageInfo<RadarUserVO> pageRadarUser(RadarUserDTO dto, PageDTO pageDTO) {
		PageHelper.startPage(pageDTO.getPage(), pageDTO.getLimit());
		List<RadarUserVO> list = radarTwinMapper.selectRadarUser(dto);
		return new PageInfo<>(list);
	}

	public ResponseVO saveRadarUser(RadarUserDTO dto) {
		String useFlag = dto.getUseFlag();
		if (StringUtils.isBlank(useFlag)) {
			dto.setUseFlag("1");
		}
		int count = radarTwinMapper.checkRadarUserCount(dto.getUserId());
		if (count == 0) { // 没有分配，直接保存
			boolean ret = radarTwinMapper.saveRadarUser(dto) > 0;
			if (ret) {// 同步刷新推送用户缓存
				fashRadarUserIds();
			}
			return new ResponseVO(ret);
		} else {
			return new ResponseVO("当前用户已经分配有消息推送权限，请重新选择用户！", 0);
		}
	}

	public boolean batchDeleteRadarUser(RadarUserDTO dto) {

		boolean ret = radarTwinMapper.batchDeleteRadarUser(dto) > 0;
		if (ret) {// 同步刷新推送用户缓存
			fashRadarUserIds();
		}
		return ret;
	}

	@SuppressWarnings("unchecked")
	private void fashRadarUserIds() {
		String cache_useIds = CommonFunction.RADAR_USER_ID;
		redisTemplate.delete(cache_useIds);
		redisTemplate.opsForValue().set(cache_useIds, radarTwinMapper.getRadarUsers(), CommonFunction.TIMEOUT,
				TimeUnit.HOURS);// 默认缓存2小时
	}

	public void convertRadarPoints(CommonParamsDTO commonParamsDTO) {
		Integer line = commonParamsDTO.getLine();
		String facilityNo = commonParamsDTO.getFacilityNo();
		String idLane = commonParamsDTO.getLaneType();
		Integer limit = commonParamsDTO.getLimit();// 每段拆分个数
		List<RadarPointVO> list = radarTwinMapper.selectRadarPointOldList();// 获取坐标源点
		List<RadarPointVO> listFist = list.stream().filter(
				e -> e.getFacilityNo().equals(facilityNo) && e.getLine().equals(line) && e.getIdLane().equals(idLane))
				.sorted(Comparator.comparing(RadarPointVO::getxRatio)).collect(Collectors.toList());
		int n = limit; // 加入点数
		List<RadarPointDTO> dtoList = new ArrayList<RadarPointDTO>();
		for (int i = 1; i < listFist.size(); i++) {
			RadarPointVO one = listFist.get(i - 1);
			RadarPointVO two = listFist.get(i);
			Double xRatio_1 = one.getxRatio();
			Double yRatio_1 = one.getyRatio();
			Double xRatio_2 = two.getxRatio();
			Double yRatio_2 = two.getyRatio();
			Double x_gap = Math.abs(xRatio_2 - xRatio_1);
			Double y_gap = yRatio_2 - yRatio_1;
			Double tanA = NumbersUtils.round((Math.abs(y_gap) / x_gap), 2); // 计算三角坐标轴长
			Double gap_x = NumbersUtils.round((Math.abs(x_gap) / n), 2);
			RadarPointDTO start = this.getRadarPointDTO(line, facilityNo, idLane, xRatio_1, yRatio_1);
			start.setFinishFlag(one.getFinishFlag());
			dtoList.add(start);
			if (y_gap > 0) {// y增加
				for (int j = 1; j < n; j++) {
					Double xRatio = NumbersUtils.round((xRatio_1 + gap_x * j), 2);
					Double gap_y1 = tanA * gap_x * j;
					Double yRatio = NumbersUtils.round((yRatio_1 + gap_y1), 2);
					RadarPointDTO dto = this.getRadarPointDTO(line, facilityNo, idLane, xRatio, yRatio);
					dtoList.add(dto);
				}
			}
			if (y_gap < 0) {// y减少
				for (int j = 1; j < n; j++) {
					Double xRatio = NumbersUtils.round((xRatio_1 + gap_x * j), 2);
					Double gap_y1 = tanA * gap_x * (n - j);
					Double yRatio = NumbersUtils.round((yRatio_2 + gap_y1), 2);
					RadarPointDTO dto = this.getRadarPointDTO(line, facilityNo, idLane, xRatio, yRatio);
					dtoList.add(dto);
				}
			}
			if (y_gap == 0) {// y相等
				for (int j = 1; j < n; j++) {
					Double xRatio = NumbersUtils.round((xRatio_1 + gap_x * j), 2);
					Double yRatio = NumbersUtils.round((yRatio_2), 2);
					RadarPointDTO dto = this.getRadarPointDTO(line, facilityNo, idLane, xRatio, yRatio);
					dtoList.add(dto);
				}
			}
			RadarPointDTO end = this.getRadarPointDTO(line, facilityNo, idLane, xRatio_2, yRatio_2);
			end.setFinishFlag(two.getFinishFlag());
			dtoList.add(end);
		}
		List<RadarPointDTO> uniquelist = dtoList.stream().collect(Collectors.collectingAndThen(
				Collectors.toCollection(() -> new TreeSet<>(
						Comparator.comparing(RadarPointDTO::getxRatio).thenComparing(RadarPointDTO::getyRatio))),
				ArrayList::new));
		int total = uniquelist.size();
		int co = 0;
		if (total > 0) {
			int count = 1;
			List<RadarPointDTO> dataList = new ArrayList<RadarPointDTO>();
			for (int j = 0; j < total; j++, count++) {
				dataList.add(uniquelist.get(j));
				if (count % 200 == 0) {
					co = radarTwinMapper.insertData(dataList);
					dataList.clear();
					count = 1;
				}
			}
			if (!dataList.isEmpty()) {
				co = radarTwinMapper.insertData(dataList);
			}
		}
		LOGGER.info("成功插入数量:{}" + co);

	}

	private RadarPointDTO getRadarPointDTO(Integer line, String facilityNo, String idLane, Double xRatio,
			Double yRatio) {
		RadarPointDTO dto = new RadarPointDTO();
		dto.setFacilityNo(facilityNo);
		dto.setFinishFlag("0");
		dto.setLine(line);
		dto.setIdLane(idLane);
		dto.setxRatio(xRatio);
		dto.setyRatio(yRatio);
		return dto;
	}

	/**
	 * @描述 设施详情
	 */
	public Object selectAllRadarFacility(RadarAlarmDTO dto) {
		List<RadarFacilityVO> ret = new ArrayList<RadarFacilityVO>();
		if (CollectionUtils.isEmpty(RADAR_FUSE_DATA)) {
			RADAR_FUSE_DATA = radarTwinMapper.selectRadarFuseData();
		}
		RADAR_FUSE_DATA.stream().collect(Collectors.groupingBy(RadarFuseDataVO::getFacilityNo))
				.forEach((key, group) -> {
					RadarFacilityVO vo = this.getRadarFacilityVO(key, group);
					ret.add(vo);
				});
		return ret;
	}

	private RadarFacilityVO getRadarFacilityVO(String facilityNo, List<RadarFuseDataVO> resList) {
		RadarFacilityVO vo = new RadarFacilityVO();
		vo.setFacilityNo(facilityNo);
		RadarFuseDataVO fuse = resList.get(0);
		vo.setFacilityName(fuse.getFacilityName());
		vo.setMilePost(fuse.getMilePost());
		vo.setLat(fuse.getLat());
		vo.setLng(fuse.getLng());
		vo.setRoadNo(fuse.getRoadNo());
		vo.setRoadName(fuse.getRoadName());
		List<RadarFacilityLineVO> lines = new ArrayList<RadarFacilityLineVO>();
		resList.forEach(x -> {
			RadarFacilityLineVO line = new RadarFacilityLineVO();
			line.setLine(x.getLine());
			line.setFacilityNo(x.getFacilityNo());
			line.setFacilityName(x.getFacilityName());
			line.setFacilityLength(x.getFacilityLength());
			line.setDirectionNo(x.getDirectionNo());
			line.setDirectionName(x.getDirectionName());
			line.setNodeStart(x.getNodeStart());
			line.setNodeEnd(x.getNodeEnd());
			line.setNodeLength(x.getNodeLength());
			line.setLanes(x.getLanes());
			line.setStartMilePost(x.getStartMilePost());
			line.setEndMilePost(x.getEndMilePost());
			lines.add(line);
		});
		vo.setLines(lines);
		return vo;
	}

	/**
	 * @描述 查询事件发生路段桩号范围3公里内的设施
	 * @param dto
	 * @return
	 */
	public Object selectRadarFacilityByEventId(RadarAlarmDTO dto) {
		EventVO vo = radarTwinMapper.selectByEventId(dto.getEventId());
		int code = 0;
		String message = "";
		Integer km = dto.getKm();
		RadarFacilityVO radarFacility = new RadarFacilityVO();
		if (vo != null) {
			Integer roadNo = vo.getRoadNo();
			String milePost = vo.getMilePost();
			if (CollectionUtils.isEmpty(RADAR_FUSE_DATA)) {
				RADAR_FUSE_DATA = radarTwinMapper.selectRadarFuseData();
			}
			if (roadNo != null) {
				List<RadarFuseDataVO> roadList = RADAR_FUSE_DATA.stream().filter(x -> x.getRoadNo().equals(roadNo))
						.collect(Collectors.toList());
				if (roadList.isEmpty()) {
					message = "事件发生的路段，没有发现雷达设备！";
				} else {
					if (StringUtils.isBlank(milePost)) {
						message = "当前事件没有填写桩号信息，请核对！";
					} else {
						List<RadarFacilityLineVO> lineList = new ArrayList<RadarFacilityLineVO>();
						Integer mile_value = MilePostUtils.pileno2IntValue(milePost);
						roadList.forEach(x -> {
							Integer startMilePost = MilePostUtils.pileno2IntValue(x.getStartMilePost());// 开始桩号
							Integer endMilePost = MilePostUtils.pileno2IntValue(x.getEndMilePost());// 结束桩号桩号
							Integer min_v = mile_value < startMilePost ? startMilePost - mile_value : 0;
							Integer max_v = mile_value > endMilePost ? mile_value - endMilePost : 0;
							/*
							 * if ((mile_value >= startMilePost && mile_value <= endMilePost) || (min_v >= 0
							 * && min_v < km * 1000) || (max_v > 0 && max_v < km * 1000)) {
							 * sets.add(x.getFacilityNo()); // 将符合条件的设施id加入结果中 }
							 */
							if (mile_value >= startMilePost && mile_value <= endMilePost) {// 在设施范围内
								RadarFacilityLineVO line = new RadarFacilityLineVO();
								line.setFacilityNo(x.getFacilityNo());
								line.setLine(0);
								lineList.add(line);
							}
							if (min_v >= 0 && min_v < km * 1000) {// 在距离起点范围内
								RadarFacilityLineVO line = new RadarFacilityLineVO();
								line.setFacilityNo(x.getFacilityNo());
								line.setLine(min_v);
								lineList.add(line);
							}
							if (max_v > 0 && max_v < km * 1000) {// 在距离终点范围内
								RadarFacilityLineVO line = new RadarFacilityLineVO();
								line.setFacilityNo(x.getFacilityNo());
								line.setLine(max_v);
								lineList.add(line);
							}
						});
						// 处理返回结果,获取距离最小的设施id
						if (lineList.isEmpty()) {
							message = "当前事件发生地(" + km + "公里)范围内没有找到雷达设备，请核对！";
						} else {
							code = 1;
							RadarFacilityLineVO resLine = lineList.stream()
									.min(Comparator.comparing(RadarFacilityLineVO::getLine)).get();
							String facilityNo = resLine.getFacilityNo();// 当前设施
							List<RadarFuseDataVO> resList = roadList.stream()
									.filter(y -> y.getFacilityNo().equals(facilityNo)).collect(Collectors.toList());
							radarFacility = this.getRadarFacilityVO(facilityNo, resList);
						}

					}
				}
			} else {
				message = "当前事件没有填写所属路段信息，请核对！";
			}
		} else {
			message = "根据事件ID，查询不到事件信息，请联系系统管理员核对！";
		}

		Gson gson = new Gson();
		JsonObject result = new JsonObject();
		result.addProperty("code", code);
		result.addProperty("message", message);
		result.add("result", gson.toJsonTree(radarFacility));
		return gson.toJson(result);
	}

	public Object selectAllRadarDevice(RadarAlarmDTO dto) {
		String facilityNo = dto.getFacilityNo();
		List<RadarDevice2VO> result = new ArrayList<>();
		// 查询摄像机以及摄像机状态
		List<RadarDevice2VO> cameraList = radarTwinMapper.selectCameraDeviceStatus(facilityNo);
		result.addAll(cameraList);
		// 查询雷达以及雷达状态
		List<RadarDevice2VO> radarList = radarTwinMapper.selectRadarDeviceStatus(facilityNo);
		result.addAll(radarList);
		if (!result.isEmpty()) {
			result.stream().forEach(x -> { // 区分上下行
				String directionName = x.getDirectionName();
				if (directionName.contains("下行")) {
					x.setLine(1);
				} else {
					x.setLine(2);
				}
			});
		}
		return result;
	}
}
