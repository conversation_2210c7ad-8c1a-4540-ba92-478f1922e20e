package com.bt.itstunnel.service;

import java.util.List;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bt.itstunnel.common.CommonCacheAction;
import com.bt.itstunnel.domain.dto.TunnelRecoveryDTO;
import com.bt.itstunnel.domain.dto.TunnelRecoveryDetailDTO;
import com.bt.itstunnel.domain.entity.TunnelResPoseBean;
import com.bt.itstunnel.domain.vo.TunnelPlcDeviceInfoVO;
import com.bt.itstunnel.domain.vo.TunnelPreplanDetailVO;
import com.bt.itstunnel.mapper.TunnelPlcDeviceInfoMapper;
import com.bt.itstunnel.mapper.TunnelPreplanDetailMapper;
import com.bt.itstunnel.mapper.TunnelRecoveryDetailMapper;
import com.bt.itstunnel.mapper.TunnelRecoveryMapper;
import com.bt.itstunnel.utils.TimeUtils;

@Service("tunnelRecoveryService")
public class TunnelRecoveryService {
	@Autowired
	private TunnelRecoveryMapper tunnelRecoveryMapper;
	@Autowired
	private TunnelRecoveryDetailMapper tunnelRecoveryDetailMapper;
	@Autowired
	private TunnelPreplanDetailMapper tunnelPreplanDetailMapper;
	@Autowired
	private TunnelPlcDeviceInfoMapper tunnelPlcDeviceInfoMapper;
	@Autowired
	RedisService redisService;
	/**
	 * 1、删除当前预案之前的恢复信息
	 * 2、隧道预案执行保存当前隧道预案执行前状态
	 */
	public void autoSaveTunnelRecovery(String preplanId, String creater, String tunnelId) {
		this.deleteTunnelRecoveryByPreplanId(preplanId);// 删除当前预案之前的恢复信息
		List<TunnelPreplanDetailVO> preplanDetaiList = tunnelPreplanDetailMapper.selectTunnelPreplanDetail(preplanId);
		if (preplanDetaiList.size() > 0) {
			TunnelRecoveryDTO tunnelRecoveryDTO = new TunnelRecoveryDTO();
			String recoveryId = UUID.randomUUID().toString();
			tunnelRecoveryDTO.setId(recoveryId);
			tunnelRecoveryDTO.setPreplanId(preplanId);
			tunnelRecoveryDTO.setCreater(creater);
			tunnelRecoveryDTO.setCreateTime(TimeUtils.getTimeString("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis()));
			tunnelRecoveryMapper.add(tunnelRecoveryDTO);
			List<TunnelPlcDeviceInfoVO> tunnelPlcDeviceInfoList = tunnelPlcDeviceInfoMapper
					.selectListByFacilityNo(tunnelId);
			for (TunnelPreplanDetailVO preplanDetailVO : preplanDetaiList) {
				String deviceId = preplanDetailVO.getDeviceId();
				TunnelRecoveryDetailDTO tunnelRecoveryDetail = new TunnelRecoveryDetailDTO();
				tunnelRecoveryDetail.setId(UUID.randomUUID().toString());
				tunnelRecoveryDetail.setDeviceId(deviceId);
				tunnelRecoveryDetail.setRecoveryId(recoveryId);
				tunnelRecoveryDetail.setCmdCode("Modify");
				TunnelPlcDeviceInfoVO infoVO = this.getPlcDeviceInfo(tunnelPlcDeviceInfoList, deviceId);
				if (infoVO != null) {
//					String state = CommonCacheAction.PLC_DEVICE_STATE_MAP.get(infoVO.getCode()).getDeviceState();
					String state= redisService.getTunnelResPoseBean(infoVO.getCode()).getDeviceState();
					tunnelRecoveryDetail.setState(state);
				} else {
					tunnelRecoveryDetail.setState("");
				}
				tunnelRecoveryDetailMapper.add(tunnelRecoveryDetail);
			}
		}
	}

	protected TunnelPlcDeviceInfoVO getPlcDeviceInfo(List<TunnelPlcDeviceInfoVO> list, String deviceId) {
		TunnelPlcDeviceInfoVO info = null;
		if (list.size() > 0) {
			for (int i = 0; i < list.size(); i++) {
				if (deviceId.equals(list.get(i).getId())) {
					info = list.get(i);
					break;
				}
			}
		}
		return info;
	}

	public void deleteTunnelRecoveryByPreplanId(String preplanId) {
		if (preplanId != null) {
			List<TunnelRecoveryDTO> list = tunnelRecoveryMapper.getTunnelRecoveryBypreplanId(preplanId);
			if (list.size() > 0) {
				for (TunnelRecoveryDTO dto : list) {
					tunnelRecoveryDetailMapper.batchDeleteTunnelRecoveryDetail(dto.getId());
				}
				tunnelRecoveryMapper.batchDeleteTunnelRecovery(preplanId);
			}
		}
	}

}
