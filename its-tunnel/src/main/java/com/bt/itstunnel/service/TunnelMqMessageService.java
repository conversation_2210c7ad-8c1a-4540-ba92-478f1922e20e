package com.bt.itstunnel.service;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bt.itstunnel.domain.dto.TunnelMqMessageDTO;
import com.bt.itstunnel.mapper.TunnelMqMessageMapper;
import com.bt.itstunnel.rabbitmq.TunnelInputConsumer;

/**
 * 
 * @Description: 接收存储MQ消息用于分析
 * <AUTHOR>
 * @date 2023年3月2日 上午9:44:11
 *
 */

@Service("tunnelMqMessageService")
public class TunnelMqMessageService {
	private final static Logger LOGGER = LoggerFactory.getLogger(TunnelInputConsumer.class);
	@Autowired
	private TunnelMqMessageMapper tunnelMqMessageMapper;

	public void insertData(List<TunnelMqMessageDTO> list) {
		int total = list.size();
		int i = 0;
		if (total > 0) {
			int count = 1;
			List<TunnelMqMessageDTO> dataList = new ArrayList<TunnelMqMessageDTO>();
			for (int j = 0; j < total; j++, count++) {
				dataList.add(list.get(j));
				if (count % 100 == 0) {
					i = tunnelMqMessageMapper.insertData(dataList);
					dataList.clear();
					count = 1;
				}
			}
			if (!dataList.isEmpty()) {
				i = tunnelMqMessageMapper.insertData(dataList);
			}
		}

		if (i > 0) {
			LOGGER.info("成功插入PLC指令数量:{}", i);
		} else {
			LOGGER.info("插入PLC指令失败:{}", i);
		}
	}
}
