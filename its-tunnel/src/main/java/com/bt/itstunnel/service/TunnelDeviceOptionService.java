package com.bt.itstunnel.service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.bt.itstunnel.common.CommonCacheAction;
import com.bt.itstunnel.common.CommonFunction;
import com.bt.itstunnel.domain.dto.CommonParamsDTO;
import com.bt.itstunnel.domain.dto.TunnelPreplanDTO;
import com.bt.itstunnel.domain.dto.TunnelRecoveryDetailDTO;
import com.bt.itstunnel.domain.entity.LightingResPoseBean;
import com.bt.itstunnel.domain.entity.TunnelResPoseBean;
import com.bt.itstunnel.domain.vo.TunnelPlcDeviceInfoVO;
import com.bt.itstunnel.domain.vo.TunnelPreplanDetailVO;
import com.bt.itstunnel.domain.vo.TunnelPreplanVO;
import com.bt.itstunnel.mapper.TunnelPlcDeviceInfoMapper;
import com.bt.itstunnel.mapper.TunnelPreplanDetailMapper;
import com.bt.itstunnel.mapper.TunnelPreplanMapper;
import com.bt.itstunnel.mapper.TunnelRecoveryDetailMapper;
import com.bt.itstunnel.mapper.TunnelResponseMapper;
import com.bt.itstunnel.utils.IdUtils;

@Service("tunnelDeviceOptionService")
public class TunnelDeviceOptionService {
	public static final Log log = LogFactory.getLog(TunnelDeviceOptionService.class);
	public static Map<String, Integer> FAN_DELAYTE_MAP = new ConcurrentHashMap<String, Integer>();// 批量操作风机时记录同组，或者同一隧道延迟操作时间
	@Autowired
	TunnelOperateLogService tunnelOperateLogService;
	@Autowired
	TunnelResponseMapper tunnelResponseMapper;
	@Autowired
	private TunnelPlcDeviceInfoMapper tunnelPlcDeviceInfoMapper;
	@Autowired
	private TunnelPreplanMapper tunnelPreplanMapper;
	@Autowired
	TunnelPreplanDetailMapper tunnelPreplanDetailMapper;
	@Autowired
	private TunnelRecoveryDetailMapper tunnelRecoveryDetailMapper;
	@Autowired
	TunnelAlarmService tunnelAlarmService;
	@Autowired
	TunnelRecoveryService tunnelRecoveryService;
	@Autowired
	DeviceService deviceService;
	@Autowired
	RabbitmqService rabbitmqService;
	@Autowired
	PowerService powerService;
	@Autowired
	RedisService redisService;

	@Value("${tunnel.server.test}")
	private boolean test;

	@Value("${tunnel.server.testmq}")
	private String testmq;

	/*
	 * PLC设备控制
	 */
	public Map<String, String> operationPlcDevice(CommonParamsDTO commonParamsDTO, String operater) {
		Map<String, String> resultMap = new HashMap<String, String>();
		String plcId = commonParamsDTO.getPlcId();
		String operationName = commonParamsDTO.getOperationName();
		String message = "";
		if (StringUtils.isNotBlank(operationName) && StringUtils.isNotBlank(plcId)) {
			TunnelPlcDeviceInfoVO plcDeviceInfoVO = this.getTunnelPlcDeviceInfoVO(plcId);
			String typeCode = plcDeviceInfoVO.getTypeCode();
			String operations[] = operationName.split("-");
			String deviceState = operations[1];
			if (typeCode.equals(operations[0])) {
				boolean falg = true;
				String groupNum = plcDeviceInfoVO.getGroupNum();// 风机分组判断
				String deviceCode = plcDeviceInfoVO.getCode();
				if ("Fan".equals(typeCode) && StringUtils.isNotBlank(groupNum) && !"3".equals(deviceState)) {
					Long time = redisService.getTunnelFanTime(groupNum);
					String fanContrl_time = CommonCacheAction.SYS_PARAMS_MAP.get("FanDelayTime");
					if (time != 0 && StringUtils.isNotBlank(fanContrl_time)) {
						Long times = (System.currentTimeMillis() - time) / 1000;// 秒钟
						Long fan_time = Long.valueOf(fanContrl_time);
						if (times < fan_time) {
							falg = false;
							message = "同组风机操作间隔必须大于指定时间：" + fanContrl_time + "秒,如需调整，可在【参数设置】修改！";
						} else {
							redisService.setTunnelFanTime(groupNum, System.currentTimeMillis());
						}
					}
					redisService.setTunnelFanTime(groupNum, System.currentTimeMillis());// 更新分组风机操作时间
				}
				if (falg) {
					if ("LightingSwitch".equals(typeCode) || "MainsSwitch".equals(typeCode)) {
						if (deviceCode.startsWith("dljk_")) {// 判断是否是新的电力监控控制方式
							message = powerService.submitPowerOption(deviceCode, deviceState, operater, false);
						} else {
							message = this.submitPlcOption(deviceCode, deviceState, operater);
						}
					} else {
						message = this.submitPlcOption(deviceCode, deviceState, operater);
					}
				}
			} else {
				message = "操作指令格式不对，请联系系统管理员！";
			}
		} else {
			message = "提交参数不正确！";
		}
		resultMap.put("message", message);
		return resultMap;
	}

	/**
	 * 单控设备
	 */
	/**
	 * 提交修改设备操作 {，指令码，修改的PLC数量，隧道名称#PLC#设备1：状态#设备2…设备n：状态，隧道名称#PLC#设备1：状态#设备2…设备n：状态,指令序号，} 如：{，Modify
	 * ，2，四方山隧道#PLC2#Fan2:2# TrafficLight1:3，泸河隧道#PLC1#Fan5:2，45，} 1、指令码 ：Modify 2、修改的PLC数量 3、隧道名称 4、#PLC#
	 * 5、#设备1：状态#设备2…设备n：状态， 6、
	 */
	public String submitPlcOption(String deviceCode, String deviceState, String operater) {
		boolean isSendCmd = false;// 是否已发送命令
		String submitFlag = "";
		try {
			TunnelResPoseBean tunnelResPoseBean = redisService.getTunnelResPoseBean(deviceCode);
			if (tunnelResPoseBean != null) {
				String old_deviceState = tunnelResPoseBean.getDeviceState();
				String typeCode = tunnelResPoseBean.getTypeCode();
				String queueName = getSendQueueName(typeCode);
				String cmdSeq = getCmdSeq(typeCode);
				String cmdSb = "{,Modify,1," + deviceCode + ":" + deviceState + "," + cmdSeq + ",}";// 指令序号
				tunnelResPoseBean
						.setOperOldname(CommonFunction.getOperateName(tunnelResPoseBean.getDeviceState(), typeCode));
				if (typeCode.equalsIgnoreCase("Fan")) { // 风机操作特殊处理
					submitFlag = checkFanOption(deviceCode, deviceState, old_deviceState);
					if (StringUtils.isNotBlank(submitFlag)) {
						isSendCmd = true;
					}
				}
				if (!isSendCmd) {// 当前未发送命令的 现在就发送
					submitFlag = rabbitmqService.rabbitmqPublish(deviceCode, cmdSb, queueName);
				}
				tunnelResPoseBean.setOperLastname(CommonFunction.getOperateName(deviceState, typeCode));
				int sended = 0;
				if (submitFlag.equals("ok")) {// 根据指令序号,对服务器应答消息进行判断,看服务器对发送指令知否进行应答
					if (isGetSubmitPlcOptionRespose(cmdSeq, 5)) { //操作设备响应操作，5秒不反应响应超时
						sended = 1;
					}else {
						submitFlag = "设备(" + deviceCode + ")操作时服务端响应超时。";
					}

				}
				tunnelOperateLogService.makeOperateLog(tunnelResPoseBean, cmdSeq, cmdSb, sended, operater);
			}
		} catch (Exception e) {
			log.error("发生异常:" + e.getMessage());
			submitFlag = "发生异常:" + e.getMessage();
		}
		return submitFlag;
	}

	/**
	 * @描述 风机控制操作检测
	 * @param deviceCode
	 * @param deviceState
	 * @param old_deviceState
	 * @return
	 */
	private String checkFanOption(String deviceCode, String deviceState, String old_deviceState) {
		String submitFlag = "";
		if ("3".equals(deviceState)) {
			String showState = old_deviceState;
			if (old_deviceState.indexOf("&") > -1) {
				showState = old_deviceState.split("&")[0];
			}
			if ("1".equals(showState) || "2".equals(showState)) {
				redisService.setTunnelFanTime(deviceCode + "_" + showState, System.currentTimeMillis());
			}
		} else {
			long lastTime = 0;
			switch (deviceState) {
			case "1":
				lastTime = redisService.getTunnelFanTime(deviceCode + "_2");
				break;
			case "2":
				lastTime = redisService.getTunnelFanTime(deviceCode + "_1");
				break;
			}
			if (lastTime != 0) {
				long times = (System.currentTimeMillis() - lastTime) / 1000;// 秒
				if (times < 30) {// 风机操作处理：start 是风机发生相反方向旋转时，则先停止,30秒后再发送命令
					submitFlag = "风机在" + (deviceState.equals("1") ? "反转(向隧道入口吹)" : "正转(向隧道出口吹)") + "后，不能直接进行"
							+ (deviceState.equals("1") ? "正转(向隧道出口吹)" : "反转(向隧道入口吹)") + "操作，要停止30秒后，才能操作！";
				}
			}
		}
		return submitFlag;
	}

	/**
	 * 需要区分电力监控不同控制方式
	 * @param deviceCode
	 * @param deviceState
	 * @param operater
	 * @return
	 */
	public String submitBatchPlcOption(String deviceCode, String deviceState, String operater) {
		String submitFlag = "";
		try {
			TunnelResPoseBean tunnelResPoseBean = redisService.getTunnelResPoseBean(deviceCode);
			if (tunnelResPoseBean != null) {
				String typeCode = tunnelResPoseBean.getTypeCode();
				String queueName = getSendQueueName(typeCode);
				String cmdSeq = getCmdSeq(typeCode);
				String cmdSb = "{,Modify,1," + deviceCode + ":" + deviceState + "," + cmdSeq + ",}";// 指令序号
				submitFlag = rabbitmqService.rabbitmqPublish(deviceCode, cmdSb, queueName);
				int sended = 0;
				if (submitFlag.equals("ok")) {// 根据指令序号,对服务器应答消息进行判断,看服务器对发送指令知否进行应答
					sended = 1;
				}
				tunnelResPoseBean
						.setOperOldname(CommonFunction.getOperateName(tunnelResPoseBean.getDeviceState(), typeCode));
				tunnelResPoseBean.setOperLastname(CommonFunction.getOperateName(deviceState, typeCode));
				tunnelOperateLogService.makeOperateLog(tunnelResPoseBean, cmdSeq, cmdSb, sended, operater);
			}
		} catch (Exception e) {
			log.error("发生异常:" + e.getMessage());
			submitFlag = "发生异常:" + e.getMessage();
		}
		return submitFlag;
	}

	public boolean isGetSubmitPlcOptionRespose(String msgSeq, int seconds) {
		int sleep = 1000;
		int count = (seconds * 1000) / sleep;
		boolean falg = false;
		for (int i = 0; i < count; i++) {
			try {
				Thread.sleep(sleep);
				if (redisService.isResposeMsgSeq(msgSeq)) {
					falg = true;
					count = 0;
					break;
				}
			} catch (Exception e) {
				if (e.toString().indexOf("cmdState=Err") > -1) {
					throw new RuntimeException("服务应答提示出错(" + e.getMessage() + ")");
				}
			}
		}
		return falg;
	}

	private String getSendQueueName(String deviceTypeCode) {
		String queueName = "tunnel.command";
		if (test) {
			queueName = testmq;
		}
		return queueName;
	}

	// 区别不同类别设备提交指令的队列
	// PLC_STATUS(PLC设备);LC_STATUS(调光设备);ET_STATUS(紧急电话);ALARM_STATUS(隧道报警);POWER_STATUS(电力监控);ED_STATUS(事件监测)
	private String getCmdSeq(String deviceTypeCode) {
		String cmdSeq = IdUtils.getSeqId();
		String[] et_Types = { "EmergencyFTel", "EmergencyTel" };// 紧急电话
		String[] power_Types = { "LightingSwitch", "MainsSwitch" };// 电力监控
		String[] lc_Types = { "LightingControl", "DoubLightingControl" };// 调光系统
		String[] plc_Types = { "DoubWaterPump", "Fan", "FireDoor", "FireValve", "LaneIndicator", "LaneIndicatorL",
				"LaneIndicatorTurn", "TrafficLight", "TrafficLightL", "WaterPump", "RefugeLamp", "LaneIndicator_BL",
				"LaneIndicatorL_BL" };// plc设备
		String[] alarm_Types = { "HandControl" };// 报警控制
		if (compareTypeCode(et_Types, deviceTypeCode)) {
			cmdSeq += "-PHONE";
		} else if (compareTypeCode(power_Types, deviceTypeCode)) {
			cmdSeq += "-POWER";
		} else if (compareTypeCode(lc_Types, deviceTypeCode)) {
			cmdSeq += "-DIMMING";
		} else if (compareTypeCode(plc_Types, deviceTypeCode)) {
			cmdSeq += "-PLC";
		} else if (compareTypeCode(alarm_Types, deviceTypeCode)) {
			cmdSeq += "-FIRE";
		} else {
			cmdSeq += "-PLC";
		}
		return cmdSeq;
	}

	public Object batchControl(CommonParamsDTO commonParamsDTO, String operater) {
		Map<String, String> resultMap = new HashMap<String, String>();
		String message = "";
		String contrlType = commonParamsDTO.getContrlType();
		List<String> lightTypes = commonParamsDTO.getLightTypes();
		String method = commonParamsDTO.getMethod();
		Integer line = commonParamsDTO.getLine();
		List<String> tunnelIds = commonParamsDTO.getTunnelIds();
		if (StringUtils.isBlank(contrlType)) {
			message = "控制策略类型不能为空";
		}
		if (StringUtils.isBlank(method)) {
			message = "控制类型不能为空";
		}
		if (line == null) {
			message = "选择线路不能为空";
		}
		if (tunnelIds.size() <= 0) {
			message = "所属隧道不能为空";
		}
		if (contrlType.equals("3")) {
			if (lightTypes.size() <= 0) {
				message = "启用照明策略，照明类型不能为空";
			}
		}
		if (StringUtils.isNotBlank(message)) {
			resultMap.put("code", "0");
			resultMap.put("message", message);
			return resultMap;
		} else {
			CommonParamsDTO commonParams = new CommonParamsDTO();
			List<String> idList = new ArrayList<String>();
			switch (contrlType) {
			case "1":
				idList.add("39"); // 红绿灯
				idList.add("40"); // 带左转红绿灯
				idList.add("19"); // 车道指示器
				idList.add("21"); // 带左转车道指示器
				// idList.add("20"); // 车道指示器（百隆路）
				// idList.add("22"); // 带左转车道指示器（百隆路）
				idList.add("23"); // 转向灯
				break;
			case "2":
				idList.add("10"); // 风机
				break;
			case "3":
				idList.add("45"); // 照明灯
				idList.add("49");// 电源开关
				break;
			default:
				idList.add("0");// 默认是空值
				break;
			}
			commonParams.setTypeIds(idList);// 控制设备类型
			commonParams.setTunnelIds(tunnelIds);
			if (line != 3) {
				commonParams.setLine(line);
			}
			List<TunnelPlcDeviceInfoVO> tunnelPlcDeviceInfoList = tunnelPlcDeviceInfoMapper
					.queryOperatePlcDeviceInfo(commonParams);// 要操作的设备列表
			int length = tunnelPlcDeviceInfoList.size();
			int succe = 0;
			if (length > 0) {
				tunnelPlcDeviceInfoList.stream().sorted(Comparator.comparing(TunnelPlcDeviceInfoVO::getCode));
				for (TunnelPlcDeviceInfoVO entry : tunnelPlcDeviceInfoList) {
					String deviceCode = entry.getCode();
					String typeCode = entry.getTypeCode();
					String laneType = entry.getLaneType(); // 车道类型 旧版（1左慢、2左快、3右快、4右慢、5左应急、6右应急）新版（1超车道、2行车道、3应急车道）
					String lightType = entry.getLightType();// 照明灯、开关分类：1-入口引道灯、2-入口加强灯、3-过度加强灯、4-基本段、5-出口加强段、6-出口引道灯、7-应急灯
					String state = "";
					if ("1".equals(contrlType)) {// 交通策略
						// 1、带左转车道指示器：正绿反红1、正红反绿2、双面红叉3 正转反红4、正红反转5、双红反转6
						// 2、带左转红绿灯： 熄灭0、亮红灯1、亮绿灯2、亮黄灯3、亮红灯+左4、亮绿灯+左5、亮黄灯+左6、左转7
						// 3、转向灯：无转向0、正面转向1、反面转向2、双面转向3
						if ("LaneIndicatorTurn".equals(typeCode)) { // 转向灯
							state = "0";
						}
						String[] deviceTypes = { "LaneIndicator", "LaneIndicatorL", };// 车道指示器
						if (compareTypeCode(deviceTypes, typeCode)) {
							switch (method) {// method 1、正常通行 2、车道禁行 3、双向行驶 4、快车道封闭5、慢车道封闭 6、应急等关闭
							case "1":
								if ("3".equals(laneType)) { // 应急车道封闭
									state = "3";
								} else {
									state = "1";
								}
								break;
							case "2":
								state = "3";
								break;
							case "3":
								if ("1".equals(laneType)) { // 快车道逆行
									state = "2";
								} else if ("3".equals(laneType)) { // 应急车道封闭
									state = "3";
								} else {
									state = "1";
								}
								break;
							case "4":
								if ("1".equals(laneType) || "3".equals(laneType)) { // 快车道封闭
									state = "3";
								} else {
									state = "1";
								}
								break;
							case "5":
								if ("2".equals(laneType) || "3".equals(laneType)) { // 慢车道封闭
									state = "3";
								} else {
									state = "1";
								}
								break;
							case "6":
								if ("3".equals(laneType)) { // 应急车道封闭
									state = "3";
								} else {
									state = "1";
								}
								break;
							}

						}
						if ("TrafficLight".equals(typeCode) || "TrafficLightL".equals(typeCode)) {
							if ("1".equals(method) || "6".equals(method)) {
								state = "2";
							}
							if ("2".equals(method)) {
								state = "1";
							}
							if ("3".equals(method) || "4".equals(method) || "5".equals(method)) {
								state = "3";
							}
						}
						message = submitBatchPlcOption(deviceCode, state, operater);
						if (message.equalsIgnoreCase("ok")) {
							succe++;
						}
					}
					if ("2".equals(contrlType)) { // 通风策略 正转1 、反转2 、停止3
						if ("1".equals(method)) {
							state = "1";
						}
						if ("2".equals(method)) {
							state = "2";
						}
						if ("3".equals(method)) {
							state = "3";
						}
						String groupId = entry.getFacilityNo() + "_" + entry.getLine();
						int no = this.findlastDeplayFanNo(groupId);
						String result = delayFans(groupId, deviceCode, state, typeCode, no, operater);
						if (result.equalsIgnoreCase("ok")) {
							succe++;
						}
					}
					if ("3".equals(contrlType)) { // 照明策略
						if ("1".equals(method)) { // 1开灯 2关灯
							state = "1";
						}
						if ("2".equals(method)) {
							state = "2";
						}
						if (lightTypes.contains(lightType)) {
							if (deviceCode.startsWith("dljk_")) {// 判断是否是新的电力监控控制方式
								message = powerService.submitPowerOption(deviceCode, state, operater, true);
							} else {
								message = this.submitBatchPlcOption(deviceCode, state, operater);
							}
							if (message.equalsIgnoreCase("ok")) {
								succe++;
							}
						}
					}
				}
				if ("2".equals(contrlType)) {
					FAN_DELAYTE_MAP.clear();// 清空临时信息表
				}
			}
			String code = "0";
			if (succe > 0) {
				code = "1";
				message = "需执行设备数量：" + length + ";成功执行设备数量：" + succe;
			} else {
				message = "没有设备被执行！";
			}
			resultMap.put("code", code);
			resultMap.put("message", message);
			return resultMap;
		}
	}

	private boolean isNumber(String str) {
		return StringUtils.isNumeric(str);
	}

	private String delayFans(String groupId, String deviceCode, String deviceState, String typeCode, int j,
			String operater) {
		if (deviceState.indexOf("&") > -1) {
			deviceState = deviceState.split("&")[0];
		}
		int time = 15;// 延时时间（秒）
		if (!deviceState.equals("3")) {
			String fanDelayTime = CommonCacheAction.SYS_PARAMS_MAP.get("fanDelayTime");
			if (StringUtils.isNotBlank(fanDelayTime) && isNumber(fanDelayTime)) {
				time = Integer.valueOf(fanDelayTime) * (1 + j);
			} else {
				time = 15 * (1 + j);
			}
		}
		String submitFlag = "";
		try {
			String queueName = getSendQueueName(typeCode);
			String cmdSeq = getCmdSeq(typeCode);
			String cmdSb = "{,Modify,1," + deviceCode + ":" + deviceState + "," + cmdSeq + ",}";// 指令序号
			TunnelResPoseBean tunnelResPoseBean = redisService.getTunnelResPoseBean(deviceCode);
			if (tunnelResPoseBean != null) {
				String old_deviceState = tunnelResPoseBean.getDeviceState();
				if (old_deviceState.indexOf("&") > -1) {
					old_deviceState = old_deviceState.split("&")[0];
				}
				// 风机运行状态： 正转1 、反转2 、停止3
				if (deviceState.equals("3") || old_deviceState.equals(deviceState)) {// 执行前状态和预案状态一致或者停止指令，直接发送指令
					submitFlag = rabbitmqService.rabbitmqPublish(deviceCode, cmdSb, queueName);
				} else {
					// 状态不一致，先发停止，等待间隔，在发指令
					String stopCmd = "{,Modify,1," + deviceCode + ":3," + cmdSeq + ",}";// 停止命令
					submitFlag = rabbitmqService.rabbitmqPublish(deviceCode, stopCmd, queueName);
					if (submitFlag.equals("ok")) { // 先进行风机停止操作，在延迟提交正式命令
						new Thread(new DelaySendMsg(deviceCode, deviceState, queueName, time)).start();// 统一延时时间（秒）
						FAN_DELAYTE_MAP.put(groupId, (j + 1));
					}
				}
				int sended = 0;
				if (submitFlag.equals("ok")) {
					sended = 1;
				}
				tunnelResPoseBean.setOperOldname(CommonFunction.getOperateName(old_deviceState, typeCode));
				tunnelResPoseBean.setOperLastname(CommonFunction.getOperateName(deviceState, typeCode));
				tunnelOperateLogService.makeOperateLog(tunnelResPoseBean, cmdSeq, cmdSb, sended, operater);
			}

		} catch (Exception e) {
			log.error("延迟操作发生异常:" + e.getMessage());
		}
		return submitFlag;
	}

	protected boolean compareTypeCode(String[] strs, String value) {
		for (String s : strs) {
			if (s.equals(value))
				return true;
		}
		return false;
	}

	/**
	 * 执行预案
	 */
	public Object executePreplan(CommonParamsDTO commonParamsDTO, HttpServletRequest request) throws Exception {
		String operater = "";
		String userId = (String) request.getAttribute("userId");
		if (userId != null) {
			operater = deviceService.getUserNameByUserId(userId);
		}
		Map<String, String> resultMap = new HashMap<String, String>();
		String result = "";
		String code = "0";
		if (commonParamsDTO == null || commonParamsDTO.getPreplanId() == null) {
			result = "执行预案Id不能为空！";
		} else {
			String preplanId = commonParamsDTO.getPreplanId();
			TunnelPreplanVO tunnelPreplanVO = tunnelPreplanMapper.getTunnelPreplanVO(preplanId);
			if (tunnelPreplanVO != null) {
				TunnelPreplanDTO tunnelPreplanDTO = getTunnelPreplanDTOProtity(tunnelPreplanVO);
				tunnelPreplanDTO.setUseFlag("20");
				tunnelPreplanDTO.setUseNum(tunnelPreplanDTO.getUseNum() + 1);
				tunnelPreplanMapper.update(tunnelPreplanDTO);// 更新为执行状态
				tunnelRecoveryService.autoSaveTunnelRecovery(preplanId, operater, tunnelPreplanVO.getTunnelId());// 保存预案执行前设备状态
				List<TunnelPreplanDetailVO> list = tunnelPreplanDetailMapper.selectTunnelPreplanDetail(preplanId);// 获取预案操作列表
				int succe = 0;
				int size = list.size();
				if (size > 0) {
					try {
						List<TunnelPlcDeviceInfoVO> tunnelPlcDeviceInfoList = tunnelPlcDeviceInfoMapper
								.selectListByFacilityNo(tunnelPreplanVO.getTunnelId());
						for (TunnelPreplanDetailVO preplanDetail : list) {
							String state = preplanDetail.getState();
							TunnelPlcDeviceInfoVO tunnelPlcDeviceInfo = this.getPlcDeviceInfo(tunnelPlcDeviceInfoList,
									preplanDetail.getDeviceId());
							String typeCode = tunnelPlcDeviceInfo.getTypeCode();
							String deviceCode = tunnelPlcDeviceInfo.getCode();
							String message = "";
							if (typeCode.equalsIgnoreCase("Fan")) {
								String groupId = tunnelPlcDeviceInfo.getFacilityNo() + "_"
										+ tunnelPlcDeviceInfo.getLine();
								int no = this.findlastDeplayFanNo(groupId);
								message = delayFans(groupId, deviceCode, state, typeCode, no, operater);
							} else {
								if (deviceCode.startsWith("dljk_")) {// 判断是否是新的电力监控控制方式
									message = powerService.submitPowerOption(deviceCode, state, operater, true);
								} else {
									message = submitBatchPlcOption(deviceCode, state, operater);
								}
							}
							if (message.equalsIgnoreCase("ok")) {
								succe++;
							}
						}
						FAN_DELAYTE_MAP.clear(); // 清空临时信息表
						code = "1";
						result = "操作设备总数：" + size + "，成功执行数量：" + succe + "，失败数量：" + (size - succe);
					} catch (Exception e) {
						log.error("发生异常:" + e.getMessage());
						result = "发生异常:" + e.getMessage();
					}
				}
			}
		}
		resultMap.put("message", result);
		resultMap.put("code", code);
		return resultMap;
	}

	private TunnelPreplanDTO getTunnelPreplanDTOProtity(TunnelPreplanVO TunnelPreplanVO) {
		TunnelPreplanDTO tunnelPreplanDTO = new TunnelPreplanDTO();
		tunnelPreplanDTO.setId(TunnelPreplanVO.getId());
		tunnelPreplanDTO.setName(TunnelPreplanVO.getName());
		tunnelPreplanDTO.setCreater(TunnelPreplanVO.getCreater());
		tunnelPreplanDTO.setCreateTime(TunnelPreplanVO.getCreateTime());
		tunnelPreplanDTO.setTunnelId(TunnelPreplanVO.getTunnelId());
		tunnelPreplanDTO.setPlanType(TunnelPreplanVO.getPlanType());
		tunnelPreplanDTO.setUseFlag(TunnelPreplanVO.getUseFlag());
		tunnelPreplanDTO.setUseNum(TunnelPreplanVO.getUseNum());
		return tunnelPreplanDTO;
	}

	/**
	 * 取消预案，恢复之前状态
	 * 
	 * @param preplanId
	 * @param creater
	 * @return
	 * @throws Exception
	 */
	public Object disExcutePreplan(CommonParamsDTO commonParamsDTO, HttpServletRequest request) throws Exception {
		Map<String, String> resultMap = new HashMap<String, String>();
		String operater = "";
		String userId = (String) request.getAttribute("userId");
		if (userId != null) {
			operater = deviceService.getUserNameByUserId(userId);
		}
		String result = "";
		String code = "0";
		if (commonParamsDTO == null || commonParamsDTO.getPreplanId() == null) {
			result = "恢复预案Id不能为空！";
		} else {
			String preplanId = commonParamsDTO.getPreplanId();
			TunnelPreplanVO tunnelPreplanVO = tunnelPreplanMapper.getTunnelPreplanVO(preplanId);
			if (tunnelPreplanVO != null) {
				if ("20".equals(tunnelPreplanVO.getUseFlag())) {
					TunnelPreplanDTO tunnelPreplanDTO = getTunnelPreplanDTOProtity(tunnelPreplanVO);
					tunnelPreplanDTO.setUseFlag("10");
					tunnelPreplanMapper.update(tunnelPreplanDTO);// 更新为未执行状态
					List<TunnelRecoveryDetailDTO> recoveryDetailList = tunnelRecoveryDetailMapper
							.selectListByPreplanId(preplanId);
					int size = recoveryDetailList.size();
					if (size > 0) {
						int succe = 0;
						try {
							List<TunnelPlcDeviceInfoVO> tunnelPlcDeviceInfoList = tunnelPlcDeviceInfoMapper
									.selectListByFacilityNo(tunnelPreplanVO.getTunnelId());
							for (TunnelRecoveryDetailDTO temp : recoveryDetailList) {
								String state = temp.getState();
								TunnelPlcDeviceInfoVO tunnelPlcDeviceInfo = this
										.getPlcDeviceInfo(tunnelPlcDeviceInfoList, temp.getDeviceId());
								if (tunnelPlcDeviceInfo != null) {
									String typeCode = tunnelPlcDeviceInfo.getTypeCode();
									String deviceCode = tunnelPlcDeviceInfo.getCode();

									String message = "";
									if (typeCode.equalsIgnoreCase("Fan")) {
										String groupId = tunnelPlcDeviceInfo.getFacilityNo() + "_"
												+ tunnelPlcDeviceInfo.getLine();
										int no = findlastDeplayFanNo(groupId);
										message = delayFans(groupId, deviceCode, state, typeCode, no, operater);
									} else {
										if (deviceCode.startsWith("dljk_")) {// 判断是否是新的电力监控控制方式
											message = powerService.submitPowerOption(deviceCode, state, operater, true);
										} else {
											message = submitBatchPlcOption(deviceCode, state, operater);
										}
									}
									if (message.equalsIgnoreCase("ok")) {
										succe++;
									}
								}
							}
							FAN_DELAYTE_MAP.clear(); // 清空临时信息表
							code = "1";
							result = "操作设备总数：" + size + "，成功执行数量：" + succe + "，失败数量：" + (size - succe);
						} catch (Exception e) {
							log.error("发生异常:" + e.getMessage());
							result = "发生异常:" + e.getMessage();
						}
					}
				} else {
					result = "预案未被执行，不用恢复！";
				}
			} else {
				result = "预案信息不存在！";
			}
		}
		resultMap.put("message", result);
		resultMap.put("code", code);
		return resultMap;
	}

	/**
	 * 调光指令提交
	 * @param deviceCode
	 * @param orders
	 * @param operater
	 * @return
	 */
	public String submitControlOrder(String deviceCode, String orders, String operater) {
		// 如：{, Control, BC0001, 4,1,[1],45,}
		String submitFlag = "";
		try {
			TunnelResPoseBean oldPoseBean = redisService.getTunnelResPoseBean(deviceCode);
			if (oldPoseBean != null) {
				String old_deviceState = oldPoseBean.getDeviceState();
				String typeCode = oldPoseBean.getTypeCode();
				String queueName = getSendQueueName(typeCode);
				String cmdSeq = getCmdSeq(typeCode);
				String cmdSb = "{,Control," + deviceCode + "," + orders + "," + cmdSeq + ",}";
				oldPoseBean.setOperOldname(CommonFunction.getOperateName(old_deviceState, typeCode));
				submitFlag = rabbitmqService.rabbitmqPublish(deviceCode, cmdSb, queueName);
				int sended = 0;
				if (submitFlag.equals("ok")) {// 根据指令序号,对服务器应答消息进行判断,看服务器对发送指令知否进行应答
					if (isGetSubmitPlcOptionRespose(cmdSeq, 4)) {
						sended = 1;
					}else {
						submitFlag = "设备(" + deviceCode + ")操作时服务端响应超时。";
					}
					
				}
				String deviceState = redisService.getTunnelResPoseBean(deviceCode).getDeviceState();
				oldPoseBean.setOperLastname(CommonFunction.getOperateName(deviceState, typeCode));
				tunnelOperateLogService.makeOperateLog(oldPoseBean, cmdSeq, cmdSb, sended, operater);
			}
		} catch (Exception e) {
			log.error("发生异常:" + e.getMessage());
			submitFlag = "发生异常:" + e.getMessage();
		}
		return submitFlag;
	}

	/**
	 * 调光指令查询
	 * @param deviceCode
	 * @param cmdCode
	 * @return
	 */
	public LightingResPoseBean queryLightControlDetail(String deviceCode, String cmdCode) {
		LightingResPoseBean lightingResPoseBean = new LightingResPoseBean();
		String submitFlag = "";
		try {
			// 3:洞外亮度值和洞内入口、出口、基本段的照明值 4:控制模式查询 5:手动设置值查询 6:时间设置值查询 7:自动设置值查询 8:报警运行状态查询
			// 9、标定值
			TunnelResPoseBean oldPoseBean = redisService.getTunnelResPoseBean(deviceCode);
			if (oldPoseBean != null) {
				String typeCode = oldPoseBean.getTypeCode();
				String queueName = getSendQueueName(typeCode);
				String cmdSeq = getCmdSeq(typeCode);
				String cmdSb = "{,Check," + deviceCode + "," + cmdCode + "," + cmdSeq + ",}";// 照明控制指令
				submitFlag = rabbitmqService.rabbitmqPublish(deviceCode, cmdSb, queueName);
				if (submitFlag.equals("ok")) {// 提交成功，等待返回
					if (isGetSubmitPlcOptionRespose(cmdSeq, 4)) {// 10秒
						lightingResPoseBean = redisService.getLightingResult(cmdSeq);
						lightingResPoseBean.setIsOK("1");
					} else {
						lightingResPoseBean.setIsOK("0");
						lightingResPoseBean.setLightContent("设备(" + deviceCode + ")操作时服务端响应超时。");
					}
				} else {
					lightingResPoseBean.setIsOK("0");
					lightingResPoseBean.setLightContent(submitFlag);
				}
				lightingResPoseBean.setDeviceId(oldPoseBean.getId());
				lightingResPoseBean.setDeviceCode(deviceCode);
			}

		} catch (Exception e) {
			log.error("发生异常:" + e.getMessage());
			lightingResPoseBean.setIsOK("0");
			lightingResPoseBean.setLightContent("发生异常:" + e);
		}
		return lightingResPoseBean;

	}

	protected TunnelPlcDeviceInfoVO getPlcDeviceInfo(List<TunnelPlcDeviceInfoVO> list, String deviceId) {
		TunnelPlcDeviceInfoVO info = null;
		if (list.size() > 0) {
			for (int i = 0; i < list.size(); i++) {
				if (deviceId.equals(list.get(i).getId())) {
					info = list.get(i);
					break;
				}
			}
		}
		return info;
	}

	public TunnelPlcDeviceInfoVO getTunnelPlcDeviceInfoVO(String id) {
		TunnelPlcDeviceInfoVO deviceInfoVO = null;
		List<TunnelPlcDeviceInfoVO> listVO = tunnelPlcDeviceInfoMapper.getPlcDeviceInfo(id);
		if (listVO.size() > 0) {
			deviceInfoVO = listVO.get(0);
		}
		return deviceInfoVO;
	}

	// 获取同组批量控制最后的时间

	private int findlastDeplayFanNo(String groupId) {
		int time = 0;
		if (FAN_DELAYTE_MAP.size() > 0) {
			if (FAN_DELAYTE_MAP.containsKey(groupId)) {
				time = FAN_DELAYTE_MAP.get(groupId);
			}
		}
		return time;
	}

	class DelaySendMsg implements Runnable {
		private String deviceCode = "";
		private String deviceState = "";
		private String queueName = "";
		private int time = 20;// 秒

		public DelaySendMsg(String deviceCode, String deviceState, String queueName, int time) {
			this.deviceCode = deviceCode;
			this.deviceState = deviceState;
			this.queueName = queueName;
			this.time = time;
		}

		@Override
		public void run() {
			try {
				Thread.sleep(1000 * time);
				String cmd = "{,Modify,1," + deviceCode + ":" + deviceState + "," + IdUtils.getSeqId() + "-PLC,}";
				rabbitmqService.rabbitmqPublish(deviceCode, cmd, queueName);
			} catch (Exception e) {
				e.printStackTrace();
				log.error("风机指令异常：" + e.getMessage());
			}
		}
	}

}
