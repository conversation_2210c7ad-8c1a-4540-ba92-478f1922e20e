package com.bt.itsinner.domain.entity;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.ReentrantLock;

//雾区用的阻塞队列
public class BlockedQueue<T> {
    private final List<T> data = new ArrayList<>(1);
    private final ReentrantLock lock = new ReentrantLock();
    private final Condition condition = lock.newCondition();

    public void add(T t) {
        lock.lock();
        try {
            data.add(t);
            condition.signal();
        } finally {
            lock.unlock();
        }
    }

    public T poll() {
        lock.lock();
        try {
            while (data.isEmpty()) {
                condition.await();
            }
            return data.remove(0);
        } catch (InterruptedException e) {
            e.printStackTrace();
        } finally {
            lock.unlock();
        }
        return null;
    }

}

