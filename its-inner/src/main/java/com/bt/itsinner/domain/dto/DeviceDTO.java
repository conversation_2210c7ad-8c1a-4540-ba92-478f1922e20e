package com.bt.itsinner.domain.dto;

import javax.validation.constraints.NotBlank;
import java.util.List;

public class DeviceDTO {

	/**
	 * 设备id-主键
	 */
	private String deviceId;
	/**
	 * 设施NO-facility表主键
	 */
	private String facilityNo;
	/**
	 * 方向NO-direction表主键
	 */
	private String directionNo;
	/**
	 * 设备名称
	 */
	@NotBlank(message = "名称不能为空")
	private String deviceName;
	/**
	 * 设备类型 1车检器  2情报板  4 视频  5隧道类型
	 */

	private String deviceTypeNo;
	/**
	 * 桩号
	 */
	private String milePost;
	/**
	 * 桩号对应的double值(单位:km)
	 */
	private Integer mpValue;
	/**
	 * 是否启用  (0停用，1在用)
	 */
	private Integer use;
	/**
	 * 状态(0离线 1在线)
	 */
	private Integer status;
	/**
	 * 经度
	 */
	private String lng;
	/**
	 * 纬度
	 */
	private String lat;
	/**
	 * 路段编号
	 */
	private Integer roadNo;
	/**
	 * 计算经纬度开关0关闭 1打开
	 */
	private Integer computeSwitch;

	private String facilityTypeNo;

    private List<String> ids; //批量删除

    private String tableName;   //子表名称

	/**
	 * 设置：设备id-主键
	 */
	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}
	/**
	 * 获取：设备id-主键
	 */
	public String getDeviceId() {
		return deviceId;
	}
	/**
	 * 设置：设施NO-facility表主键
	 */
	public void setFacilityNo(String facilityNo) {
		this.facilityNo = facilityNo;
	}
	/**
	 * 获取：设施NO-facility表主键
	 */
	public String getFacilityNo() {
		return facilityNo;
	}
	/**
	 * 设置：方向NO-direction表主键
	 */
	public void setDirectionNo(String directionNo) {
		this.directionNo = directionNo;
	}
	/**
	 * 获取：方向NO-direction表主键
	 */
	public String getDirectionNo() {
		return directionNo;
	}
	/**
	 * 设置：设备名称
	 */
	public void setDeviceName(String deviceName) {
		this.deviceName = deviceName;
	}
	/**
	 * 获取：设备名称
	 */
	public String getDeviceName() {
		return deviceName;
	}
	/**
	 * 设置：设备类型 1车检器  2情报板  4 视频  5隧道类型
	 */
	public void setDeviceTypeNo(String deviceTypeNo) {
		this.deviceTypeNo = deviceTypeNo;
	}
	/**
	 * 获取：设备类型 1车检器  2情报板  4 视频  5隧道类型
	 */
	public String getDeviceTypeNo() {
		return deviceTypeNo;
	}
	/**
	 * 设置：桩号
	 */
	public void setMilePost(String milePost) {
		this.milePost = milePost;
	}
	/**
	 * 获取：桩号
	 */
	public String getMilePost() {
		return milePost;
	}
	/**
	 * 设置：桩号对应的double值(单位:km)
	 */
	public void setMpValue(Integer mpValue) {
		this.mpValue = mpValue;
	}

/**
	 * 获取：桩号对应的double值(单位:km)
	 */
	public Integer getMpValue() {
		return mpValue;
	}

/**
	 * 设置：是否启用  (0停用，1在用)
	 */
	public void setUse(Integer use) {
		this.use = use;
	}

/**
	 * 获取：是否启用  (0停用，1在用)
	 */
	public Integer getUse() {
		return use;
	}

/**
	 * 设置：状态(0离线 1在线)
	 */
	public void setStatus(Integer status) {
		this.status = status;
	}
	/**
	 * 获取：状态(0离线 1在线)
	 */
	public Integer getStatus() {
		return status;
	}
	/**
	 * 设置：经度
	 */
	public void setLng(String lng) {
		this.lng = lng;
	}
	/**
	 * 获取：经度
	 */
	public String getLng() {
		return lng;
	}
	/**
	 * 设置：纬度
	 */
	public void setLat(String lat) {
		this.lat = lat;
	}
	/**
	 * 获取：纬度
	 */
	public String getLat() {
		return lat;
	}
	/**
	 * 设置：路段编号
	 */
	public void setRoadNo(Integer roadNo) {
		this.roadNo = roadNo;
	}
	/**
	 * 获取：路段编号
	 */
	public Integer getRoadNo() {
		return roadNo;
	}
	/**
	 * 设置：计算经纬度开关0关闭 1打开
	 */
	public void setComputeSwitch(Integer computeSwitch) {
		this.computeSwitch = computeSwitch;
	}
	/**
	 * 获取：计算经纬度开关0关闭 1打开
	 */
	public Integer getComputeSwitch() {
		return computeSwitch;
	}

	public String getFacilityTypeNo() {
		return facilityTypeNo;
	}

	public void setFacilityTypeNo(String facilityTypeNo) {
		this.facilityTypeNo = facilityTypeNo;
	}

	public List<String> getIds() {
		return ids;
	}

	public void setIds(List<String> ids) {
		this.ids = ids;
	}

	public String getTableName() {
		return tableName;
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}
}
