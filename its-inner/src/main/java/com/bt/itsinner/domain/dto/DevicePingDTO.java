package com.bt.itsinner.domain.dto;

/**
 * <AUTHOR>
 * @date 2023年5月30日 下午3:09:51
 * @Description ping相关的设备信息
 */
public class DevicePingDTO {
    private String deviceId;// 设备id
    private String deviceName;// 设备名称
    private String ip;// ip
    private Integer count;// ping包次数
    private String sourceId;// 区域id，1沿海，2罗城，3大化，4昭平，5灵山，6来宾
    private String sourceName;// 分公司简称
    private Integer roadNo;// 路段编号
    private String roadName;// 路段名称
    private Integer deviceType;// 设备类型
    private String deviceTypeName;// 设备类型名称
    private String uuid;// 指令下发标识id

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public Integer getRoadNo() {
        return roadNo;
    }

    public void setRoadNo(Integer roadNo) {
        this.roadNo = roadNo;
    }

    public String getRoadName() {
        return roadName;
    }

    public void setRoadName(String roadName) {
        this.roadName = roadName;
    }

    public Integer getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(Integer deviceType) {
        this.deviceType = deviceType;
    }

    public String getDeviceTypeName() {
        return deviceTypeName;
    }

    public void setDeviceTypeName(String deviceTypeName) {
        this.deviceTypeName = deviceTypeName;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
}
