package com.bt.itsinner.feign;

import com.bt.itscore.domain.dto.FogMessageDTO;
import com.bt.itscore.domain.dto.MessageDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "its-innerjob")
public interface ItsInnerjobFeignClient {

	@GetMapping(value="/detector/fogControl")
	//发送雾区控制指令
	public FogMessageDTO fogControl(@RequestBody FogMessageDTO message);

}
