<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itsinner.mapper.CarFlowStationMapper">	
	
			
	<insert id="add" parameterType="com.bt.itsinner.domain.dto.CarFlowStationDTO">
		 insert into car_flow_station (id,create_time,toll_station_id,direction,vehicle_type,value,total_count) 
		  values (#{id},#{createTime},#{tollStationId},#{direction},#{vehicleType},#{value},#{totalCount})
	</insert>
	
	<insert id="insertList" parameterType="java.util.List" useGeneratedKeys="false">
    	insert into car_flow_station (id,create_time,toll_station_id,direction,vehicle_type,value,total_count) values
			<foreach collection="list" item="item" index="index" separator=",">
				(
					#{item.id},
					#{item.createTime},
					#{item.tollStationId},
					#{item.direction},
					#{item.vehicleType},
					#{item.value},
					#{item.totalCount}
				)
		     </foreach>		
    </insert>    

	
</mapper>