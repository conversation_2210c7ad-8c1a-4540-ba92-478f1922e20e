<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatif.org//DTD Mapper 3.0//EN" "http://mybatif.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itsinner.mapper.DeviceFogInductMapper">

    <resultMap type="com.bt.itsinner.domain.entity.DeviceFogInduct" id="deviceFogInductMap">
        <result property="facilityNo" column="facility_no"/>
        <result property="directionNo" column="direction_no"/>
        <result property="deviceName" column="device_name"/>
        <result property="milePost" column="mile_post"/>
        <result property="use" column="use"/>
        <result property="status" column="status"/>
        <result property="lng" column="lng"/>
        <result property="lat" column="lat"/>
        <result property="roadNo" column="road_no"/>
        <result property="deviceId" column="device_id"/>
        <result property="type" column="type"/>
        <result property="protocol" column="protocol"/>
        <result property="ipAddress" column="ip_address"/>
        <result property="port" column="port"/>
        <result property="hostId" column="host_id"/>
        <result property="sourceId" column="source_id"/>
        <result property="visibilityId" column="visibility_id"/>
        <result property="cameraId" column="camera_id"/>
        <result property="lightNumber" column="light_number"/>
    </resultMap>

    <resultMap type="com.bt.itsinner.domain.entity.OutfieldLinkage" id="outfieldLinkageMap">
        <result property="deviceId" column="device_id"/>
        <result property="type" column="type"/>
        <result property="content" column="content"/>
        <result property="time" column="time"/>
    </resultMap>

    <resultMap type="com.bt.itscore.domain.dto.FogInductStatusDTO" id="fogInductStatusMap">
        <result property="deviceId" column="device_id"/>
        <result property="visibility" column="visibility"></result>
        <result property="workMode" column="work_mode"/>
        <result property="lightLevel" column="light_level"/>
        <result property="flashFrequency" column="flash_frequency"/>
        <result property="tailLength" column="tail_length"/>
        <result property="dutyCycle" column="duty_cycle"/>
        <result property="offlineLight" column="offline_light"/>
        <result property="time" column="time"/>
        <result property="status" column="status"/>
    </resultMap>

    <select id="selectList" resultMap="deviceFogInductMap" parameterType="com.bt.itsinner.domain.entity.DeviceFogInduct">
        select f.road_no,dd.* from
        ( select d.device_id,d.device_name,d.mile_post,d.lng,d.lat,d.facility_no,d.status,d.source_id,
        c.visibility_id,c.camera_id,c.host_id,c.type,c.protocol,c.ip_address,c.port,c.light_number,d.use,d.direction_no
        from device d, device_foginduct c
        <if test="roadNo != null &amp;&amp; roadNo > 0 ">,facility f2 </if>
        where d.device_type_no=9 and d.device_id=c.device_id
        <if test="deviceName != null &amp;&amp; deviceName != '' "> AND (d.device_name like CONCAT('%', #{deviceName}, '%')
            or d.mile_post like CONCAT('%', #{deviceName}, '%'))</if>
        <if test="use != null &amp;&amp; use>=0 "> AND d.use =#{use}</if>
        <if test="protocol != null"> AND c.protocol =#{protocol}</if>
        <if test="roadNo != null &amp;&amp; roadNo > 0 "> and f2.road_no =#{roadNo} and f2.facility_no =d.facility_no </if>
        <if test="facilityNo != null &amp;&amp; facilityNo != '' "> AND d.facility_no =#{facilityNo}</if>
        <if test="sourceId != null"> AND d.source_id =#{sourceId}</if>
        <if test="idList != null &amp;&amp; idList.size()>0">
            and c.device_id in
            <foreach collection="idList" item="deviceId" index="index" open="(" close=")" separator=",">
                concat("",#{deviceId},"")
            </foreach>
        </if>
        ) dd
        left join facility f on dd.facility_no=f.facility_no
        left join direction e on dd.direction_no=e.direction_no
        order by dd.device_name asc
    </select>

    <select id="queryLinkageContent" resultMap="outfieldLinkageMap" parameterType="com.bt.itsinner.domain.entity.OutfieldLinkage">
        select device_id,type,content,time from outfield_linkage
        where 1=1
        <if test="deviceId != null"> AND device_id =#{deviceId}</if>
        <if test="type != null"> AND type =#{type}</if>
        order by id desc limit 1
    </select>

    <insert id="saveLinkageContent" parameterType="com.bt.itsinner.domain.entity.OutfieldLinkage">
        insert into outfield_linkage (device_id,type,content,time)
        values (#{deviceId},#{type},#{content},#{time})
    </insert>

    <update id="updateLinkageContent" parameterType="com.bt.itsinner.domain.entity.OutfieldLinkage">
        update outfield_linkage set
        <if test="content != null"> content=#{content},</if>
        <if test="time != null"> time=#{time},</if>
        type=#{type}
        where device_id = #{deviceId} and type = #{type}
    </update>

    <delete id="deleteLinkageContent" parameterType="com.bt.itsinner.domain.entity.OutfieldLinkage">
        delete from outfield_linkage where
        device_id = #{deviceId} and type = #{type}
    </delete>

    <insert id="updateStatus" parameterType="com.bt.itscore.domain.dto.FogInductStatusDTO">
        insert into foginduct_status_inner (device_id,visibility,work_mode,light_level,flash_frequency,tail_length,duty_cycle,time,offline_light,status)
        values
        (#{deviceId},#{visibility},#{workMode},#{lightLevel},#{flashFrequency},#{tailLength},#{dutyCycle},#{time},#{offlineLight},#{status})
        ON DUPLICATE KEY UPDATE
        <if test="visibility != null">visibility = #{visibility},</if>
        <if test="workMode != null">work_mode=#{workMode},</if>
        <if test="lightLevel != null">light_level=#{lightLevel},</if>
        <if test="flashFrequency != null">flash_frequency=#{flashFrequency},</if>
        <if test="tailLength != null">tail_length=#{tailLength},</if>
        <if test="dutyCycle != null">duty_cycle=#{dutyCycle},</if>
        <if test="offlineLight != null">offline_light=#{offlineLight},</if>
        <if test="status != null">status=#{status},</if>
        time=#{time}
    </insert>

    <select id="queryStatus" resultMap="fogInductStatusMap" parameterType="com.bt.itscore.domain.dto.FogInductPlanDTO">
        select * from foginduct_status_inner where TO_DAYS(time) = TO_DAYS(now())
        <if test="deviceId != null and deviceId != ''">and device_id =#{deviceId}</if>
    </select>







</mapper>