<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itsinner.mapper.CarFlowTotalMapper">	

	<insert id="add" parameterType="com.bt.itsinner.domain.dto.CarFlowTotalDTO">
		 insert into car_flow_total (id,create_time,e_today,e_yesterday,e_today_last_year,x_today,x_yesterday,x_today_last_year) 
		  values (#{id},#{createTime},#{eToday},#{eYesterday},#{eTodayLastYear},#{xToday},#{xYesterday},#{xTodayLastYear})
	</insert>
	
	<insert id="insertList" parameterType="java.util.List" useGeneratedKeys="false">
    	insert into car_flow_total (id,create_time,e_today,e_yesterday,e_today_last_year,x_today,x_yesterday,x_today_last_year) values
			<foreach collection="list" item="item" index="index" separator=",">
				(
					#{item.id},
					#{item.createTime},
					#{item.eToday},
					#{item.eYesterday},
					#{item.eTodayLastYear},
					#{item.xToday},
					#{item.xYesterday},
					#{item.xTodayLastYear}
				)
		     </foreach>		
    </insert>    

</mapper>