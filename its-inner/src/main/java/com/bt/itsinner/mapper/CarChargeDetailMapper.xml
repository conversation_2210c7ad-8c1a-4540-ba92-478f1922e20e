<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itsinner.mapper.CarChargeDetailMapper">	

	<insert id="add" parameterType="com.bt.itsinner.domain.dto.CarChargeDetailDTO">
		 insert into car_charge_detail (id,create_time,toll_station_id,cash_fee,other_fee,total_fee,total_count) 
		 values (#{id},#{createTime},#{tollStationId},#{cashFee},#{otherFee},#{totalFee},#{totalCount})
	</insert>	
	
	<insert id="insertList" parameterType="java.util.List" useGeneratedKeys="false">
    	insert into car_charge_detail (id,create_time,toll_station_id,cash_fee,other_fee,total_fee,total_count) values
			<foreach collection="list" item="item" index="index" separator=",">
				(
					#{item.id},
					#{item.createTime},
					#{item.tollStationId},
					#{item.cashFee},
					#{item.otherFee},
					#{item.totalFee},
					#{item.totalCount}
				)
		     </foreach>		
    </insert>    

   
	
</mapper>