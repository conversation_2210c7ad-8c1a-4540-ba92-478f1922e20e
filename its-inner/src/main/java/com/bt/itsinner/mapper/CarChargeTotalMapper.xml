<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itsinner.mapper.CarChargeTotalMapper">	

	<insert id="add" parameterType="com.bt.itsinner.domain.dto.CarChargeTotalDTO">
		 insert into car_charge_total (id,create_time,today_fee,today_cash_fee,today_other_fee,year_fee,year_cash_fee,year_other_fee,today_last_year_fee) 
		 values (#{id},#{createTime},#{todayFee},#{todayCashFee},#{todayOtherFee},#{yearFee},#{yearCashFee},#{yearOtherFee},#{todayLastYearFee})
	</insert>	
	
	<insert id="insertList" parameterType="java.util.List" useGeneratedKeys="false">
    	insert into car_charge_total (id,create_time,today_fee,today_cash_fee,today_other_fee,year_fee,year_cash_fee,year_other_fee,today_last_year_fee) values
			<foreach collection="list" item="item" index="index" separator=",">
				(
					#{item.id},
					#{item.createTime},
					#{item.todayFee},
					#{item.todayCashFee},
					#{item.todayOtherFee},
					#{item.yearFee},
					#{item.yearCashFee},
					#{item.yearOtherFee},
					#{item.todayLastYearFee}
				)
		     </foreach>		
    </insert>    
</mapper>