package com.bt.itsinner.mapper;

import com.bt.itscore.domain.dto.FogInductStatusDTO;
import com.bt.itsinner.domain.entity.DeviceFogInduct;
import com.bt.itsinner.domain.entity.OutfieldLinkage;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface DeviceFogInductMapper {
    public List<DeviceFogInduct> selectList(DeviceFogInduct paramDeviceFogInduct);

    public OutfieldLinkage queryLinkageContent(OutfieldLinkage linkage);

    public int saveLinkageContent(OutfieldLinkage linkage);

    public int updateLinkageContent(OutfieldLinkage linkage);

    public int deleteLinkageContent(OutfieldLinkage linkage);

    public int updateStatus(FogInductStatusDTO fogInductStatusDTO);

    public List<FogInductStatusDTO> queryStatus(FogInductStatusDTO fogInductStatusDTO);

}
