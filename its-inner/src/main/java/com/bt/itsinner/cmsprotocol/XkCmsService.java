package com.bt.itsinner.cmsprotocol;

import com.bt.itsinner.domain.dto.CmsBrightnessDTO;
import com.bt.itscore.domain.dto.CmsPageNodeDTO;
import com.bt.itscore.domain.dto.CmsRepertoryDTO;
import com.bt.itsinner.domain.dto.DeviceCmsDTO;
import com.bt.itsinner.utils.CmsUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.List;

@Service("XkCmsService")
public class XkCmsService {

    /**
     * 显科协议 开放的功能已接入完毕，从旧CS和itsway系统迁移过来未做修改
     */

    private String CMS_SINGLESEND_DIR;
    private String CMS_GROUPSEND_DIR;
    private String CMS_RECEIVE_DIR;
    private String CMS_IMAGES_DIR;

    private static final String CMD_BEGIN_FRAME = "02";//帧起始符，固定
    private static final String CMD_END_FRAME = "03";//帧结束符，固定
    private static final String CMD_DEVICE_ADDRESS = "3030";//范围01~99，设备地址，用户自定义
    private static final String CMD_DEST_ADDRESS = "3232";//范围01~99，目的地址，用户自定义
    private static final String CMD_ONLINE_CHECK = "3030";//通讯状态检测
    private static final String CMD_STATUS_CHECK = "3031";//总状态检测指令
    private static final String CMD_DEV_DETAIL = "3037";//设备详细状态
    private static final String CMD_DEAD_PEXIL = "3032";//读屏幕坏点数
    private static final String CMD_DRIVER_CHA = "3039";//设备驱动通道信息的好坏
    private static final String CMD_HAND_RESET = "3033";//手动复位情报板
    private static final String CMD_DEV_ONOFF = "3034";//打开或关闭系统设备
    private static final String CMD_GET_BRIGHTNESS = "3035";//读取当前亮度调节方式及亮度值
    private static final String CMD_BRIGHTNESS_SET = "3036";//设置亮度值
    private static final String CMD_GENERL_SEND = "3230";//文件下发通用指令
    private static final String CMD_FILE_GET = "3231";//文件读取指令
    private static final String CMD_SHOW_LIST = "3232";//显示指定列表
    private static final String CMD_LIST_GET = "3233";//获取当前显示列表
    private static final String CMD_DEVICE_DETAIL = "3037";//获得设备详细状态
    private static final String CMD_DAMAGED_PIXEL = "3032";//获得屏幕坏点数
    private static final String CMD_SET_TIME = "3430";//设置时间
    private static final String CMD_GET_TIME = "3431";//读取时间
    private static final String CMD_GET_INIT_TIME = "3432";//读取启动时间
    //中间指令
    private static final String CMD_DISPLAY_HEAD = "3130";//立即显示，头帧且唯一帧
    private static final String CMD_DISPLAY_ADD = "3131";//立即显示，追加帧
    private static final String CMD_DISPLAY_END = "3132";//立即显示，末尾帧

    private static final String CMS_LIST_DIR = "list\\";//list的根目录
    private static final String CMS_IMAGE_DIR = "image\\";//image的根目录
    private static final String CMS_LIST_NAME = "008.xkl";//指定的节目单名称

    private static final int CMD_MAX_LENGTH = 4096;//最大支持4096的String(2048的byte)
    private static final int CMD_TIME_OUT = 5000;//单指令超时设定5秒
    private static final int CONNECT_TIME_OUT = 5000;//建立TCP连接超时时间

    private static final int AUTO_PLAY_TIME = 5;//自动发布节目单时默认的单屏显示时长，这里表示5秒

    private static final String CHARSET = "GBK";

    private static final boolean useNew = true;//使用新版的repertory 或旧版的 program 的字段

    private final static Logger logger = LoggerFactory.getLogger(XkCmsService.class);

    @Value("${file.upload.path}")
    private String fileUploadPath;


    /**
     * 初始化文件上传路径
     */
    private void initFileUploadPath() {
        CMS_SINGLESEND_DIR = fileUploadPath + "/singlesend/";
        CMS_GROUPSEND_DIR = fileUploadPath + "/groupsend/";
        CMS_RECEIVE_DIR = fileUploadPath + "/receive/";
        CMS_IMAGES_DIR = fileUploadPath + "/images/";
    }


    public boolean cmsSendPage(DeviceCmsDTO deviceCms, CmsPageNodeDTO cmsPageNode) {
        initFileUploadPath();
        String path = CMS_SINGLESEND_DIR + deviceCms.getDeviceId().toString() + "/";
        File file = new File(path);
        if (file.exists()) {
            CmsUtils.deleteFile(path);//使用高优先级，不管其它客户端是否在操作，直接新建
            //return false;//当前有其它客户端正在发布情报板
        }
        file.mkdirs();
        if (!createPageList(cmsPageNode, path)) {//创建节目单
            logger.debug("创建节目单失败");
            return false;
        }
        return uploadPageList(deviceCms, path);//上传节目单
    }



    private boolean createPageList(CmsPageNodeDTO cmsPageNode, String sendPath) {
        logger.debug("创建显科节目单开始");

        /*	节目格式
		[LIST]
		ItemCount=002
		Item00=5,1,0,1,1,\C000000\I001\C032000\Fs2424\T255255000000\B000255000000\U美丽广西\N清洁乡村
		Item01=5,1,0,1,1,\C032000\Fs2424\T255255000000\B000255000000\U雨天路滑\N谨慎驾驶
		*/

        //封装节目头信息及ITEM_NO信息
        String listStr = "";
        listStr = "[LIST]" + "\r\n";
        listStr += "ItemCount=" + String.format("%03d", cmsPageNode.getChildren().size()) + "\r\n";

        for (int i = 0; i < cmsPageNode.getChildren().size(); i++) {
            CmsRepertoryDTO repertory = cmsPageNode.getChildren().get(i);
            StringBuffer buffer = new StringBuffer();
            int delay = repertory.getHoldTime();
            int inMode = Integer.parseInt(repertory.getInputMode());
            int outMode = Integer.parseInt(repertory.getOutputMode());
            //出入特效转换
            int[] convertMode = {1, 3, 2, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 12, 0};
            inMode = convertMode[inMode];
            outMode = convertMode[outMode];
            int speed = repertory.getSpeed();
            //速度取值为1-2-4-8-16，缺省为 1——需转换
            if (speed <= 1) {
                speed = 1;
            } else if (speed <= 2) {
                speed = 2;
            } else if (speed <= 4) {
                speed = 4;
            } else if (speed <= 8) {
                speed = 8;
            } else {
                speed = 16;
            }
            buffer.append(delay + "," + inMode + ",0," + outMode + "," + speed + ",");
            //如果有图片，先生成图片坐标
            if (repertory.getMultiBody() != null) {
//                buffer.append("\\C" + String.format("%03d", repertory.getImageX()) + String.format("%03d", repertory.getImageY()));
//                //无后缀的图片名
//                buffer.append("\\I" + repertory.getImageName());
            }
            String txtinfo = repertory.getMessageBody();
            txtinfo = txtinfo.replace("<br>", "\\N");//替换换行符
            txtinfo = txtinfo.replace("&nbsp;", " ");//替换空格
            txtinfo = txtinfo.replace("&ensp;", " ");//替换空格
            if (!StringUtils.isBlank(txtinfo)) {
                //如果有文字，文字坐标生成
                buffer.append("\\C" + String.format("%03d", repertory.getLabelX()) + String.format("%03d", repertory.getLabelY()));
            }
            String fontType = repertory.getFontType();
            int fontSize = repertory.getFontSize();
            if (fontSize == 12) {
                fontSize = 16;
            }
            //如果有字体
            if (!StringUtils.isBlank(fontType) && fontSize > 0) {
                String fontSizeStr = String.format("%02d", fontSize);
                buffer.append("\\F" + CmsUtils.fontConvert(fontType) + fontSizeStr);
            }
            String fontColor = repertory.getFontColor();
            //如果有字体颜色
            if (!StringUtils.isBlank(fontColor)) {
                buffer.append("\\T" + CmsUtils.colorConvert(fontColor) + "000");
            }
            String bgColor = repertory.getBackColor();
            //如果有背景颜色
            if (!StringUtils.isBlank(bgColor)) {
                buffer.append("\\B" + CmsUtils.colorConvert(bgColor) + "000");
            }
            //补上最后的正文
            if (!StringUtils.isBlank(txtinfo)) {
                buffer.append("\\U" + txtinfo);
            }
            //如果有图片，获取的是图片的完整名字,并复制到发送目录下
            String img = repertory.getMultiBody();
            if (!StringUtils.isBlank(img)) {
//                String srcImage = CMS_IMAGES_DIR + File.separator + repertory.getImageSize() + File.separator + img;//图片子路径及图片完整名称（带扩展名）32\002.bmp
//                String tagImage = sendPath + img;
//                if (CmsUtils.copyFile(srcImage, tagImage)) {
//                    logger.debug("图片复制到发送目录失败！");
//                    return false;
//                }
            }

            //封装一行的ITEM信息
            String itemstr = "Item" + String.format("%02d", i) + "=" + buffer.toString();
            listStr += itemstr + "\r\n";
        }
        String savePath = sendPath + CMS_LIST_NAME;
        boolean flag = CmsUtils.createFilebyCharset(listStr, savePath,"GBK");
        logger.debug("生成显科播放列表:" + flag + "\r\n" + listStr);
        return flag;
    }

    /**
     * 上传节目xkl文件及图片到设备
     *
     * @param deviceCms 设备信息
     * @param sendPath  节目xkl及位图文件的存储路径
     */
    private boolean uploadPageList(DeviceCmsDTO deviceCms, String sendPath) {
        //String path = CMS_SINGLESEND_DIR + deviceCms.getDevice_id().toString() + "\\";
        //1.优先上传图片文件
        if (!uploadImages(deviceCms, sendPath)) {
            return false;
        }
        //2.其次上传“008.xkl”节目单
        if (!uploadList(deviceCms, sendPath)) {
            return false;
        }
        return true;
    }

    /**
     * 情报板上传图片文件
     *
     * @param deviceCms 设备信息
     * @param sendPath  图片文件路径，仅根路径
     * @return
     */
    private boolean uploadImages(DeviceCmsDTO deviceCms, String sendPath) {
        File file = new File(sendPath);
        if (!file.exists()) {
            return false;
        }
        // 循环上传多个图片文件，任意一次上传失败均返回false
        File[] filelist = file.listFiles();
        if (filelist == null) {
            return false;
        }
        for (int i = 0; i < filelist.length; i++) {
            File file2 = filelist[i];
            logger.debug("显科情报板上传图片文件" + file2.getName() + "开始");
            if (file2.isFile() && (file2.getName().indexOf(".bmp") != -1)) {
                //1.读取文件流
                byte[] data = CmsUtils.readFileToBytes(file2);
                //2.转换文件流
                String strbyte = CmsUtils.bytesToHexString(data).toUpperCase();
                if (strbyte == null || strbyte == "") {
                    return false;
                }
                //3.启动传输
                String filename = file2.getName();
                if (!sendFiletoCms(deviceCms, filename, strbyte)) {
                    logger.debug("显科情报板上传图片文件" + file2.getName() + "失败");
                    return false;
                }
            }
        }
        logger.debug("显科情报板上传图片失败，结束");
        return true;
    }

    /**
     * 情报板上传节目单文件
     *
     * @param cms 设备信息
     * @param sendPath  节目单路径，仅根路径
     * @return
     */
    private boolean uploadList(DeviceCmsDTO cms, String sendPath) {
        String filePath = sendPath + CMS_LIST_NAME;
        File file = new File(filePath);
        if (!file.exists()) {
            return false;
        }
        //1.读取文件流
        byte[] data = CmsUtils.readFileToBytes(file);
        //2.转换文件流
        String strbyte = CmsUtils.bytesToHexString(data).toUpperCase();
        if (StringUtils.isBlank(strbyte)) {
            return false;
        }
        //3.启动传输
        Socket socket = new Socket();
        DataOutputStream out = null;
        DataInputStream in = null;
        boolean flag = false;
        try {
            socket.connect(new InetSocketAddress(cms.getIpAddress(), cms.getPort()), CMD_TIME_OUT);
            socket.setSoTimeout(CMD_TIME_OUT);
            out = new DataOutputStream(socket.getOutputStream());
            in = new DataInputStream(socket.getInputStream());
            flag = sendFiletoCms(in,out, CMS_LIST_NAME, strbyte);
            logger.debug("显科情报板上传节目单文件" + file.getName() + " " + flag);
            //播放列表发送成功后，发送立即显示命令指定播放列表
            if (flag) {
                flag = flag && setCurrentList(in,out, "008.xkl");
            }
        } catch (IOException e) {
            logger.debug("显科 setCurrentList 失败，流异常:" + e.getMessage());
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                }
            }
        }
        return flag;
    }

    //指定当前播放内容
    private boolean setCurrentList(DataInputStream in, DataOutputStream out, String listName) {
        logger.debug("显科指定当前播放内容：" + listName + "开始");
        String cmd = CMD_SHOW_LIST;
        String data = CmsUtils.stringToHexString(listName);
        String sendStr = cmd + CMD_DEVICE_ADDRESS + data;
        String recvHex = sendCmd(in, out, sendStr);

        boolean flag = (recvHex != null && recvHex.length() >= 2 && recvHex.substring(0, 2).equals("01"));
        logger.debug("显科指定当前播放内容：" + listName + " " + flag);
        return flag;
    }

    private boolean setCurrentList(DeviceCmsDTO cms, String listName){
        Socket socket = new Socket();
        DataOutputStream out = null;
        DataInputStream in = null;
        try {
            socket.connect(new InetSocketAddress(cms.getIpAddress(), cms.getPort()), CMD_TIME_OUT);
            socket.setSoTimeout(CMD_TIME_OUT);
            out = new DataOutputStream(socket.getOutputStream());
            in = new DataInputStream(socket.getInputStream());
            return setCurrentList(in, out, listName);
        } catch (IOException e) {
            logger.debug("显科 setCurrentList 失败，流异常:" + e.getMessage());
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                }
            }
        }
        return false;
    }

    /**
     * 上传文件通用方法，协议文件：4.10  文件下发指令
     * 注意：不同的命令，格式编排有所不同
     *
     * @param deviceCms 设备信息
     * @param filename  文件名称，带扩展名
     * @param strByte   文件内容
     * @return 指令格式：
     * 0x02(帧头) 0x32 0x30(指令头) 0x31 0x31(目的地址)0x37(0x30 更新，0x31 立即显示，0x32 重新启动，默认为 0x31) 0x37(文件帧标记，0x30 覆盖【头帧或唯一帧】，0x31 追加【中间帧】，0x32 帧结束)
     * 0x30 0x30 0x30（文件名长度）0x30 。。。。。。 0x30 (文件名，不定长度，包括文件路径，默认起始目录为\\xkcms\\，例如发送播放列表文件 000.xkl 时，文件名为  list\\000.xkl。)
     * 0x30 0x30 0x30 0x30 (帧序列，即文件偏移地址为帧序列 x 2048 个字节) 0x30 。。。。。。 0x30 (文件具体内容，不定长度)0x00 0x00(校验位) 0x03(帧尾)
     */
    private boolean sendFiletoCms(DeviceCmsDTO deviceCms, String filename, String strByte){
        Socket socket = null;
        DataOutputStream out = null;
        DataInputStream in = null;
        boolean flag = false;
        int offsetIndex = 0;//偏移量
        try {
            //1.创建传输套接字
            socket = new Socket();
            socket.connect(new InetSocketAddress(deviceCms.getIpAddress(), deviceCms.getPort()), CMD_TIME_OUT);
            socket.setSoTimeout(CMD_TIME_OUT);
            out = new DataOutputStream(socket.getOutputStream());
            in = new DataInputStream(socket.getInputStream());
            flag = sendFiletoCms(in,out,filename,strByte);
        } catch (IOException e) {
            logger.info("显科 sendFiletoCms 失败，设备无响应:" + e.getMessage());
        } finally {
            CmsUtils.closeIOStream(in,out);
        }
        return false;
    }
    private boolean sendFiletoCms(DataInputStream in,DataOutputStream out, String filename, String strByte) {
        boolean flag = false;
        int offsetIndex = 0;//偏移量
        try {
            //2.分步传输字节流
            while (strByte != null) {//不使用“strByte.length() ！= 0”条件的原因
                //这里注意判断依据，按照协议当strbyte的长度恰巧等于DATA_MAX_LENTH时仍然需要发送内容为空（即strbyte长度为0）的结束帧，此时走单个指令即可完成的流程
                //1.这里需要优先确定指令格式，根据帧的长度属性
                String cmdMode = "";
                if (offsetIndex == 0) {
                    cmdMode = CMD_DISPLAY_HEAD;//头帧或唯一帧
                } else {
                    if (strByte.length() < CMD_MAX_LENGTH) {
                        cmdMode = CMD_DISPLAY_END;//末尾帧
                    } else {
                        cmdMode = CMD_DISPLAY_ADD;//中间帧
                    }
                }
                //2.基本命令，此处先不加帧头，为了计算CRC方便
                String controlstr = CMD_GENERL_SEND + CMD_DEVICE_ADDRESS + CMD_DISPLAY_HEAD;//cmdMode;
                //3.文件长度补齐，0x30 0x30 0x30（文件名长度），如：image/000.bmp
                String pathstr = "";
                if (filename.indexOf(".bmp") != -1) {
                    pathstr = CMS_IMAGE_DIR + filename;
                } else if (filename.indexOf(".xkl") != -1) {
                    pathstr = CMS_LIST_DIR + filename;
                } else {
                    // 下发的文件格式异常！
                    return false;
                }
                // 如文件名称的长度为8，需要补齐为：008，再转换成hexstring的格式：30 30 38
                String pathlength = String.format("%03d", pathstr.length());
                controlstr += CmsUtils.stringToHexString(pathlength);
                //4.不定长度文件名
                controlstr += CmsUtils.stringToHexString(pathstr);
                //5.转换偏移地址，文件拆分时偏移的字节量
                String offsetstr = CmsUtils.stringToHexString(String.format("%04d", offsetIndex));// 偏移地址
                controlstr += offsetstr;
                //6.校验位
                String tempstr = "";
                if (strByte.length() == CMD_MAX_LENGTH) {//特殊处理，末尾需追加内容为空的指令--不需要
                    tempstr = strByte;
                    strByte = "";//末尾把strbyte置为""
                    logger.info("[显科]长度正好2048字节，不分割");
                } else if (strByte.length() <= CMD_MAX_LENGTH) {//单个指令即可完成
                    tempstr = strByte;
                    strByte = null;//末尾把strbyte置为null
                    logger.info("[显科]长度小于2048字节，不分割:"+tempstr.length());
                } else {//需要拆分成多个传输
                    tempstr = strByte.substring(0, CMD_MAX_LENGTH);
                    offsetIndex += 1;
                    strByte = strByte.substring(CMD_MAX_LENGTH);//末尾把strbyte置为新的长度
                    logger.info("[显科]长度大于2048字节，分割，下一段"+strByte.length());
                }
                controlstr += tempstr;//传入的strbyte已经是hexstring的格式
                String crcstr = CmsUtils.getVerifyCRC(controlstr, 4);
                controlstr += crcstr;
                //7.转义字符
                String convertstr = CmsUtils.escape(controlstr);
                //8.指令的完整形式
                convertstr = CMD_BEGIN_FRAME + convertstr + CMD_END_FRAME;
                logger.info("显科上传文件通用方法:\r\n" + convertstr);
                //9.传输
                byte[] senddata = CmsUtils.hexStringToBytes(convertstr);
                out.write(senddata);
            }
            //在文件分段传输中，是所有分段传输完毕后，情报板才有回复信息，不是每段都有回复信息
            int readlenth = 0;
            byte[] respondata = new byte[1024];
            // 这里的read()是阻塞式的，所以在前面加上了socket.setSoTimeout()设置，表示最大的响应时间
            readlenth = in.read(respondata);
            if (readlenth > 5) {
                respondata = Arrays.copyOfRange(respondata, 0, readlenth);
                String recv = CmsUtils.bytesToHexString(respondata);
                logger.info("显科上传文件通用方法回复为:\r\n" + recv);
                // 回复指令：0x02(帧头) 0x32 0x30(指令头) 0x31 0x31(目的地址)  0x01(指令执行情况，00 表示异常，01 表示正常) 0x00 0x00(校验位) 0x03(帧尾)
                byte rsp = respondata[5];
                flag = recv.substring(10, 12).equals("01");
            }
            logger.info("显科上传文件通用方法:" + flag);
            return flag;
        } catch (IOException e) {
            logger.info("显科 sendFiletoCms 失败，设备无响应:" + e.getMessage());
        }
        return false;
    }




    public List<String> TotalStatusCheck(DeviceCmsDTO cms) {
        /**
         * 设备总状态检查
         * @param cms
         * @return 12项，通讯、电源电压、风扇、门开关状态、系统、驱动通道、LED像素、感光头、防雷器，其他保留；30表示异常，31表示正常
         */
        String sendHexStr = CMD_STATUS_CHECK + CMD_DEVICE_ADDRESS;
        String hexReulst = sendCmd(cms, sendHexStr);
        if (hexReulst == null) {
            return null;
        }
        //指令执行情况
        boolean commandIsExecuted = (Integer.parseInt(hexReulst.substring(0, 2), 16) == 1);
        //设备状态
        if (!commandIsExecuted) {
            return null;
        }
        String deviceStatus = CmsUtils.hexStringToString(hexReulst.substring(2, 20), "GB2312");
        //每个设备的状态返回1字节ASCII码符号"0"or"1"
        ArrayList<String> result = new ArrayList<String>();
        for (int i = 0; i < deviceStatus.length(); i++) {
            result.add(deviceStatus.substring(i, i + 1));
        }
        return result;
    }


    public boolean OnlineStatusCheck(DeviceCmsDTO cms) {
        /**
         * 通讯状态检测
         * 说明：返回值
         * @param cms
         * @return 是否正常
         */
        String sendHexStr = CMD_ONLINE_CHECK + CMD_DEVICE_ADDRESS;
        String hexReulst = sendCmd(cms, sendHexStr);
        if (hexReulst == null) {
            return false;
        }
        //指令执行情况
        boolean commandIsExecuted = (Integer.parseInt(hexReulst.substring(0, 2), 16) == 1);
        return commandIsExecuted;
    }


    public CmsBrightnessDTO getBrightness(DeviceCmsDTO cms) {
        /**
         * 获取当前亮度控制模式或显示亮度
         * @param cms
         * @return 共8字节：6字节情报板亮度 + 2字节环境亮度
         */
        CmsBrightnessDTO CmsBrightnessDTO = new CmsBrightnessDTO();
        String sendHexStr = CMD_GET_BRIGHTNESS + CMD_DEVICE_ADDRESS;
        String hexReulst = sendCmd(cms, sendHexStr);
        if (hexReulst == null) {
            return CmsBrightnessDTO;
        }
        //指令执行情况
        boolean commandIsExecuted = (Integer.parseInt(hexReulst.substring(0, 2), 16) == 1);
        if (!commandIsExecuted) {
            return CmsBrightnessDTO;
        }
        //亮度模式 0x30 手动，0x31 自动，0x32 程序调光
        Integer lightMode = Integer.parseInt(CmsUtils.hexStringToString(hexReulst.substring(2, 4), "GB2312"));
        CmsBrightnessDTO.setMode(lightMode);
        //红绿蓝三基色亮度0-31
        String redLight = CmsUtils.hexStringToString(hexReulst.substring(4, 8), "GB2312");
        String greenLight = CmsUtils.hexStringToString(hexReulst.substring(8, 12), "GB2312");
        String blueLight = CmsUtils.hexStringToString(hexReulst.substring(12, 16), "GB2312");
        //环境亮度
        String environmentLight = CmsUtils.hexStringToString(hexReulst.substring(16, 20), "GB2312");
        //一般三基色亮度一致，选取红色亮度，并转换0-31为0-100
        int light = Integer.parseInt(redLight);
        light = (int) (100 * (float) light / 31);
        CmsBrightnessDTO.setBrightness(light);
        return CmsBrightnessDTO;
    }


    public boolean setBrightness(DeviceCmsDTO cms, CmsBrightnessDTO cmsBrightnessDTO) {
        /**
         * 设置亮度，亮度值范围：0~31，值为255时表示自动调节
         */
        if(cmsBrightnessDTO.getMode()==1)
        {
            String mode = "31";
            String lightHexValue = "3030";
            String sendHexStr = CMD_BRIGHTNESS_SET + CMD_DEVICE_ADDRESS + mode + lightHexValue + lightHexValue + lightHexValue;
            String hexResult = sendCmd(cms, sendHexStr);
            if (hexResult == null) {
                return false;
            }
            boolean result = Integer.parseInt(hexResult.substring(0, 2), 16) == 1;
            return result;
        }
        else {
            String mode = "30";
            String lightHexValue = "3030";

            int intValue = (int) 0.31 * cmsBrightnessDTO.getBrightness();
            if (intValue < 0) {
                intValue = 0;
            } else if (intValue > 31) {
                intValue = 31;
            }
            lightHexValue = CmsUtils.stringToHexString(String.valueOf(intValue));
            String sendHexStr = CMD_BRIGHTNESS_SET + CMD_DEVICE_ADDRESS + mode + lightHexValue + lightHexValue + lightHexValue;
            String hexResult = sendCmd(cms, sendHexStr);
            if (hexResult == null) {
                return false;
            }
            boolean result = Integer.parseInt(hexResult.substring(0, 2), 16) == 1;
            return result;
        }
    }





    public boolean downloadFileFromCms(DeviceCmsDTO cms, String fileName, String savePath) {
        /**
         * 从设备下载文件通用指令
         * @param deviceCms    情报板
         * @param fileName    包括路径的文件名
         * @param savePath    保存路径
         * @return
         */
        Socket socket = null;
        DataOutputStream out = null;
        DataInputStream in = null;
        boolean flag = false;
        int offsetindex = 0;//偏移量
        try {
            //1.创建传输套接字
            socket = new Socket();
            socket.setSoTimeout(CMD_TIME_OUT);
            socket.connect(new InetSocketAddress(cms.getIpAddress(), cms.getPort()), CONNECT_TIME_OUT);
            out = new DataOutputStream(socket.getOutputStream());
            in = new DataInputStream(socket.getInputStream());
            return downloadFileFromCms(in, out, fileName, savePath);
        } catch (IOException e) {
            logger.debug("显科 downloadFileFromCms 失败:" + e.getMessage());
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
                if (out != null) {
                    out.close();
                }
            } catch (IOException e) {
            }
        }
        return false;
    }

    private boolean downloadFileFromCms(DataInputStream in, DataOutputStream out, String fileName, String savePath) {
        String recvHex = getFileStringFromCms(in, out, fileName);
        if (recvHex == null || recvHex.length() == 0) {
            return false;
        }
        //将获得的十六进制文件数据存储到本地计算机中
        byte[] bytesFileData = CmsUtils.hexStringToBytes(recvHex);
        FileOutputStream fos = null;
        try {
            fos = new FileOutputStream(savePath);
            fos.write(bytesFileData);
            fos.flush();
            return true;
        } catch (IOException e) {
            logger.debug("显科 downloadFileFromCms 失败:" + e.getMessage());
        } finally {
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                }
            }
        }
        return false;
    }

    private String getFileStringFromCms(DataInputStream in, DataOutputStream out, String fileName) {
        logger.debug("从显科情报板获取文件String");
        // "09"：2 字节帧类型
        String cmd = CMD_FILE_GET;
        //目的地址
        String deviceAddress = CMD_DEVICE_ADDRESS;
        //文件名：不定长 ASCII 码字符串
        fileName = CmsUtils.stringToHexString(fileName);
        String fileNameLenHex = String.format("%03d", fileName.length() / 2);
        fileNameLenHex = CmsUtils.stringToHexString(fileNameLenHex);
        //帧序列，即文件偏移地址为帧序列x 2048个字节
        int offset = 0;
        int dataLenGot = 2048;
        StringBuffer fileData = new StringBuffer();
        while (dataLenGot >= 2048) {
            String frameSerial = String.format("%04d", offset);
            frameSerial = CmsUtils.stringToHexString(frameSerial);
//            String frameSerial = addZero(Integer.toHexString(offset*CMD_MAX_LENGTH/2),8);
            String data = cmd + deviceAddress + fileNameLenHex + fileName + frameSerial;
            String recv = sendCmd(in, out, data);
            logger.debug("回复为\r\n" + recv);
            if (recv == null) {
                //接收不到这一段的数据
                if (fileData.length() == 0) {
                    return null;
                } else {
                    break;
                }
            } else {
                fileData.append(recv);
                dataLenGot = recv.length() / 2;
                offset++;
            }
        }
        //处理获得的报文，提取文件数据内容
        int fileNameLen = fileName.length() / 2;
        String trueFileData = fileData.substring((4 + fileNameLen + 4) * 2);
        return trueFileData;
    }

    //获取当前播放列表
    public String downloadCurrentList(DeviceCmsDTO cms) {
        Socket socket = null;
        DataOutputStream out = null;
        DataInputStream in = null;
        boolean flag = false;
        int offsetIndex = 0;//偏移量
        try {
            //1.创建传输套接字
            socket = new Socket();
            socket.connect(new InetSocketAddress(cms.getIpAddress(), cms.getPort()), CONNECT_TIME_OUT);
            socket.setSoTimeout(CMD_TIME_OUT);
            out = new DataOutputStream(socket.getOutputStream());
            in = new DataInputStream(socket.getInputStream());
            //先获取当前播放列表
            String data = CMD_LIST_GET + CMD_DEVICE_ADDRESS;
            String recvHex = sendCmd(in, out, data);
            if (recvHex == null || recvHex.length() < 2) {
                return null;
            }
            int result = Integer.parseInt(recvHex.substring(0, 2), 16);
            if (result == 0) {
                return null;
            }
            String listName = CmsUtils.hexStringToString(recvHex.substring(2), CHARSET);
            logger.debug("当前播放列表是: " + listName);
            //再下载当前播放列表
            return getFileStringFromCms(in, out, "list\\\\" + listName);
        } catch (IOException e) {
            logger.debug("显科 downloadCurrentList 失败:" + e.getMessage());
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
                if (out != null) {
                    out.close();
                }
            } catch (IOException e2) {
            }
        }
        return null;
    }

    public boolean TurnOnOrOffCms(DeviceCmsDTO cms, boolean isOn) {
        String cmsSwitch = isOn ? "31" : "30";
        String sendStr = "3034" + CMD_DEVICE_ADDRESS + "30" + cmsSwitch;
        String hex = sendCmd(cms, sendStr);
        if (hex != null && hex.length() >= 2 && hex.substring(0, 2).equals("01")) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获得设备详细状态
     *
     * @param cms 情报板设备对象
     * @return 箱体个数[0]
     * 亮度A[1] 1-正常，0-异常，下同
     * 亮度B [2]
     * 总温度[3]
     * 220v电源[4]
     * 防雷器[5]
     * 箱体1温度[6]
     * 箱体1电源1[7]
     * 箱体1电源2[8]
     * 箱体1电源3[9]
     * 箱体1电源4[10]
     * 箱体1电源5[11]
     * ...
     * <p>
     * 门开关
     * 箱体驱动好坏
     * 单元系统好坏
     */

    public List<Integer> getDeviceDetail(DeviceCmsDTO cms) {
        String cmd = CMD_DEVICE_DETAIL;
        //目的地址
        String deviceAddress = CMD_DEVICE_ADDRESS;

        String data = cmd + deviceAddress;
        String recv = sendCmd(cms, data);

        List<Integer> list = new ArrayList<>();
        //处理获得的报文
        if (recv != null && recv.length() >= 36) {
            boolean isExecuted = recv.substring(0, 2).equals("01");
            if (isExecuted) {
                int boxNumber = Integer.parseInt(CmsUtils.hexStringToString(recv.substring(2, 6), CHARSET));
                list.add(boxNumber);
                int lightA = Integer.parseInt(CmsUtils.hexStringToString(recv.substring(6, 12), CHARSET));
                list.add(lightA);
                int lightB = Integer.parseInt(CmsUtils.hexStringToString(recv.substring(12, 18), CHARSET));
                list.add(lightB);
                int temperature = Integer.parseInt(CmsUtils.hexStringToString(recv.substring(18, 24), CHARSET));
                list.add(temperature);
                int power = Integer.parseInt(CmsUtils.hexStringToString(recv.substring(24, 30), CHARSET));
                list.add(power);
                int lightningArrester = Integer.parseInt(CmsUtils.hexStringToString(recv.substring(30, 36), CHARSET));
                list.add(lightningArrester);
                if (recv.length() >= 36 + 48 * boxNumber) {
                    for (int i = 0; i < boxNumber; i++) {
                        String str = recv.substring(36 + 48 * i, 46 + 48 * (i + 1));
                        int temperatureSingle = Integer.parseInt(CmsUtils.hexStringToString(str.substring(0, 6), CHARSET));
                        list.add(temperatureSingle);
                        int power1 = Integer.parseInt(CmsUtils.hexStringToString(str.substring(6, 12), CHARSET));
                        list.add(power1);
                        int power2 = Integer.parseInt(CmsUtils.hexStringToString(str.substring(12, 18), CHARSET));
                        list.add(power2);
                        int power3 = Integer.parseInt(CmsUtils.hexStringToString(str.substring(18, 24), CHARSET));
                        list.add(power3);
                        int power4 = Integer.parseInt(CmsUtils.hexStringToString(str.substring(24, 30), CHARSET));
                        list.add(power4);
                        int power5 = Integer.parseInt(CmsUtils.hexStringToString(str.substring(30, 36), CHARSET));
                        list.add(power5);
                        int gateSwitch = Integer.parseInt(CmsUtils.hexStringToString(str.substring(36, 42), CHARSET));
                        gateSwitch = gateSwitch == 5 ? 1 : 0;
                        list.add(gateSwitch);
                    }
                }
                if (recv.length() >= 36 + 48 * boxNumber + 12) {
                    int boxDriver = Integer.parseInt(CmsUtils.hexStringToString(recv.substring(36 + 48 * boxNumber, 36 + 48 * boxNumber + 6), CHARSET));
                    int unitSystem = Integer.parseInt(CmsUtils.hexStringToString(recv.substring(36 + 48 * boxNumber + 6, 36 + 48 * boxNumber + 12), CHARSET));
                    unitSystem = unitSystem == 5 ? 1 : 0;
                    list.add(boxDriver);
                    list.add(unitSystem);
                }
            }
        }
        return list;
    }


    public List<String> getDamagedPixel(DeviceCmsDTO cms) {
        String data = CMD_DAMAGED_PIXEL + CMD_DEVICE_ADDRESS + "31";
        String recv = sendCmd(cms, data);

        List<String> list = new ArrayList<>();
        //处理获得的报文
        if (recv != null && recv.length() >= 26) {
            boolean isExecuted = recv.substring(0, 2).equals("01");
            if (isExecuted) {
                int count = Integer.parseInt(recv.substring(2, 14));
                int width = Integer.parseInt(recv.substring(14, 20));
                int height = Integer.parseInt(recv.substring(20, 26));
                list.add(count + "");
                list.add(width + "");
                list.add(height + "");
                if (recv.length() > 26 + count / 4) {
                    String binary = recv.substring(26,26 + count / 4);
                    binary = Integer.toBinaryString(Integer.parseInt(binary,16));
                    for (int i = 0; i < count; i++) {
                        char pixel = binary.charAt(i);
                        if((int)pixel==1)
                        {
                            int x = i%width;
                            int y = i/width;
                            list.add("("+x+","+y+")");
                        }
                    }
                }
            }
        }
        return list;
    }

    /**
     * 手动复位设备
     * @param cms 情报板
     * @return
     */

    public boolean resetDevice(DeviceCmsDTO cms)
    {
        String data =  CMD_HAND_RESET+CMD_DEVICE_ADDRESS;
        String recv = sendCmd(cms, data);
        return (recv != null && recv.length() >= 2&&recv.substring(0,2).equals("01"));
    }


    /**
     * 显示指定预置播放列表（0-999）
     * @param cms 情报板
     * @param listNumber 列表（0-999）
     * @return
     */

    public boolean showPresetPlaylist(DeviceCmsDTO cms, int listNumber) {
        String data =  CMD_SHOW_LIST+CMD_DEVICE_ADDRESS+CmsUtils.stringToHexString(String.format("%03d",listNumber)+".xkl");
        String recv = sendCmd(cms, data);
        return (recv != null && recv.length() >= 2&&recv.substring(0,2).equals("01"));
    }

    /**
     * 获取当前播放列表编号
     * @param cms 情报板
     * @return
     */

    public int getCurrentPlaylistNumber(DeviceCmsDTO cms)
    {
        int result = 0;

        String data =  CMD_LIST_GET+CMD_DEVICE_ADDRESS;
        String recv = sendCmd(cms, data);
        if(recv != null && recv.length() >= 16&&recv.substring(0,2).equals("01"))
        {
            result = Integer.parseInt(CmsUtils.hexStringToString(recv.substring(2,8)));
        }
        return result;
    }

    /**
     * 时间同步
     * @param cms 情报板
     * @return
     */

    public boolean timeSynchronizion(DeviceCmsDTO cms)
    {
        boolean flag = false;

        Calendar cal = Calendar.getInstance();
        int second = cal.get(Calendar.SECOND);//秒
        int minute = cal.get(Calendar.MINUTE);//分
        int hour = cal.get(Calendar.HOUR_OF_DAY);//小时
        int weekday = cal.get(Calendar.DAY_OF_WEEK);//星期
        int day = cal.get(Calendar.DATE);//获取日
        int month = cal.get(Calendar.MONTH) + 1;//获取月份
        int year = cal.get(Calendar.YEAR);//获取年份
        String data = String.format("%04d",year)+String.format("%02d",month)+String.format("%02d",day)+String.format("%02d",hour)+String.format("%02d",minute)+String.format("%02d",second);
        data = CMD_SET_TIME+CMD_DEVICE_ADDRESS+CmsUtils.stringToHexString(data);
        String recv = sendCmd(cms,data);
        flag = (recv!=null&&recv.length()>=2&&recv.substring(0,2).equals("01"));
        return flag;
    }

    /**
     * 获取此时设备时间
     * @param cms
     * @return
     */

    public String getCurrentDeviceTime(DeviceCmsDTO cms)
    {
        String result = null;
        String data = CMD_GET_TIME+CMD_DEVICE_ADDRESS;
        String recv = sendCmd(cms,data);
        if(recv!=null&&recv.length()>=30&&recv.substring(0,2).equals("01")){
            result = CmsUtils.hexStringToString(recv.substring(2,30));
            //解析时间
            String year = result.substring(0,4);
            String month = result.substring(4,6);
            String day = result.substring(6,8);
            String hour = result.substring(8,10);
            String minute = result.substring(10,12);
            String second = result.substring(12,14);
            result = year+"-"+month+"-"+day+" "+hour+":"+minute+":"+second;
        }
        return result;
    }

    /**
     * 获得设备最后启动时间(1970-01-01 00:00:00)
     * @param cms 情报板
     * @return
     */

    public String getLastInitInfo(DeviceCmsDTO cms)
    {
        String result = null;
        String data = CMD_GET_INIT_TIME+CMD_DEVICE_ADDRESS;
        String recv = sendCmd(cms,data);
        if(recv!=null&&recv.length()>=30&&recv.substring(0,2).equals("01")){
            result = CmsUtils.hexStringToString(recv.substring(2,30));
            //解析时间
            String year = result.substring(0,4);
            String month = result.substring(4,6);
            String day = result.substring(6,8);
            String hour = result.substring(8,10);
            String minute = result.substring(10,12);
            String second = result.substring(12,14);
            result = year+"-"+month+"-"+day+" "+hour+":"+minute+":"+second;
        }
        return result;
    }

    public String sendCmd(DeviceCmsDTO deviceCms, String sendHexStr) {
        Socket socket = null;
        DataOutputStream out = null;
        DataInputStream in = null;
        boolean flag = false;
        int offsetindex = 0;//偏移量
        try {
            //1.创建传输套接字
            socket = new Socket();
            socket.connect(new InetSocketAddress(deviceCms.getIpAddress(), deviceCms.getPort()), CONNECT_TIME_OUT);//连接超时时间
            socket.setSoTimeout(CMD_TIME_OUT);//读超时时间
            out = new DataOutputStream(socket.getOutputStream());
            in = new DataInputStream(socket.getInputStream());
            return sendCmd(in, out, sendHexStr);
        } catch (IOException e) {
            logger.debug("显科 generalInquire 失败:" + e.getMessage());
        } finally {
            CmsUtils.closeIOStream(in,out);
        }
        return null;
    }

    public String sendCmd(DataInputStream in, DataOutputStream out, String sendHexStr) {
        boolean flag = false;
        int offsetIndex = 0;//偏移量
        try {
            //计算校验位
            String crcstr = CmsUtils.getVerifyCRC(sendHexStr, 4);
            sendHexStr = sendHexStr + crcstr;
            //转义字符
            String convertstr = CmsUtils.escape(sendHexStr);
            //指令的完整形式
            convertstr = CMD_BEGIN_FRAME + convertstr + CMD_END_FRAME;
            logger.info("显科通用发送指令:\r\n" + convertstr);
            //传输
            byte[] sendData = CmsUtils.hexStringToBytes(convertstr);
            out.write(sendData);
            out.flush();
            int readLenth = 0;
            byte[] responData = new byte[CMD_MAX_LENGTH / 2];
            readLenth = in.read(responData);
            if (readLenth > 0) //说明有数据
            {
                responData = Arrays.copyOfRange(responData, 0, readLenth);
                String hexReuslt = CmsUtils.bytesToHexString(responData);
                //反转义
                hexReuslt = CmsUtils.deEscape(hexReuslt);
                //除去帧头、指令头、设备地址、校验位和帧尾
                hexReuslt = hexReuslt.substring(10, hexReuslt.length() - 6);
                return hexReuslt;
            }
        } catch (IOException e) {
            logger.debug("显科 generalInquire 失败:" + e.getMessage());
        }
        return null;
    }

}
