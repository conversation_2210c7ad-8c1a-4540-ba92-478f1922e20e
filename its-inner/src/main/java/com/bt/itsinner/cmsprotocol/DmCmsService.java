package com.bt.itsinner.cmsprotocol;


import com.bt.itscore.domain.dto.CmsPageNodeDTO;
import com.bt.itscore.domain.dto.CmsRepertoryDTO;
import com.bt.itsinner.domain.dto.DeviceCmsDTO;
import com.bt.itsinner.domain.entity.CmsMultiMedia;
import com.bt.itsinner.utils.CmsUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


@Service("dmCmsService")
public class DmCmsService {
    /**
     * 深圳电明情报板发布类，从旧CS和itsway系统迁移过来未做修改
     */

    private final static Logger LOGGER = LoggerFactory.getLogger(DmCmsService.class);

    private String CMS_SINGLESEND_DIR = "";
    private String CMS_GROUPSEND_DIR = "";
    private String CMS_RECEIVE_DIR = "";
    private String CMS_IMAGES_DIR = "";

    private static final String CMD_BEGIN_FRAME = "02";//帧起始符，固定
    private static final String CMD_END_FRAME = "03";//帧结束符，固定
    private static final String CMD_SOURCE_ADDRESS = "3030";//范围01~99，源地址，用户自定义
    private static final String CMD_DEST_ADDRESS = "3030";//范围01~99，目的地址，用户自定义
    private static final String CMD_STATUS_CHECK = "3031";//总状态检测指令
    private static final String CMD_ONLINE_CHECK = "3239";//通讯状态检测
    private static final String CMD_DEAD_PEXIL = "3639";//读屏幕坏点数
    private static final String CMD_BOX_FANS = "3039";//箱体风扇开关
    private static final String CMD_MONITOR_SWITCH = "3131";//显示屏开关
    private static final String CMD_BRIGHTNESS_MODE = "3231";//获取当前亮度控制模式或显示亮度
    private static final String CMD_BRIGHTNESS_VALUE = "3233";//设置亮度值
    private static final String CMD_DISPLAY_LIST = "3437";//显示指定的播放列表
    private static final String CMD_LIST_NUMBER = "3439";//获取当前显示列表
    private static final String CMD_GENERL_SEND = "3035";//文件下发通用指令
    private static final String CMD_FILE_SEND = "3731";//下发播放列表并立即显示
    private static final String CMD_FILE_LIST = "3339";//下发播放列表文件
    private static final String CMD_FILE_ADD = "2B";//下发文件时追加
    private static final String CMD_FILE_RECOVER = "2D";//下发文件时覆盖

    private static final String CMS_ROOT_DIR = "icon/";//情报板节目单根目录
    private static final String CMS_LIST_NAME = "play00.lst";//指定的节目单名称

    private static final int CMD_MAX_LENGTH = 2048;//深圳电明V1最大支持4096String(2048byte)；桂林海威(V0)旧版支持1024byte
    private static final int CMD_MAX_LENGTH_V1 = 4096;//V1支持
    private static final int CMD_MAX_LENGTH_NEW = 1024;
    private static final int CMD_TIME_OUT = 5000;//单指令超时设定5秒


    @Value("${file.upload.path}")
    private String fileUploadPath;

    /**
     * 初始化文件上传路径
     */
    private void initFileUploadPath() {
        CMS_SINGLESEND_DIR = fileUploadPath + "/singlesend/";
        CMS_GROUPSEND_DIR = fileUploadPath + "/groupsend/";
        CMS_RECEIVE_DIR = fileUploadPath + "/receive/";
        CMS_IMAGES_DIR = fileUploadPath + "/images/";
    }


    //***********************************************************************************************************************//
    // 新的发布流程20190305
    public boolean cmsSendPage(DeviceCmsDTO deviceCms, CmsPageNodeDTO cmsPageNode) {
        initFileUploadPath();
        String path = CMS_SINGLESEND_DIR + deviceCms.getDeviceId().toString() + "/";
        File file = new File(path);
        if (file.exists()) {
            CmsUtils.deleteFile(path);//使用高优先级，不管其它客户端是否在操作，直接新建
            //return false;//当前有其它客户端正在发布情报板
        }
        file.mkdirs();
        if (!createPageList(cmsPageNode, path, deviceCms)) {//创建节目单
            return false;
        }
        return uploadPageList(deviceCms, path);//上传节目单
    }

    /**
     * 生产节目单文件
     *
     * @param cmsPageNode
     * @param sendPath
     * @return
     */
    private boolean createPageList(CmsPageNodeDTO cmsPageNode, String sendPath, DeviceCmsDTO cms) {
		/*	节目格式
		[PLAYLIST]
		ITEM_NO=002
		ITEM000=500,0,0,0,0,\C000000\B001\C032000\Fs2424\T255255000000\K000255000000\W美丽广西\A清洁乡村
		ITEM001=500,0,0,0,0,\C032000\Fs2424\T255255000000\K000255000000\W雨天路滑\A谨慎驾驶
		*/

        //封装节目头信息及ITEM_NO信息
        String listStr = "";
        listStr = "[PLAYLIST]" + "\r\n";
        listStr += "ITEM_NO=" + String.format("%03d", cmsPageNode.getChildren().size()) + "\r\n";

        for (int i = 0; i < cmsPageNode.getChildren().size(); i++) {
            CmsRepertoryDTO repertory = cmsPageNode.getChildren().get(i);
            //处理时间字段，单位转换*100
            String tm = "" + repertory.getHoldTime() * 100;
            //昭蒙的协议需要特殊处理
            if ("V2".equals(cms.getVersion())) {
                tm = "" + repertory.getHoldTime() * 1000;
            }
            listStr += "ITEM" + String.format("%03d", i) + "=" + tm + ",";
            //ITEMYYY = delay, inputmode, stopmode, outputmode, speed, str
            listStr += repertory.getInputMode() + "," + "0" + ",";
            listStr += repertory.getOutputMode() + "," + String.valueOf(repertory.getSpeed()) + ",";
            //如果是图文发布
            if (StringUtils.isNotBlank(repertory.getMultiBody())) {
                List<CmsMultiMedia> multiList = MultiUtils.getMediaList(repertory);
                boolean uninitializedWordFlag = true;
                for (CmsMultiMedia m : multiList) {
                    //如果是文字
                    if ("txt".equals(m.getType())) {
                        //如果是首个文字，加上字体信息
                        if (uninitializedWordFlag) {
                            listStr += "\\F" + CmsUtils.fontConvert(repertory.getFontType()) + repertory.getFontSize().toString() + repertory.getFontSize().toString();
                            listStr += "\\T" + CmsUtils.colorConvert(repertory.getFontColor()) + "000";
                            if (!"V2".equals(cms.getVersion())) {
                                listStr += "\\K" + CmsUtils.colorConvert(repertory.getBackColor()) + "000";
                            }
                            //然后置为已初始化
                            uninitializedWordFlag = false;
                        }
                        //处理坐标信息
                        listStr += "\\C" + String.format("%03d", m.getX()) + String.format("%03d", m.getY());
                        listStr = dealMessage(m.getContent(),cms.getVersion(),listStr,m.getX(),m.getY(),repertory.getFontSize());
                    }
                    else if("image".equals(m.getType()))
                    {
                        //如果是预置图片
                        listStr += "\\C" + String.format("%03d", m.getX()) + String.format("%03d", m.getY());
                        listStr += "\\B" + m.getFileName().substring(0, 3);
                        //默认或需要发送时才发送
                        if(m.isNeedSendFile()==null||m.isNeedSendFile()) {
                            // 这里注意：images是图片的根目录
                            String srcImage = CMS_IMAGES_DIR + m.getFileName();
                            String tagImage = sendPath + m.getFileName();
                            if (!CmsUtils.copyFile(srcImage, tagImage)) {
                                return false;
                            }
                        }
                    }
                }
                listStr+="\r\n";

            } else {
                //否则是普通发布
                listStr += "\\C" + String.format("%03d", repertory.getLabelX()) + String.format("%03d", repertory.getLabelY());
                listStr += "\\F" + CmsUtils.fontConvert(repertory.getFontType()) + repertory.getFontSize().toString() + repertory.getFontSize().toString();
                listStr += "\\T" + CmsUtils.colorConvert(repertory.getFontColor()) + "000";
                if (!"V2".equals(cms.getVersion())) {
                    listStr += "\\K" + CmsUtils.colorConvert(repertory.getBackColor()) + "000";
                }
                String msg = repertory.getMessageBody();
                msg = msg.replace("<br>", "\\A");//替换换行符
                msg = msg.replace("&nbsp;", " ");//替换空格
                msg = msg.replace("&ensp;", " ");//替换空格
                //昭蒙的协议需要特殊处理
                if ("V2".equals(cms.getVersion())) {
                    msg = msg.replace("\\A", "\\A\\W");//替换换行符
                    String[] strs = msg.split("\\\\A");
                    msg = strs[0];
                    for (int j = 0; j < strs.length; j++) {
                        String temp = strs[j];
                        if (temp.length() <= 2) {
                            continue;
                        }
                        //已测试：如果一行最后一个是半角字符，那么
                        //从倒数第一个非半角字符（全角字符）开始到末尾，如果是奇数需要补空格
                        if (temp.substring(temp.length() - 1, temp.length()).matches("[\\x00-\\xff]")) {

                            int record = -1;
                            for (int k = temp.length() - 2; k >= 0; k--) {
                                if (!temp.substring(k, k + 1).matches("[\\x00-\\xff]")) {
                                    record = k;
                                    break;
                                }
                            }
                            //如果有全角字符且从全角字符开始到结尾的半角字符个数是奇数
                            if (record >= 0 && (temp.length() - 1 - record) % 2 == 1) {
                                strs[j] += " ";
                            }
                            //如果没有全角字符且半角字符的个数是奇数
                            else if (record < 0 && temp.length() % 2 == 1) {
                                strs[j] += " ";
                            }
                        }
                        if (j > 0) {
                            String s = "\\C" + String.format("%03d", repertory.getLabelX()) + String.format("%03d", j * repertory.getFontSize() + repertory.getLabelY()) + strs[j];
                            msg += s;
                        } else {
                            msg = strs[j];
                        }
                    }
                }
                listStr += "\\W" + msg + "\r\n";
            }

        }

        String savePath = sendPath + CMS_LIST_NAME;
        return CmsUtils.createFilebyCharset(listStr, savePath, "GBK");
    }

    private String dealMessage(String msg, String version,String listStr,int x,int y,int fontSize)
    {
        msg = msg.replace("<br>", "\\A");//替换换行符
        msg = msg.replace("&nbsp;", " ");//替换空格
        msg = msg.replace("&ensp;", " ");//替换空格
        //昭蒙的协议需要特殊处理
        if ("V2".equals(version)) {
            msg = msg.replace("\\A", "\\A\\W");//替换换行符
            String[] strs = msg.split("\\\\A");
            msg = strs[0];
            for (int j = 0; j < strs.length; j++) {
                String temp = strs[j];
                if (temp.length() <= 2) {
                    continue;
                }
                //已测试：如果一行最后一个是半角字符，那么
                //从倒数第一个非半角字符（全角字符）开始到末尾，如果是奇数需要补空格
                if (temp.substring(temp.length() - 1, temp.length()).matches("[\\x00-\\xff]")) {

                    int record = -1;
                    for (int k = temp.length() - 2; k >= 0; k--) {
                        if (!temp.substring(k, k + 1).matches("[\\x00-\\xff]")) {
                            record = k;
                            break;
                        }
                    }
                    //如果有全角字符且从全角字符开始到结尾的半角字符个数是奇数
                    if (record >= 0 && (temp.length() - 1 - record) % 2 == 1) {
                        strs[j] += " ";
                    }
                    //如果没有全角字符且半角字符的个数是奇数
                    else if (record < 0 && temp.length() % 2 == 1) {
                        strs[j] += " ";
                    }
                }
                if (j > 0) {
                    String s = "\\C" + String.format("%03d", x) + String.format("%03d", j * fontSize + y) + strs[j];
                    msg += s;
                } else {
                    msg = strs[j];
                }
            }
        }
        listStr += "\\W" + msg;
        return listStr;
    }

    /**
     * 上传节目单文件
     *
     * @param deviceCms
     * @param sendPath
     * @return
     */
    private boolean uploadPageList(DeviceCmsDTO deviceCms, String sendPath) {
        //String path = CMS_SINGLESEND_DIR + deviceCms.getDevice_id().toString() + "\\";
        //1.优先上传图片文件
        if (!uploadImages(deviceCms, sendPath)) {
            return false;
        }
        //2.其次上传“play00.lst”节目单
        if (!uploadList(deviceCms, sendPath)) {
            return false;
        }
        return true;
    }

    /**
     * 情报板上传图片文件
     *
     * @param deviceCms 设备信息
     * @param sendPath  图片文件路径，仅根路径
     * @return
     */
    private boolean uploadImages(DeviceCmsDTO deviceCms, String sendPath) {
        File file = new File(sendPath);
        if (!file.exists()) {
            return false;
        }
        // 循环上传多个图片文件，任意一次上传失败均返回false
        File[] filelist = file.listFiles();
        if (filelist == null || filelist.length == 0) {
            return false;
        }
        for (int i = 0; i < filelist.length; i++) {
            File file2 = filelist[i];
            if (file2.isFile() && (file2.getName().indexOf(".bmp") != -1)) {
                //1.读取文件流
                byte[] data = CmsUtils.readFileToBytes(file2);
                //2.转换文件流
                String strbyte = CmsUtils.bytesToHexString(data).toUpperCase();
                if (StringUtils.isBlank(strbyte)) {
                    return false;
                }
                //3.启动传输，厂家建议上传图片使用“上传文件通用方法，命令3035”
                String filename = file2.getName();
                if (!SocketClientTransferGenerl(deviceCms, filename, strbyte)) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 情报板上传节目单文件
     *
     * @param deviceCms 设备信息
     * @param sendPath  节目单路径，仅根路径
     * @return
     */
    private boolean uploadList(DeviceCmsDTO deviceCms, String sendPath) {
        String filePath = sendPath + CMS_LIST_NAME;
        File file = new File(filePath);
        if (!file.exists()) {
            return false;
        }
        //1.读取文件流
        byte[] data = CmsUtils.readFileToBytes(file);
        //2.转换文件流
        String strbyte = CmsUtils.bytesToHexString(data).toUpperCase();
        if (StringUtils.isBlank(strbyte)) {
            return false;
        }
        //3.启动传输
        return SocketClientTransferFile(deviceCms, CMS_LIST_NAME, strbyte);
    }

    /**
     * 上传文件专用方法，命令3731，协议位置：         文件下发通用指令
     * 注意：不同的命令，格式编排有所不同
     *
     * @param deviceCms 设备信息
     * @param filename  文件名称，带扩展名
     * @param strbyte   文件内容
     * @return
     */
    private static boolean SocketClientTransferFile(DeviceCmsDTO deviceCms, String filename, String strbyte) {
        //帧格式：02(帧头) + 3131(2字节目的地址) + 3232(2字节源地址) + 3731(2字节上传播放列表并立即显示) + 1字节分割标记(2B表示追加，2D表示覆盖)
        //接上行： + 8字节分割偏移地址   + 10字节文件名(“play00.lst”) + n字数据(不定长度) + 2字节校验 + 03(帧尾)
        Socket socket = new Socket();
        DataOutputStream out = null;
        DataInputStream in = null;
        boolean flag = false;
        int offsetindex = 0;//偏移量
        int maxlen = CMD_MAX_LENGTH;
        /*if("V1".equals(deviceCms.getVersion()))
        {
            maxlen = CMD_MAX_LENGTH_NEW;
        }*/
        if("V1".equals(deviceCms.getVersion()))
        {
            maxlen = CMD_MAX_LENGTH_V1;
        }
        try {
            //1.创建传输套接字
            socket.connect(new InetSocketAddress(deviceCms.getIpAddress(), deviceCms.getPort()), CMD_TIME_OUT);
            socket.setSoTimeout(CMD_TIME_OUT);
            //2.确认传输模式，这里有待验证一下：协议文档对这个命令的说明不够详细20180918
            String traMode = CMD_FILE_ADD;
            //3.分步传输字节流
            while (strbyte != null) {
                //这里注意判断依据，按照协议当strbyte的长度恰巧等于DATA_MAX_LENTH时仍然需要发送内容为空（即strbyte长度为0）的结束帧，此时走单个指令即可完成的流程
                String controlstr = CMD_DEST_ADDRESS + CMD_SOURCE_ADDRESS + CMD_FILE_SEND;
                controlstr += traMode;
                String offsetstr = String.valueOf(offsetindex * maxlen/2);
                int len = offsetstr.length();
                for (int i = len; i < 8; i++) {//补齐8字节偏移地址
                    offsetstr = "0" + offsetstr;
                }
                controlstr += CmsUtils.stringToHexString(offsetstr).toUpperCase();
                controlstr += CmsUtils.stringToHexString(filename).toUpperCase();
                String tempstr = "";
                if (strbyte.length() == maxlen) {//特殊处理，末尾需追加内容为空的指令
                    tempstr = strbyte;
                    strbyte = "";//末尾把strbyte置为""
                } else if (strbyte.length() < maxlen) {//单个指令即可完成
                    tempstr = strbyte;
                    strbyte = null;//末尾把strbyte置为null
                } else {//需要拆分成多个传输
                    tempstr = strbyte.substring(0, maxlen);
                    offsetindex += 1;
                    strbyte = strbyte.substring(maxlen);//末尾把strbyte置为新的长度
                }
                controlstr += tempstr.toUpperCase();
                byte[] bytedata = CmsUtils.hexStringToBytes(controlstr);
                String crcstr = Integer.toHexString(getVerifyCRC(bytedata)).toUpperCase();
                for (int i = crcstr.length(); i < 4; i++) {//补齐4字节CRC校验位
                    crcstr = "0" + crcstr;
                }
                controlstr += crcstr;
                //6.转义字符
                String convertstr = "";
                for (int i = 0; i < (controlstr.length() / 2); i++) {
                    convertstr += commandConvert(controlstr.substring(i * 2, i * 2 + 2));
                }
                //7.指令的完整形式
                convertstr = CMD_BEGIN_FRAME + convertstr + CMD_END_FRAME;
                System.out.println("[电明]" + convertstr);
                //8.传输
                byte[] senddata = new byte[convertstr.length() / 2];

                for (int i = 0; i < (convertstr.length() / 2); i++) {
                    String datastr = convertstr.substring(i * 2, i * 2 + 2);
                    senddata[i] = (byte) Integer.parseInt(datastr, 16);
                }
                out = new DataOutputStream(socket.getOutputStream());
                in = new DataInputStream(socket.getInputStream());
                out.write(senddata);
                Thread.sleep(50);//预留足够的时间等待情报板相应

                //9.注意：在文件分段传输中，是所有分段传输完毕后，情报板才有回复信息，不是每段都有回复信息
                if (strbyte == null) {
                    int readlenth = -1;
                    byte[] respondata = new byte[11];
                    // 这里的read()是阻塞式的，所以在前面加上了socket.setSoTimeout()设置，表示最大的响应时间
                    readlenth = in.read(respondata);
                    if (readlenth != -1) {
                        String hex = CmsUtils.bytesToHexString(respondata);
                        byte rsp = respondata[7];
                        if (rsp == (byte) 0x31) {
                            flag = true;
                        } else {
                            flag = false;
                        }
                    }
                }
            }
        } catch (SocketTimeoutException se) {
            return false;
        } catch (Exception e) {
            return false;
        } finally {
           CmsUtils.closeIOStream(in,out);
        }
        return flag;
    }

    /**
     * 上传文件通用方法，命令3035，协议位置：5.5.4.5.1  文件下发通用指令
     * 注意：不同的命令，格式编排有所不同
     *
     * @param deviceCms 设备信息
     * @param filename  文件名称，带扩展名
     * @param strbyte   文件内容
     * @return
     */
    private boolean SocketClientTransferGenerl(DeviceCmsDTO deviceCms, String filename, String strbyte) {
        //帧格式：02(帧头) + 3131(2字节目的地址) + 3232(2字节源地址) + 3035(2字节下发通用指令) + 不定长文件完整路径名
        //接上行：+ 1字节分割标记(2B表示追加+，2D表示覆盖-) + 8字节分割偏移地址  + n字数据(不定长度) + 2字节校验 + 03(帧尾)
        Socket socket = new Socket();
        DataOutputStream out = null;
        DataInputStream in = null;
        boolean flag = false;
        int offsetindex = 0;//偏移量
        int maxlen = CMD_MAX_LENGTH;
        if ("V1".equals(deviceCms.getVersion())) {
            maxlen = CMD_MAX_LENGTH_V1;
        }
        try {
            //1.创建传输套接字
            socket.connect(new InetSocketAddress(deviceCms.getIpAddress(), deviceCms.getPort()), CMD_TIME_OUT);
            socket.setSoTimeout(CMD_TIME_OUT);
            //2.确认传输模式
            String traMode = CMD_FILE_ADD;
            //3.分步传输字节流
            while (strbyte != null) {//不使用“strbyte.length() ！= 0”条件的原因
                //这里注意判断依据，按照协议当strbyte的长度恰巧等于DATA_MAX_LENTH时仍然需要发送内容为空（即strbyte长度为0）的结束帧，此时走单个指令即可完成的流程
                //1.基本命令，此处先不加帧头，为了计算CRC方便
                String controlstr = CMD_DEST_ADDRESS + CMD_SOURCE_ADDRESS + CMD_GENERL_SEND;
                //2.不定长文件完整路径，如：icon/000.bmp
                String pathstr = CMS_ROOT_DIR + filename;
                controlstr += CmsUtils.stringToHexString(pathstr).toUpperCase();
                //3.传输模式，如果是分段传输则使用追加方式打开，否则用覆盖方式打开
                controlstr += traMode;
                //4.转换偏移地址，文件拆分时偏移的字节量
                String offsetstr = String.valueOf(offsetindex * maxlen/2);
                int len = offsetstr.length();
                for (int i = len; i < 8; i++) {//补齐8字节偏移地址
                    offsetstr = "0" + offsetstr;
                }
                controlstr += CmsUtils.stringToHexString(offsetstr).toUpperCase();
                //5.校验位
                String tempstr = "";
                if (strbyte.length() == maxlen) {//特殊处理，末尾需追加内容为空的指令
                    tempstr = strbyte;
                    strbyte = "";//末尾把strbyte置为""
                } else if (strbyte.length() < maxlen) {//单个指令即可完成
                    tempstr = strbyte;
                    strbyte = null;//末尾把strbyte置为null
                } else {//需要拆分成多个传输
                    tempstr = strbyte.substring(0, maxlen);
                    offsetindex += 1;
                    strbyte = strbyte.substring(maxlen);//末尾把strbyte置为新的长度
                }
                controlstr += tempstr.toUpperCase();
                byte[] bytedata = CmsUtils.hexStringToBytes(controlstr);
                String crcstr = Integer.toHexString(getVerifyCRC(bytedata)).toUpperCase();
                for (int i = crcstr.length(); i < 4; i++) {//补齐4字节CRC校验位
                    crcstr = "0" + crcstr;
                }
                controlstr += crcstr;
                //6.转义字符
                String convertstr = "";
                for (int i = 0; i < (controlstr.length() / 2); i++) {
                    convertstr += commandConvert(controlstr.substring(i * 2, i * 2 + 2));
                }
                //7.指令的完整形式
                convertstr = CMD_BEGIN_FRAME + convertstr + CMD_END_FRAME;
                System.out.println("[电明]" + convertstr);
                //8.传输
                byte[] senddata = new byte[convertstr.length() / 2];
                for (int i = 0; i < (convertstr.length() / 2); i++) {
                    String datastr = convertstr.substring(i * 2, i * 2 + 2);
                    senddata[i] = (byte) Integer.parseInt(datastr, 16);
                }
                out = new DataOutputStream(socket.getOutputStream());
                in = new DataInputStream(socket.getInputStream());
                out.write(senddata);
                Thread.sleep(50);//预留足够的时间等待情报板相应

                //每段都有回复信息
                if (strbyte != null) {
                    int readlenth = -1;
                    byte[] respondata = new byte[11];
                    // 这里的read()是阻塞式的，所以在前面加上了socket.setSoTimeout()设置，表示最大的响应时间
                    readlenth = in.read(respondata);
                    if (readlenth != -1) {
                        byte rsp = respondata[7];
                        if (rsp == (byte) 0x31) {
                            flag = true;
                        }
                        else
                        {
                            return false;
                        }
                    }
                }// end if strbyte == null
            }// end while
        } catch (SocketTimeoutException se) {
            return false;
        } catch (Exception e) {
            return false;
        } finally {
            try {
                if (socket != null) {
                    socket.close();
                }
                if (in != null) {
                    in.close();
                }
                if (out != null) {
                    out.close();
                }
            } catch (Exception e2) {
            }
        }
        return flag;
    }

    /**
     * 深圳电明厂家协议的CRC校验
     *
     * @param bytes 待校验的字节
     * @return 校验结果int
     */
    private static int getVerifyCRC(byte[] bytes) {
        int crc = 0x00;
        for (int i = 0; i < bytes.length; i++) {
            byte b = bytes[i];
            for (int j = 0; j < 8; j++) {
                boolean bit = ((b >> (7 - j) & 1) == 1);
                boolean c15 = ((crc >> 15 & 1) == 1);
                crc <<= 1;
                if (c15 ^ bit) {
                    crc ^= 0x1021;
                }
            }
        }
        crc &= 0xffff;
        return crc;
    }

    /**
     * 按照协议文件转义特殊的命令符，要求传入的长度为2字节的参数
     *
     * @param cmd 待转义的字符，长度为2字节
     * @return 转义结果字符
     */
    private static String commandConvert(String cmd) {
        String convend = "";
        if (cmd.equals("02")) {
            convend = "1BE7";
        } else if (cmd.equals("03")) {
            convend = "1BE8";
        } else if (cmd.equals("1B")) {
            convend = "1B00";
        } else {
            convend = cmd.toUpperCase();
        }
        return convend;
    }


    public List<String> TotalStatusCheck(String ip, int port) {
        //帧格式：02(帧头) + 3131(2字节目的地址) + 3232(2字节源地址) + 3031(2字节总状态检查) + 2字节校验 + 03(帧尾)
        List<String> flagList = new ArrayList<String>();
        if (StringUtils.isBlank(ip)) {
            return null;
        }
        Socket socket = new Socket();
        DataOutputStream out = null;
        DataInputStream in = null;
        try {
            //1.创建传输套接字
            socket.connect(new InetSocketAddress(ip, port), CMD_TIME_OUT);
            socket.setSoTimeout(CMD_TIME_OUT);
            //2.基本命令，此处先不加帧头，为了计算CRC方便
            String controlstr = CMD_DEST_ADDRESS + CMD_SOURCE_ADDRESS + CMD_STATUS_CHECK;
            //3.校验位
            byte[] bytedata = CmsUtils.hexStringToBytes(controlstr);
            String crcstr = Integer.toHexString(getVerifyCRC(bytedata)).toUpperCase();
            for (int i = crcstr.length(); i < 4; i++) {//补齐4字节CRC校验位
                crcstr = "0" + crcstr;
            }
            controlstr += crcstr;
            //4.转义字符
            String convertstr = "";
            for (int i = 0; i < (controlstr.length() / 2); i++) {
                convertstr += commandConvert(controlstr.substring(i * 2, i * 2 + 2));
            }
            //5.指令的完整形式
            convertstr = CMD_BEGIN_FRAME + convertstr + CMD_END_FRAME;
            //6.传输
            byte[] senddata = new byte[convertstr.length() / 2];
            for (int i = 0; i < (convertstr.length() / 2); i++) {
                String datastr = convertstr.substring(i * 2, i * 2 + 2);
                senddata[i] = (byte) Integer.parseInt(datastr, 16);
            }
            out = new DataOutputStream(socket.getOutputStream());
            in = new DataInputStream(socket.getInputStream());
            out.write(senddata);
            Thread.sleep(50);//预留足够的时间等待情报板相应

            //7.注意：在文件分段传输中，是所有分段传输完毕后，情报板才有回复信息，不是每段都有回复信息
            int readlenth = -1;
            byte[] respondata = new byte[16];
            // 这里的read()是阻塞式的，所以在前面加上了socket.setSoTimeout()设置，表示最大的响应时间
            readlenth = in.read(respondata);
            if (readlenth != -1) {
                //设备回复：02 30 31 30 31 30 32 31 31 31 31 31 31 C7 7B 03
                for (int i = 7; i < 13; i++) {//其中：31 31 31 31 31 31
                    byte rsp = respondata[i];
                    if (rsp == (byte) 0x31) {
                        flagList.add("1");
                    } else {
                        flagList.add("0");
                    }
                }
            }

        } catch (SocketTimeoutException se) {
            return null;
        } catch (Exception e) {
            return null;
        } finally {
            CmsUtils.closeIOStream(in, out);

        }
        return flagList;
    }


    public boolean OnlineStatusCheck(String ip, int port) {
        //帧格式：02(帧头) + 3131(2字节目的地址) + 3232(2字节源地址) + 3239(2字节通讯状态检测) + 2字节校验 + 03(帧尾)
        boolean flag = false;
        if (StringUtils.isBlank(ip)) {
            return false;
        }
        Socket socket = new Socket();
        DataOutputStream out = null;
        DataInputStream in = null;
        try {
            //1.创建传输套接字
            socket.connect(new InetSocketAddress(ip, port), CMD_TIME_OUT);
            socket.setSoTimeout(CMD_TIME_OUT);
            //2.基本命令，此处先不加帧头，为了计算CRC方便
            String controlstr = CMD_DEST_ADDRESS + CMD_SOURCE_ADDRESS + CMD_ONLINE_CHECK;
            //3.校验位
            byte[] bytedata = CmsUtils.hexStringToBytes(controlstr);
            String crcstr = Integer.toHexString(getVerifyCRC(bytedata)).toUpperCase();
            for (int i = crcstr.length(); i < 4; i++) {//补齐4字节CRC校验位
                crcstr = "0" + crcstr;
            }
            controlstr += crcstr;
            //4.转义字符
            String convertstr = "";
            for (int i = 0; i < (controlstr.length() / 2); i++) {
                convertstr += commandConvert(controlstr.substring(i * 2, i * 2 + 2));
            }
            //5.指令的完整形式
            convertstr = CMD_BEGIN_FRAME + convertstr + CMD_END_FRAME;
            //6.传输
            byte[] senddata = new byte[convertstr.length() / 2];
            for (int i = 0; i < (convertstr.length() / 2); i++) {
                String datastr = convertstr.substring(i * 2, i * 2 + 2);
                senddata[i] = (byte) Integer.parseInt(datastr, 16);
            }
            out = new DataOutputStream(socket.getOutputStream());
            in = new DataInputStream(socket.getInputStream());
            out.write(senddata);
            Thread.sleep(50);//预留足够的时间等待情报板相应

            //7.注意：在文件分段传输中，是所有分段传输完毕后，情报板才有回复信息，不是每段都有回复信息
            int readlenth = -1;
            byte[] respondata = new byte[16];
            // 这里的read()是阻塞式的，所以在前面加上了socket.setSoTimeout()设置，表示最大的响应时间
            readlenth = in.read(respondata);
            if (readlenth != -1) {
                //设备回复：02 30 31 30 31 33 30 31 DA 00 03
                byte rsp = respondata[7];
                if (rsp == (byte) 0x31) {
                    flag = true;
                }
            }
        } catch (UnknownHostException e) {
            return false;
        } catch (IOException e) {
            return false;
        } catch (InterruptedException e) {
            return false;
        } finally {
            CmsUtils.closeIOStream(in,out);
        }
        return flag;
    }

    public boolean OnlineStatusCheck(DeviceCmsDTO cms) {
        String recv = generalInquire(cms, "3239", "");
        boolean flag = (recv != null && recv.length() >= 2 && recv.substring(0, 2).equals("31"));
        return flag;
    }

    public List<String> getBrightnessControlMode(String ip, int port) {
        //帧格式：02(帧头) + 3131(2字节目的地址) + 3232(2字节源地址) + 3231(2字节获取当前亮度控制模式或显示亮度 ) + 2字节校验 + 03(帧尾)
        List<String> flagList = new ArrayList<String>();
        if (StringUtils.isBlank(ip)) {
            return null;
        }
        Socket socket = new Socket();
        DataOutputStream out = null;
        DataInputStream in = null;
        try {
            //1.创建传输套接字
            socket.connect(new InetSocketAddress(ip, port), CMD_TIME_OUT);
            socket.setSoTimeout(CMD_TIME_OUT);
            //2.基本命令，此处先不加帧头，为了计算CRC方便
            String controlstr = CMD_DEST_ADDRESS + CMD_SOURCE_ADDRESS + CMD_BRIGHTNESS_MODE;
            //3.校验位
            byte[] bytedata = CmsUtils.hexStringToBytes(controlstr);
            String crcstr = Integer.toHexString(getVerifyCRC(bytedata)).toUpperCase();
            for (int i = crcstr.length(); i < 4; i++) {//补齐4字节CRC校验位
                crcstr = "0" + crcstr;
            }
            controlstr += crcstr;
            //4.转义字符
            String convertstr = "";
            for (int i = 0; i < (controlstr.length() / 2); i++) {
                convertstr += commandConvert(controlstr.substring(i * 2, i * 2 + 2));
            }
            //5.指令的完整形式
            convertstr = CMD_BEGIN_FRAME + convertstr + CMD_END_FRAME;
            //6.传输
            byte[] senddata = new byte[convertstr.length() / 2];
            for (int i = 0; i < (convertstr.length() / 2); i++) {
                String datastr = convertstr.substring(i * 2, i * 2 + 2);
                senddata[i] = (byte) Integer.parseInt(datastr, 16);
            }
            out = new DataOutputStream(socket.getOutputStream());
            in = new DataInputStream(socket.getInputStream());
            out.write(senddata);
            Thread.sleep(50);//预留足够的时间等待情报板相应

            //7.注意：在文件分段传输中，是所有分段传输完毕后，情报板才有回复信息，不是每段都有回复信息
            int readlenth = -1;
            byte[] respondata = new byte[16];
            // 这里的read()是阻塞式的，所以在前面加上了socket.setSoTimeout()设置，表示最大的响应时间
            readlenth = in.read(respondata);
            if (readlenth != -1) {
                //设备回复：02 30 31 30 31 32 32 46 46 46 46 46 46 49 35 1C 69 03
                for (int i = 7; i < 15; i++) {//其中：46 46 46 46 46 46 49 35
                    int rsp = respondata[i] & 0xFF;
                    String hex = Integer.toHexString(rsp);
                    flagList.add(hex);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            CmsUtils.closeIOStream(in,out);
        }
        return flagList;
    }


    public boolean setBrightnessValue(String ip, int port, String value) {
        //帧格式：02(帧头) + 3131(2字节目的地址) + 3232(2字节源地址) + 3233(2字节设置亮度) + 6字节亮度值(2字节红色 + 2字节绿色 + 2字节蓝色) + 2字节校验 + 03(帧尾)
        //如果红色、绿色、蓝色的亮度值都为FF FF，则为自动控制模式
        boolean flag = false;
        if (StringUtils.isBlank(ip) || StringUtils.isBlank(value) || (value.length() > 2)) {
            return false;
        }
        //过滤亮度值，值范围：0~31 或等于FF
        if (!value.equals("FF")) {
            try {
                int intValue = Integer.valueOf(value);
                if ((intValue > 31) || (intValue < 0)) {
                    return false;
                }
            } catch (NumberFormatException e) {
                return false;
            }
        }
        Socket socket = new Socket();
        DataOutputStream out = null;
        DataInputStream in = null;
        try {
            //1.创建传输套接字
            socket.connect(new InetSocketAddress(ip, port), CMD_TIME_OUT);
            socket.setSoTimeout(CMD_TIME_OUT);
            //2.基本命令，此处先不加帧头，为了计算CRC方便
            String controlstr = CMD_DEST_ADDRESS + CMD_SOURCE_ADDRESS + CMD_BRIGHTNESS_VALUE;
            //3.亮度平均值转换
            String strValue = value;
            for (int i = value.length(); i < 2; i++) {//补齐2字节亮度值
                strValue = "0" + strValue;
            }
            strValue = CmsUtils.stringToHexString(strValue);
            controlstr += strValue;//红色
            controlstr += strValue;//绿色
            controlstr += strValue;//蓝色
            //4.校验位
            byte[] bytedata = CmsUtils.hexStringToBytes(controlstr);
            String crcstr = Integer.toHexString(getVerifyCRC(bytedata)).toUpperCase();
            for (int i = crcstr.length(); i < 4; i++) {//补齐4字节CRC校验位
                crcstr = "0" + crcstr;
            }
            controlstr += crcstr;
            //5.转义字符
            String convertstr = "";
            for (int i = 0; i < (controlstr.length() / 2); i++) {
                convertstr += commandConvert(controlstr.substring(i * 2, i * 2 + 2));
            }
            //6.指令的完整形式
            convertstr = CMD_BEGIN_FRAME + convertstr + CMD_END_FRAME;
            //7.传输
            byte[] senddata = new byte[convertstr.length() / 2];
            for (int i = 0; i < (convertstr.length() / 2); i++) {
                String datastr = convertstr.substring(i * 2, i * 2 + 2);
                senddata[i] = (byte) Integer.parseInt(datastr, 16);
            }
            out = new DataOutputStream(socket.getOutputStream());
            in = new DataInputStream(socket.getInputStream());
            out.write(senddata);
            Thread.sleep(50);//预留足够的时间等待情报板相应

            //7.注意：在文件分段传输中，是所有分段传输完毕后，情报板才有回复信息，不是每段都有回复信息
            int readlenth = -1;
            byte[] respondata = new byte[16];
            // 这里的read()是阻塞式的，所以在前面加上了socket.setSoTimeout()设置，表示最大的响应时间
            readlenth = in.read(respondata);
            if (readlenth > 0) {
                respondata = Arrays.copyOfRange(respondata, 0, readlenth);
                String recv = CmsUtils.bytesToHexString(respondata).toUpperCase();
                //设备回复：02 30 31 30 31 32 34 31 21 F4 03
                flag = (recv != null && recv.length() >= 14 && recv.substring(14, 16).equals("31"));
            }
        } catch (Exception e) {
            return false;
        } finally {
            try {
                if (socket != null) {
                    socket.close();
                }
                if (in != null) {
                    in.close();
                }
                if (out != null) {
                    out.close();
                }
            } catch (Exception e2) {
            }
        }
        return flag;
    }





    public String generalInquire(DataInputStream in, DataOutputStream out, String cmd, String content) {
        try {
            //分步传输字节流
            String controlstr = CMD_DEST_ADDRESS + CMD_SOURCE_ADDRESS + cmd + content;
            //5.校验位
            byte[] bytedata = CmsUtils.hexStringToBytes(controlstr);
            String crcstr = Integer.toHexString(getVerifyCRC(bytedata)).toUpperCase();
            for (int i = crcstr.length(); i < 4; i++) {//补齐4字节CRC校验位
                crcstr = "0" + crcstr;
            }
            controlstr += crcstr;
            //6.转义字符
            String convertstr = "";
            for (int i = 0; i < (controlstr.length() / 2); i++) {
                convertstr += commandConvert(controlstr.substring(i * 2, i * 2 + 2));
            }
            //7.指令的完整形式
            convertstr = CMD_BEGIN_FRAME + convertstr + CMD_END_FRAME;
            LOGGER.info("[电明]发送指令:" + convertstr);
            //8.传输
            byte[] senddata = CmsUtils.hexStringToBytes(convertstr);
            out.write(senddata);
            Thread.sleep(50);//预留足够的时间等待情报板相应
            int readLenth = 1024;
            byte[] respondata = new byte[readLenth];
            readLenth = in.read(respondata);
            if (readLenth > 0) {
                respondata = Arrays.copyOfRange(respondata, 0, readLenth);
                String hexResult = CmsUtils.bytesToHexString(respondata);
                //去除帧头、地址、校验位和帧尾
                hexResult = hexResult.substring(14, hexResult.length() - 6);
                //反转义
                hexResult = deEscape(hexResult);
                return hexResult;
            }
        } catch (Exception e) {
            LOGGER.info("[电明]发送指令失败，原因:" + e.getMessage());
        }
        return null;
    }

    public String generalInquire(DeviceCmsDTO cms, String cmd, String content) {
        Socket socket = new Socket();
        DataOutputStream out = null;
        DataInputStream in = null;
        try {
            socket.connect(new InetSocketAddress(cms.getIpAddress(), cms.getPort()), CMD_TIME_OUT);
            socket.setSoTimeout(CMD_TIME_OUT);
            in = new DataInputStream(socket.getInputStream());
            out = new DataOutputStream(socket.getOutputStream());
            return generalInquire(in, out, cmd, content);
        } catch (IOException e) {
            LOGGER.info("[电明]发送指令失败，原因:" + e.getMessage());
            return null;
        } finally {
            closeIOStream(in, out);
        }
    }

    private boolean closeIOStream(InputStream in, OutputStream out) {
        boolean flag = true;
        if (in != null) {
            try {
                in.close();
            } catch (IOException e) {
                flag = false;
            }
        }
        if (out != null) {
            try {
                out.close();
            } catch (IOException e) {
                flag = false;
            }
        }
        return flag;
    }



    public boolean uploadImageFile(DeviceCmsDTO cms, String path, String name) {
        Socket socket = new Socket();
        DataOutputStream out = null;
        DataInputStream in = null;
        boolean flag = false;
        int offsetindex = 0;//偏移量
        String strbyte = null;
        //读图片文件
        File file = new File(path);
        if (file.isFile() && (file.getName().toLowerCase().endsWith(".bmp"))) {
            //1.读取文件流
            byte[] data = CmsUtils.readFileToBytes(file);
            //2.转换文件流
            strbyte = CmsUtils.bytesToHexString(data).toUpperCase();
            if (StringUtils.isBlank(strbyte)) {
                return false;
            }
        }
        try {
            //创建传输套接字
            socket.setSoTimeout(CMD_TIME_OUT);
            socket.connect(new InetSocketAddress(cms.getIpAddress(), cms.getPort()), CMD_TIME_OUT);
            String traMode = CMD_FILE_ADD;
            if (strbyte.length() < CMD_MAX_LENGTH) {
                traMode = CMD_FILE_RECOVER;
            }
            //分步传输字节流
            while (strbyte != null) {
                String controlstr = CMD_DEST_ADDRESS + CMD_SOURCE_ADDRESS + "3431";
                //传输模式，如果是分段传输则使用追加方式打开，否则用覆盖方式打开
                controlstr += traMode;
                //转换偏移地址，文件拆分时偏移的字节量
                String offsetstr = String.format("%08d", offsetindex * CMD_MAX_LENGTH);
                controlstr += CmsUtils.stringToHexString(offsetstr).toUpperCase();
                controlstr += CmsUtils.stringToHexString(name).toUpperCase();
                //校验位
                String tempstr = "";
                if (strbyte.length() == CMD_MAX_LENGTH) {//特殊处理，末尾需追加内容为空的指令
                    tempstr = strbyte;
                    strbyte = "";//末尾把strbyte置为""
                } else if (strbyte.length() < CMD_MAX_LENGTH) {//单个指令即可完成
                    tempstr = strbyte;
                    strbyte = null;//末尾把strbyte置为null
                } else {//需要拆分成多个传输
                    tempstr = strbyte.substring(0, CMD_MAX_LENGTH);
                    offsetindex += 1;
                    strbyte = strbyte.substring(CMD_MAX_LENGTH);//末尾把strbyte置为新的长度
                }
                controlstr += tempstr.toUpperCase();
                byte[] bytedata = CmsUtils.hexStringToBytes(controlstr);
                String crcstr = Integer.toHexString(getVerifyCRC(bytedata)).toUpperCase();
                for (int i = crcstr.length(); i < 4; i++) {//补齐4字节CRC校验位
                    crcstr = "0" + crcstr;
                }
                controlstr += crcstr;
                //转义字符
                String convertstr = escape(controlstr);
                //指令的完整形式
                convertstr = CMD_BEGIN_FRAME + convertstr + CMD_END_FRAME;
                //传输
                byte[] senddata = CmsUtils.hexStringToBytes(convertstr.toUpperCase());
                out = new DataOutputStream(socket.getOutputStream());
                in = new DataInputStream(socket.getInputStream());
                out.write(senddata);
                //在文件分段传输中，是所有分段传输完毕后，情报板才有回复信息，不是每段都有回复信息
                if (strbyte == null) {
                    int readlenth = -1;
                    byte[] respondata = new byte[1024];
                    readlenth = in.read(respondata);
                    if (readlenth > 16) {
                        respondata = Arrays.copyOfRange(respondata, 0, readlenth);
                        flag = CmsUtils.bytesToHexString(respondata).substring(14, 16).equals("31");
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.info("[电明]上传图片文件" + name + "失败,原因：" + e.getMessage());
            return false;
        } finally {
            closeIOStream(in, out);
        }
        return flag;
    }


    public boolean downloadFileGenerlCmd(DeviceCmsDTO cms, String name, String savePath) {
        Socket socket = new Socket();
        DataOutputStream out = null;
        DataInputStream in = null;
        FileOutputStream fos = null;
        try {
            socket.setSoTimeout(CMD_TIME_OUT);
            socket.connect(new InetSocketAddress(cms.getIpAddress(), cms.getPort()), CMD_TIME_OUT);
            in = new DataInputStream(socket.getInputStream());
            out = new DataOutputStream(socket.getOutputStream());
            int offset = 0;
            int dataLen = CMD_MAX_LENGTH;
            StringBuffer fileData = new StringBuffer();
            while (dataLen >= CMD_MAX_LENGTH) {
                String sendContent = CmsUtils.stringToHexString(name + "+" + String.format("%08d", offset));
                String recv = generalInquire(in, out, "3037", sendContent);
                if (recv == null) {
                    //接收不到这一段的数据
                    return false;
                } else {
                    fileData.append(recv);
                    dataLen = recv.length() / 2;
                    offset += dataLen;
                }
            }
            //将获得的十六进制文件数据存储到本地计算机中
            byte[] bytesFileData = CmsUtils.hexStringToBytes(fileData.toString());
            fos = new FileOutputStream(savePath);
            ;
            fos.write(bytesFileData);
            fos.flush();
            LOGGER.debug("[电明]下载情报板文件" + name + "到计算机成功");
            return true;
        } catch (Exception e) {
            LOGGER.info("[电明]下载情报板文件" + name + "到计算机失败：" + e.getMessage());
        } finally {
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                }
            }
            closeIOStream(in, out);
        }
        return false;
    }


    public boolean restartDevice(DeviceCmsDTO cms) {
        String recv = generalInquire(cms, "3637", "");
        boolean result = (recv != null);
        return result;
    }

    /**
     * 按照协议文件转义特殊的命令符，要求传入的长度为2字节的参数
     *
     * @param cmd 待转义的字符，长度为2字节
     * @return 转义结果字符
     */
    private static String escape(String cmd) {
        String convend = "";
        if (cmd.equals("02")) {
            convend = "1BE7";
        } else if (cmd.equals("03")) {
            convend = "1BE8";
        } else if (cmd.equals("1B")) {
            convend = "1B00";
        } else {
            convend = cmd.toUpperCase();
        }
        return convend;
    }

    private static String deEscape(String str) {
        StringBuffer sb = new StringBuffer(str.toUpperCase());
        StringBuffer result = new StringBuffer();
        while (sb.length() >= 2) {
            String temp = sb.substring(0, 2);
            if (temp.equals("1B")) {
                if (sb.length() >= 4) {
                    String temp2 = sb.substring(2, 4);
                    if (temp2.equals("E7")) {
                        result.append("02");
                    } else if (temp2.equals("E8")) {
                        result.append("03");
                    } else if (temp2.equals("00")) {
                        result.append("1B");
                    } else {
                        result.append(temp);
                        sb.delete(0, 2);
                        continue;
                    }
                    sb.delete(0, 4);
                } else {
                    result.append(temp);
                    sb.delete(0, 2);
                }
            } else {
                result.append(temp);
                sb.delete(0, 2);
            }
        }
        if (sb.length() > 0) {
            result.append(sb.toString());
        }
        return result.toString();
    }

    public static String addZero(String str, int totalLength) {
        String result = str;
        for (int i = str.length(); i < totalLength; i++) {
            result = "0" + result;
        }
        return result;
    }


}
