package com.bt.itsinner.cmsprotocol;



import com.bt.itscore.domain.dto.CmsPageNodeDTO;
import com.bt.itscore.domain.dto.CmsRepertoryDTO;
import com.bt.itsinner.domain.dto.*;
import com.bt.itsinner.utils.CmsUtils;
import com.sun.jna.Pointer;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.ArrayList;
import java.util.Date;

@Service("dpdLedService")
public class DPDLedService{
    /**
     * 德普达情报板发布接口，从旧CS系统迁移过来
     */
    private String CMS_SINGLESEND_DIR;
    private String CMS_GROUPSEND_DIR;
    private String CMS_RECEIVE_DIR;
    private String CMS_IMAGES_DIR;

    public static final int NAM_NULL = 0x0000;//空
    public static final int NAM_OPEN = 0x0002;//打开
    public static final int NAM_CLOSE = 0x0003;//关闭
    public static final int NAM_EXIT = 0x0011;//退出
    public static final int NAM_ACK = 0x0012;//保持连接
    public static final int NAM_RST = 0x0013;//重启

    public static final int NAM_PLAY = 0x0021;//切换至播放控制
    public static final int NAM_PLAY_NEW = 0x0101;//重放当前节目
    public static final int NAM_PLAY_PLAY = 0x0102;//播放当前节目
    public static final int NAM_PLAY_GOON = 0x0103;//继续播放当前节目
    public static final int NAM_PLAY_SHUT = 0x0104;//关闭当前节目及定时指令
    public static final int NAM_PLAY_STOP = 0x0105;//停止播放当前节目
    public static final int NAM_PLAY_WAIT = 0x0106;//暂停播放当前节目
    public static final int NAM_PLAY_STAY = 0x0107;//暂停/播放当前节目
    public static final int NAM_PLAY_LOAD = 0x0108;//加载新的节目文件，lpParam1为目标路径
    public static final int NAM_PLAY_STAT = 0x0109;//返回运行状态，lpParam3为返回数据存放地址，lpParam4为返回数据存储空间大小
    public static final int NAM_PLAY_INTERCUT = 0x010A;//插播指令(播放完后继续播放其他节目)，lpParam1为目标路径

    public static final int NAM_FILE = 0x0022;//切换至文件管理
    public static final int NAM_FILE_CD = 0x0201;//切换当前目录，lpParam1为目标路径
    public static final int NAM_FILE_CP = 0x0202;//拷贝文件，lpParam1为原始路径，lpParam2为目标路径
    public static final int NAM_FILE_DEL = 0x0203;//删除文件，lpParam1为目标路径
    public static final int NAM_FILE_FREE = 0x0204;//查询存储素材的目录的磁盘空间，lpParam3为返回数据存放地址，lpParam4为返回数据存储空间大小
    public static final int NAM_FILE_GET = 0x0205;//下载文件，lpParam1为远程路径，lpParam2为本地路径
    public static final int NAM_FILE_LS = 0x0206;//发回指定目录的文件列表，lpParam1为目标路径
    public static final int NAM_FILE_MV = 0x0207;//移动文件，lpParam1为原始路径，lpParam2为目标路径
    public static final int NAM_FILE_PUT = 0x0208;//上传文件，lpParam1为本地路径，lpParam2为远程路径
    public static final int NAM_FILE_PWD = 0x0209;//显示当前目录，lpParam3为返回数据存放地址，lpParam4为返回数据存储空间大小
    public static final int NAM_FILE_SIZE = 0x020A;//查询文件大小，lpParam1为目标路径，lpParam4为返回数据
    public static final int NAM_FILE_SYNC = 0x020B;//更新同步文件，lpParam1为目标路径
    public static final int NAM_FILE_INFO = 0x020C;//返回文件信息，lpParam1为目标路径，lpParam3为返回数据存放地址，lpParam4为返回数据存储空间大小
    public static final int NAM_FILE_MKDIR = 0x020D;//创建目录，lpParam1为目标路径
    public static final int NAM_FILE_RMDIR = 0x020E;//删除目录，lpParam1为目标路径

    public static final int NAM_CONF = 0x0024;//切换至参数设置
    public static final int NAM_CONF_RST = 0x0401;//重新加载配置文件
    public static final int NAM_CONF_CMD = 0x0402;//执行命令，lpParam1为命令字符串格式
    public static final int NAM_CONF_BRIGHTNESS = 0x0403;//设置亮度，lpParam1为亮度数值字符串格式
    public static final int NAM_CONF_TURN = 0x0404;//开关屏，lpParam1为开关操作字符串格式
    public static final int NAM_CONF_TIME = 0x0405;//设置时间，lpParam1为时间字符串格式
    public static final int NAM_CONF_HUBGET = 0x0406;//设置接受卡回传，lpParam1为参数数值字符串格式
    public static final int NAM_CONF_HUBSET = 0x0407;//发送接受卡参数，lpParam1为目标路径
    public static final int NAM_CONF_OUTSET = 0x0408;//发送发送卡参数，lpParam1为目标路径
    public static final int NAM_CONF_MULTISET = 0x0409;//发送复合参数文件，lpParam1为目标路径
    public static final int NAM_CONF_VER = 0x040A;//查询版本信息，lpParam3为返回数据存放地址，lpParam4为返回数据存储空间大小
    public static final int NAM_CONF_STAT = 0x040B;//返回控制程序状态，lpParam3为返回数据存放地址，lpParam4为返回数据存储空间大小

    public static final int TIME_OUT_CHECK_NETWORK = 5000;//检查网络连接的超时时间
    
    private final static Logger LOGGER = LoggerFactory.getLogger(DPDLedService.class);
    
    @Value("${file.upload.path}")
    private String fileUploadPath;

    /**
     * 初始化文件上传路径
     */
    private void initFileUploadPath() {
        CMS_SINGLESEND_DIR = fileUploadPath + "/singlesend/";
        CMS_GROUPSEND_DIR = fileUploadPath + "/groupsend/";
        CMS_RECEIVE_DIR = fileUploadPath + "/receive/";
        CMS_IMAGES_DIR = fileUploadPath + "/images/";
    }

    public boolean cmsSendPage(DeviceCmsDTO deviceCms, CmsPageNodeDTO cmsPageNode) {
        initFileUploadPath();
        String path = CMS_SINGLESEND_DIR + deviceCms.getDeviceId().toString() + "/";
        File file = new File(path);
        if (file.exists()) {
            CmsUtils.deleteFile(path);//使用高优先级，不管其它客户端是否在操作，直接新建
            //return false;//当前有其它客户端正在发布情报板
        }
        file.mkdirs();
        if (!createPageList(cmsPageNode, path,deviceCms)) {//创建节目单
            LOGGER.info("节目单创建失败");
            return false;
        }
        LOGGER.info("准备上传节目单");
        return uploadPageList(deviceCms, path);//上传节目单
    }

    /**
     * 在线检测
     *
     * @param cms
     * @return
     */
    public boolean OnlineStatusCheck(DeviceCmsDTO cms) {
        Socket socket = new Socket();
        try {
            socket.connect(new InetSocketAddress(cms.getIpAddress(), cms.getPort()), TIME_OUT_CHECK_NETWORK);
        } catch (IOException e) {
            LOGGER.info("在线检测响应超时");
            return false;
        }
        try {
            Thread.sleep(200);
            socket.close();
        } catch (InterruptedException e) {
        } catch (IOException e) {
        }
        return true;
    }

    /**
     * 设置亮度值
     * @param deviceCms
     * @param cmsBrightnessDTO   亮度平均值（0-100）
     * @return
     */

    public boolean setBrightness(DeviceCmsDTO deviceCms, CmsBrightnessDTO cmsBrightnessDTO) {
        Pointer pointer = initConnection(deviceCms);
        int value = cmsBrightnessDTO.getBrightness();
        //亮度从0-31转换为0-100
        value = (int) ((float) value * 100 / 31);
        int result = DPDLedDll.INSTANCE_DPD.netagentcommand(pointer, NAM_CONF_BRIGHTNESS,Integer.toString(value), null, null, null);
        return (result == 1);
    }


    public boolean setBrightnessControlMode(DeviceCmsDTO cms, boolean auto) {
        return false;
    }

    /**
     * 生成节目表
     * @param cmsPageNode
     * @param sendPath
     * @return
     */
    private boolean createPageList(CmsPageNodeDTO cmsPageNode, String sendPath,DeviceCmsDTO cms) {
        System.out.println("path="+System.getProperty("java.library.path"));
        //多屏的xml生成
        ArrayList<String> matters = new ArrayList<>();
        //出入屏特效转换
        int[] convertMode = {-1, 3, 2, 0, 1, 10, 11, 8, 9, 12, 14, 18, 47, 46, 16, -2};
        for (int i = 0; i < cmsPageNode.getChildren().size(); i++) {
            CmsRepertoryDTO repertory = cmsPageNode.getChildren().get(i);
            String textImageName = "text" + (i + 1) + ".bmp";
            int speed = 300;//出入屏时间定为0.3秒
            int delay = repertory.getHoldTime() * 1000;//转换停留时间
            int inputMode = convertMode[Integer.parseInt(repertory.getInputMode())];
            int outputMode = convertMode[Integer.parseInt(repertory.getOutputMode())];
            String hexInputMode = "0x" + String.format("%08x", inputMode);
            String hexOutputMode = "0x" + String.format("%08x", outputMode);
            int textWidth = 0;
            int textHeight = 0;
            String fontType = repertory.getFontType();
            int fontSize = repertory.getFontSize();

            //文字分行
            String content = repertory.getMessageBody();
            content = content.replace("<br>", "\\n");//替换换行符
            content = content.replace("&nbsp;", " ");//替换空格
            content = content.replace("&ensp;", " ");//替换空格
            String[] wordLines = content.split("\\\\n");

            //计算文字宽度和高度
            Font f = new Font(fontType, Font.PLAIN, fontSize);
            FontMetrics fm = sun.font.FontDesignMetrics.getMetrics(f);
            for (String line : wordLines) {
                int lineWidth = fm.stringWidth(line);
                if (lineWidth > textWidth) {
                    textWidth = lineWidth;
                }
            }
            textHeight = wordLines.length * fontSize;

            //生成图片形式的节目
            //boolean createFlag = createTxtImage(fontType, fontSize, textWidth, textHeight, repertory.getBackColor(), repertory.getFontColor(), content, sendPath + textImageName);
            boolean createFlag = createTxtImage(fontType, fontSize,repertory.getLabelX(),repertory.getLabelY(), cms.getWidth(), cms.getHeight(), repertory.getBackColor(), repertory.getFontColor(), content, sendPath + textImageName);

            //如果存在图片，设定只能包含一张图片，所以只可能有一项信息--暂不接入
            if (createFlag&&StringUtils.isNotBlank(repertory.getMultiBody())) {
//                String subdir = repertory.getImageSize() + "/" + repertory.getMultiBody();
//                String srcImage = CMS_IMAGES_DIR + subdir;
//                String tagImage = sendPath + repertory.getMultiBody();
//                if (CmsUtils.copyFile(srcImage, tagImage)) {
//                    return false;
//                }
//                int imageSize = repertory.getImageSize();
//                //图片叠放在底图上
//                createFlag = coverImage(sendPath + textImageName,tagImage,repertory.getImageX(),repertory.getImageY(),imageSize,imageSize);
//                //String xml = createWindowXmlOld("/xmstudio/media/" + repertory.getImageBody(), repertory.getImage_x(), repertory.getImage_y(), imageSize, imageSize, hexInputMode, hexOutputMode, speed, delay);
//                //matters.add(xml);
            }
            if (!createFlag) {
                LOGGER.info("节目生成失败！");
                return false;
            }

            //生成相应窗口的xml
            String xml = createWindowXml("/xmstudio/media/" + textImageName, hexInputMode, hexOutputMode, speed, delay);
            matters.add(xml);
        }
        String scene = createSceneXml(matters,cms.getWidth(),cms.getHeight());
        //生成最终的xml
        String finalXml = createMediaXml(scene);
        //保存为media.xmml文件
        return CmsUtils.createFilebyCharset(finalXml, sendPath + "media.xmml", "UTF-8");
    }

    /**
     * 上传节目单
     *
     * @param cms
     * @param sendPath 发送路径
     * @return
     */
    private boolean uploadPageList(DeviceCmsDTO cms, String sendPath) {

        //超时时间设置
       DPDLedDll.INSTANCE_DPD.netagentsettimeout(6000, 2);
        DPDLedDll.INSTANCE_DPD.netagentsettimeout(6000, 3);

        boolean result = true;

        //1.和控制卡建立连接
        Pointer pointer = initConnection(cms);
        LOGGER.info("和控制卡建立连接");
        if (StringUtils.isBlank(pointer.toString())) {
            LOGGER.info("和控制卡建立连接失败!");
            return false;
        }

        LOGGER.info(new Date() + " 删除现有节目单");
        //2.删除现有节目的节目表 [不管是否成功，都不影响下一步操作]
        if (!deleteFile(pointer, "/xmstudio/media/media.xmml")) {
            LOGGER.info(new Date() + " 删除现有节目单失败");
        }

        LOGGER.info(new Date() + " 发送节目表单media.xmml文件");
        //3.发送节目表media.xmml文件	,这个文件必须成功
        if (sendFile(pointer, sendPath + "media.xmml", "/xmstudio/media/media.xmml")) {
            //4.停止播放
            if (!stopDisplay(pointer)) {
                LOGGER.info(new Date() + " 停止播放失败");
            }

            //5.发送转换成图片形式的节目单
            File root = new File(sendPath);
            File[] files = root.listFiles();
            for (int i = 0; i < files.length; i++) {
                if (files[i].isFile() && files[i].exists())//判断文件是否存在
                {
                    File file = files[i];
                    //获取文件名称
                    String filename = file.getName();
                    //判断是否是以指定格式结尾的,
                    if (filename.endsWith(".bmp") || filename.endsWith(".jpg")) {
                        boolean flag = sendFile(pointer, file.getPath(), "/xmstudio/media/" + filename);
                        LOGGER.info(new Date() + " 发送节目文件：" + filename + (flag ? "成功" : "失败"));
                        if (!flag) {
                            //但凡有一个节目单文件上传失败则整个流程失败
                            result &= flag;
                            break;
                        }
                    }
                }
            }
            if (result) {
                //6.重新播放节目
                boolean replayResult = replay(pointer);
                LOGGER.info(new Date() + " 重新播放节目：" + (replayResult ? "成功" : "失败"));
                result &= replayResult;
            }
        } else {
            LOGGER.info(new Date() + " 发送节目单media.xmml失败!");
            result = false;
        }


        //7.结束会话
        closeConnection(pointer);

        return result;
    }

    //初始化连接信息
    private Pointer initConnection(DeviceCmsDTO cms) {
        Pointer pointer = DPDLedDll.INSTANCE_DPD.netagentopen(cms.getIpAddress(), cms.getPort(), "player", "player");
        //返回空表示失败，非空表示成功
        return pointer;
    }

    /**
     * 删除控制卡上的文件，操作之前需要与控制卡建立连接
     *
     * @param pathInLed 文件在控制卡中的路径
     * @param pointer   控制卡建立连接成功的会话句柄
     * @return (未确认)
     */
    private boolean deleteFile(Pointer pointer, String pathInLed) {
        int cmd = NAM_FILE_DEL;     //删除现有节目的节目表命令:lpParam1为目标路径
        //成功返回1，失败返回<=1
        int delFileResult = DPDLedDll.INSTANCE_DPD.netagentcommand(pointer, cmd, pathInLed, null, null, null);
        return (delFileResult == 1);
    }

    /**
     * 发送文件到控制卡，操作之前需要与控制卡建立连接
     *
     * @param pointer    控制卡建立连接成功的会话句柄
     * @param sourcePath 文件源路径（本机中的发送路径）
     * @param targetPath 文件目标路径（控制卡中的目标路径）
     * @return
     */
    private boolean sendFile(Pointer pointer, String sourcePath, String targetPath) {
        int cmd = NAM_FILE_PUT;
        int sendFileXmlResult = DPDLedDll.INSTANCE_DPD.netagentcommand(pointer, cmd, sourcePath, targetPath, null, null);
        //成功返回1，失败返回-1
        return (sendFileXmlResult == 1);
    }

    /**
     * 停止LED屏的播放
     *
     * @param pointer 控制卡建立连接成功的会话句柄
     * @return (未确认)
     */
    private boolean stopDisplay(Pointer pointer) {
        int cmd = NAM_PLAY_SHUT;//停止播放命令
        int stopProgramResult = DPDLedDll.INSTANCE_DPD.netagentcommand(pointer, cmd, null, null, null, null);
        //成功返回1，失败返回<=1
        return (stopProgramResult == 1);
    }

    /**
     * 重新播放节目
     *
     * @param pointer 控制卡建立连接成功的会话句柄
     * @return
     */
    private boolean replay(Pointer pointer) {
        int cmd = NAM_PLAY_LOAD;//播放新的节目命令
        int playProgramResult = DPDLedDll.INSTANCE_DPD.netagentcommand(pointer, cmd, null, null, null, null);
        //播放新的节目，返回5表示成功，-1出错
        return (playProgramResult == 5);
    }

    /**
     * 断开到LED屏的连接
     *
     * @param pointer
     * @return (未确认)
     */
    private boolean closeConnection(Pointer pointer) {
        int cmd = NAM_CLOSE;//结束会话命令
        int closeNet = DPDLedDll.INSTANCE_DPD.netagentcommand(pointer, cmd, null, null, null, null);
        return (closeNet == 1);
    }

    //---------------------------------------------------------节目单生成方法--------------------------------------------

    private String createMediaXml(String sceneXml) {
        String xml = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n" +
                "<xmml publish=\"1.0\">\n" +
                "\t<xmedia version=\"1.0\">\n" +
                "\t\t<program length=\"1\">\n" +
                sceneXml +
                "\t\t</program>\n" +
                "\t</xmedia>\n" +
                "</xmml>";
        return xml;
    }

    private String createSceneXmlOld(ArrayList<String> windowXmls) {
        StringBuffer windows = new StringBuffer();
        for (String s : windowXmls) {
            windows.append(s);
        }
        String xml =
                "\t\t\t<scene length=\"0\">\n" +
                        windows.toString() +
                        "\t\t\t</scene>\n";
        return xml;
    }

    private String createSceneXml(ArrayList<String> windowXmls,int width, int height) {
        StringBuffer windows = new StringBuffer();
        for (String s : windowXmls) {
            windows.append(s);
        }
        String xml =
                "\t\t\t<scene length=\"0\">\n" +
                "\t\t\t\t<window designer=\"picture\" left=\"0\" top=\"0\" width=\"" + width + "\" height=\"" + height + "\" options=\"0x00000020\" length=\"0\">\n"
                        + windows.toString() +
                        "\t\t\t\t</window>\n"+
                        "\t\t\t</scene>\n";
        return xml;
    }

    private String createWindowXmlOld(String filePath, int x, int y, int width, int height, String enterID, String leaveID, int actionTime, int delayTime) {
        String xml = "\t\t\t\t<window designer=\"picture\" left=\"" + x + "\" top=\"" + y + "\" width=\"" + width + "\" height=\"" + height + "\" options=\"0x00000020\" length=\"0\">\n" +
                "\t\t\t\t\t<matter designer=\"picture\" decoder=\"0x00504D42\">\n" +
                "\t\t\t\t\t\t<effect enterid=\"" + enterID + "\" entertime=\"" + actionTime + "\" leaveid=\"" + leaveID + "\" leavetime=\"" + actionTime + "\" delaytime=\"" + delayTime + "\" options=\"0x00000001\"/>\n" +
                "\t\t\t\t\t\t<source type=\"0x00000001\">" + filePath + "</source>\n" +
                "\t\t\t\t\t</matter>\n" +
                "\t\t\t\t</window>\n";
        return xml;
    }

    private String createWindowXml(String filePath, String enterID, String leaveID, int actionTime, int delayTime) {
        String xml = "\t\t\t\t\t<matter designer=\"picture\" decoder=\"0x00504D42\">\n" +
                "\t\t\t\t\t\t<effect enterid=\"" + enterID + "\" entertime=\"" + actionTime + "\" leaveid=\"" + leaveID + "\" leavetime=\"" + actionTime + "\" delaytime=\"" + delayTime + "\" options=\"0x00000001\"/>\n" +
                "\t\t\t\t\t\t<source type=\"0x00000001\">" + filePath + "</source>\n" +
                "\t\t\t\t\t</matter>\n";
        return xml;
    }

    private boolean createTxtImage(String fontType,int fontSize,int x,int y, int width, int height, String bgColor, String fontColor, String content, String fullPath) {
        Font font = new Font(fontType, Font.PLAIN, fontSize);
        //生成图片  宽高锁定为设备宽高
        BufferedImage bufferedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D graphics = bufferedImage.createGraphics();
        graphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        graphics.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER));
        //设置文字背景
        graphics.setColor(new Color(Integer.parseInt(bgColor.substring(1, 3), 16), Integer.parseInt(bgColor.substring(3, 5), 16), Integer.parseInt(bgColor.substring(5), 16)));
        graphics.fillRect(0, 0, bufferedImage.getWidth(), bufferedImage.getHeight());
        graphics.setFont(font);
        graphics.setColor(new Color(Integer.parseInt(fontColor.substring(1, 3), 16), Integer.parseInt(fontColor.substring(3, 5), 16), Integer.parseInt(fontColor.substring(5), 16)));
        //绘制文字
        //遇到\n的地方换行
        String[] wordLines = content.split("\\\\n");
        for (int i = 0; i < wordLines.length; i++) {
            int mx = x;
            int my = y+i * fontSize + graphics.getFontMetrics().getAscent() - graphics.getFontMetrics().getDescent();
            graphics.drawString(wordLines[i], x, y+i * fontSize + graphics.getFontMetrics().getAscent() - graphics.getFontMetrics().getDescent());
        }
        graphics.dispose();
        OutputStream os = null;
        try {
            File file = new File(fullPath);
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
            if (file.exists()) {
                file.delete();
            }
            os = new FileOutputStream(file);
            ImageIO.write(bufferedImage, "bmp", os);
        } catch (IOException e) {
            LOGGER.info("创建节目图片失败，原因：" + e.getMessage());
            return false;
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                }
            }
        }
        return true;
    }

    //当需要上传图片时使用，将原来已经生成文字的图片形式的节目作为底图，将图片覆盖其上
    private boolean coverImage(String baseFilePath, String coverFilePath, int x, int y, int width, int height){
        try {
            File baseFile = new File(baseFilePath);//底图
            BufferedImage buffImg = ImageIO.read(baseFile);

            File coverFile = new File(coverFilePath); //覆盖层
            BufferedImage coverImg = ImageIO.read(coverFile);

            // 创建Graphics2D对象，用在底图对象上绘图
            Graphics2D g2d = buffImg.createGraphics();

            // 绘制
            g2d.drawImage(coverImg, x, y, width, height, null);
            g2d.dispose();// 释放图形上下文使用的系统资源

            return true;
        } catch (IOException e) {
            LOGGER.info("[德普达]生成图片失败:"+e.getMessage());
        }
        return false;
    }

    //---------------------------------------------------------将删除的未用代码------------------------------------------

    private boolean uploadPlaylist(DeviceCmsDTO cms) {
        int delcmd = NAM_FILE_DEL;     //删除现有节目的节目表命令:lpParam1为目标路径
        int sendFilecmd = NAM_FILE_PUT;//发送节目表文件命令:lpParam1为本地路径，lpParam2为远程路径
        int stopcmd = NAM_PLAY_SHUT;//停止播放命令
        int sendImgcmd = NAM_FILE_PUT;//发送转换好的图片文件命令:lpParam1为本地路径，lpParam2为远程路径
        int playcmd = NAM_PLAY_LOAD;//播放新的节目命令
        int closecmd = NAM_CLOSE;//结束会话命令
        int brightness = NAM_CONF_BRIGHTNESS;//亮度
        byte[] bytenull = null;
        int intnull = 0;
        String path = CMS_SINGLESEND_DIR + cms.getDeviceId().toString() + File.separator;
        boolean result = false;

        //【下面是对广告屏进行发送信息，如果多个则循环发送】。
        //1.和控制卡建立连接，调用动态库netclient.dll
        Pointer pointer = DPDLedDll.INSTANCE_DPD.netagentopen(cms.getIpAddress(), cms.getPort(), "player", "player");
        LOGGER.info("1、和控制卡建立连接：" + pointer);
        if (StringUtils.isBlank(pointer.toString())) {
            return false;
        }

        //2.删除现有节目的节目表 [不管是否成功，都不影响下一步操作]
        int delFileResult = DPDLedDll.INSTANCE_DPD.netagentcommand(pointer, delcmd, "/xmstudio/media/media.xmml", null, null, null);
        LOGGER.info("2、删除现有节目：" + delFileResult);

        //3.发送节目表media.xmml文件	,这个文件必须成功
        int sendFileXmlResult = DPDLedDll.INSTANCE_DPD.netagentcommand(pointer, sendFilecmd, path + "media.xmml", "/xmstudio/media/media.xmml", null, null);
        LOGGER.info("3、发送节目表文件：" + sendFileXmlResult);

        if (sendFileXmlResult != 1) {
            return false;
        }

        //4.停止播放
        //比如你一直在播放某个图片，然后没有停止播放，上传文件替换了这个图片。那么播放器在需要读取这个图片的时候，就会用新文件
        int stopProgramResult = DPDLedDll.INSTANCE_DPD.netagentcommand(pointer, stopcmd, null, null, null, null);
        LOGGER.info("4、停止播放：" + stopProgramResult);

        //5.发送转换好的图片文件【这里如果有多个图片，要循环获取图片：路径+传递的节目id+名称】
        String encoding = "GBK"; //考虑到编码格式
        File file = new File(path);
        File[] files = file.listFiles();
        for (int i = 0; i < files.length; i++) {
            if (files[i].isFile() && files[i].exists())//判断文件是否存在
            {
                //获取文件名称
                String filename = files[i].getName();
                //判断是否是以指定bmp格式结尾的,
                if (filename.endsWith(".jpg")) {
                    int sendImg = DPDLedDll.INSTANCE_DPD.netagentcommand(pointer, sendFilecmd, path + filename, "/xmstudio/media/" + filename, null, null);
                    //返回1是成功，-1出错。
                    LOGGER.info("5、发送转换好的图片文件：" + i + "" + sendImg);
                }
            }
        }

        //6.播放新的节目，返回5表示成功，-1出错
        int playProgram = DPDLedDll.INSTANCE_DPD.netagentcommand(pointer, playcmd, null, null, null, null);
        LOGGER.info("6、播放新的节目：" + playProgram);
        result = (playProgram == 5);


        //7.结束会话
        int closeNet = DPDLedDll.INSTANCE_DPD.netagentcommand(pointer, closecmd, null, null, null, null);
        LOGGER.info("7、结束会话：" + closeNet);

        return result;
    }
}
