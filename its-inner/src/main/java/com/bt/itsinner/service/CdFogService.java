package com.bt.itsinner.service;

import com.bt.itscore.domain.dto.FogInductPlanDTO;
import com.bt.itscore.domain.dto.FogInductStatusDTO;
import com.bt.itscore.domain.dto.FogMessageDTO;
import com.bt.itsinner.domain.entity.*;
import com.bt.itsinner.mapper.DeviceFogInductMapper;
import com.bt.itsinner.utils.CmsUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.util.*;

import static com.bt.itscore.utils.TimeUtils.getNowTime;

@Service("cdFogService")
public class CdFogService {
    Logger logger = LogManager.getLogger(LogManager.ROOT_LOGGER_NAME);
    public static final int CDFOGPORT=30002; //Server端监听端口

    @Autowired
    private DeviceFogInductMapper deviceFogInductMapper;

    public FogInductStatusDTO control(FogMessageDTO message, DeviceFogInduct d) {
        logger.info("[畅电畅电]雾区单控开始......");
        FogInductPlanDTO planDTO = message.getFogInductPlan(); //获取预案
        FogInductStatusDTO statusDTO = initPlan(d, planDTO);  //初始化预案
        statusDTO.setDeviceId(d.getDeviceId()); //设备ip
        return statusDTO;
    }

    public FogInductStatusDTO initPlan(DeviceFogInduct d, FogInductPlanDTO plan) {
        logger.info("[畅电畅电]雾区初始化预案......");
        if (plan != null && plan.getPlanContent() != null) {
            List<FogInductPlanContent> contentList = new Gson().fromJson(plan.getPlanContent(), new TypeToken<List<FogInductPlanContent>>() {
            }.getType());
            //先写联动表
            OutfieldLinkage link = new OutfieldLinkage();
            link.setDeviceId(d.getDeviceId());
            link.setType(1);
            link.setContent(plan.getPlanContent());
            link.setTime(getNowTime());
            int i = deviceFogInductMapper.deleteLinkageContent(link);
            try {
                Thread.sleep(50);
            } catch (InterruptedException e) {
            }
            int j = deviceFogInductMapper.saveLinkageContent(link);
            return excutePlan(d, contentList);
        }
        FogInductStatusDTO result = new FogInductStatusDTO();
        result.setDeviceId(d.getDeviceId());
        result.setStatus(0);
        result.setTime(getNowTime());
        return result;
    }

    public FogInductStatusDTO excutePlan(DeviceFogInduct d, List<FogInductPlanContent> contentList) {
        logger.info("[畅电畅电]雾区执行预案......");
        FogInductStatusDTO result = new FogInductStatusDTO();
        result.setDeviceId(d.getDeviceId());
        result.setStatus(0);
        result.setTime(getNowTime());
        if (contentList == null || contentList.size() == 0) {
            return result;
        }
        //依次判断预案条件是否满足
        for (FogInductPlanContent content : contentList) {
            //获取此时时间
            Calendar cal = Calendar.getInstance();
            int hour = cal.get(Calendar.HOUR_OF_DAY);//小时
            String timeRange = content.getTime();
            List<Integer> timeList = timeRangeConvert(timeRange);
            //无时间条件或满足时间条件，继续判断能见度条件
            if (timeList.size() == 0 || timeList.contains(hour)) {
                //获取能见度条件
                String visiThr = content.getVisibility();
                List<Integer> visiList = visibilityRangeConvert(visiThr);
                //如果有能见度条件
                if (visiList.size() > 0) {
                    //获取关联能见度设备
                    String visiId = d.getVisibilityId();
                    //如果不为空则查关联表，如果为空则认为不满足条件，跳过
                    if (visiId == null) {
                        continue;
                    }
                    OutfieldLinkage visiLink = new OutfieldLinkage();
                    visiLink.setDeviceId(visiId);
                    visiLink.setType(1);
                    String visi = null;
                    OutfieldLinkage visiResult = deviceFogInductMapper.queryLinkageContent(visiLink);
                    if (visiResult != null) {
                        visi = visiResult.getContent();
                    }
                    //如果能查到
                    if (visi != null) {
                        Integer visiInt = Integer.parseInt(visi);
                        if (!(visiList.size() == 1 && visiInt > visiList.get(0)) && !(visiList.size() == 2 && visiInt < visiList.get(1) && visiInt >= visiList.get(0))) {
                            //如果都不满足能见度阈值条件
                            continue;
                        }
                    } else {
                        //如果查不到，跳过
                        continue;
                    }
                }

                //如果没有能见度条件,或者满足能见度条件，则直接执行下面的预案
                return modifyWorkMode(d, content);
            }
        }
        //始终不满足条件无法执行预案的返回失败
        return result;
    }

    //真正要实现的方法↓
    //普通手动执行
    public FogInductStatusDTO modifyWorkMode(DeviceFogInduct fog, FogInductPlanContent planContent) {
        logger.info("[畅电畅电]修改工作模式......");
        boolean flag = false;
        boolean needModifyAlarmDistance = true;
        //  Socket socket = new Socket();
        DataOutputStream out = null;
        DataInputStream in = null;
        boolean flag0 = false;
        String recv0 = null, recv1 = null, recv2 = null;
        try {
            //从这里开始开发
            //----------------------------------------------------------------------------
            //1.开启UDP连接*************
            // 创建一个发送方的DatagramSocket对象，指定本地地址和端口
            DatagramSocket socket = new DatagramSocket(CDFOGPORT);
            int cmdId = 1; //第一条指令0x01
            // 要发送的指令数据HEX
            int lightlevelPWM = ligthcdTolightPWM(planContent.getLightLevel()); //cd值转为PWM值
            int workmode=workModeMatch(planContent.getWorkMode());  //预案工作模式转为畅电雾灯工作模式
            String cmd1 = CdFogPacket.getModifyWorkModeCmd(cmdId, workmode, planContent.getFlashFrequency(),
                    lightlevelPWM);
            InetAddress serverAddress = InetAddress.getByName(fog.getIpAddress()); //目标主机地址
            int serverPort = fog.getPort();
           // cmd1="A1018800000000005232F600"; //临时改为查询指令
          //  cmd1="A1D1023C2CEC43303002F6CC"; //还原雾灯原本控制
           //   cmd1="A109021E2CEC2CEC3419F601"; //临时控制命令
            cmd1=cmd1.toUpperCase(); //转为大写
            byte[] buffer = CmsUtils.hexStringToBytes(cmd1); //HEX转为byte[]
            //创建一个DatagramPacket对象，用于发送数据到指定IP和端口
            DatagramPacket packet = new DatagramPacket(buffer, buffer.length, serverAddress, serverPort);
            // 发送数据
            socket.send(packet);
            logger.info("[畅电] " + getNowTime() + " 向雾区主机[" + fog.getDeviceName() + "|" + fog.getIpAddress() + "]发送修改工作模式指令:" + cmd1);
            // 创建一个字节数组缓冲区，用于接收数据
            byte[] receiveBuffer = new byte[12];
            try {
                // 设置超时时间为30秒
                //  socket.setSoTimeout(20000);
                int count=60;
            while(true&&count>0)
            {
                count--;
            // 创建一个DatagramPacket对象，用于接收数据
                DatagramPacket receivePacket = new DatagramPacket(receiveBuffer, receiveBuffer.length);
                // 接收数据
                socket.receive(receivePacket);
                //获取发送端的 IP 地址
                String sendIP = receivePacket.getAddress().getHostAddress();
                //获取发送端的端口号
                int sendPort = receivePacket.getPort();
                if(sendIP.equals(fog.getIpAddress()))             // 从接收到指定IP的数据包中获取数据
                {
                    byte[] rePacket = receivePacket.getData();
                    recv0 = CmsUtils.bytesToHexString(rePacket); //byte[]转为hex
                   if(recv0.startsWith("A1")&&recv0.substring(20,22).equals("F6")) { //根据帧头帧尾筛选雾区状态数据
                       logger.info("Received message: [" + recv0 + "]from[" + sendIP + ":" + sendPort + "]");
                       break;
                   }
               }
                Thread.sleep(500); //每隔0.5s试图获取一次收到的指令
            }
            } catch (Exception e) {
                logger.info("[畅电雾区]发送指令后无回复或者超时！");
            }
            if (recv0 != null && CdFogPacket.getModifyWorkModeCmdResponse(recv0,cmd1)) { //接收包的过滤
                flag0 = true;
            }
            //关闭socket连接
              socket.close();
            //--------------------------------------------------------
        } catch (IOException e) {
            logger.info("[畅电雾区]手动控制失败，原因:" + e.getMessage());
            return null;
        } finally {
            //  closeIOStream(in, out);
        }
        logger.info("[畅电雾区]flag0:" + flag0);
        FogInductStatusDTO result = new FogInductStatusDTO();
        result.setDeviceId(fog.getDeviceId());
        if (flag0) {
            logger.info("[畅电]雾区控制成功");
            //如果成功修改状态表
            FogInductStatusDTO status = new FogInductStatusDTO();
            status.setDeviceId(fog.getDeviceId());
            status.setWorkMode(planContent.getWorkMode());
            status.setTailLength(planContent.getTailLength());
            status.setDutyCycle(planContent.getDutyCycle());
            status.setFlashFrequency(planContent.getFlashFrequency());
            status.setTime(getNowTime());
            status.setLightLevel(planContent.getLightLevel());
            status.setStatus(1);
            status.setTime(getNowTime());
            result = status;
            //成功时同时回写外网设备表状态
        } else {
            result.setTime(getNowTime());
            result.setStatus(0);
        }
        return result;
    }

    private List<Integer> timeRangeConvert(String time) {
        List<Integer> timeList = new ArrayList<>();
        if (time != null && time.length() > 0) {
            time = time.replace("{", "");
            time = time.replace("}", "");
            String[] ss = time.split(",");
            for (String s : ss) {
                timeList.add(Integer.parseInt(s));
            }
        }
        return timeList;
    }

    private List<Integer> visibilityRangeConvert(String visibility) {
        List<Integer> list = new ArrayList<>();
        if (visibility != null && visibility.length() > 0) {
            visibility = visibility.replace("[", "");
            visibility = visibility.replace("]", "");
            String[] ss = visibility.split(",");
            for (String s : ss) {
                list.add(Integer.parseInt(s));
            }
        }
        return list;
    }

    private boolean closeIOStream(InputStream in, OutputStream out) {
        boolean flag = true;
        if (in != null) {
            try {
                in.close();
            } catch (IOException e) {
                flag = false;
            }
        }
        if (out != null) {
            try {
                out.close();
            } catch (IOException e) {
                flag = false;
            }
        }
        return flag;
    }

    public  int ligthcdTolightPWM(int lightcd)
    {
        //亮度cd值转为PWM
        int lightPWM=4750; //默认设置为4700,1500cd
        switch (lightcd){
            case 500:
                lightPWM=1400;
                break;
            case 1000:
                lightPWM=2900;
                break;
            case 1500:
                lightPWM=4750;
                break;
            case 2500:
                lightPWM=8200;
                break;
            case 3500:
                lightPWM=11500;
                break;
            case 4500:
                lightPWM=15200;
                break;
            case 5700:
                lightPWM=21000;
                break;
            case 7000:
                lightPWM=28000;
                break;
        }
        return lightPWM;
    }

    //预案工作模式转换为畅电工作模式
    public  int  workModeMatch(int originWorkMode) {
        //亮度cd值转为PWM
        int result=2; //默认设置为黄闪
        switch (originWorkMode){
            case 8: //黄闪
                result=2;
                break;
            case 5: //雾灯关闭
                result=0;
                break;
            case 6: //黄灯常亮
                result=1;
                break;
            case 9: //红灯闪烁
                result=7;
                break;
        }
        return result;
    }

}