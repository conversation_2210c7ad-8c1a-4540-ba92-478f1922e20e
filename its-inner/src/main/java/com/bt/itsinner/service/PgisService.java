package com.bt.itsinner.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import com.bt.itscore.domain.dto.PgisDTO;
import com.bt.itsinner.utils.HttpRestUtil;

@Service("pgisService")
public class PgisService {
	private final static Logger LOGGER = LoggerFactory.getLogger(PgisService.class);
	@Autowired
	private HttpRestUtil httpRestUtil;

	public String getPgisDatas(PgisDTO dto) {
		LOGGER.info("捷赛停车诱导对接url:" + dto.getPgisUrl() +",params=" +dto.getParams()+",id=" + dto.getFwqId());
		MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
		params.add(dto.getParams(), dto.getFwqId());
		return (String) httpRestUtil.getHttp(dto.getPgisUrl(), params);
	}
}
