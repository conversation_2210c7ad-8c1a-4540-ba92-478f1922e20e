package com.bt.itsinner.service;

import com.bt.itscore.domain.dto.FogInductPlanDTO;
import com.bt.itscore.domain.dto.FogInductStatusDTO;
import com.bt.itscore.domain.dto.FogMessageDTO;
import com.bt.itsinner.domain.entity.JsFogPacket;
import com.bt.itsinner.domain.entity.DeviceFogInduct;
import com.bt.itsinner.domain.entity.FogInductPlanContent;
import com.bt.itsinner.domain.entity.OutfieldLinkage;
import com.bt.itsinner.mapper.DeviceFogInductMapper;
import com.bt.itsinner.utils.CmsUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.bt.itscore.utils.TimeUtils.getNowTime;

//捷赛雾区服务3.0
@Service("jsFogService")
public class JsFogService {
    Logger logger = LogManager.getLogger(LogManager.ROOT_LOGGER_NAME);
    private ConcurrentHashMap<String, String> visibilityIDMap = new ConcurrentHashMap<>();//没有用到

    @Autowired
    private DeviceFogInductMapper deviceFogInductMapper;


    public FogMessageDTO control(FogMessageDTO message) {
        DeviceFogInduct query = new DeviceFogInduct();
        query.setIdList(message.getIdList());
        query.setSourceId(message.getSourceId());
        query.setProtocol("捷赛科技V3");
        List<DeviceFogInduct> fogList = deviceFogInductMapper.selectList(query);
        FogInductPlanDTO planDTO = message.getFogInductPlan();
        List<FogInductStatusDTO> statusList = new ArrayList<>();
        for (DeviceFogInduct d : fogList) {
            if (d.getVisibilityId() != null) {
                visibilityIDMap.put(d.getDeviceId(), d.getVisibilityId());
            }
            FogInductStatusDTO statusDTO = initPlan(d, planDTO);
            statusDTO.setDeviceId(d.getDeviceId());
            statusList.add(statusDTO);
        }
        FogMessageDTO result = new FogMessageDTO();
        result.setDirection(2);
        result.setUuid(UUID.randomUUID().toString());
        result.setType("controlResult");
        result.setFogInductStatusList(statusList);
        return result;
    }
    

    public FogInductStatusDTO control(FogMessageDTO message, DeviceFogInduct d) {
        FogInductPlanDTO planDTO = message.getFogInductPlan();

        if (d.getVisibilityId() != null) {
            visibilityIDMap.put(d.getDeviceId(), d.getVisibilityId());
        }
        FogInductStatusDTO statusDTO = initPlan(d, planDTO);
        statusDTO.setDeviceId(d.getDeviceId());
        return statusDTO;
    }

    //普通手动执行
    public FogInductStatusDTO modifyWorkMode(DeviceFogInduct fog, FogInductPlanContent planContent) {
        boolean flag = false;
        boolean needModifyAlarmDistance = true;
        Socket socket = new Socket();
        DataOutputStream out = null;
        DataInputStream in = null;
        boolean flag0 = false, flag1 = false, flag2 = false;
        try {
            socket.connect(new InetSocketAddress(fog.getIpAddress(), fog.getPort()), 5000);
            socket.setSoTimeout(1000);
            in = new DataInputStream(socket.getInputStream());
            out = new DataOutputStream(socket.getOutputStream());
            //首先开控制
            String cmd0 = JsFogPacket.openController();
            String recv0 = send(in, out, cmd0);
            logger.info("[捷赛科技V3]开控制器回应:"+recv0);
            if (recv0 != null && JsFogPacket.generalResponse(recv0)) {
                flag0 = true;
            }
            //修改指令
            String cmd1 = JsFogPacket.getModifyWorkModeCmd(planContent.getWorkMode(), planContent.getFlashFrequency(),
                    planContent.getLightLevel(), planContent.getDutyCycle());
            logger.info("[捷赛科技V3] " + getNowTime() + " 向雾区主机[" + fog.getDeviceName() + "|" + fog.getIpAddress() + "]发送修改工作模式指令:\r\n" + cmd1);
            String recv1 = send(in, out, cmd1);
            if (recv1 != null && JsFogPacket.getModifyWorkModeCmdResponse(recv1)) {
                flag1 = true;
            }
            logger.info("[捷赛科技V3]修改工作模式回应:"+recv1);
            String cmd2 = JsFogPacket.getModifyAlarmDistanceAndTime(1, planContent.getTailLength(), 3);
            logger.info("[捷赛科技V3] " + getNowTime() + " 向雾区主机[" + fog.getDeviceName() + "|" + fog.getIpAddress() + "]发送修改警示距离指令:\r\n" + cmd2);
            String recv2 = send(in, out, cmd2);
            if (recv2 != null && JsFogPacket.getModifyAlarmDistanceAndTimeResponse(recv2)) {
                flag2 = true;
            }
            logger.info("[捷赛科技V3]修改警示距离回应:"+recv2);
            logger.info("[捷赛科技V3] " + getNowTime() + " 已向雾区主机[" + fog.getDeviceName() + "|" + fog.getIpAddress()
                    + "]发送修改工作模式指令如下-->工作模式：" + planContent.getWorkMode() + " 频率:" + planContent.getFlashFrequency()
                    + " 亮度等级:" + planContent.getLightLevel() + " 占空比:" + planContent.getDutyCycle() + " 警示距离:" + planContent.getTailLength());
            //依次判断结果
            String cmd3 = JsFogPacket.closeController();
            send(in, out, cmd3);
        } catch (IOException e) {
            logger.info("[捷赛雾区]手动控制失败，原因:" + e.getMessage());
            return null;
        } finally {
            closeIOStream(in, out);
        }
        logger.info("[捷赛雾区]flag0:" + flag0+"|flag1:"+flag1+"|flag2:"+flag2);
        FogInductStatusDTO result = new FogInductStatusDTO();
        result.setDeviceId(fog.getDeviceId());
        if (flag0 && flag1 && flag2) {
            logger.info("[捷赛科技V3]控制成功");
            //如果成功修改状态表
            FogInductStatusDTO status = new FogInductStatusDTO();
            status.setDeviceId(fog.getDeviceId());
            status.setWorkMode(planContent.getWorkMode());
            status.setTailLength(planContent.getTailLength());
            status.setDutyCycle(planContent.getDutyCycle());
            status.setFlashFrequency(planContent.getFlashFrequency());
            status.setTime(getNowTime());
            status.setLightLevel(planContent.getLightLevel());
            status.setStatus(1);
            status.setTime(getNowTime());
            result = status;
            //成功时同时回写外网设备表状态

        } else {
            result.setTime(getNowTime());
            result.setStatus(0);
        }
        return result;
    }


    private List<Integer> timeRangeConvert(String time) {
        List<Integer> timeList = new ArrayList<>();
        if (time != null && time.length() > 0) {
            time = time.replace("{", "");
            time = time.replace("}", "");
            String[] ss = time.split(",");
            for (String s : ss) {
                timeList.add(Integer.parseInt(s));
            }
        }
        return timeList;
    }

    private List<Integer> visibilityRangeConvert(String visibility) {
        List<Integer> list = new ArrayList<>();
        if (visibility != null && visibility.length() > 0) {
            visibility = visibility.replace("[", "");
            visibility = visibility.replace("]", "");
            String[] ss = visibility.split(",");
            for (String s : ss) {
                list.add(Integer.parseInt(s));
            }
        }
        return list;
    }

    //首次执行预案
    public FogInductStatusDTO initPlan(DeviceFogInduct d, FogInductPlanDTO plan) {
        if (plan != null && plan.getPlanContent() != null) {
            List<FogInductPlanContent> contentList = new Gson().fromJson(plan.getPlanContent(), new TypeToken<List<FogInductPlanContent>>() {
            }.getType());
            //先写联动表
            OutfieldLinkage link = new OutfieldLinkage();
            link.setDeviceId(d.getDeviceId());
            link.setType(1);
            link.setContent(plan.getPlanContent());
            link.setTime(getNowTime());
            int i = deviceFogInductMapper.deleteLinkageContent(link);
            try {
                Thread.sleep(50);
            } catch (InterruptedException e) {
            }
            int j = deviceFogInductMapper.saveLinkageContent(link);
            return excutePlan(d, contentList);
        }
        FogInductStatusDTO result = new FogInductStatusDTO();
        result.setDeviceId(d.getDeviceId());
        result.setStatus(0);
        result.setTime(getNowTime());
        return result;
    }

    public FogInductStatusDTO excutePlan(DeviceFogInduct d, List<FogInductPlanContent> contentList) {
        FogInductStatusDTO result = new FogInductStatusDTO();
        result.setDeviceId(d.getDeviceId());
        result.setStatus(0);
        result.setTime(getNowTime());
        if (contentList == null || contentList.size() == 0) {
            return result;
        }
        //依次判断预案条件是否满足
        for (FogInductPlanContent content : contentList) {
            //获取此时时间
            Calendar cal = Calendar.getInstance();
            int hour = cal.get(Calendar.HOUR_OF_DAY);//小时
            String timeRange = content.getTime();
            List<Integer> timeList = timeRangeConvert(timeRange);
            //无时间条件或满足时间条件，继续判断能见度条件
            if (timeList.size() == 0 || timeList.contains(hour)) {
                //获取能见度条件
                String visiThr = content.getVisibility();
                List<Integer> visiList = visibilityRangeConvert(visiThr);
                //如果有能见度条件
                if (visiList.size() > 0) {
                    //获取关联能见度设备
                    String visiId = d.getVisibilityId();
                    //如果不为空则查关联表，如果为空则认为不满足条件，跳过
                    if (visiId == null) {
                        continue;
                    }
                    OutfieldLinkage visiLink = new OutfieldLinkage();
                    visiLink.setDeviceId(visiId);
                    visiLink.setType(1);
                    String visi = null;
                    OutfieldLinkage visiResult = deviceFogInductMapper.queryLinkageContent(visiLink);
                    if (visiResult != null) {
                        visi = visiResult.getContent();
                    }
                    //如果能查到
                    if (visi != null) {
                        Integer visiInt = Integer.parseInt(visi);
                        if (!(visiList.size() == 1 && visiInt > visiList.get(0)) && !(visiList.size() == 2 && visiInt < visiList.get(1) && visiInt >= visiList.get(0))) {
                            //如果都不满足能见度阈值条件
                            continue;
                        }
                    } else {
                        //如果查不到，跳过
                        continue;
                    }
                }

                //如果没有能见度条件,或者满足能见度条件，则直接执行下面的预案
                return modifyWorkMode(d, content);
            }
        }
        //始终不满足条件无法执行预案的返回失败
        return result;
    }

    public String send(DataInputStream in, DataOutputStream out, String cmd) {
        String word = "";
        cmd = cmd.toUpperCase(Locale.ROOT);
        try {
            //传输
            byte[] senddata = CmsUtils.hexStringToBytes(cmd);
            out.write(senddata);
            int readLenth = 1024;
            while (readLenth > 0) {
                byte[] respondata = new byte[readLenth];
                readLenth = in.read(respondata);
                Thread.sleep(50);//预留足够的时间等待
                respondata = Arrays.copyOfRange(respondata, 0, readLenth);
                String hexResult = CmsUtils.bytesToHexString(respondata).toUpperCase(Locale.ROOT);
                word += hexResult;
                logger.info("[捷赛雾区V3]收到回应:" + hexResult);
            }
            return word;
        } catch (Exception e) {
            //logger.info("[捷赛雾区]发送指令失败，原因:" + e.getMessage());
            return word;
        }
    }


    private boolean closeIOStream(InputStream in, OutputStream out) {
        boolean flag = true;
        if (in != null) {
            try {
                in.close();
            } catch (IOException e) {
                flag = false;
            }
        }
        if (out != null) {
            try {
                out.close();
            } catch (IOException e) {
                flag = false;
            }
        }
        return flag;
    }
}
