package com.bt.itsinner.service;

import java.io.FileOutputStream;
import java.io.IOException;
import java.security.DigestException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpVersion;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.bt.itscore.domain.dto.Event96333NewOrderDTO;
import com.bt.itscore.domain.dto.Event96333SubmitDTO;
import com.bt.itscore.domain.dto.Event96333VisitDTO;
import com.bt.itscore.domain.dto.SmsTemplateDTO;
import com.bt.itscore.domain.vo.ResponseVO;
import com.bt.itscore.enums.ServiceTypeEnum;
import com.bt.itscore.utils.AliyunSmsUtils;
import com.bt.itscore.utils.EncodeUtils;
import com.bt.itscore.utils.HttpClientUtils;
import com.bt.itscore.utils.SHA1Utils;
import com.bt.itscore.utils.ServiceUtils;
import com.bt.itscore.utils.TimeUtils;
import com.bt.itsinner.domain.dto.Event96333ApiParamDTO;
import com.bt.itsinner.domain.dto.Event96333DataDTO;
import com.bt.itsinner.domain.dto.Event96333OrderDetailDTO;
import com.bt.itsinner.mapper.EventMapper;
import com.google.gson.Gson;
/**
 * <AUTHOR>
 * @date 2023年2月17日 下午2:18:00
 * @Description 千方96333对接业务
 */
@EnableScheduling // 开启定时任务
@Service("event96333OrderService")
public class Event96333OrderService {
	private final static Logger LOGGER = LoggerFactory.getLogger(Event96333OrderService.class);
	@Autowired
	EventMapper eventMapper;
	@Value("${online.event.no:DD20231003090148}")
    private String onlineEventNo;

	private static Map<String, String> sourceDeptMap = new HashMap<>();
	@Autowired
	StringRedisTemplate stringRedisTemplate;

	static {
		String prefix = "E09.645.GGJ.LWSFZX.GSYZ.";
		String yanhai1 = "YHGSGLFGS";// 沿海
		String luocheng2 = "GXGS.RHGS.LCFZX";// 罗成
		String dahua3 = "GXGS_GSYZ_DHFZX";// 大化
		String zhaoping4 = "CZGSYY.XHTGSGLYXGS";// 昭平
		String lingshan5 = "GXGS.GSYZ.KFZX.LSFZX";// 灵山
		String laibin6 = "HBGS.LAIBINGAOSU";// 来宾
		String nanning7 = "HBGS.NANNINGGAOSU";// 南宁
		String qinzhou8 = "HBGS.QINZHOUGAOSU";// 钦州
		String beihai9 = "HBGS.BEIHAIGAOSU";// 北海
		sourceDeptMap.put("1", prefix + yanhai1);// 沿海
		sourceDeptMap.put("2", prefix + luocheng2);// 罗成
		sourceDeptMap.put("3", prefix + dahua3);// 大化
		sourceDeptMap.put("4", prefix + zhaoping4);// 昭平
		sourceDeptMap.put("5", prefix + lingshan5);// 灵山
		sourceDeptMap.put("6", prefix + laibin6);// 来宾
		sourceDeptMap.put("7", prefix + nanning7);// 南宁
		sourceDeptMap.put("8", prefix + qinzhou8);// 钦州
		sourceDeptMap.put("9", prefix + beihai9);// 北海

		sourceDeptMap.put(prefix + yanhai1, "1");
		sourceDeptMap.put(prefix + luocheng2, "2");
		sourceDeptMap.put(prefix + dahua3, "3");
		sourceDeptMap.put(prefix + zhaoping4, "4");
		sourceDeptMap.put(prefix + lingshan5, "5");
		sourceDeptMap.put(prefix + laibin6, "6");
		sourceDeptMap.put(prefix + nanning7, "7");
		sourceDeptMap.put(prefix + qinzhou8, "8");
		sourceDeptMap.put(prefix + beihai9, "9");

		sourceDeptMap.put(yanhai1, "1");
		sourceDeptMap.put(luocheng2, "2");
		sourceDeptMap.put(dahua3, "3");
		sourceDeptMap.put(zhaoping4, "4");
		sourceDeptMap.put(lingshan5, "5");
		sourceDeptMap.put(laibin6, "6");
		sourceDeptMap.put(nanning7, "7");
		sourceDeptMap.put(qinzhou8, "8");
		sourceDeptMap.put(beihai9, "9");
	}

	@Value("${yk.domain:https://yktest.gxits.cn:8763/s}")
	private String ykDomain;
	@Value("${event96333.website:http://**********:8627}")
	private String event96333Website;
	@Value("${event96333.login.appKey:ec4d82cbed3d11ebbcdb00163e0c0763}")
	private String appKey;
	@Value("${event96333.login.appSecret:a03a5f5d8fed0e6a650264b483a7efad}")
	private String appSecret;
	
	@Value("${heart.beat.secret:u&jQ74kX45SFm3CD}")
	private String heartBeatSecret;
	
	@Value("${deploy.source.ip:************}")
	private String serviceIp;

    @Value("${spring.application.name}")
    private String springApplicationName;

    public ResponseVO pushNew96333Order(Event96333NewOrderDTO dto) {
    	String serviceItem = dto.getServiceItem();
    	if (serviceItem != null) {
    		dto.setServiceItem(serviceItem.replaceAll("->", "-＞").replaceAll("-\\\\u003e", "-＞"));
    	}
    	dto.setJson(dto.getJson().replaceAll("->", "-＞").replaceAll("-\\\\u003e", "-＞"));
		boolean exist = eventMapper.checkEvent96333NewOrder(dto) > 0;
		boolean success = false;
		if(exist) {
			success = eventMapper.updateEvent96333NewOrder(dto) > 0;
		} else {
			success = eventMapper.add(dto) > 0;
		}
		if(success) {
			try {
				String orderDetail = query96333OrderDetail(dto.getRecordId(), dto.getServiceType());
				Event96333DataDTO detailDTO = new Gson().fromJson(orderDetail, Event96333DataDTO.class);
				Event96333OrderDetailDTO data = detailDTO.getData();
				if (data != null) {
					String state = data.getState();
				    String replayContent = data.getReplayContent();
				    if ("03".equals(state)) {
				    	// 已办结的工单不推送
				    	LOGGER.warn("千方推送了已办结的工单：{}", dto.getRecordId());
				    	return new ResponseVO(true);
				    }
				    dto.setReplayContent(replayContent);
				    String carno = data.getCarno();
				    if (StringUtils.isNotBlank(carno)) {
                        dto.setCarno(carno);
                    }
				    String roadCode = data.getRoadCode();
				    String roadName = data.getRoadName();
				    String road = "";
				    if (StringUtils.isNotBlank(roadCode)) {
				        road += roadCode;
				    }
				    if (StringUtils.isNotBlank(roadName)) {
				        road += roadName;
				    }
				    
				    dto.setRoadSection(road);
				    dto.setBusiFieldName(data.getBusiFieldName());
				}

				dto.setBusiId(data.getId());
			} catch(Exception e){
				e.printStackTrace();
				LOGGER.error("接收新工单时，查询工单详情的id失败。");
			}
			ResponseVO addEvent96333Order = addEvent96333Order(dto);
			Integer code = addEvent96333Order.getCode();
			if(code != null && code != 1) {
				addEvent96333Order = addEvent96333Order(dto);
			}
			return addEvent96333Order;
		}
		return new ResponseVO(false);
	}

    /**
     * @描述 调用阿里云接口，入库96333事件
     */
    private ResponseVO addEvent96333Order(Event96333NewOrderDTO dto) {
//        String recordId = dto.getRecordId();
        String eventUploadUrl = ykDomain + "/its-ms/ms/addEvent96333Order";
//        if(recordId != null && recordId.startsWith("ZX")) {
//            eventUploadUrl = "https://8.134.57.103:8763/s/its-ms/ms/addEvent96333Order";
//        }
        String sourceDept = dto.getSourceDept();
        String sourceId = sourceDeptMap.get(sourceDept);
        dto.setSourceId(NumberUtils.toInt(sourceId));
        Map<String, String> headParam = ServiceUtils.getInnerLoginHead();
        String post = HttpClientUtils.post(eventUploadUrl, headParam, new Gson().toJson(dto));
        if(post == null) {
        	return new ResponseVO(false);
        }
        ResponseVO fromJson = new Gson().fromJson(post, ResponseVO.class);
        Integer code = fromJson.getCode();
        if(code != null && code == 401) {
        	fromJson.setCode(0);
        }
        return fromJson;
    }

    /**
     * @描述 96333工单回退(撤回)推送
     */
    public ResponseVO pushCancel96333Order(Event96333NewOrderDTO dto) {
    	detail(dto);// 查询工单详情及打印日志，为了异常检查使用
		boolean exist = eventMapper.checkCanceledOrder(dto) > 0;
		boolean success = false;
		if(exist) {
			success = eventMapper.updateCanceledOrder(dto) > 0;
		} else {
			success = eventMapper.addCanceledOrder(dto) > 0;
		}
		if(success) {
			return cancel96333Order(dto);
		}
		return new ResponseVO(false);
	}

    /**
     * @描述 调用阿里云接口，撤回96333工单（删除云控工单）
     */
    private ResponseVO cancel96333Order(Event96333NewOrderDTO dto) {
        String eventCancelUrl = ykDomain + "/its-ms/ms/cancel96333Order";
        String sourceDept = dto.getSourceDept();
        String sourceId = sourceDeptMap.get(sourceDept);
        dto.setSourceId(NumberUtils.toInt(sourceId));
        Map<String, String> headParam = ServiceUtils.getInnerLoginHead();
        String post = HttpClientUtils.post(eventCancelUrl, headParam, new Gson().toJson(dto));
        if(post == null) {
        	return new ResponseVO(false);
        }
        ResponseVO fromJson = new Gson().fromJson(post, ResponseVO.class);
        return fromJson;
    }

    // 查询96333工单详情
    public String query96333OrderDetail(String recordId, String serviceType) {
    	LOGGER.info("96333工单进度查询start：{}，serviceType：{}", recordId, serviceType);
		Map<String, Object> maps = new HashMap<>();
		maps.put("appKey", appKey);
		maps.put("recordId", recordId);
		if(serviceType == null) {
			if(recordId.startsWith("DD")) {
				serviceType = ServiceTypeEnum.DD.getValue();
			} else if(recordId.startsWith("TS")) {
				serviceType = ServiceTypeEnum.TS.getValue();
			} else if(recordId.startsWith("JY")) {
				serviceType = ServiceTypeEnum.JY.getValue();
			} else if(recordId.startsWith("ZX")) {
                serviceType = ServiceTypeEnum.ZX.getValue();
            }
		}
		maps.put("serviceType", serviceType);
		try {
			String sign = SHA1Utils.sign(appSecret, maps);
			Event96333ApiParamDTO paramDTO = new Event96333ApiParamDTO();
			paramDTO.setAppKey(appKey);
			paramDTO.setSign(sign);
			paramDTO.setRecordId(recordId);
			paramDTO.setServiceType(serviceType);
			String url = event96333Website + "/api/order/handlePace";// 工单进度查询
			String param = new Gson().toJson(paramDTO);
			LOGGER.info("96333工单进度查询start");
			String requestResult = HttpClientUtils.post(url, null, param);
			LOGGER.info("96333工单进度查询end");
			return requestResult;
		} catch (DigestException e) {
			e.printStackTrace();
			LOGGER.info("96333工单进度查询异常end：");
			return null;
		}
    }

	public String handlePace(Event96333NewOrderDTO dto) {
		String recordId = dto.getRecordId();
		String serviceType = dto.getServiceType();
		return query96333OrderDetail(recordId, serviceType);
	}

	public ResponseVO handlePaces(Event96333NewOrderDTO dto) {
		String recordId = dto.getRecordId();
		String[] split = recordId.split(",");
		
		Event96333NewOrderDTO x = new Event96333NewOrderDTO();
		
		for (String eventNo : split) {
			x.setRecordId(eventNo);
			String serviceType = dto.getServiceType();
			Map<String, Object> maps = new HashMap<>();
			maps.put("appKey", appKey);
			maps.put("recordId", eventNo);
			if(serviceType == null) {
				if(eventNo.startsWith("DD")) {
					serviceType = ServiceTypeEnum.DD.getValue();
				} else if(eventNo.startsWith("TS")) {
					serviceType = ServiceTypeEnum.TS.getValue();
				} else if(eventNo.startsWith("JY")) {
					serviceType = ServiceTypeEnum.JY.getValue();
				}
			}
			maps.put("serviceType", serviceType);
			try {
				String sign = SHA1Utils.sign(appSecret, maps);
				Event96333ApiParamDTO paramDTO = new Event96333ApiParamDTO();
				paramDTO.setAppKey(appKey);
				paramDTO.setSign(sign);
				paramDTO.setRecordId(eventNo);
				paramDTO.setServiceType(serviceType);
				String url = event96333Website + "/api/order/handlePace";//工单进度查询
				String param = new Gson().toJson(paramDTO);
				String post = HttpClientUtils.post(url, null, param);
				if(post != null && post.contains("\"reasonAble\":0")) {
					LOGGER.warn(eventNo + "," + 0); 
				} else if(post != null && post.contains("\"reasonAble\":1")) {
					LOGGER.warn(eventNo + "," + 1); 
				}
			} catch (DigestException e) {
				e.printStackTrace();
			}
		}

		return new ResponseVO(true);
	}

	public String detail(Event96333NewOrderDTO dto) {
		String recordId = dto.getRecordId();
		LOGGER.info("96333工单进度查询start：{}", recordId);
		Map<String, Object> maps = new HashMap<>();
		maps.put("appKey", appKey);
		maps.put("recordId", recordId);
		try {
			LOGGER.info("appSecret：{}", appSecret);
			String sign = SHA1Utils.sign(appSecret, maps);
			Event96333ApiParamDTO paramDTO = new Event96333ApiParamDTO();
			paramDTO.setAppKey(appKey);
			paramDTO.setSign(sign);
			paramDTO.setRecordId(recordId);
			String url = event96333Website + "/api/order/handlePace";//工单进度查询
			String param = new Gson().toJson(paramDTO);
			LOGGER.info("96333工单进度查询param：{}", param);
			String post = HttpClientUtils.post(url, null, param);
			LOGGER.info("96333工单进度查询post：{}", post);
			return post;
		} catch (DigestException e) {
			e.printStackTrace();
			LOGGER.info("96333工单进度查询异常end：");
		}
		return null;
	}

	public boolean finish(Event96333SubmitDTO dto) {
		String eventNo = dto.getEventNo();
		if(eventNo == null) {
			LOGGER.error("finish工单号为空");
			return false;
		}
		boolean success = resultSubmit(dto);
		if(!success) {
			success = resultSubmit(dto);
		}
		LOGGER.info("提交工单{}结果（true成功，false失败）：{}",eventNo, success);
		String serverTime = TimeUtils.getTimeString();
		// 存入提交处理结果成功码表
		Map<String, Object> map = new HashMap<>();
		map.put("success", success ? 1 : 0);
		map.put("eventNo", eventNo);
		map.put("createTime", serverTime);
		eventMapper.addFinishSuccessCode(map);
		if(!success) {
			String json = new Gson().toJson(dto);
			LOGGER.error("提交失败:{}", json);
			dto.setCreateTime(serverTime);
			int ret = eventMapper.addEvent96333OrderFail(dto);
			LOGGER.error("存入失败表event_96333_fail:{}", ret);

			// 发邮件通知维护，手动处理下（概率比较小）
			SmsTemplateDTO smstemplateDTO = new SmsTemplateDTO();
			smstemplateDTO.setContent("{\"serviceNames\":\"" + dto.getEventNo() + "-" + dto.getBusiId() + "，完结事件提交到96333失败\"}");
			smstemplateDTO.setTemplateId(stringRedisTemplate.opsForValue().get("its-event:SmsTemplateCode-SERVICE_OFFLINE"));
			smstemplateDTO.setMobile("***********");
			AliyunSmsUtils.sendAnSms(smstemplateDTO);
		}

		return success;
	}

//	private boolean finishEventBusiness(Event96333SubmitDTO dto) {
//		String serviceType = dto.getServiceType();
//		if("04".equals(serviceType)) {
//			return finishDD(dto);
//		} else if("01".equals(serviceType)) {
//			return finishTS(dto);
//		} else if("03".equals(serviceType)) {
//			return finishJY(dto);
//		}
//
//		return false;
//	}

	private boolean resultSubmit(Event96333SubmitDTO dto) {
		// 调用接口8.2.5resultSubmit
		String recordId = dto.getEventNo();
		LOGGER.info("96333处理结果报送消息start：{}", recordId);
		LOGGER.info(new Gson().toJson(dto));
		Map<String, Object> maps = new HashMap<>();
		maps.put("appKey", appKey);
		maps.put("recordId", recordId);
		maps.put("closeTime", TimeUtils.getTimeString(TimeUtils.DATE_TIME_7));
		maps.put("operator", dto.getSubmitAccount());
		Integer sourceId = dto.getSourceId();
		String department = sourceDeptMap.get("" + sourceId);
		if(department != null) {
			maps.put("department", department);
		}
		maps.put("delcontent", dto.getDealResult());
		maps.put("state", "03");
		maps.put("serviceType", dto.getServiceType());
		if("03".equals(dto.getServiceType()) || "01".equals(dto.getServiceType())) {
//			maps.put("complaintType", dto.getComplaintType());
			maps.put("reasonAble", dto.getComplaintType());
		}
		try {
			LOGGER.info("appKey：{}", appKey);
			LOGGER.info("appSecret：{}", appSecret);
			String sign = SHA1Utils.sign(appSecret, maps);
			maps.put("sign", sign);
			LOGGER.info("96333处理结果报送消息sign：{}", sign);
			String url = event96333Website + "/api/order/resultSubmit";//工单处理结果报送
			String post = HttpClientUtils.post(url, null, new Gson().toJson(maps));
			LOGGER.info("96333处理结果报送消息post：{}", post);
			if(StringUtils.isBlank(post)) {
				return false;
			}
			ResponseVO ret = new Gson().fromJson(post, ResponseVO.class);
			Integer code = ret.getCode();
			if(code != null && code == 0) {
				return true;
			}
		} catch (DigestException e) {
			e.printStackTrace();
			LOGGER.info("96333处理结果报送消息异常end：");
		}
		return false;
	}

	/**
	 * @描述 回访结果提交到96333系统
	 */
	public boolean addEventVisit(Event96333VisitDTO dto) {
		// api/order/visitSubmit
		String eventNo = dto.getEventNo();
		if(eventNo == null) {
			LOGGER.error("visit工单号为空");
			return false;
		}
		boolean success = visitSubmit(dto);
		if(!success) {
			success = visitSubmit(dto);
		}
		LOGGER.info("回访工单{}结果（true成功，false失败）：{}",eventNo, success);
		String serverTime = TimeUtils.getTimeString();
		// 存入提交处理结果成功码表
		Map<String, Object> map = new HashMap<>();
		map.put("success", success ? 1 : 0);
		map.put("eventNo", eventNo);
		map.put("createTime", serverTime);
		eventMapper.addVisitSuccessCode(map);
		if(!success) {
			String json = new Gson().toJson(dto);
			LOGGER.error("回访失败:{}", json);
			dto.setCreateTime(serverTime);
			int ret = eventMapper.addEvent96333VisitFail(dto);
			LOGGER.error("存入回访失败表event_96333_visit_fail:{}", ret);

			// 发邮件通知维护，手动处理下（概率比较小）
			SmsTemplateDTO smstemplateDTO = new SmsTemplateDTO();
			smstemplateDTO.setContent("{\"serviceNames\":\"" + eventNo + "-" + serverTime + "，回访事件提交到96333失败\"}");
			smstemplateDTO.setTemplateId(stringRedisTemplate.opsForValue().get("its-event:SmsTemplateCode-SERVICE_OFFLINE"));
			smstemplateDTO.setMobile("***********");
			AliyunSmsUtils.sendAnSms(smstemplateDTO);
		}

		return success;
	}

	private boolean visitSubmit(Event96333VisitDTO dto) {
		// 调用接口8.1.4visitSubmit
		String recordId = dto.getEventNo();
		LOGGER.info("96333回访结果报送消息start：{}", recordId);
		LOGGER.info(new Gson().toJson(dto));
		Map<String, Object> maps = new HashMap<>();
		maps.put("appKey", appKey);
		maps.put("recordId", recordId);
		maps.put("satId", dto.getSatId());
		maps.put("name", dto.getName());
		maps.put("phone", dto.getPhone());
		maps.put("checkTime", dto.getCheckTime());
		maps.put("checkOperator", dto.getCheckOperator());
		maps.put("checkDepartment", dto.getCheckDepartment());
		maps.put("scord", dto.getScord());
		maps.put("checkContent", dto.getCheckContent());
		maps.put("serviceType", dto.getServiceType());
//		Integer sourceId = dto.getSourceId();
//		maps.put("department", sourceDeptMap.get("" + sourceId));
		try {
			LOGGER.info("appKey：{}", appKey);
			LOGGER.info("appSecret：{}", appSecret);
			String sign = SHA1Utils.sign(appSecret, maps);
			maps.put("sign", sign);
			LOGGER.info("96333回访结果报送消息sign：{}", sign);
			String url = event96333Website + "/api/order/visitSubmit";// 回访结果提交消息
			String post = HttpClientUtils.post(url, null, new Gson().toJson(maps));
			LOGGER.info("96333回访结果报送消息post：{}", post);
			if(StringUtils.isBlank(post)) {
				return false;
			}
			ResponseVO ret = new Gson().fromJson(post, ResponseVO.class);
			Integer code = ret.getCode();
			if(code != null && code == 0) {
				return true;
			}
		} catch (DigestException e) {
			e.printStackTrace();
			LOGGER.info("96333回访结果报送消息异常end：");
		}
		return false;
	}

//	public static void main(String[] args) {
//		Map<String, Object> maps = new HashMap<>();
//		maps.put("appKey", "123das");
//		maps.put("recordId", "DD151213");
//		maps.put("closeTime", TimeUtils.getTimeString(TimeUtils.DATE_TIME_7));
//		maps.put("operator", "张三");
//		maps.put("department", "GXGS.GSYZ.KFZX.LSFZX");
//		maps.put("delcontent", "处理结果");
//		maps.put("state", "03");
//		maps.put("sign", null);
//		System.out.println(new Gson().toJson(maps));
//	}
    /**
     * @描述 监听千方系统接口是否能正常访问
     */
    @Scheduled(cron = "30 */2 * * * ?")
    private void check96333Service() {
        if (!"its-inner-xfz".equals(springApplicationName)) {
            return ;
        }
        Event96333NewOrderDTO dto = new Event96333NewOrderDTO();
        dto.setRecordId(onlineEventNo);
        dto.setServiceType(ServiceTypeEnum.DD.getValue());
        String resullt = handlePace(dto);
        if (resullt != null && resullt.contains(dto.getRecordId())) {
            LOGGER.debug("resullt:{}", resullt);
            LOGGER.debug("HeartBeatSender:{}", LocalDateTime.now());
            // HTTP（POST）请求阿里云接口（/its-ms/ms/serviceHeartBeat）
            // 入库服务名称，IP，来源（沿海、罗城、大化、昭平、灵山），时间，
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("serviceName", "96333系统");
            param.put("ip", serviceIp);
            param.put("sourceName", "千方");
            param.put("sourceTime", TimeUtils.getTimeString());
            long timestamp = System.currentTimeMillis();
            LOGGER.debug("heartBeatSecret:{}", heartBeatSecret);
            String token = EncodeUtils.encode(heartBeatSecret + timestamp, "MD5");
            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("ykSolt", "" + timestamp);
            String post = HttpClientUtils.post(ykDomain + "/its-ms/ms/serviceHeartBeat", headers, new Gson().toJson(param));
            LOGGER.info("上传千方系统接口心跳到阿里云，返回:{}", post);
        }
    }

    public String txtlog(Event96333NewOrderDTO dto) {
        Map<String, Object> maps = new HashMap<>();
        maps.put("appKey", appKey);
        maps.put("callId", dto.getCallid());
        try {
            String sign = SHA1Utils.sign(appSecret, maps);
            Event96333ApiParamDTO paramDTO = new Event96333ApiParamDTO();
            paramDTO.setAppKey(appKey);
            paramDTO.setSign(sign);
            paramDTO.setCallId(dto.getCallid());
            String url = event96333Website + "/api/order/txlog";//来电记录消息
            String param = new Gson().toJson(paramDTO);
            LOGGER.info("param:{}", param);
            String post = HttpClientUtils.post(url, null, param);
            return post;
        } catch (DigestException e) {
            e.printStackTrace();
        }
        return "请求异常";
    }

    public String record(Event96333NewOrderDTO dto) {
        String callid = dto.getCallid();
        Map<String, Object> maps = new HashMap<>();
        maps.put("appKey", appKey);
        maps.put("callId", callid);
        try {
            String sign = SHA1Utils.sign(appSecret, maps);
            Event96333ApiParamDTO paramDTO = new Event96333ApiParamDTO();
            paramDTO.setAppKey(appKey);
            paramDTO.setSign(sign);
            paramDTO.setCallId(callid);
            String url = event96333Website + "/api/order/record";//来电记录消息
            String param = new Gson().toJson(paramDTO);
            LOGGER.info("param:{}", param);
            String post = save96333OrderRecordFile(url+"?appKey="+appKey+"&callId="+callid+"&sign="+sign, callid);
            return post;
        } catch (DigestException e) {
            e.printStackTrace();
        }
        return "请求异常";
    }

    /**
     * @描述 保存96333录音文件到新发展服务器
     */
    public static String save96333OrderRecordFile(String url, String fileName) {
        CloseableHttpClient httpClient = null;
        String content = null;
        FileOutputStream fos = null;
        try {
            httpClient = HttpClientUtils.getClient();
            RequestConfig config = HttpClientUtils.getConfig();
            CloseableHttpResponse res = null;
            HttpGet get = new HttpGet(url);
            get.setProtocolVersion(HttpVersion.HTTP_1_0);
            get.setConfig(config);
//        get.setHeader("content-type", "application/octet-stream;charset=UTF-8");
            get.setHeader("content-type", "application/json;charset=UTF-8");
            res = httpClient.execute(get);
            HttpEntity entity = res.getEntity();
            //获取文件字节流
            byte[] bytes = EntityUtils.toByteArray(entity);
            if (bytes.length > 0) {
                content = new String(bytes, "UTF-8");
            }
            //文件保存路径
            String localPath = "/home/<USER>/" + fileName + ".wav";
            fos = new FileOutputStream(localPath);
            fos.write(bytes);
            fos.flush();
            //关闭流
            fos.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            e.printStackTrace();
        } finally {
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if(httpClient != null) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return content;
    }

    public String knowledgeIssued(Event96333NewOrderDTO dto) {
        Map<String, Object> maps = new HashMap<>();
        maps.put("appKey", appKey);
        maps.put("startDate", dto.getStartTime());
        maps.put("endDate", dto.getEndTime());
        try {
            String sign = SHA1Utils.sign(appSecret, maps);
            Event96333ApiParamDTO paramDTO = new Event96333ApiParamDTO();
            paramDTO.setAppKey(appKey);
            paramDTO.setSign(sign);
            paramDTO.setStartDate(dto.getStartTime());
            paramDTO.setEndDate(dto.getEndTime());
            String url = event96333Website + "/api/knowledge/issued";//知识库记录消息
            String param = new Gson().toJson(paramDTO);
            LOGGER.info("param:{}", param);
            String post = HttpClientUtils.post(url, null, param);
            return post;
        } catch (DigestException e) {
            e.printStackTrace();
        }
        return "请求异常";
    }
}
