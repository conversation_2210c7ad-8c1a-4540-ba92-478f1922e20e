package com.bt.itsinner.service;

import com.bt.itscore.utils.GsonUtils;
import com.bt.itsinner.domain.dto.WdDTO;
import com.bt.itsinner.mapper.WdMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import rk.netDevice.sdk.p2.*;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;

@Service("WdService")
public class WdService {

    @Autowired
    private WdMapper wdMapper;
    private static final Logger logger = LoggerFactory.getLogger(WdService.class);

    public void getTcpData(String[] args) throws IOException, InterruptedException {
        RSServer server = RSServer.Initiate(12004);//初始化
        server.addDataListener(new IDataListener() {//添加监听

            @Override
            public void receiveRealtimeData(RealTimeData data) {//实时数据接收处理
                logger.info("【温湿度检测仪】监听到数据：" + GsonUtils.beanToJson(data));
                // 遍历节点数据。数据包括网络设备的数据以及各个节点数据。温湿度数据存放在节点数据中
                for (NodeData nd : data.getNodeList()) {
                    logger.info("【温湿度检测仪】监听到数据: 设备:" + data.getDeviceId()
                            + "\t节点:" + nd.getNodeId() + "\t温度:" + nd.getTem()
                            + "\t湿度:" + nd.getHum() + "\t经度:" + data.getLng()
                            + "\t纬度:" + data.getLat() + "\t坐标类型:"
                            + data.getCoordinateType() + "\t继电器状态:"
                            + data.getRelayStatus());
                    WdDTO dto = new WdDTO();
                    dto.setDevKey(String.valueOf(data.getDeviceId()));
                    dto.setTempValue(String.valueOf(nd.getTem()));
                    dto.setHumiValue(String.valueOf(nd.getHum()));
                    // 本地时区
                    ZoneId zoneId = ZoneId.systemDefault();
                    LocalDateTime now = LocalDateTime.now();
                    ZoneOffset nowOffset = zoneId.getRules().getOffset(now);
                    Instant nowInstant = now.toInstant(nowOffset);
                    long timeStamp = nowInstant.getEpochSecond();
                    dto.setTimeStamp(timeStamp);
                    dto.setUploadStatus(0);
                    logger.info("【温湿度检测仪】内网入参：" + GsonUtils.beanToJson(dto));
                    //写入fail_wd_today，upload_status=0
                    try {
                        wdMapper.add(dto);
                        logger.info("【温湿度检测仪】监听数据入库成功！");
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                }
            }

            @Override
            public void receiveLoginData(LoginData loginData) {

            }

            @Override
            public void receiveStoreData(StoreData storeData) {

            }

            @Override
            public void receiveTelecontrolAck(TelecontrolAck telecontrolAck) {

            }

            @Override
            public void receiveTimmingAck(TimmingAck timmingAck) {

            }

            @Override
            public void receiveParamIds(ParamIdsData paramIdsData) {

            }

            @Override
            public void receiveParam(ParamData paramData) {

            }

            @Override
            public void receiveWriteParamAck(WriteParamAck writeParamAck) {

            }

            @Override
            public void receiveTransDataAck(TransDataAck transDataAck) {

            }

            @Override
            public void receiveHeartbeatData(HeartbeatData heartbeatData) {

            }
        });
        server.start();
    }

    public void wdAdd(WdDTO dto) {
        wdMapper.add(dto);
    }

}
