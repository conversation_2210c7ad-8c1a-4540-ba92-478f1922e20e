package com.bt.itsoutfield.service;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.bt.itsoutfield.domain.dto.CmsOperationRecord;
import java.util.List;
import com.bt.itsoutfield.mapper.CmsOperationRecordMapper;
@Service("cmsOperationRecordService")
public class CmsOperationRecordService{

    @Resource
    private CmsOperationRecordMapper cmsOperationRecordMapper;

    public int insert(CmsOperationRecord record) {
        return cmsOperationRecordMapper.insert(record);
    }
    
    public int batchInsert(List<CmsOperationRecord> list) {
        return cmsOperationRecordMapper.batchInsert(list);
    }

    //操作情报板节目删除，日志留底

    /**
     *
     * @param cmsTreeNodeId 节目单ID
     * @param userId 用户ID
     * @return
     */
    public boolean logOperationPage(String cmsTreeNodeId,String userId)
    {
        CmsOperationRecord record = new CmsOperationRecord();
        record.setCmsId(cmsTreeNodeId);
        record.setUserId(userId);
        return false;
    }

}
