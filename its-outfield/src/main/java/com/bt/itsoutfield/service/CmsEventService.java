package com.bt.itsoutfield.service;

import com.alibaba.fastjson.JSON;
import com.bt.itscore.domain.dto.CmsPageNodeDTO;
import com.bt.itscore.domain.dto.CmsRepertoryDTO;
import com.bt.itscore.domain.dto.EventProgressMessageDTO;
import com.bt.itscore.domain.dto.IdIntegerDTO;
import com.bt.itsoutfield.domain.dto.DeviceCmsDTO;
import com.bt.itsoutfield.domain.dto.EventAutoSmsTemplateDTO;
import com.bt.itsoutfield.domain.entity.*;
import com.bt.itsoutfield.domain.vo.*;
import com.bt.itsoutfield.feign.ItsDeviceFeignClient;
import com.bt.itsoutfield.feign.WebSocketFeignClient;
import com.bt.itsoutfield.feign.vo.TreeVo;
import com.bt.itsoutfield.mapper.*;
import com.bt.itsoutfield.utils.CmsHistoryStatusEnum;
import com.google.gson.Gson;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service("cmsEventService")
public class CmsEventService {
    private final static Logger LOGGER = LoggerFactory.getLogger(CmsEventService.class);
    @Autowired
    private ItsDeviceFeignClient itsDeviceFeignClient;
    @Autowired
    private DeviceRoadMapper deviceRoadMapper;
    @Autowired
    private EventAutoSmsTemplateMapper eventAutoSmsTemplateMapper;
    @Autowired
    private CmsHistoryMapper cmsHistoryMapper;
    @Autowired
    private ProgressMapper progressMapper;

    @Autowired
    private CmsHistoryDetailMapper cmsHistoryDetailMapper;

    @Autowired
    private WebSocketFeignClient itsWebSocketFeignClient;

    @Autowired
    private EventMapper eventMapper;


    //快速信息模板查询
    public List<EventAutoSmsTemplateVO> cmsTemplateQuery(Integer eventType) {
        EventAutoSmsTemplateDTO query = new EventAutoSmsTemplateDTO();
        query.setAlarmType(7);
        query.setType(eventType);
        List<EventAutoSmsTemplateVO> list = eventAutoSmsTemplateMapper.selectList(query);
        return list;
    }

    //快速信息模板增加
    public boolean cmsTemplateAdd(EventAutoSmsTemplateDTO dto) {
        boolean flag = false;
        if (dto != null) {
            flag = eventAutoSmsTemplateMapper.add(dto) > 0;
        }
        return flag;
    }

    //快速信息模板删除
    public boolean cmsTemplateDelete(IdIntegerDTO dto) {
        boolean flag = false;
        if (dto != null && dto.getId() != null) {
            flag = eventAutoSmsTemplateMapper.delete(dto) > 0;
        }
        return flag;
    }

    public List<String> generateWHList(List<DeviceCmsVO> list) {
        List<String> result = new ArrayList<>();
        HashSet<String> set = new HashSet<>();
        for (DeviceCmsVO c : list) {
            String wh = c.getWidth() + "x" + c.getHeight();
            set.add(wh);
        }
        for (String s : set) {
            result.add(s);
        }
        return result;
    }

    //根据信息和种类快速分屏
    public Map<String, CmsPageNodeDTO> cmsTemplate(String message, List<String> whList, String accountor) {
        Map<String, CmsPageNodeDTO> map = new TreeMap<>();
        //字体类型默认宋体
        String fontType = "SimSun";
        //字色默认红色
        String fontColor = "#FF0000";
        //背景默认黑色
        String bgColor = "#000000";
        //其余参数采取默认值
        int holdTime = 5;
        int speed = 1;
        String inputMode = "0";
        String outputMode = "0";
        CmsRepertoryDTO repertory = new CmsRepertoryDTO();
        repertory.setFontColor(fontColor);
        repertory.setFontType(fontType);
        repertory.setBackColor(bgColor);
        repertory.setHoldTime(holdTime);
        repertory.setSpeed(speed);
        repertory.setInputMode(inputMode);
        repertory.setOutputmode(outputMode);
        repertory.setMessagebody(message);
        CmsPageNodeDTO node = new CmsPageNodeDTO();
        List<CmsRepertoryDTO> children = new ArrayList<>();
        children.add(repertory);
        node.setChildren(children);
        node.setAccountor(accountor);
        String json = new Gson().toJson(node, CmsPageNodeDTO.class);
        for (String wh : whList) {
            String[] ss = wh.split("x");
            if (ss.length == 2) {
                int width = Integer.parseInt(ss[0]);
                int height = Integer.parseInt(ss[1]);
                //计算最佳字号
//                int fontSize = autoCountFontSize(wh, message);
                int fontSize = autoCountFontSizeNew(width,height,message);
                CmsPageNodeDTO nodeCopy = new Gson().fromJson(json, CmsPageNodeDTO.class);
                nodeCopy.setWidth(width);
                nodeCopy.setHeight(height);
                nodeCopy.getChildren().get(0).setFontSize(fontSize);
                nodeCopy = CmsPageService.autoSplitScreen(nodeCopy, true);
                //计算是否溢出
                String[] word = nodeCopy.getChildren().get(0).getMessageBody().split("<br>");
                int rowNum = word.length;
                if (fontSize * rowNum > height) {
                    nodeCopy.getChildren().get(0).setLabelX(0);
                    nodeCopy.getChildren().get(0).setLabelY(2);
                    nodeCopy.getChildren().get(0).setLayout("to_left");
                }
                map.put(wh, nodeCopy);
            }
        }
        return map;
    }

    //确定字号
    public static int autoCountFontSizeNew(int width, int height, String message) {
        int fontSize = 24;
        if (message == null || message.length() == 0) {
            return fontSize;
        }
        //去除所有换行符
        message = message.replace("\\r\\n", "");
        message = message.replace("</br>", "");
        message = message.replace("\\n", "");
        message = message.replace("\n", "");
        message = message.replace("<br>", "");
        //去除连续的分屏符和换行符
        message = message.replaceAll("(<page>){2,}", "<page>");
        message = message.replaceAll("(<br>){2,}", "");
        //除去头和尾的分屏符号和换行符号
        while (message.startsWith("<page>") || message.startsWith("<br>") || message.endsWith("<page>") || message.endsWith("<br>")) {
            message = message.replaceAll("^(<page>|<br>)", "");
            message = message.replaceAll("(<page>|<br>)$", "");
        }
        //计算内容字数
        int num = message.length();
        //从最大号开始遍历，先看看一屏是否排的下，如果可以，返回该字号
        List<Integer> fontSizeList = Arrays.asList(96,80,64,48,32,24,16,12);
        for (Integer size : fontSizeList) {
            fontSize = size;
            //一行、一列最多几个字
            int column = width/size;
            int row = height/size;
            int total = column * row;
            if (total >= num){
                break;
            }
        }
        return fontSize;
    }


    //根据屏幕尺寸自动调节字号
    public static int autoCountFontSize(String wh, String message) {
        int fontSize = 24;
        if (message == null || message.length() == 0) {
            return fontSize;
        }
        switch (wh) {
            case "96x48": {
                if (message.length() > 8) {
                    fontSize = 16;
                }
                break;
            }
            case "192x96": {
                if (message.length() > 32) {
                    fontSize = 16;
                }
                break;
            }
            case "320x32": {
                if (message.length() > 13) {
                    fontSize = 16;
                }
                break;
            }
            case "352x32": {
                fontSize = 32;
                if (message.length() > 11) {
                    fontSize = 16;
                }
                break;
            }
            case "480x384": {
                fontSize = 64;
                if (message.length() > 42) {
                    fontSize = 48;
                }
                break;
            }
            case "128x64": {
                fontSize = 24;
                if (message.length() > 10) {
                    fontSize = 16;
                }
                break;
            }
            case "448x336": {
                fontSize = 64;
                if (message.length() > 35) {
                    fontSize = 48;
                }
                break;
            }
            case "640x128": {
                fontSize = 64;
                if (message.length() > 20) {
                    fontSize = 48;
                }
                break;
            }
            case "768x128": {
                fontSize = 64;
                if (message.length() > 24) {
                    fontSize = 48;
                }
                break;
            }
            case "896x128": {
                fontSize = 64;
                if (message.length() > 28) {
                    fontSize = 48;
                }
                break;
            }
            case "1024x128": {
                fontSize = 64;
                if (message.length() > 32) {
                    fontSize = 48;
                }
                break;
            }
            case "224x160": {
                fontSize = 32;
                if (message.length() > 35) {
                    fontSize = 24;
                }
                break;
            }
            case "320x64": {
                fontSize = 32;
                if (message.length() > 20) {
                    fontSize = 24;
                }
                break;
            }
        }
        return fontSize;
    }

    public List<EventCmsVO> eventSelectCmsByLat(DeviceCmsDTO cmsDTO) {
        List<EventCmsVO> list = new ArrayList<>();
        List<Integer> facilityTypeNos = new ArrayList<>();
        String facilityTypeNo = cmsDTO.getFacilityTypeNo();
        if (facilityTypeNo != null && facilityTypeNo.trim().length() > 0) {
            String[] arr = facilityTypeNo.split(",");
            for (String s : arr) {
                facilityTypeNos.add(NumberUtils.toInt(s));
            }
        }
        cmsDTO.setFacilityTypeNos(facilityTypeNos);
        List<DeviceCmsVO> deviceCmsVOS = deviceRoadMapper.eventSelectCmsByLat(cmsDTO);
        //按照路段分组
        if (deviceCmsVOS.size()>0) {
            Map<String,List<DeviceCmsVO>> map = deviceCmsVOS.stream().collect(Collectors.groupingBy(DeviceCmsVO::getRoadNo));
            map.forEach((k,v)->{
                EventCmsVO vo = new EventCmsVO();
                vo.setRoadNo(k);
                vo.setDeviceCmsVOS(v);
                vo.setRoadName(deviceRoadMapper.selectRoadName(k));
                list.add(vo);
            });
        }
        //与事件路段同路段的，根据方向上下游（同向上游、同向下游、对向上游、对向下游）匹配
        //1.确定搜索方向
        String directionNoForSearch = null;
        String directionNoForOrigin = cmsDTO.getDirectionNo() == null ? null : cmsDTO.getDirectionNo() + "";
        if (cmsDTO.getSearchDirection().contains("1") || cmsDTO.getSearchDirection().contains("2")) {
            //双向
            if (cmsDTO.getSearchDirection().contains("3") || cmsDTO.getSearchDirection().contains("4")) {
                directionNoForSearch = null;
            } else {
                //正向
                directionNoForSearch = cmsDTO.getDirectionNo();
            }
        } else if (cmsDTO.getSearchDirection().contains("3") || cmsDTO.getSearchDirection().contains("4")) {
            //反向
            directionNoForSearch = deviceRoadMapper.selectOppositeDirection(cmsDTO);
        }
        final String directionSearch = directionNoForSearch;
        list.forEach(item->{
            if (item.getRoadNo().equals(cmsDTO.getRoadNo())) {
                Iterator<DeviceCmsVO> iterator = item.getDeviceCmsVOS().iterator();
                while (iterator.hasNext()) {
                    DeviceCmsVO vo = iterator.next();
                    if (!StringUtils.isEmpty(directionSearch) && !directionSearch.equals(vo.getDirectionNo())) {
                        iterator.remove();
                    }
                }
            }
        });
        cmsDTO.setDirectionNo(directionNoForOrigin);
        //2.筛选搜索方向
        List<DeviceCmsVO> result = new ArrayList<>();
        list.forEach(item->{
            List<DeviceCmsVO> voList = item.getDeviceCmsVOS();
            if (item.getRoadNo().equals(cmsDTO.getRoadNo())) {
                //同路段才做处理
                Direction direction = new Direction();
                direction.setDirectionNo(cmsDTO.getDirectionNo());
                direction = deviceRoadMapper.getDirectionInfo(direction);
                for (DeviceCmsVO c : voList) {
                    //跳过无桩号的情报板
                    if (c.getMilePost() == null || c.getMilePost().length() == 0) {
                        continue;
                    }
                    if (cmsDTO.getWidth() != null && cmsDTO.getHeight() != null) {
                        if (c.getWidth() < cmsDTO.getWidth())
                            continue;
                    }
                    if (cmsDTO.getHeight() != null) {
                        if (c.getHeight() < cmsDTO.getHeight())
                            continue;
                    }
                    if (cmsDTO.getMilePost() != null) {
                        int originMp = getMilePostVaule(cmsDTO.getMpValue(), cmsDTO.getMilePost());
                        int cmsMp = getMilePostVaule(c.getMpValue(), c.getMilePost());
                        //桩号范围1--半径确定
                        if (cmsDTO.getRadius() != null && Math.abs(originMp - cmsMp) > cmsDTO.getRadius() * 1000) {
                            continue;
                        }
                        //桩号范围2--范围确定
                        if (cmsDTO.getMilePostRange() != null) {
                            String[] split = cmsDTO.getMilePostRange().split("-");
                            if (split.length == 2) {
                                int minMp = getMilePostVaule(null, split[0]);
                                int maxMp = getMilePostVaule(null, split[1]);
                                if (cmsMp < minMp || cmsMp > maxMp) {
                                    continue;
                                }
                            }
                        }
                        //如果不包含同向上游
                        if (!cmsDTO.getSearchDirection().contains("1")) {
                            if ((cmsDTO.getDirectionNo().equals(c.getDirectionNo()) && direction.getRoadLine() == 0 && cmsMp <= originMp)//事件在上行，情报板在上行小桩号
                                    || (cmsDTO.getDirectionNo().equals(c.getDirectionNo()) && direction.getRoadLine() == 1 && cmsMp > originMp))//事件在下行，情报板在下行大桩号
                            {
                                continue;
                            }
                        }
                        //如果不包含同向下游
                        if (!cmsDTO.getSearchDirection().contains("2")) {
                            if ((cmsDTO.getDirectionNo().equals(c.getDirectionNo()) && direction.getRoadLine() == 0 && cmsMp > originMp)//事件在上行，情报板在上行大桩号
                                    || (cmsDTO.getDirectionNo().equals(c.getDirectionNo()) && direction.getRoadLine() == 1 && cmsMp <= originMp))//事件在下行，情报板在下行小桩号
                            {
                                continue;
                            }
                        }
                        //如果不包含异向上游
                        if (!cmsDTO.getSearchDirection().contains("3")) {
                            if ((!cmsDTO.getDirectionNo().equals(c.getDirectionNo()) && direction.getRoadLine() == 0 && cmsMp <= originMp)//事件在上行，情报板在下行小桩号
                                    || (!cmsDTO.getDirectionNo().equals(c.getDirectionNo()) && direction.getRoadLine() == 1 && cmsMp > originMp))//事件在下行，情报板在上行大桩号
                            {
                                continue;
                            }
                        }
                        //如果不包含异向下游
                        if (!cmsDTO.getSearchDirection().contains("4")) {
                            if ((!cmsDTO.getDirectionNo().equals(c.getDirectionNo()) && direction.getRoadLine() == 0 && cmsMp > originMp)//事件在上行，情报板在下行大桩号
                                    || (!cmsDTO.getDirectionNo().equals(c.getDirectionNo()) && direction.getRoadLine() == 1 && cmsMp <= originMp))//事件在下行，情报板在上行小桩号
                            {
                                continue;
                            }
                        }
                    }
                    result.add(c);
                }
                item.setDeviceCmsVOS(result);
            }
        });
        return list;
    }

    //事件交通诱导选择情报板
    //服务区-7 收费站-8 隧道-2 1-中心 3-互通立交 4-大桥 11-路面
    //必传：路段、桩号、方向
    public List<DeviceCmsVO> eventSelectCms(DeviceCmsDTO cms) {
        List<DeviceCmsVO> list = new ArrayList<>();
        //先确定设施列表
        String types = cms.getFacilityTypeNo();
        List<TreeVo> trees = itsDeviceFeignClient.selectList(Integer.parseInt(cms.getRoadNo()), cms.getFacilityTypeNo());
        List<String> facilities = new ArrayList<>();
        for (TreeVo t : trees) {
            for (TreeVo c : t.getChildren()) {
                facilities.add(c.getId());
            }
        }
        //再确定搜索方向
        String directionNoForSearch = null;
        String directionNoForOrigin = cms.getDirectionNo() == null ? null : cms.getDirectionNo() + "";
        if (cms.getSearchDirection().contains("1") || cms.getSearchDirection().contains("2")) {
            //双向
            if (cms.getSearchDirection().contains("3") || cms.getSearchDirection().contains("4")) {
                directionNoForSearch = null;
            }
            //正向
            else {
                directionNoForSearch = cms.getDirectionNo();
            }
        } else if (cms.getSearchDirection().contains("3") || cms.getSearchDirection().contains("4")) {
            //反向
            directionNoForSearch = deviceRoadMapper.selectOppositeDirection(cms);
        }
        cms.setFacilities(facilities);
        cms.setDirectionNo(directionNoForSearch);
        list = deviceRoadMapper.selectCmsList(cms);
        List<DeviceCmsVO> result = new ArrayList<>();
        cms.setDirectionNo(directionNoForOrigin);
        //有情报板则继续搜索
        if (list.size() > 0) {
            Direction direction = new Direction();
            direction.setDirectionNo(cms.getDirectionNo());
            direction = deviceRoadMapper.getDirectionInfo(direction);
            for (DeviceCmsVO c : list) {
                //跳过无桩号的情报板
                if (c.getMilePost() == null || c.getMilePost().length() == 0) {
                    continue;
                }
                if (cms.getWidth() != null && cms.getHeight() != null) {
                    if (c.getWidth() < cms.getWidth())
                        continue;
                }
                if (cms.getHeight() != null) {
                    if (c.getHeight() < cms.getHeight())
                        continue;
                }
                if (cms.getMilePost() != null) {
                    int originMp = getMilePostVaule(cms.getMpValue(), cms.getMilePost());
                    int cmsMp = getMilePostVaule(c.getMpValue(), c.getMilePost());
                    //桩号范围1--半径确定
                    if (cms.getRadius() != null && Math.abs(originMp - cmsMp) > cms.getRadius() * 1000) {
                        continue;
                    }
                    //桩号范围2--范围确定
                    if (cms.getMilePostRange() != null) {
                        String[] split = cms.getMilePostRange().split("-");
                        if (split.length == 2) {
                            int minMp = getMilePostVaule(null, split[0]);
                            int maxMp = getMilePostVaule(null, split[1]);
                            if (cmsMp < minMp || cmsMp > maxMp) {
                                continue;
                            }
                        }
                    }
                    //如果不包含同向上游
                    if (!cms.getSearchDirection().contains("1")) {
                        if ((cms.getDirectionNo().equals(c.getDirectionNo()) && direction.getRoadLine() == 0 && cmsMp <= originMp)//事件在上行，情报板在上行小桩号
                                || (cms.getDirectionNo().equals(c.getDirectionNo()) && direction.getRoadLine() == 1 && cmsMp > originMp))//事件在下行，情报板在下行大桩号
                        {
                            continue;
                        }
                    }
                    //如果不包含同向下游
                    if (!cms.getSearchDirection().contains("2")) {
                        if ((cms.getDirectionNo().equals(c.getDirectionNo()) && direction.getRoadLine() == 0 && cmsMp > originMp)//事件在上行，情报板在上行大桩号
                                || (cms.getDirectionNo().equals(c.getDirectionNo()) && direction.getRoadLine() == 1 && cmsMp <= originMp))//事件在下行，情报板在下行小桩号
                        {
                            continue;
                        }
                    }
                    //如果不包含异向上游
                    if (!cms.getSearchDirection().contains("3")) {
                        if ((!cms.getDirectionNo().equals(c.getDirectionNo()) && direction.getRoadLine() == 0 && cmsMp <= originMp)//事件在上行，情报板在下行小桩号
                                || (!cms.getDirectionNo().equals(c.getDirectionNo()) && direction.getRoadLine() == 1 && cmsMp > originMp))//事件在下行，情报板在上行大桩号
                        {
                            continue;
                        }
                    }
                    //如果不包含异向下游
                    if (!cms.getSearchDirection().contains("4")) {
                        if ((!cms.getDirectionNo().equals(c.getDirectionNo()) && direction.getRoadLine() == 0 && cmsMp > originMp)//事件在上行，情报板在下行大桩号
                                || (!cms.getDirectionNo().equals(c.getDirectionNo()) && direction.getRoadLine() == 1 && cmsMp <= originMp))//事件在下行，情报板在上行小桩号
                        {
                            continue;
                        }
                    }
                }
                result.add(c);
            }
        }
        return result;
    }

    //根据桩号计算mp值
    public static int getMilePostVaule(Integer mpValue, String milePost) {
        int mp = 0;
        if (mpValue != null && mpValue > 0) {
            mp = mpValue;
        } else if (milePost != null) {
            String mile = milePost.replaceAll("[AKLR]", "");
            mile = mile.replace("+", "");
            mp = Integer.parseInt(mile);
        }
        return mp;
    }

    public EventRecord loadEventRecord(EventRecord record) {
        //首先查询cms_history
        List<CmsHistory> cmsHistoryList = cmsHistoryMapper.getCmsHistoryByEventId(record.getEventId());
        //其次查询诱导内容
        CmsHistory content = cmsHistoryMapper.getMessageByEventId(record.getEventId());
        //如果查询不到事件直接返回空
        if (content == null) {
            EventRecord temp = new EventRecord();
            temp.setIsEnd(0);
            temp.setIsRollback(0);
            return temp;
        }
        //还需要更新一下事件表，拿到返回的status
        EventRecord sts = updateEventPublishStatusMain(record.getEventId(),cmsHistoryList,content);
        EventRecord result = new EventRecord();
        result.setEndTime(content.getRollBackTime());
        result.setPublishTime(content.getExecuteTime());
        result.setPublishType(content.getPublishType());
        result.setNeedRoll(content.getIsRollback());
        result.setPlanType(content.getPlanType());
        result.setIsEnd(content.getIsEnd()==null?0: content.getIsEnd());
        //根据deviceId查询详细内容
        DeviceCmsDTO dto = new DeviceCmsDTO();
        List<String> idList = new ArrayList<>();
        HashMap<String, Integer> map = new HashMap<>();
        //整合所有分辨率的节目单
        Map<String, CmsPageNodeDTO> contentMap = new TreeMap<>();
        Map<String, CmsHistory> historyMap = new HashMap<>();
        for (CmsHistory h : cmsHistoryList) {
            idList.add(h.getCmsId());
            map.put(h.getCmsId(), h.getStatus());
            //按照ID存map
            historyMap.put(h.getCmsId(), h);
        }

        dto.setIdList(idList);
        List<DeviceCmsVO> cmsList = new ArrayList<>();
        if (idList != null && idList.size() > 0) {
            cmsList = deviceRoadMapper.selectCmsList(dto);
        }
        for (DeviceCmsVO vo : cmsList) {
            vo.setPublishStatus(map.get(vo.getDeviceId()) == null ? 0 : map.get(vo.getDeviceId()));
            vo.setReason(historyMap.get(vo.getDeviceId()).getReason());
            if (vo.getWidth() != null && vo.getHeight() != null) {
                String wh = vo.getWidth() + "x" + vo.getHeight();
                if (contentMap.get(wh) == null) {
                    String json = historyMap.get(vo.getDeviceId()).getJson();
                    if (json != null) {
                        CmsPageNodeDTO node = new Gson().fromJson(json, CmsPageNodeDTO.class);
                        node.setWidth(vo.getWidth());
                        node.setHeight(vo.getHeight());
                        contentMap.put(wh, node);
                    }
                }
            }
        }
        result.setMessage(content.getMessage());
        result.setCmsList(cmsList);
        result.setEventId(record.getEventId());
        //仅当事件结束时关闭
        Integer eventStatus = queryEventEnd(record.getEventId());
        if (eventStatus != null && eventStatus == 8) {
            closeEvent(record);
            result.setIsEnd(1);
            //sts = 2;
        }
        result.setStatus(sts.getStatus());
        result.setIsRollback(sts.getIsRollback());
        result.setContentMap(contentMap);
        return result;
    }

    //事件时写cmsHistory
    public synchronized boolean writeCmsToCmsHistory(String deviceId, String pageId, CmsPageNodeDTO node, String time, CmsIssueFlagVO cmsIssueFlagVO) {
        if (node.getEventId() == null)
            return false;
        //首先查询是否存在事件ID，存在则为更新，不存在则为新增
        CmsHistory query = new CmsHistory();
        query.setEventId(node.getEventId());
        query.setCmsId(deviceId);
        int count = cmsHistoryMapper.countByEventIdAndCmsId(query);

        CmsHistory history = new CmsHistory();
        history.setCmsId(deviceId);
        history.setExecuteTime(time);
        //为撤回时不写以下几项.如果是撤回，那一定是更新
        if (!node.isSelected()) {
            history.setEventId(node.getEventId());
            history.setHisId(pageId);
            history.setRollBackTime(node.getRollTime());
            history.setIsRollback(node.getNeedRoll());
            history.setMessage(node.getText());
            history.setPublishType(node.getAppended());
            //如果是追加，不再写入node
            if (node.getAppended() == null || node.getAppended() != 1) {
                history.setJson(new Gson().toJson(node, CmsPageNodeDTO.class));
            }
            //每次入库时将要发布的情报板latest置为最新
            history.setLatest(1);
        }
        Integer status = null;
        if (cmsIssueFlagVO != null) {
            //属于撤回
            if (node.isSelected()) {
                status = cmsIssueFlagVO.getFlag() ? 2 : 3;
                history.setReason(cmsIssueFlagVO.getFlag() ?"":cmsIssueFlagVO.getReason());
            } else {
                status = cmsIssueFlagVO.getFlag() ? 1 : 0;
                history.setReason(cmsIssueFlagVO.getFlag() ?"":cmsIssueFlagVO.getReason());
            }
        } else {
            //cmsIssueFlagVO为空时撤回失败，此时原因为无节目单(此处应该是无法进入的分歧)
            if (node.isSelected()) {
                //撤回失败的情况
                status = 3;
                history.setReason("(没有可恢复的常态节目单)");
            } else {
                //发布失败的情况
                status = 0;
                history.setReason("(内网服务无响应)");
            }
        }
        history.setStatus(status);
        history.setPublishType(node.getAppended());
        history.setMessage(node.getText());
        history.setEventId(node.getEventId());

        //虚拟节目单不入库
        // 将此过程详细数据更新到cms_story_detail并刷新进展结果
        updateCmsHistoryDetailAndProgress(deviceId, node, history, status);
        boolean flag = false;
        if (count > 0) {
            //return cmsHistoryMapper.update(history) > 0;
            flag = updateStatusSpecial(history);
            LOGGER.info("[交通诱导]设备ID:"+deviceId+"|入库更新结果:"+flag);
        } else {
            flag = cmsHistoryMapper.add(history) > 0;
            LOGGER.info("[交通诱导]设备ID:"+deviceId+"|入库更新结果:"+flag);
        }
        return flag;
    }

    private void refreshProgressData(CmsHistory oldHistory) {
        LOGGER.info("--------start update progress data,the event id: {}",oldHistory.getEventId());
        Integer eventProgressId = oldHistory.getEventProgressId();
        String operator = oldHistory.getOperator();
        List<CmsHistory> cmsHistories = cmsHistoryDetailMapper.queryCmsHistoryDetailByProgressId(eventProgressId);
        if (CollectionUtils.isEmpty(cmsHistories)) {
            return;
        }
        int size = cmsHistories.size();
        AtomicInteger isNeedRecord = new AtomicInteger();
        cmsHistories.forEach(item -> {
            Integer itemStatus = item.getStatus();
            if (itemStatus != null) {
                isNeedRecord.getAndIncrement();
            }
        });
        // 说明已经更新完成，解析拼接数据入库
        if (size == isNeedRecord.get()) {
            StringBuilder sb = new StringBuilder();
            StringBuilder publishSuccessBuf = new StringBuilder();
            StringBuilder cancelSuccessBuf = new StringBuilder();
            StringBuilder failedBuf = new StringBuilder();

            for (CmsHistory cmsHistory : cmsHistories) {
                Integer historyStatus = cmsHistory.getStatus();
                if (historyStatus == null) {
                    continue;
                }
                // 发布成功
                if (historyStatus == 1) {
                    publishSuccessBuf.append(cmsHistory.getCmsName()).append("、");
                }
                // 回撤成功
                if (historyStatus == 2) {
                    cancelSuccessBuf.append(cmsHistory.getCmsName()).append("、");
                }
                // 回撤失败
                if (historyStatus == 3) {
                    failedBuf.append(cmsHistory.getCmsName()).append("、");
                }
            }
            handleDesc(sb, publishSuccessBuf, "执行设备【");
            handleDesc(sb, cancelSuccessBuf, "执行设备【");
            handleDesc(sb, failedBuf, "执行失败【");
            EventProgress eventProgress = progressMapper.queryById(eventProgressId);
            if (!StringUtils.isEmpty(sb.toString())) {
                if (publishSuccessBuf.length() > 0 || cancelSuccessBuf.length() > 0) {
                    eventProgress.setIsUse(1);
                    LOGGER.info("--------The event publish or auto cancel success progress desc: {}", sb.toString());
                }
                if (!StringUtils.isEmpty(operator) && failedBuf.length() > 0) {
                    eventProgress.setIsUse(1);
                    LOGGER.info("--------The event manual cancel progress desc: {}", sb.toString());
                }
                if (sb.toString().contains("执行失败【")) {
                    sb.append("，请再次手动尝试撤销");
                }
                Integer isUse = eventProgress.getIsUse();
                if (isUse == 1) {
                    eventProgress.setProgressDesc(eventProgress.getProgressDesc() + sb.toString());
                    int update = progressMapper.update(eventProgress);
                    LOGGER.info("Update the progress desc content: {},size: {}", new Gson().toJson(eventProgress), update);
                    // 异步推送websocket消息
                    pushMessageToWebsocket(update, eventProgress);
                } else {
                    // 移除进展过程数据
                    int deleteProgress = progressMapper.deleteById(eventProgress);
                    int deleteCmsHistory = cmsHistoryDetailMapper.deleteByEventProgressId(eventProgress.getId());
                    LOGGER.info("The progress id: {},delete the event progress size: {},delete the cms history size: {}", eventProgress.getId(), deleteProgress, deleteCmsHistory);
                }
            }
        }
    }

    private void pushMessageToWebsocket(int update, EventProgress progress) {
        String eventId = progress.getEventId();
        Integer id = progress.getId();
        if (update <= 0 || StringUtils.isEmpty(eventId)) {
            LOGGER.error("The cms message of progress data is empty,update size is: {},The event id: {}", update, eventId);
            return;
        }
        // 基于事件id查询关联emer_user(去重后的userId)
        List<String> userList = progressMapper.queryEventEmerUserByEventId(eventId);
        if (CollectionUtils.isEmpty(userList)) {
            LOGGER.error("The event user of progress data is empty,The event id: {}", eventId);
            return;
        }
        Set<String> userSet = new HashSet<>(userList);
        // 查询事件本身关联的填报人和创建人
        Event event = eventMapper.queryUserByEventId(eventId);
        if (event != null) {
            if (!StringUtils.isEmpty(event.getRecordManId())) {
                userSet.add(event.getRecordManId());
            }
            if (!StringUtils.isEmpty(event.getCreateUserId())) {
                userSet.add(event.getCreateUserId());
            }
        }
        userSet.add(progress.getCreateUserId());
        ArrayList<String> distinctList = new ArrayList<>(userSet);
        new Thread(new Runnable() {
            @Override
            public void run() {
                EventProgressMessageDTO eventProgressMessageDTO = new EventProgressMessageDTO();
                eventProgressMessageDTO.setWebsocketType("addEventProgress");
                eventProgressMessageDTO.setEventId(eventId);
                eventProgressMessageDTO.setId(id);
                eventProgressMessageDTO.setUserIds(distinctList);
                itsWebSocketFeignClient.pushEventProgress(eventProgressMessageDTO);
            }
        }).start();
    }

    public void handleDesc(StringBuilder sb, StringBuilder buf, String message) {
        if (!StringUtils.isEmpty(buf.toString()) && !StringUtils.isEmpty(buf.deleteCharAt(buf.length() - 1))) {
            sb.append(System.lineSeparator()).append(message).append(buf).append("】");
        }
    }

    //情报板发布前预入库
    public synchronized boolean writeCmsToCmsHistoryBefore(String deviceId, CmsPageNodeDTO node) {
        if (node.getEventId() == null)
            return false;
        //首先查询是否存在事件ID，存在则为更新，不存在则为新增
        CmsHistory query = new CmsHistory();
        query.setEventId(node.getEventId());
        query.setCmsId(deviceId);
        int count = cmsHistoryMapper.countByEventIdAndCmsId(query);
        CmsHistory history = new CmsHistory();
        history.setCmsId(deviceId);
        //为撤回时不写以下几项
        if (!node.isSelected()) {
            history.setEventId(node.getEventId());
            history.setRollBackTime(node.getRollTime());
            history.setIsRollback(node.getNeedRoll());
            history.setMessage(node.getText());
            history.setPublishType(node.getAppended());
            history.setJson(new Gson().toJson(node, CmsPageNodeDTO.class));
            //每次预入库时将要预发布的情报板latest置为最新
            history.setLatest(1);
        }

        history.setPublishType(node.getAppended());
        history.setMessage(node.getText());
        history.setEventId(node.getEventId());
        boolean flag =false;
        if (count > 0) {
            flag =  updateStatusSpecial(history);
            LOGGER.info("[交通诱导]设备ID:"+deviceId+"|预入库更新结果:"+flag);
        } else {
            flag =cmsHistoryMapper.add(history)>0;
            LOGGER.info("[交通诱导]设备ID:"+deviceId+"|预入库新增结果:"+flag);
        }
        return flag;
    }


    //加锁
    public synchronized boolean writeMessageToCmsHistory(CmsPageNodeDTO node, String time) {
        if (node.getEventId() == null)
            return false;
        CmsHistory history = new CmsHistory();
        history.setEventId(node.getEventId());
        history.setStatus(node.isSelected() ? 1 : 0);

        //为撤回时不写以下几项
        if (!node.isSelected()) {
            history.setRollBackTime(node.getRollTime());
            history.setIsRollback(node.getNeedRoll());
            history.setMessage("main");
            history.setPublishType(node.getAppended());
            //每次调整消息时都将事件的latest置为最新
            history.setLatest(1);
            history.setPlanType(node.getPlanType());
            //入库的时候isEnd写为0;撤回时必须无法变动此字段
            history.setIsEnd(0);
            //撤回时
            if (node.getWriteDate() != null) {
                history.setExecuteTime(node.getWriteDate());
            } else {
                history.setExecuteTime(time);
            }
        }
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        //首先查询是否存在事件ID，存在则为更新，不存在则为新增
        int count = cmsHistoryMapper.update(history);
        boolean updateFlag = false;
        boolean addFlag = false;
        boolean flag = updateFlag||addFlag;
        if (count <= 0) {
            addFlag = cmsHistoryMapper.add(history) > 0;
        } else {
            updateFlag = count > 0;
        }
        //boolean flag = cmsHistoryMapper.updateOrAdd(history)>0;
        LOGGER.info("[交通诱导]事件"+history.getEventId()+"更新状态:"+history.getStatus()+"|selected:"+node.isSelected()+"|update result:"+updateFlag+"|add result:"+addFlag);
        return flag;
    }

    public Map<String, String> eventBatchReSendGenerateMap(@RequestBody CmsPageNodeDTO node) {
        Map<String, String> map = new HashMap<>();
        //获取idList所指的CmsHistory
        if (node.getEventId() != null && (node.getIdList() == null || node.getIdList().size() == 0)) {
            //重发所有未成功的情报板
            CmsHistory query = new CmsHistory();
            query.setEventId(node.getEventId());
            List<CmsHistory> hisList = cmsHistoryMapper.getNotSendAll(query);
            List<String> deviceIdList = new ArrayList<>();
            for (CmsHistory h : hisList) {
                deviceIdList.add(h.getCmsId());
            }
            if (deviceIdList != null && deviceIdList.size() > 0)
                node.setIdList(deviceIdList);
        }
        if (node.getIdList() != null && node.getIdList().size() > 0) {
            List<CmsHistory> tempList = new ArrayList<>();
            for (String cmsId : node.getIdList()) {
                CmsHistory tmp = new CmsHistory();
                tmp.setEventId(node.getEventId());
                tmp.setCmsId(cmsId);
                tempList.add(tmp);
            }
            List<CmsHistory> cmsHistoryList = cmsHistoryMapper.getCmsItemFromHistory(tempList);
            for (CmsHistory h : cmsHistoryList) {
                map.put(h.getCmsId(), h.getJson());
            }

        }
        return map;
    }

    public List<CmsHistory> getCmsItemFromHistory(List<CmsHistory> list)
    {
        return cmsHistoryMapper.getCmsItemFromHistory(list);
    }

    public boolean closeEvent(EventRecord record) {
        CmsHistory h = new CmsHistory();
        h.setEventId(record.getEventId());
        h.setIsEnd(1);
        //关闭
        return cmsHistoryMapper.update(h) > 0;
    }

    //重发布前的清理工作
    public boolean cleanBeforeResetCmsEventSend(CmsPageNodeDTO node) {
        boolean flag = false;
        //将所有前一次的事件发布情报板最新状态置为0
        if (node.getEventId() != null) {
            CmsHistory h = new CmsHistory();
            h.setEventId(node.getEventId());
            flag = cmsHistoryMapper.cleanBeforeRePublish(h) > 0;
        }

        return flag;
    }

    public List<CmsHistory> getCmsHistoryByEventId(String eventId) {
        return cmsHistoryMapper.getCmsHistoryByEventId(eventId);
    }

    public List<CmsHistory> getNotRollAll(CmsHistory cmsHistory) {
        return cmsHistoryMapper.getNotRollAll(cmsHistory);
    }

    public Integer queryEventEnd(String eventId) {
        return cmsHistoryMapper.queryEventEnd(eventId);
    }

    //针对二次发布，需要根据上一次发布状态对本次发布状态进行修正
    public boolean updateStatusSpecial(CmsHistory now) {
        if (now.getCmsId() == null) {
            //主干更新不用管
            System.out.println();
            return cmsHistoryMapper.update(now) > 0;
        }
        CmsHistory last = cmsHistoryMapper.getOneCmsItemFromHistory(now);
        boolean flag = false;
        if (last != null && last.getStatus()!=null && now.getStatus() != null) {
            if (now.getStatus() == 0) {
                if (last.getStatus() == null) {
                    flag = cmsHistoryMapper.update(now) > 0;
                }
                else if (last.getStatus() == 1 || last.getStatus() == 3 || last.getStatus() == 4) {
                    now.setStatus(4);
                    flag = cmsHistoryMapper.update(now) > 0;
                }
                else
                {
                    flag = cmsHistoryMapper.update(now) > 0;
                }
            } else if (last.getStatus() != null && last.getStatus() == 4 && (now.getStatus() == 2 || now.getStatus() == 3)) {
                if (now.getStatus() == 2) {
                    now.setStatus(0);
                    flag = cmsHistoryMapper.update(now) > 0;
                } else {
                    now.setStatus(4);
                    flag = cmsHistoryMapper.update(now) > 0;
                }
            } else {
                flag = cmsHistoryMapper.update(now) > 0;
            }
        }
        else
        {
            flag = cmsHistoryMapper.update(now)>0;
        }
        return flag;
    }

    @Transactional
    public int insertToEventProgressAndHistoryDetail(String userId, CmsPageNodeDTO node, boolean isPublish) {
        EventProgress progressDTO = new EventProgress();
        progressDTO.setEventId(node.getEventId());
        long currentTime = System.currentTimeMillis() / 1000;
        progressDTO.setOccurTime(currentTime);
        String message = "";
        if (isPublish) {
            List<CmsRepertoryDTO> children = node.getChildren();
            message = getMessage(children);
            progressDTO.setProgressDesc(CmsHistoryStatusEnum.PUBLISH_SUCCESS.getDesc() + System.lineSeparator() + "发布内容【" + message + "】");
        } else {
            // 查询上一次发布成功的消息
            List<CmsRepertoryDTO> dtoList = queryCmsHistoryByEventId(node.getDeviceId(), node.getEventId());
            message = getMessage(dtoList);
            progressDTO.setProgressDesc(CmsHistoryStatusEnum.RECALL_SUCCESS.getDesc() + System.lineSeparator() + "撤销内容【" + message + "】");
        }
        progressDTO.setCreateTime(currentTime);
        String accountor = node.getAccountor();
        if (!"系统自动".equals(accountor)) {
            progressDTO.setCreateUserId(userId);
        }
        if (!StringUtils.isEmpty(accountor) && accountor.contains("-去除权限")) {
            accountor = accountor.substring(0, accountor.indexOf("-去除权限"));
        }
        progressDTO.setTheme(1);
        progressDTO.setCardType(0);
        progressDTO.setCardValid(1);
        progressDTO.setPid(0);
        progressDTO.setIsUse(0);
        // 预插入进展数据
        int add = progressMapper.add(progressDTO);
        // 预插入cms_history_detail
        for (String deviceId : node.getIdList()) {
            CmsHistory history = new CmsHistory();
            history.setEventProgressId(progressDTO.getId());
            history.setEventId(node.getEventId());
            history.setCmsId(deviceId);
            // 区分操作类型（0-手动发布触发操作，1-手动撤销触发操作，2-自动撤销触发操作）
            Integer operateType = null;
            if (isPublish) {
                operateType = 0;
                history.setJson(new Gson().toJson(node, CmsPageNodeDTO.class));
            }
            if (!isPublish) {
                operateType = StringUtils.isEmpty(userId) ? 2 : 1;
            }
            if (!StringUtils.isEmpty(accountor) && accountor.contains("系统自动")) {
                accountor = null;
            }
            history.setOperator(accountor);
            history.setOperateType(operateType);
            history.setIsRollback(node.getNeedRoll());
            history.setHisId(node.getUuid());
            cmsHistoryDetailMapper.add(history);
        }
        return add;
    }

    private static String getMessage(List<CmsRepertoryDTO> children) {
        String message = "";
        if (CollectionUtils.isEmpty(children)) {
            return message;
        }
        if (!CollectionUtils.isEmpty(children)) {
            message = children.get(0).getMessageBody();
        }
        if (message.contains("<br>")) {
            message = message.replace("<br>", "");
        }
        if (message.contains(System.lineSeparator())) {
            message = message.replace(System.lineSeparator(), "");
        }
        return message;
    }


    private void updateCmsHistoryDetailAndProgress(String deviceId, CmsPageNodeDTO node, CmsHistory history, Integer status) {
        LOGGER.info("--------start update cms history detail and progress,the event id: {}",history.getEventId());
        // 提取操作类型
        Integer operateType = null;
        String accountor = node.getAccountor();
        if ((status == 0 || status == 1) && !StringUtils.isEmpty(accountor)) {
            operateType = 0;
        }
        if (!StringUtils.isEmpty(accountor) && accountor.contains("系统自动")) {
            accountor = null;
        }
        if (status == 2 || status == 3) {
            operateType = StringUtils.isEmpty(accountor) ? 2 : 1;
        }
        if (!StringUtils.isEmpty(accountor) && accountor.contains("-去除权限")) {
            accountor = accountor.substring(0, accountor.indexOf("-去除权限"));
        }
        CmsHistory queryHistory = new CmsHistory();
        queryHistory.setHisId(node.getUuid());
        queryHistory.setCmsId(deviceId);
        queryHistory.setEventId(node.getEventId());
        queryHistory.setOperator(accountor);
        queryHistory.setOperateType(operateType);
        List<CmsHistory> cmsHistoryList = cmsHistoryDetailMapper.queryCmsHistoryDetail(queryHistory);
        if (CollectionUtils.isEmpty(cmsHistoryList) || cmsHistoryList.size() > 1) {
            LOGGER.error("The cms history detail data is error,device id: {},event id: {},operator: {},operate type: {},data size: {}",
                    deviceId, node.getEventId(), accountor, operateType, cmsHistoryList.size());
        }
        if (cmsHistoryList.size() == 1) {
            CmsHistory oldHistory = cmsHistoryList.get(0);
            List<DeviceVO> deviceVOList = deviceRoadMapper.selectDeviceById(deviceId);
            if (!CollectionUtils.isEmpty(deviceVOList) && deviceVOList.size() == 1) {
                oldHistory.setCmsName(deviceVOList.get(0).getDeviceName());
            }
            oldHistory.setStatus(status);
            oldHistory.setIsRollback(history.getIsRollback());
            oldHistory.setPublishType(history.getPublishType());
            oldHistory.setIsRollback(history.getIsRollback());
            oldHistory.setExecuteTime(history.getExecuteTime());
            oldHistory.setMessage(history.getMessage());
            oldHistory.setLatest(history.getLatest());
            cmsHistoryDetailMapper.update(oldHistory);
            // 检查当前的进展id是否已经更新完毕，将成功的数据反刷到
            LOGGER.info("--------update cms history detail,the params: {}",new Gson().toJson(oldHistory));
            refreshProgressData(oldHistory);
        }
    }

    public List<CmsRepertoryDTO> queryCmsHistoryByEventId(String deviceId, String eventId) {
        String jsonStr = cmsHistoryDetailMapper.queryByEventIdAndCmsId(deviceId, eventId);
        if (StringUtils.isEmpty(jsonStr)) {
            return null;
        }
        CmsPageNodeDTO cmsHistory = JSON.parseObject(jsonStr, CmsPageNodeDTO.class);
        if (cmsHistory == null) {
            return null;
        }
        return cmsHistory.getChildren();
    }

    public int countByEventIdAndCmsId(CmsHistory query) {
        return cmsHistoryMapper.countByEventIdAndCmsId(query);
    }

    public int updateEventPublishStatus(String eventId)
    {
        List<CmsHistory> list = cmsHistoryMapper.getCmsHistoryByEventId(eventId);
        CmsHistory main = cmsHistoryMapper.getMessageByEventId(eventId);
        return updateEventPublishStatus(eventId,list,main);
    }

    public Integer updateEventPublishStatus(String eventId,List<CmsHistory> list,CmsHistory main)
    {
        return updateEventPublishStatusMain(eventId, list,main).getStatus();
    }

    public EventRecord updateEventPublishStatusMain(String eventId,List<CmsHistory> list,CmsHistory main)
    {
        EventRecord r = new EventRecord();
        r.setEventId(eventId);
        r.setStatus(main.getStatus());
        r.setIsRollback(0);
        r.setIsEnd(0);
        int status = 3;
        int sendRightNum = 0;
        int sendFailNum = 0;
        int rollRightNum = 0;//撤回成功
        int rollFailNum =0;//撤回失败
        int total = list.size();
        if(list.size()==0)
        {
            //未发布状态不改动
            return r;
        }
        for (CmsHistory ch:list) {
            if(ch.getStatus()==null)
            {
                continue;
            }
            //如果是撤回成功，发布成功和撤回成功同时累加
            if(ch.getStatus()==2)
            {
                sendRightNum++;
                rollRightNum++;
            }
            //如果是撤回失败，只加发布成功数、撤回失败数
            else if(ch.getStatus()==3)
            {
                sendRightNum++;
                rollFailNum++;
            }
            //如果是发布成功，只加发布成功数
            else if(ch.getStatus()==1)
            {
                sendRightNum++;
            }
            //如果是重发布失败需撤回，视作发布失败；如果是发布失败，不加
            else
            {
                sendFailNum++;
            }
        }
        if(main.getStatus()==null||main.getStatus()==2)
        {
            //未发布和已结束状态不要做修正
            return r;
        }
        //计算发布数和撤回数
        if(rollRightNum==total&&total!=0)
        {
            //如果撤回成功数=总数，说明全部撤回，状态为1,已下达撤回
            status = 1;
        }
        else if(rollRightNum+rollFailNum>0)
        {
            //如果撤回成功数！=总数，撤回成功数+撤回失败数大于0，说明已撤回，但是有失败，可能全部失败也可能部分失败，状态为1,已下达撤回
            status = 1;
        }
        else if(sendRightNum==0)
        {
            //如果撤回成功数！=总数，而发布数=0，说明全部为发布失败或未发布，状态为3
            status = 3;
        }
        else
        {
            //除此以外的情况，为发布中，状态为0
            status = 0;
        }
        boolean flag = false;
        CmsHistory h = new CmsHistory();
        h.setEventId(eventId);
        h.setStatus(status);
        //对消息状态进行修正并反写
        cmsHistoryMapper.update(h);
        //对事件状态进行反写
        CmsHistory ev = new CmsHistory();
        ev.setEventId(eventId);
        ev.setStatus(status==3?0:1);
        ev.setLatest(status==1?1:0);
        cmsHistoryMapper.updateEventPublish(ev);
        //修正
        r.setStatus(status);
        r.setIsRollback(rollRightNum==total?1:((rollRightNum+sendFailNum)==total?1:0));
        return r;
    }
}
