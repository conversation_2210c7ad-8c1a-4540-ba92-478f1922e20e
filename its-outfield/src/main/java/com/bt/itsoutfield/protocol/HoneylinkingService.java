package com.bt.itsoutfield.protocol;

import com.alibaba.fastjson.JSONObject;
import com.bt.itscore.utils.HttpClientUtils;
import com.bt.itscore.utils.NumbersUtils;
import com.bt.itscore.utils.TimeUtils;
import com.bt.itsoutfield.domain.dto.*;
import com.bt.itsoutfield.domain.vo.DeviceWdVO;
import com.bt.itsoutfield.domain.vo.OutfieldApiUrlVO;
import com.bt.itsoutfield.domain.vo.WdFluxVO;
import com.bt.itsoutfield.mapper.DeviceRoadMapper;
import com.bt.itsoutfield.mapper.OutfieldApiUrlMapper;
import com.bt.itsoutfield.utils.CmsUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("honeylinkingService")
public class HoneylinkingService {
    /***
     * 鸿睿云平台温湿度系统
     * 接口API: http://doc.honeylinking.com/@ln2q4jdrdg/APIbanben.html
     */

    private static String userVerificationUrl = "http://honeylinking.com/api/v1.6/checkUser";//用户检验
    private static String getDeviceInformationUrl = "http://honeylinking.com/api/v1.6/getDeviceInfor";//获取用户信息
    private Map<String, DeviceWdVO> deviceWdMap;

    @Autowired
    private OutfieldApiUrlMapper outfieldApiUrlMapper;

    //通过定时任务定时刷新url
    public void updateURL() {
        OutfieldApiUrlDTO dto = new OutfieldApiUrlDTO();
        dto.setModel("its-outfield");
        dto.setProtocol("honeylinking");
        List<OutfieldApiUrlVO> urlList = outfieldApiUrlMapper.select(dto);
        HashMap<String, String> urlMap = new HashMap<>();
        for (OutfieldApiUrlVO url : urlList) {
            urlMap.put(url.getName(), url.getUrl());
        }
        String temp1 = urlMap.get("USER_VERIFICATION");
        String temp2 = urlMap.get("GET_DEVICE_INFORMATION");
        if (StringUtils.isNotBlank(temp1)) {
            userVerificationUrl = temp1;
        }
        if (StringUtils.isNotBlank(temp2)) {
            getDeviceInformationUrl = temp2;
        }
    }


    //判断设备是否在云控平台
    private boolean isDeviceInYkSystem(String deviceId) {
        return (deviceWdMap != null && deviceWdMap.get(deviceId) != null);
    }

    /**
     * 用户名有效性测试
     *
     * @param username
     * @param password
     * @return
     */
    public boolean loginAuthTest(String username, String password) {
        boolean flag = false;
        APIDTO apidto = new APIDTO();
        apidto.setUsername(username);
        apidto.setPassword(password);
        String resultStr = HttpClientUtils.post(userVerificationUrl, null, new Gson().toJson(apidto));
        APIDTO result = new Gson().fromJson(resultStr, APIDTO.class);
        flag = (StringUtils.isNotBlank(result.getState()) && result.getState().equalsIgnoreCase("success"));
        return flag;
    }

    /**
     * 获取该账号下所有有效设备
     *
     * @param username
     * @param password
     * @return
     */
    public List<HoneylinkingDTO> getAllDeviceInformation(String username, String password) {
        APIDTO apidto = new APIDTO();
        apidto.setUsername(username);
        apidto.setPassword(password);
        String url = getDeviceInformationUrl+"?username="+username+"&password="+password;
        String resultStr = HttpClientUtils.get(url, null);
        List<HoneylinkingDTO> resultList = new Gson().fromJson(resultStr, new TypeToken<ArrayList<HoneylinkingDTO>>() {
        }.getType());
        return resultList;
    }

    public List<WdTodayDTO> getWdInfoFromAPI(Map<String, DeviceWdVO> map) {
        deviceWdMap = map;
        List<WdTodayDTO> wdList = null;
        //有多少种用户名和密码
        HashMap<String, String> userInfoMap = new HashMap<>();
        for (Map.Entry<String, DeviceWdVO> entry : deviceWdMap.entrySet()) {
            DeviceWdVO vo = entry.getValue();
            if (StringUtils.isNotBlank(vo.getUsername()) && StringUtils.isNotBlank(vo.getPassword())) {
                userInfoMap.put(vo.getUsername(), vo.getPassword());
            }
        }
        HashMap<String,HoneylinkingDTO> hlMap = new HashMap<>();
        //根据用户名和密码发送信息
        for (Map.Entry<String, String> entry : userInfoMap.entrySet()) {
            List<HoneylinkingDTO> tempList = getAllDeviceInformation(entry.getKey(), entry.getValue());
            if (tempList != null) {
                for (HoneylinkingDTO hl:tempList) {
                    hlMap.put(hl.getDeviceNumber(),hl);//根据deviceId不重复地存放一次数据
                }
            }
        }

        //转换成统一的数据格式
        if(hlMap.size()>0) {
            wdList = convertToWdData(hlMap);
        }
        return wdList;
    }

    /**
     * 转换为统一的数据方便入库
     *
     * @param map
     * @return
     */
    public List<WdTodayDTO> convertToWdData(HashMap<String,HoneylinkingDTO> map) {
        List<WdTodayDTO> wdList = new ArrayList<>();
        for (Map.Entry<String,HoneylinkingDTO> entry : map.entrySet()) {
            HoneylinkingDTO h = entry.getValue();;
            if (StringUtils.isBlank(h.getDeviceNumber()) || !isDeviceInYkSystem(h.getDeviceNumber())) {
                continue;//不是系统中的设备就不处理
            }
            WdTodayDTO w = new WdTodayDTO();
            w.setDev_key(h.getDeviceNumber());
            w.setDev_name(deviceWdMap.get(h.getDeviceNumber()).getDeviceName());
            int status = "1".equals(h.getIsOffline()) ? 0 : 1;
            w.setStatus(Integer.toString(status));
            //是否在线数据需要反写回device表
            List<HoneylinkingDTO.RsBean> rsList = h.getRs();
            String writeDate = "";
            for (HoneylinkingDTO.RsBean r : rsList) {
                if ("温度".equals(r.getBname()) && NumbersUtils.isNumber(r.getData())) {
                    w.setTemp_value(r.getData());
                    if (writeDate.length() == 0) {
                        writeDate = r.getDataTime();
                    }
                }
                if ("湿度".equals(r.getBname()) && NumbersUtils.isNumber(r.getData())) {
                    w.setHumi_value(r.getData());
                    if (writeDate.length() == 0) {
                        writeDate = r.getDataTime();
                    }
                }
            }
            //如果日期不合法，转为当前日期
            if (!CmsUtils.isValidDate(writeDate, "yyyy/M/d h:mm:ss")) {
                writeDate = TimeUtils.getNowTime();
            } else {
                writeDate = CmsUtils.convertTimeFormat(writeDate, "yyyy/M/d h:mm:ss", "yyyy-MM-dd hh:mm:ss");
            }
            w.setTime(writeDate);
            wdList.add(w);
        }
        return wdList;
    }
}
