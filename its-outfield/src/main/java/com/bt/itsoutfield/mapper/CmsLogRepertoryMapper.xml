<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itsoutfield.mapper.CmsLogRepertoryMapper">
    <resultMap type="com.bt.itsoutfield.domain.vo.CmsLogRepertoryVO" id="cmsLogRepertoryMap">
        <result property="id" column="id"/>
        <result property="name" column="name"></result>
        <result property="pageId" column="page_id"></result>
        <result property="nodeId" column="node_id"></result>
        <result property="labelX" column="label_x"></result>
        <result property="labelY" column="label_y"></result>
        <result property="fontSize" column="font_size"></result>
        <result property="fontType" column="font_type"></result>
        <result property="fontColor" column="font_color"></result>
        <result property="messageBody" column="message_body"></result>
        <result property="multiBody" column="multi_body"></result>
        <result property="backColor" column="back_color"></result>
        <result property="holdTime" column="hold_time"></result>
        <result property="speed" column="speed"></result>
        <result property="inputMode" column="input_mode"></result>
        <result property="outputMode" column="output_mode"></result>
        <result property="layout" column="layout"></result>
        <result property="deviceId" column="device_id"></result>
        <result property="deviceName" column="device_name"></result>
        <result property="releaseTime" column="release_time"></result>
        <result property="roadNo" column="road_no"></result>
        <result property="milePost" column="mile_post"></result>
        <result property="cmsType" column="cms_type"></result>
        <result property="facilityNo" column="facility_no"></result>
        <result property="flag" column="flag"></result>
        <result property="reason" column="reason"></result>
        <result property="remark" column="remark"></result>
        <result property="accountor" column="accountor"></result>
        <result property="isDynamic" column="is_dynamic"></result>
        <result property="idSub" column="id_sub"></result>
        <result property="messageBodyAll" column="message_body_all"></result>
        <result property="roadName" column="road_name"></result>
        <result property="exportYear" column="export_year"></result>
        <result property="exportMonth" column="export_month"></result>
        <result property="releaseHour" column="release_hour"></result>
        <result property="width" column="width"></result>
        <result property="height" column="height"></result>
    </resultMap>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into cms_log_repertory
        (name,page_id,node_id,label_x,label_y,font_size,font_type,font_color,message_body,multi_body,back_color,hold_time,speed,input_mode,output_mode,layout,device_id,device_name,flag,reason,release_time,accountor,is_dynamic)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.name},#{item.pageId},#{item.nodeId},#{item.labelX},#{item.labelY},#{item.fontSize},#{item.fontType},#{item.fontColor},#{item.messageBody},#{item.multiBody},#{item.backColor},#{item.holdTime},#{item.speed},#{item.inputMode},#{item.outputMode},#{item.layout},#{item.deviceId},#{item.deviceName},#{item.flag},#{item.reason},#{item.releaseTime},#{item.accountor},#{item.isDynamic})
        </foreach>
    </insert>


    <select id="list" resultMap="cmsLogRepertoryMap" parameterType="com.bt.itsoutfield.domain.dto.CmsLogRepertoryDTO">
        select
        c.id,c.device_id,c.device_name,c.page_id,c.message_body,c.release_time,c.id_sub,c.content_type,dc.cms_type,
        HOUR(c.release_time) release_hour,c.name,c.node_id,dc.width,dc.height,
        dc.position_id,d.facility_no,r.road_alias as road_name,r.road_no,c.reason,c.flag,c.accountor,c.remark,c.layout,
        c.label_x,c.label_y,c.font_size,c.font_type,c.font_color,c.back_color,c.hold_time,c.speed,c.input_mode,c.output_mode
        from cms_log_repertory c LEFT JOIN device_cms dc ON c.device_id=dc.device_id
        LEFT JOIN device d on c.device_id=d.device_id
        LEFT JOIN facility f on f.facility_no=d.facility_no
        LEFT JOIN road r on f.road_no=r.road_no
        LEFT JOIN organization_road orr ON f.ROAD_NO = orr.road_no AND f.MP_VALUE BETWEEN
        orr.start_mile AND orr.end_mile
        <where>
            f.facility_no IN (Facility-Permissions-Check)
            <if test="createDateStart != null and createDateStart != ''">and c.release_time>=#{createDateStart}</if>
            <if test="createDateEnd != null and createDateEnd != ''">and #{createDateEnd}>=c.release_time</if>
            <if test="roadNo != null">and r.road_no=#{roadNo}</if>
            <if test="exportYear != null and exportYear != ''">and YEAR(c.release_time)=#{exportYear}</if>
            <if test="exportMonth != null and exportMonth != ''">and MONTH(c.release_time)=#{exportMonth}</if>
            <if test="pageId != null and pageId != ''">and c.page_id=#{pageId}</if>
            <if test="flag != null">and c.flag=#{flag}</if>
            <if test="deviceId != null">and c.device_id=#{deviceId}</if>
            <if test="deviceName != null">and d.device_name like concat("%",#{deviceName},"%")</if>
            <if test="accountor != null">and c.accountor like CONCAT('%', #{accountor}, '%')</if>
            <if test="cmsType != null &amp;&amp; cmsType >=0 ">AND dc.cms_type =#{cmsType}</if>
            <if test="facilityNo != null &amp;&amp; facilityNo >=0 ">AND d.facility_no =#{facilityNo}</if>
            <if test="orgId != null &amp;&amp; orgId !=''">AND orr.org_id =#{orgId}</if>
            <if test="messageBody != null &amp;&amp; messageBody != '' ">AND c.message_body like CONCAT('%',
                #{messageBody}, '%')
            </if>
            <if test="idList != null &amp;&amp; idList.size()>0">
                and c.device_id in
                <foreach collection="idList" item="deviceId" index="index" open="(" close=")" separator=",">
                    concat("",#{deviceId},"")
                </foreach>
            </if>
            <if test="roads != null &amp;&amp; roads.size()>0">
                AND f.road_no in
                <foreach collection="roads" item="id" index="index" open="(" close=")" separator=",">
                    concat("",#{id},"")
                </foreach>
            </if>
        </where>
        order by c.release_time desc,c.id asc
    </select>

    <select id="getAllPageId" resultType="java.lang.String" parameterType="com.bt.itsoutfield.domain.dto.CmsLogRepertoryDTO">
        select c.page_id
        from cms_log_repertory c
        where c.device_id = #{deviceId}
        group by c.page_id
        order by c.page_id desc
    </select>

    <select id="getLastPageId" resultMap="cmsLogRepertoryMap" parameterType="com.bt.itsoutfield.domain.dto.CmsLogRepertoryDTO">
        select c.device_id,
        substring_index(substring_index(group_concat(c.page_id order by c.page_id desc), ',', #{withdraw}), ',',-1) as page_id
        from (select device_id, page_id from cms_log_repertory
        <where>
            is_dynamic !=1 and flag !=0
            <if test="idList != null &amp;&amp; idList.size()>0">
                and device_id in
                <foreach collection="idList" item="deviceId" index="index" open="(" close=")" separator=",">
                    concat("",#{deviceId},"")
                </foreach>
            </if>
        </where>
        group by device_id, page_id) c
        group by c.device_id
        having (count(c.device_id) >= #{withdraw})
        order by device_id
    </select>

    <update id="updateContentType" parameterType="com.bt.itsoutfield.domain.dto.CmsLogRepertoryDTO">
        update cms_log_repertory
        set content_type = #{contentType}
        where message_body = #{messageBody}
    </update>

    <update id="updateIdSub" parameterType="com.bt.itsoutfield.domain.dto.CmsLogRepertoryDTO">
        update cms_log_repertory
        set id_sub = #{idSub}
        where node_id = #{nodeId}
          and release_time = #{releaseTime}
    </update>

    <update id="batchDeleteIdSub" parameterType="com.bt.itsoutfield.domain.dto.CmsLogRepertoryDTO">
        update cms_log_repertory
        set idSub = null
        where id_sub = CONCAT(#{nodeId}, #{releaseTime})
    </update>

    <update id="updateRemark" parameterType="com.bt.itsoutfield.domain.dto.CmsLogRepertoryDTO">
        update cms_log_repertory set remark = #{remark} where id in
        <foreach collection="idList" item="id" index="index" open="(" close=")" separator=",">
            concat("",#{id},"")
        </foreach>
    </update>
    <!--发布记录列表-->
    <select id="getLogRepertoryListList" resultMap="cmsLogRepertoryMap"
            parameterType="com.bt.itsoutfield.domain.dto.CmsLogRepertoryDTO">
        select * from cms_log_repertory clr where clr.device_id in(
        SELECT d.DEVICE_ID from device d INNER JOIN device_cms dc ON dc.DEVICE_ID = d.DEVICE_ID
        LEFT JOIN facility f ON d.FACILITY_NO = f.FACILITY_NO
        LEFT JOIN organization_road orr ON f.ROAD_NO = orr.road_no AND (f.MP_VALUE *1000) BETWEEN
        orr.start_mile AND orr.end_mile WHERE d.device_type_no=2 and f.facility_no IN (Facility-Permissions-Check)
        <if test="deviceName != null &amp;&amp; deviceName != '' ">AND d.device_name like CONCAT('%', #{deviceName},
            '%')
        </if>
        <if test="deviceId != null">AND d.device_id =#{deviceId}</if>
        <if test="cmsType != null &amp;&amp; cmsType >=0 ">AND dc.cms_type =#{cmsType}</if>
        <if test="orgId != null &amp;&amp; orgId !=''">AND orr.org_id =#{orgId}</if>
        <if test="roadNo != null">and orr.road_no =#{roadNo}</if>
        and d.facility_no IN (Facility-Permissions-Check)
        )
        and clr.release_time between #{createDateStart} and #{createDateEnd}
        <if test="flag != null &amp;&amp; flag >= 0">and clr.flag=#{flag}</if>
        <if test="accountor != null &amp;&amp; accountor !='' ">and clr.accountor like CONCAT('%', #{accountor}, '%')
        </if>
        <if test="messageBody != null &amp;&amp; messageBody != '' ">AND clr.messageBody like CONCAT('%',
            #{messageBody}, '%')
        </if>
        order by clr.release_time desc,clr.id desc
    </select>

    <select id="rollBackQueryList" resultMap="cmsLogRepertoryMap" parameterType="com.bt.itsoutfield.domain.dto.CmsLogRepertoryDTO">
        select
        c.id,c.device_id,c.device_name,c.page_id,c.message_body,c.release_time,c.id_sub,c.content_type,dc.cms_type,
        HOUR(c.release_time) release_hour,c.name,c.node_id,dc.width,dc.height,
        dc.position_id,d.facility_no,r.road_alias as road_name,r.road_no,c.reason,c.flag,c.accountor,c.remark,c.layout,
        c.label_x,c.label_y,c.font_size,c.font_type,c.font_color,c.back_color,c.hold_time,c.speed,c.input_mode,c.output_mode
        from cms_log_repertory c LEFT JOIN device_cms dc ON c.device_id=dc.device_id
        LEFT JOIN device d on c.device_id=d.device_id
        LEFT JOIN facility f on f.facility_no=d.facility_no
        LEFT JOIN road r on f.road_no=r.road_no
        LEFT JOIN organization_road orr ON f.ROAD_NO = orr.road_no AND f.MP_VALUE BETWEEN
        orr.start_mile AND orr.end_mile
        <where>
            <if test="roadNo != null">and r.road_no=#{roadNo}</if>
            <if test="pageId != null and pageId != ''">and c.page_id=#{pageId}</if>
            <if test="flag != null">and c.flag=#{flag}</if>
            <if test="deviceId != null">and c.device_id=#{deviceId}</if>
            <if test="deviceName != null">and d.device_name like concat("%",#{deviceName},"%")</if>
            <if test="accountor != null">and c.accountor like CONCAT('%', #{accountor}, '%')</if>
            <if test="cmsType != null &amp;&amp; cmsType >=0 ">AND dc.cms_type =#{cmsType}</if>
            <if test="facilityNo != null &amp;&amp; facilityNo >=0 ">AND d.facility_no =#{facilityNo}</if>
            <if test="orgId != null &amp;&amp; orgId !=''">AND orr.org_id =#{orgId}</if>
            <if test="messageBody != null &amp;&amp; messageBody != '' ">AND c.message_body like CONCAT('%',
                #{messageBody}, '%')
            </if>
            <if test="idList != null &amp;&amp; idList.size()>0">
                and c.device_id in
                <foreach collection="idList" item="deviceId" index="index" open="(" close=")" separator=",">
                    concat("",#{deviceId},"")
                </foreach>
            </if>
        </where>
        order by c.release_time desc,c.id asc
    </select>


</mapper>