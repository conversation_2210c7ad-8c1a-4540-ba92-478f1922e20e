package com.bt.itsoutfield.domain.vo;

public class RoadDeviceCheckVO {
	private String id;//  bigint NULL AUTO_INCREMENT COMMENT '主键' ,
	private String roadNo;//  int NULL COMMENT '路段no' ,
	private String facilityNo;//  varchar(100) NULL COMMENT '设施编号' ,
	private String milePost;//  varchar(50) NULL COMMENT '桩号' ,
	private String deviceTypeNo;// 设备类型no
	private String deviceTypeName;
	private String deviceSubtypeNo;// 设备子类型
	private String deviceId;//  int NULL COMMENT '设备id' ,
	private String deviceName;//  varchar(100) NULL COMMENT '设备名称' ,
	private Integer status;//  int NULL COMMENT '设备状态' ,
	private Integer lastStatus;//  int NULL COMMENT '设备状态'
	private String offlineStartTime;//  离线开始时间' ,
	private String offlineEndTime;// 离线恢复时间' ,
	private String offlineTime;// 离线恢复时间' ,
	private String description;//  描述 ,
	private String roadName;
	private String facilityName;
	private String ipAddress;


	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getRoadNo() {
		return roadNo;
	}

	public void setRoadNo(String roadNo) {
		this.roadNo = roadNo;
	}

	public String getFacilityNo() {
		return facilityNo;
	}

	public void setFacilityNo(String facilityNo) {
		this.facilityNo = facilityNo;
	}

	public String getMilePost() {
		return milePost;
	}

	public void setMilePost(String milePost) {
		this.milePost = milePost;
	}

	public String getDeviceTypeNo() {
		return deviceTypeNo;
	}

	public void setDeviceTypeNo(String deviceTypeNo) {
		this.deviceTypeNo = deviceTypeNo;
	}

	public String getDeviceSubtypeNo() {
		return deviceSubtypeNo;
	}

	public void setDeviceSubtypeNo(String deviceSubtypeNo) {
		this.deviceSubtypeNo = deviceSubtypeNo;
	}

	public String getDeviceId() {
		return deviceId;
	}

	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}

	public String getDeviceName() {
		return deviceName;
	}

	public void setDeviceName(String deviceName) {
		this.deviceName = deviceName;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getLastStatus() {
		return lastStatus;
	}

	public void setLastStatus(Integer lastStatus) {
		this.lastStatus = lastStatus;
	}

	public String getOfflineStartTime() {
		return offlineStartTime;
	}

	public void setOfflineStartTime(String offlineStartTime) {
		this.offlineStartTime = offlineStartTime;
	}

	public String getOfflineEndTime() {
		return offlineEndTime;
	}

	public void setOfflineEndTime(String offlineEndTime) {
		this.offlineEndTime = offlineEndTime;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getFacilityName() {
		return facilityName;
	}

	public void setFacilityName(String facilityName) {
		this.facilityName = facilityName;
	}

	public String getDeviceTypeName() {
		return deviceTypeName;
	}

	public void setDeviceTypeName(String deviceTypeName) {
		this.deviceTypeName = deviceTypeName;
	}

	public String getRoadName() {
		return roadName;
	}

	public void setRoadName(String roadName) {
		this.roadName = roadName;
	}

	public String getOfflineTime() {
		return offlineTime;
	}

	public void setOfflineTime(String offlineTime) {
		this.offlineTime = offlineTime;
	}

	public String getIpAddress() {
		return ipAddress;
	}

	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}
}
