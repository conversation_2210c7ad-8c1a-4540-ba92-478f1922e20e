
package com.bt.itsoutfield.domain.entity;

public class WdAlarm {
	private Integer id;//编号,
	private String alarm_time;// 报警时间,
	private Integer alarm_type;//报警类别：1-气象站、大桥低温报警 2-气象站能见度报警 4-雾区诱导的能见度报警 3没有启用
	private String message;//报警详情,
	private String device_id;//设备ID,
	private String device_name;//设备名称
	private String temp_value;//温度值,
	private String humi_value;//湿度值,
	private String visibility_value;//能见度
	private String lat;//经度,
	private String lng;//纬度,
	private String facility_name;//所属设施
	private String direction_name;//方向
	private String mile_post;//桩号
	private String start_time;//起始时间
	private String end_time;//终止时间
	private Integer push_status;//推送状态：1表示已经推送，0表示未推送，2表示重复不推',
	private Integer sound_status;//是否播放声频文件  0：不播放 1：播放',
	private String plan_id;//预案ID',
	private String camera_id;//关联摄像机ID',
	private Integer check_status;//确认标志  0:未确认 1、确认  3:误报',
	private String road_no;
	private String road_name;

	private String websocketType;
	private String event_type;
	private String event_sub_type;
	private String alarm_desc;
	private String create_time;

	// 新增字段
	private Integer misinformation; //是否误报(0-否 1-是)
	private String handle_man;//处理人
	private Integer handle_status;//是否确认(0-否 1-是)
	private Long handle_time;//确认时间
	private String remark;//处理描述

	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getAlarm_time() {
		return alarm_time;
	}
	public void setAlarm_time(String alarm_time) {
		this.alarm_time = alarm_time;
	}
	public Integer getAlarm_type() {
		return alarm_type;
	}
	public void setAlarm_type(Integer alarm_type) {
		this.alarm_type = alarm_type;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public String getDevice_id() {
		return device_id;
	}
	public void setDevice_id(String device_id) {
		this.device_id = device_id;
	}
	public String getDevice_name() {
		return device_name;
	}
	public void setDevice_name(String device_name) {
		this.device_name = device_name;
	}
	public String getTemp_value() {
		return temp_value;
	}
	public void setTemp_value(String temp_value) {
		this.temp_value = temp_value;
	}
	public String getHumi_value() {
		return humi_value;
	}
	public void setHumi_value(String humi_value) {
		this.humi_value = humi_value;
	}
	public String getLat() {
		return lat;
	}
	public void setLat(String lat) {
		this.lat = lat;
	}
	public String getLng() {
		return lng;
	}
	public void setLng(String lng) {
		this.lng = lng;
	}
	public String getFacility_name() {
		return facility_name;
	}
	public void setFacility_name(String facility_name) {
		this.facility_name = facility_name;
	}
	public String getMile_post() {
		return mile_post;
	}
	public void setMile_post(String mile_post) {
		this.mile_post = mile_post;
	}
	public String getDirection_name() {
		return direction_name;
	}
	public void setDirection_name(String direction_name) {
		this.direction_name = direction_name;
	}
	public String getStart_time() {
		return start_time;
	}
	public void setStart_time(String start_time) {
		this.start_time = start_time;
	}
	public String getEnd_time() {
		return end_time;
	}
	public void setEnd_time(String end_time) {
		this.end_time = end_time;
	}
	public Integer getPush_status() {
		return push_status;
	}
	public void setPush_status(Integer push_status) {
		this.push_status = push_status;
	}
	public Integer getSound_status() {
		return sound_status;
	}
	public void setSound_status(Integer sound_status) {
		this.sound_status = sound_status;
	}
	public String getPlan_id() {
		return plan_id;
	}
	public void setPlan_id(String plan_id) {
		this.plan_id = plan_id;
	}
	public String getCamera_id() {
		return camera_id;
	}
	public void setCamera_id(String camera_id) {
		this.camera_id = camera_id;
	}
	public Integer getCheck_status() {
		return check_status;
	}
	public void setCheck_status(Integer check_status) {
		this.check_status = check_status;
	}
	public String getVisibility_value() {
		return visibility_value;
	}
	public void setVisibility_value(String visibility_value) {
		this.visibility_value = visibility_value;
	}

	public String getRoad_no() {
		return road_no;
	}

	public void setRoad_no(String road_no) {
		this.road_no = road_no;
	}

	public String getRoad_name() {
		return road_name;
	}

	public void setRoad_name(String road_name) {
		this.road_name = road_name;
	}

	public String getEvent_type() {
		return event_type;
	}

	public void setEvent_type(String event_type) {
		this.event_type = event_type;
	}

	public String getEvent_sub_type() {
		return event_sub_type;
	}

	public void setEvent_sub_type(String event_sub_type) {
		this.event_sub_type = event_sub_type;
	}


	public String getCreate_time() {
		return create_time;
	}

	public void setCreate_time(String create_time) {
		this.create_time = create_time;
	}

	public String getAlarm_desc() {
		return alarm_desc;
	}

	public void setAlarm_desc(String alarm_desc) {
		this.alarm_desc = alarm_desc;
	}

	public String getWebsocketType() {
		return websocketType;
	}

	public void setWebsocketType(String websocketType) {
		this.websocketType = websocketType;
	}

	public Integer getMisinformation() {
		return misinformation;
	}

	public void setMisinformation(Integer misinformation) {
		this.misinformation = misinformation;
	}

	public String getHandle_man() {
		return handle_man;
	}

	public void setHandle_man(String handle_man) {
		this.handle_man = handle_man;
	}

	public Integer getHandle_status() {
		return handle_status;
	}

	public void setHandle_status(Integer handle_status) {
		this.handle_status = handle_status;
	}

	public Long getHandle_time() {
		return handle_time;
	}

	public void setHandle_time(Long handle_time) {
		this.handle_time = handle_time;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
}
