package com.bt.itsuser.controller;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import com.bt.itscore.exception.AuthzException;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import com.bt.itscore.auth.CheckAuthz;
import com.bt.itscore.auth.Login;
import com.bt.itscore.domain.dto.IdIntegerDTO;
import com.bt.itscore.domain.dto.IdStringDTO;
import com.bt.itscore.domain.dto.PageDTO;
import com.bt.itscore.domain.vo.OrganizationRoadVO;
import com.bt.itscore.domain.vo.PageVO;
import com.bt.itscore.domain.vo.ResponseVO;
import com.bt.itscore.exception.ArgumentException;
import com.bt.itscore.utils.ValidUtils;
import com.bt.itsuser.domain.dto.OrganizationDTO;
import com.bt.itsuser.domain.dto.OrganizationUserDTO;
import com.bt.itsuser.domain.vo.OrganizationVO;
import com.bt.itsuser.service.OrganizationService;

/**
* <AUTHOR>
* @date 2021年3月16日 下午4:01:36
* @Description 组织机构
 */
@RestController
@RequestMapping("organization")
public class OrganizationController {

	private final static Logger LOGGER = LoggerFactory.getLogger(OrganizationController.class);

	@Autowired
	private OrganizationService organizationService;

	@Login
	@CheckAuthz(hasPermissions = "system:organization:add")
	@PostMapping("add")
	public ResponseVO add(@Valid @RequestBody OrganizationDTO dto, BindingResult result) {
        ValidUtils.error(result);
		boolean ret = organizationService.add(dto);
		return new ResponseVO(ret);
	}

	@Login
	@CheckAuthz(hasPermissions = "system:organization:delete")
	@PostMapping("delete")
	public ResponseVO delete(@Valid @RequestBody IdStringDTO dto, BindingResult result) {
		ValidUtils.error(result);
		boolean ret = organizationService.delete(dto);
		return new ResponseVO(ret);
	}

	@Login
	@CheckAuthz(hasPermissions = "system:organization:update")
	@PostMapping("update")
	public ResponseVO update(@Valid @RequestBody OrganizationDTO dto, BindingResult result) {
		if(StringUtils.isBlank(dto.getOrgId())) {
			throw new ArgumentException("请求body参数校验不通过:orgId参数不能为空");
		}
		ValidUtils.error(result);
		boolean ret = organizationService.update(dto);
		return new ResponseVO(ret);
	}

	@Login
	@PostMapping("page")
	public PageVO page(@Valid PageDTO pageDTO, @RequestBody OrganizationDTO dto, BindingResult result) {
		ValidUtils.error(result);
		return new PageVO(organizationService.page(pageDTO, dto));
	}
	
	@Login
	@PostMapping("tree")
	public List<OrganizationVO> tree(@RequestBody OrganizationDTO dto, HttpServletRequest request) {
		String userId = (String) request.getAttribute("userId");
		String role = (String) request.getAttribute("role");
		List<String> roleList = RoleController.rolesOfLoginUser(role);
		return organizationService.tree(dto, userId, roleList);
	}
	
	@Login
	@PostMapping("orgOwn")
	public List<OrganizationVO> treeOwn(@RequestBody OrganizationDTO dto, HttpServletRequest request) {
		return organizationService.orgOwn(dto,request);
	}

	@Login
	@PostMapping("findOrgByUser")
	public List<OrganizationVO> findOrgByUser(HttpServletRequest request) {
		return organizationService.findOrgByUser(request);
	}
	
	@Login
	@PostMapping("selectListWithUser")
	public List<OrganizationVO> selectListWithUser() {
		return organizationService.selectListWithUser();
	}

	/**
	 * app查看通讯录，可根据所属运营公司、应急角色类型来查询
	 * @param dto
	 * @param result
	 * @return
	 */
	@Login
	@PostMapping("selectByOrgIdAndType")
	public List<OrganizationVO> selectByOrgIdAndType(@Valid @RequestBody OrganizationUserDTO dto,BindingResult result) {
		ValidUtils.error(result);
		List<OrganizationVO> vos = organizationService.selectByOrgIdAndType(dto);
		return vos;
	}

    /** 通过路段编号roadNo查询组织机构 **/
	@Login
	@PostMapping("selectByRoadNo")
	public OrganizationRoadVO selectByRoadNo(@RequestBody IdIntegerDTO dto) {
		return organizationService.selectByRoadNo(dto);
	}
	
	/**
	 * 已废弃，使用eventTree代替
	 * 
	 * @api {POST} organization/ownTree 查询本公司树型机构organization/ownTree
	 * @apiDescription 查询本公司树型机构；创建人：龙思颖，修改人：无
	 * @apiGroup 机构OrganizationController
	 * @apiHeader {String} Authorization token
	 * @apiBody {Number} use=1 1-启用，0-禁用
	 * @apiBody {Number} [orgType] 1-公司，0-部门
	 * @apiSuccess (Success 200) {String} orgId 机构ID
	 * @apiSuccess (Success 200) {String} orgName 机构名称
	 * @apiSuccess (Success 200) {String} shortName 机构简称
	 * @apiSuccess (Success 200) {Object} children 下级机构
	 * @apiSuccess (Success 200) {String} children.orgId 下级机构ID
	 * @apiSuccess (Success 200) {String} children.orgName 下级机构名称
	 * @apiSuccess (Success 200) {String} children.shortName 下级机构简称
	 * @apiSuccessExample {json} Success-Response:
	 *     HTTP/1.1 200 OK
			   	[{
			   	orgId: "dfbd6525-b051-4719-b356-cd4e5454212", 
			   	orgName: "广西北部湾投资集团有限公司", 
			   	shortName: "广西北部湾投资集团有限公司",
				children: [{
					orgId: "54a9b1c1-c6fa-4b1b-8c01-00e2abd80407", 
					orgName: "广西北部湾投资集团有限公司总部",
					shortName: "广西北部湾投资集团有限公司总部",
					children: []
				}]
			}]
	 * @apiSampleRequest /organization/ownTree
	 */
	@Login
	@PostMapping("ownTree")
	@Deprecated
	public List<OrganizationVO> ownTree(@RequestBody OrganizationDTO dto, HttpServletRequest request) {
		String account = (String)request.getAttribute("account");
		return organizationService.ownTree(account, dto);
	}

	/**
	 * 根据用户的roleId，查询所有具有事件处置权限的机构树形菜单
	 * @param dto 查询参数：use是否使用，orgtype查询的机构级别
	 * @return 单个根节点的tree list
	 *
	 * @api {POST} organization/eventTree 根据用户的roleId，查询所有具有事件处置权限的机构树形菜单
	 * @apiDescription 根据用户的roleId，查询所有具有事件处置权限的机构树形菜单；创建人：覃士蘅，修改人：无
	 * @apiGroup 机构OrganizationController
	 * @apiHeader {String} Authorization token
	 * @apiBody {Number} use=1 1-启用，0-禁用
	 * @apiBody {Number} [orgType] 1-公司，0-部门
	 * @apiSuccess (Success 200) {String} orgId 机构ID
	 * @apiSuccess (Success 200) {String} orgName 机构名称
	 * @apiSuccess (Success 200) {String} shortName 机构简称
	 * @apiSuccess (Success 200) {Object} children 下级机构
	 * @apiSuccess (Success 200) {Object} children.orgId 下级机构ID
	 * @apiSuccess (Success 200) {Object} children.orgName 下级机构名称
	 * @apiSuccess (Success 200) {Object} children.shortName 下级机构简称
	 * @apiSuccessExample {json} Success-Response:
	 * HTTP/1.1 200 OK
	 	   	[{
	 	   	orgId: "dfbd6525-b051-4719-b356-cd4e5454212",
	 	   	orgName: "广西北部湾投资集团有限公司",
	 	   	shortName: "广西北部湾投资集团有限公司",
	 		children: [{
	 			orgId: "54a9b1c1-c6fa-4b1b-8c01-00e2abd80407",
	 			orgName: "广西北部湾投资集团有限公司总部",
	 			shortName: "广西北部湾投资集团有限公司总部",
	 			children: []
	        }]
	 * }]
	 */
	@Login
	@PostMapping("eventTree")
	public List<OrganizationVO> eventTree(@RequestBody OrganizationDTO dto, HttpServletRequest request) {
		String role = (String) request.getAttribute("role");
		List<String> roleList = new ArrayList<>();

		//role = "1b5f77c7-6236-11ec-969f-00163e0125eb;843c3cf1-5179-11ec-b93e-00163e0125eb";

		if (role == null || role.length() < 1) {
			LOGGER.info("没有角色权限");
			throw new AuthzException("没有角色权限");
		}
		if (role.contains(";")) {
			// 构造roleList
			String[] roleArr = role.split(";");
			Collections.addAll(roleList, roleArr);
		}


		return organizationService.eventTree(roleList, dto);
	}

	/**
	 * 根据用户的roleId，查询所有具有事件处置权限的机构，返回多个根节点（根节点为role_event表中直接关联的orgid）
	 * @param dto dto 查询参数：use是否使用，orgtype查询的机构级别
	 * @return 多个根节点的tree list
	 *
	 * @api {POST} organization/eventTreeFlat 根据用户的roleId，查询所有具有事件处置权限的机构，返回多个根节点（根节点为role_event表中直接关联的orgid）
	 * @apiDescription 根据用户的roleId，查询所有具有事件处置权限的机构，返回多个根节点（根节点为role_event表中直接关联的orgid）；创建人：覃士蘅，修改人：无
	 * @apiGroup 机构OrganizationController
	 * @apiHeader {String} Authorization token
	 * @apiBody {Number} use=1 1-启用，0-禁用
	 * @apiBody {Number} [orgType] 1-公司，0-部门
	 * @apiSuccess (Success 200) {String} orgId 机构ID
	 * @apiSuccess (Success 200) {String} orgName 机构名称
	 * @apiSuccess (Success 200) {String} shortName 机构简称
	 * @apiSuccess (Success 200) {Object} children 下级机构
	 * @apiSuccess (Success 200) {Object} children.orgId 下级机构ID
	 * @apiSuccess (Success 200) {Object} children.orgName 下级机构名称
	 * @apiSuccess (Success 200) {Object} children.shortName 下级机构简称
	 * @apiSuccessExample {json} Success-Response:
	 * HTTP/1.1 200 OK
		[{
			orgId: "dfbd6525-b051-4719-b356-cd4e5454212",
			orgName: "广西北部湾投资集团有限公司",
			shortName: "广西北部湾投资集团有限公司",
			children: [{
				orgId: "54a9b1c1-c6fa-4b1b-8c01-00e2abd80407",
				orgName: "广西北部湾投资集团有限公司总部",
				shortName: "广西北部湾投资集团有限公司总部",
				children: []
			}]
	 * }]
	 */
	@Login
	@PostMapping("eventTreeFlat")
	public List<OrganizationVO> eventTreeFlat(@RequestBody OrganizationDTO dto, HttpServletRequest request) {
		String role = (String) request.getAttribute("role");
		List<String> roleList = RoleController.rolesOfLoginUser(role);

		return organizationService.eventTreeFlat(roleList, dto);
	}

	/**
	 * 根据用户的roleId，查询所有具有事件处置权限的机构列表
	 * @param request
	 * @return list
	 */
	@Login
	@PostMapping("eventOrgList")
	public List<String> eventOrgList(HttpServletRequest request) {
		String role = (String) request.getAttribute("role");
		List<String> roleList = new ArrayList<>();

		if (role == null || role.length() < 1) {
			LOGGER.info("没有角色权限");
			throw new AuthzException("没有角色权限");
		}
		if (role.contains(";")) {
			// 构造roleList
			String[] roleArr = role.split(";");
			Collections.addAll(roleList, roleArr);
		}

		return organizationService.getEventOrgList(roleList);
	}

	/**
	 * @描述 包含所有树（多个顶级公司）
	 * @作者 邓云钢
	 */
	@Login
	@PostMapping("allTree")
	public List<OrganizationVO> allTree(@RequestBody OrganizationDTO dto, HttpServletRequest request) {
		String userId = (String) request.getAttribute("userId");
		return organizationService.allTree(dto, userId);
	}

	/**
	 * @描述 登录用户拥有的组织机构tree
	 * @作者 邓云钢
	 */
	@Login
	@PostMapping("loginUserOrgTree")
	public List<OrganizationVO> loginUserOrgTree(@RequestBody OrganizationDTO dto, HttpServletRequest request) {
		String role = (String) request.getAttribute("role");
		List<String> roleList = RoleController.rolesOfLoginUser(role);
		return organizationService.loginUserOrgTree(dto, roleList);
	}

}
