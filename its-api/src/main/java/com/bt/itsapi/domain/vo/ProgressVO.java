package com.bt.itsapi.domain.vo;

import java.util.List;

public class ProgressVO {
	private Integer id;
	private String eventId;
	private Long occurTime;
	private String progressDesc;
	private Long createTime;
	List<ProgressAttachVO> attachs;
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getEventId() {
		return eventId;
	}
	public void setEventId(String eventId) {
		this.eventId = eventId;
	}
	public Long getOccurTime() {
		return occurTime;
	}
	public void setOccurTime(Long occurTime) {
		this.occurTime = occurTime;
	}
	public String getProgressDesc() {
		return progressDesc;
	}
	public void setProgressDesc(String progressDesc) {
		this.progressDesc = progressDesc;
	}
	public Long getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}
	public List<ProgressAttachVO> getAttachs() {
		return attachs;
	}
	public void setAttachs(List<ProgressAttachVO> attachs) {
		this.attachs = attachs;
	}
}
