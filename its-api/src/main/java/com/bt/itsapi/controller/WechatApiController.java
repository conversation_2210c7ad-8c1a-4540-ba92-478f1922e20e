package com.bt.itsapi.controller;

import com.bt.itsapi.domain.vo.MpOpenCheckSignatureReqVO;
import com.bt.itsapi.domain.vo.MpOpenHandleMessageReqVO;
import com.bt.itsapi.service.WechatApiService;
import com.bt.itscore.domain.dto.WechatEventDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: lvms
 * @date: 2023年4月19日 16:41:20
 * @Description: 微信公众号开发 相关接口
 */
@RestController
@RequestMapping("wechat")
public class WechatApiController {

  private final static Logger LOGGER = LoggerFactory.getLogger(WechatApiController.class);

  @Autowired
  private WechatApiService wechatApiService;

  /**
   * @描述 接收微信公众号的校验签名
   * 对应 <a href="https://developers.weixin.qq.com/doc/offiaccount/Basic_Information/Access_Overview.html">文档</a>
   */
  @GetMapping(value="/checkSignature/{appId}", produces = "text/plain;charset=UTF-8" )
  public String checkSignature(@PathVariable("appId") String appId,
      @RequestParam(value = "signature") String signature,
      @RequestParam(value = "nonce") String nonce,
      @RequestParam(value = "timestamp") String timestamp,
      @RequestParam(value = "echostr") String echostr) {
    LOGGER.info("[checkSignature] 接收到来自微信服务器的认证消息({},{},{})]", timestamp, nonce, signature);
    // 校验请求签名
    if (wechatApiService.checkSignature(timestamp, nonce, signature, appId)) {
      // 校验通过
      return echostr;
    }
    // 校验不通过
    return "非法请求";
  }

  /**
   * @描述 接收微信公众号的消息推送
   * 对应 <a href="https://developers.weixin.qq.com/doc/offiaccount/Message_Management/Receiving_event_pushes.html">文档</a>
   */
  @PostMapping(value="/checkSignature/{appId}", produces = "application/xml; charset=UTF-8")
  public String handleMessage(@PathVariable("appId") String appId, @RequestBody String content, MpOpenHandleMessageReqVO reqVO) {
    LOGGER.info("[handleMessage][appId({}) 推送消息，参数({},{},{}) 内容({})]", appId, reqVO.getSignature(), reqVO.getTimestamp(), reqVO.getNonce(), content);
    return wechatApiService.handleMessage(content, reqVO, appId);
  }
}
