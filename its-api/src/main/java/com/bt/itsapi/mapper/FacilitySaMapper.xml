<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itsapi.mapper.FacilitySaMapper">
    <resultMap id="facilityMap" type="com.bt.itsapi.domain.vo.FacilityVO" >
        <result property="facilityNo" column="facility_no"/>
        <result property="facilityName" column="facility_name"/>
        <result property="roadNo" column="road_no"/>
        <result property="roadName" column="road_name"/>
        <result property="roadAlias" column="road_alias"/>
        <result property="upFacilityTypeNo" column="facility_type_no"/>
        <result property="milePost" column="mile_post"/>
        <result property="upFacilityNo" column="up_facility_no"/>
        <result property="mpValue" column="mp_value"/>
        <result property="sort" column="sort"/>
        <result property="lng" column="lng"/>
        <result property="lat" column="lat"/>
        <result property="facilityCode" column="facility_code"/>
        <result property="companyName" column="org_name"/>
    </resultMap>
    <select id="selectAll" parameterType="com.bt.itsapi.domain.dto.FacilityDTO" resultMap="facilityMap">
        SELECT f.facility_no,f.facility_name,f.road_no,f.facility_type_no,f.mile_post,f.up_facility_no,f.mp_value,f.sort,f.lng,f.lat,
        f.facility_code, r.road_alias,r.road_name,o.org_name,
        fa.id,fa.file_name,fa.disk_file_name,fa.file_size,fa.content_type,fa.digest,fa.disk_directory,fa.create_time
        FROM facility_sa sa ,road r,facility f LEFT JOIN facility_attach fa ON f.facility_no=fa.facility_no
        LEFT JOIN organization_road zr ON zr.road_no=f.road_no
        LEFT JOIN organization o ON o.org_id=zr.org_id
        WHERE f.facility_no=sa.facility_no AND f.road_no=r.road_no
        ORDER BY f.facility_name ASC
    </select>
</mapper>