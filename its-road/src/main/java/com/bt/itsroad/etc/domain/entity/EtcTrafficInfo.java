package com.bt.itsroad.etc.domain.entity;

import java.util.Map;

public class EtcTrafficInfo {
	private String currentDayTime;// 查询时间
	private String stationName;//收费站名
	private String code;// 门架编号
	private String milePost;//门架桩号
	private String direction; // 方向
	private String deviceName;//设备名称
	private String lng;//经度
	private String lat;//纬度
	private String roadName;//路段名称
	private String deviceTypeNo;//类型名称
	private String deviceId;//设备id
	private int[]  currentDay_24; //今日24小时数量  24个值
	private int[] yesterday_24; //昨日24小时数量 24个值
    private int[] currentDay_1; // 当前一小时内车流量  12个值 
	private int[] yesterday_1; //昨日同期一小时车流量 12个值
	
	private Integer currentDayData;//今日总车流
	private Integer yesterdayData;// 昨日总车流
	private Map<String, Integer> yesterdayClassify; // 昨日车型分类 key:车型中文 ，value:车流量
	private Map<String, Integer> currentDayClassify; // 今日车型分类  key:车型中文 ，value:车流量
	
	public String getCurrentDayTime() {
		return currentDayTime;
	}
	public void setCurrentDayTime(String currentDayTime) {
		this.currentDayTime = currentDayTime;
	}
	public String getStationName() {
		return stationName;
	}
	public void setStationName(String stationName) {
		this.stationName = stationName;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getMilePost() {
		return milePost;
	}
	public void setMilePost(String milePost) {
		this.milePost = milePost;
	}
	public String getDirection() {
		return direction;
	}
	public void setDirection(String direction) {
		this.direction = direction;
	}
	public Integer getCurrentDayData() {
		return currentDayData;
	}
	public void setCurrentDayData(Integer currentDayData) {
		this.currentDayData = currentDayData;
	}
	public String getDeviceName() {
		return deviceName;
	}
	public void setDeviceName(String deviceName) {
		this.deviceName = deviceName;
	}
	public String getLng() {
		return lng;
	}
	public void setLng(String lng) {
		this.lng = lng;
	}
	public String getLat() {
		return lat;
	}
	public void setLat(String lat) {
		this.lat = lat;
	}
	public String getRoadName() {
		return roadName;
	}
	public void setRoadName(String roadName) {
		this.roadName = roadName;
	}
	public String getDeviceTypeNo() {
		return deviceTypeNo;
	}
	public void setDeviceTypeNo(String deviceTypeNo) {
		this.deviceTypeNo = deviceTypeNo;
	}
	public String getDeviceId() {
		return deviceId;
	}
	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}
	public int[] getCurrentDay_24() {
		return currentDay_24;
	}
	public void setCurrentDay_24(int[] currentDay_24) {
		this.currentDay_24 = currentDay_24;
	}
	public Integer getYesterdayData() {
		return yesterdayData;
	}
	public void setYesterdayData(Integer yesterdayData) {
		this.yesterdayData = yesterdayData;
	}
	public int[] getYesterday_24() {
		return yesterday_24;
	}
	public void setYesterday_24(int[] yesterday_24) {
		this.yesterday_24 = yesterday_24;
	}
	public int[] getCurrentDay_1() {
		return currentDay_1;
	}
	public void setCurrentDay_1(int[] currentDay_1) {
		this.currentDay_1 = currentDay_1;
	}
	public int[] getYesterday_1() {
		return yesterday_1;
	}
	public void setYesterday_1(int[] yesterday_1) {
		this.yesterday_1 = yesterday_1;
	}
	public Map<String, Integer> getYesterdayClassify() {
		return yesterdayClassify;
	}
	public void setYesterdayClassify(Map<String, Integer> yesterdayClassify) {
		this.yesterdayClassify = yesterdayClassify;
	}
	public Map<String, Integer> getCurrentDayClassify() {
		return currentDayClassify;
	}
	public void setCurrentDayClassify(Map<String, Integer> currentDayClassify) {
		this.currentDayClassify = currentDayClassify;
	}
	
	
}
