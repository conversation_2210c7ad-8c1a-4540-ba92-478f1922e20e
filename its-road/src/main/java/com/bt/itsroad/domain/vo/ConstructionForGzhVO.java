package com.bt.itsroad.domain.vo;

import java.util.List;
/**
 * <AUTHOR>
 * @date 2023年3月15日 下午3:36:26
 * @Description 微信公众号上的施工信息
 */
public class ConstructionForGzhVO {
	private String id;
	private String roadName;
	private String directionName;
	private String startMilePost;
	private String endMilePost;
	private Long startTime;
	private Long endTime;
	private String constructionRange;
	private List<OccupiedLaneVO> occupiedLanes;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getRoadName() {
		return roadName;
	}

	public void setRoadName(String roadName) {
		this.roadName = roadName;
	}

	public String getDirectionName() {
		return directionName;
	}

	public void setDirectionName(String directionName) {
		this.directionName = directionName;
	}

	public String getStartMilePost() {
		return startMilePost;
	}

	public void setStartMilePost(String startMilePost) {
		this.startMilePost = startMilePost;
	}

	public String getEndMilePost() {
		return endMilePost;
	}

	public void setEndMilePost(String endMilePost) {
		this.endMilePost = endMilePost;
	}

	public Long getStartTime() {
		return startTime;
	}

	public void setStartTime(Long startTime) {
		this.startTime = startTime;
	}

	public Long getEndTime() {
		return endTime;
	}

	public void setEndTime(Long endTime) {
		this.endTime = endTime;
	}

	public String getConstructionRange() {
		return constructionRange;
	}

	public void setConstructionRange(String constructionRange) {
		this.constructionRange = constructionRange;
	}

	public List<OccupiedLaneVO> getOccupiedLanes() {
		return occupiedLanes;
	}

	public void setOccupiedLanes(List<OccupiedLaneVO> occupiedLanes) {
		this.occupiedLanes = occupiedLanes;
	}
}
