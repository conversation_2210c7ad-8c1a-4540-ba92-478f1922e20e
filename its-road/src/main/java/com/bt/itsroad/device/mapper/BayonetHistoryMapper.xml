<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itsroad.device.mapper.BayonetHistoryMapper">

    <insert id="addHistory" parameterType="com.bt.itsroad.device.domain.dto.BayonetHistoryDTO">
        insert into bayonet_history
        (id,
        car_no,
        front_big_img,
        collection_time,
        lane_id,
        update_time,
        danger_car,
        entry_flag,
        facility_no,
        upload_status,
        device_id,
        danger_class,
        push_status,
        body_big_img,
        car_models)
        VALUES
            (#{id},
            #{carno},
            #{front_big_img},
            #{collection_time},
            #{ykLaneId},
            #{update_time},
            #{dangerCar},
            #{entryFlag},
            #{facilityNo},
             0,
            #{deviceId},
            #{dangerClass},
             0,
            #{bodyBigImg},
            #{carModels})
    </insert>

    <insert id="bayonetHistoryAttachAdd" parameterType="com.bt.itsroad.device.domain.dto.BayonetHistoryAttachDTO">
        INSERT INTO bayonet_history_attach (history_id,attach_type,file_name,disk_file_name,file_size,content_type,create_time
        ) VALUES (
                     #{historyId},
                     #{attachType},
                     #{fileName},
                     #{diskFileName},
                     #{fileSize},
                     #{contentType},
                     #{createTime}
                 )
            ON DUPLICATE KEY UPDATE
                                 history_id=values(history_id),
                                 attach_type=values(attach_type),
                                 file_name=values(file_name),
                                 disk_file_name=values(disk_file_name),
                                 file_size=values(file_size),
                                 content_type=values(content_type)
    </insert>

    <select id="selectId" resultType="String">
        select id from bayonet_history where id = #{id}
    </select>

    <resultMap type="com.bt.itsroad.device.domain.dto.BayonetHistoryDTO" id="StayTimeMap">
        <result column="car_no" property="carno"/>
        <result column="collection_time" property="collection_time"/>
    </resultMap>

    <select id="selectCollectionTimeAll" resultMap="StayTimeMap">
        select car_no, collection_time
        from bayonet_history
        where lane_id regexp (
 	    select concat("^", replace(group_concat(distinct (lane_id)), ',', '|^') )
	    from device_camera
        where device_id in (select distinct d.device_id from device d where d.facility_no =#{facilityNo}    )
              and lane_id is not null and entry_flag = #{entryFlag}
    )
        and collection_time >= #{startTime}
        order by car_no desc, collection_time asc
    </select>

    <resultMap type="com.bt.itsroad.device.domain.dto.DangerCarDTO" id="DangerCarMap">
        <result column="device_id" property="deviceId"/>
        <result column="entry_flag" property="entryFlag"/>
        <result column="facility_no" property="facilityNo"/>
        <result column="id" property="id"/>
        <result column="car_no" property="carNo"/>
        <result column="front_big_img" property="frontBigImg"/>
        <result column="enter_time" property="enterTime"/>
        <result column="exit_time" property="exitTime"/>
        <result column="exit_status" property="exitStatus"/>
        <result column="source_id" property="sourceId"/>
    </resultMap>

    <select id="selectEntryFlag" resultMap="DangerCarMap">
        SELECT
            d.device_id,
            dc.entry_flag,
            d.facility_no,
            d.source_id
        FROM
            device_camera dc, device d
        WHERE
          dc.lane_id = #{ykLaneId}
          AND dc.device_id = d.device_id
    </select>

    <resultMap type="com.bt.itsroad.device.domain.dto.BayonetHistoryDTO" id="BayonetHistoryMap">
        <result column="id" property="id"/>
        <result column="car_no" property="carno"/>
        <result column="lane_id" property="lane_id"/>
        <result column="entry_flag" property="entryFlag"/>
        <result column="collection_time" property="collection_time"/>
        <result column="front_big_img" property="front_big_img"/>
        <result column="body_big_img" property="bodyBigImg"/>
        <result column="car_models" property="carModels"/>
    </resultMap>

    <select id="selectList" parameterType="com.bt.itsroad.device.domain.dto.BayonetHistoryDTO" resultMap="BayonetHistoryMap">
        SELECT bh.car_no,bh.collection_time,dc.entry_flag
        FROM bayonet_history bh, device_camera dc, device d
        WHERE
            dc.lane_id = bh.lane_id
          AND dc.device_id = d.device_id
        <if test="facilityNo != null &amp;&amp; facilityNo != '' "> AND d.facility_no =#{facilityNo}</if>
        <if test="carno != null &amp;&amp; carno != '' "> AND bh.car_no LIKE CONCAT('%',#{carno},'%')</if>
        <if test="startTime != null">
            AND bh.collection_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND bh.collection_time &lt;= #{endTime}
        </if>
        ORDER BY collection_time DESC
    </select>

    <select id="recentBayonetHistory" parameterType="com.bt.itsroad.device.domain.dto.BayonetHistoryDTO" resultMap="BayonetHistoryMap">
        SELECT bh.car_no,bh.collection_time,dc.entry_flag
        FROM bayonet_history bh, device_camera dc, device d
        WHERE
        dc.lane_id = bh.lane_id
        AND dc.device_id = d.device_id
        <if test="facilityNo != null &amp;&amp; facilityNo != '' "> AND d.facility_no =#{facilityNo}</if>
        <if test="beginTime != null &amp;&amp; beginTime != '' "> AND bh.collection_time >= #{beginTime}</if>
        ORDER BY collection_time DESC LIMIT 10
    </select>

    <resultMap id="BayonetHistoryPresentMap" type="com.bt.itsroad.device.domain.dto.BayonetHistoryPresentDTO">
        <result column="id" property="id"/>
        <result column="car_no" property="carNo"/>
        <result column="facility_no" property="facilityNo"/>
        <result column="device_id" property="deviceId"/>
        <result column="exit_status" property="exitStatus"/>
        <result column="exit_time" property="exitTime"/>
        <result column="enter_time" property="enterTime"/>
    </resultMap>
    <select id="selectExit" resultMap="BayonetHistoryPresentMap">
        SELECT
            *
        FROM
            bayonet_history_present
        WHERE
            exit_status = 1
          AND ( enter_time >= #{startTime} OR exit_time >= #{startTime} )
          AND facility_no = #{facilityNo}
          AND auto_exit IS NULL
    </select>

    <resultMap id="BayonetHistoryVOMap" type="com.bt.itsroad.device.domain.vo.BayonetHistoryVO">
        <result column="id" property="id"/>
        <result column="car_no" property="carNo"/>
        <result column="exit_time" property="exitTime"/>
        <result column="enter_time" property="enterTime"/>
        <result column="facility_name" property="facilityName"/>
        <result column="danger_car" property="dangerCar"/>
        <result column="danger_class" property="dangerClass"/>
        <result column="car_models" property="carModels"/>
    </resultMap>

    <select id="selectPresentList" parameterType="com.bt.itsroad.device.domain.dto.BayonetHistoryDTO" resultMap="BayonetHistoryVOMap">
        SELECT b.*,f.facility_name FROM bayonet_history_present b, facility f WHERE b.facility_no = f.facility_no
        <if test="facilityNo != null &amp;&amp; facilityNo != '' "> AND b.facility_no =#{facilityNo}</if>
        <if test="carno != null &amp;&amp; carno != '' "> AND b.car_no LIKE CONCAT('%',#{carno},'%')</if>
        <if test="dangerCar != null"> AND b.danger_car =#{dangerCar}</if>
        <if test="beginTime != null">
            AND ( b.enter_time >= #{beginTime} OR b.exit_time >= #{beginTime} )
        </if>
        <if test="endTime != null">
            AND ( b.enter_time &lt;= #{endTime} OR b.exit_time &lt;= #{endTime} )
        </if>
        ORDER BY
        ( CASE WHEN b.enter_time IS NOT NULL THEN b.enter_time ELSE exit_time END ) DESC
    </select>


    <resultMap type="com.bt.itsroad.device.domain.dto.BayonetHistoryAttachDTO" id="AttachMap">
        <id column="id" property="id"/>
        <result column="file_name" property="fileName"/>
        <result column="disk_file_name" property="diskFileName"/>
        <result column="file_size" property="fileSize"/>
        <result column="content_type" property="contentType"/>
        <result column="digest" property="digest"/>
        <result column="disk_directory" property="diskDirectory"/>
        <result column="create_time" property="createTime"/>
        <result column="entry_flag" property="entryFlag"/>
    </resultMap>
    <select id="selectAttach" parameterType="com.bt.itsroad.device.domain.dto.BayonetHistoryDTO" resultMap="AttachMap">
        SELECT h.entry_flag,a.* FROM bayonet_history_attach a, bayonet_history h WHERE a.history_id = h.id AND a.history_id IN
        <foreach collection="historyIdList" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="selectSourceId" parameterType="com.bt.itsroad.device.domain.dto.BayonetHistoryDTO" resultType="String">
        SELECT d.source_id FROM bayonet_history_present b, device d WHERE b.id = #{id} AND b.device_id = d.device_id
    </select>

    <select id="selectEntry" parameterType="com.bt.itsroad.device.domain.dto.BayonetHistoryDTO" resultMap="BayonetHistoryMap">
        SELECT id,entry_flag FROM bayonet_history WHERE id IN
        <foreach collection="historyIdList" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="selectFrontImgUrls" resultType="String">
        SELECT front_big_img AS imgUrl FROM bayonet_history WHERE id = #{id}
    </select>

    <select id="selectBodyImgUrls" resultType="String">
        SELECT body_big_img AS imgUrl FROM bayonet_history WHERE id = #{id}
    </select>

    <select id="selectHistoryIdList" resultType="string">
        SELECT
            enter_id AS id
        FROM
            bayonet_history_present
        WHERE
            id = #{id}
        UNION
        SELECT
            exit_id AS id
        FROM
            bayonet_history_present
        WHERE
            id = #{id}
    </select>

    <select id="selectHistory" parameterType="com.bt.itsroad.device.domain.dto.BayonetHistoryDTO" resultMap="BayonetHistoryMap">
        SELECT * FROM bayonet_history WHERE id IN
        <foreach collection="historyIdList" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="selectLaneId" resultType="String">
        SELECT lane_id FROM device_camera_lane_id WHERE lane_id_factory = #{laneId}
    </select>

    <select id="selectBayonetLaneId" resultType="com.bt.itsroad.device.domain.vo.BayonetLaneIdVO">
        SELECT lane_id AS laneId, lane_id_factory AS laneIdFactory FROM device_camera_lane_id
    </select>

    <insert id="batchAddHistory" parameterType="com.bt.itsroad.device.domain.dto.BayonetHistoryDTO">
        INSERT INTO bayonet_history
        (id,
        car_no,
        front_big_img,
        collection_time,
        lane_id,
        update_time,
        danger_car,
        entry_flag,
        facility_no,
        upload_status,
        device_id,
        danger_class,
        push_status,
        body_big_img,
        car_models)
        VALUES
        <foreach collection="list" item="c" index="index" separator=",">
            (#{c.id},
            #{c.carno},
            #{c.front_big_img},
            #{c.collection_time},
            #{c.ykLaneId},
            #{c.update_time},
            #{c.dangerCar},
            #{c.entryFlag},
            #{c.facilityNo},
             0,
            #{c.deviceId},
            #{c.dangerClass},
             0,
            #{c.bodyBigImg},
            #{c.carModels})
        </foreach>
    </insert>
</mapper>