package com.bt.itsroad.alarm.domain.vo;
/**
 * 查询所有告警返回VO
 * <AUTHOR>
 *
 */
public class AlarmAllVO {
	private String id;//告警ID
	private String lng; //经度
	private String lat; //纬度
	private Integer type;//告警二级类型
	private String detail;//告警详情
    private String eventId; //事件id
	private Long createTime; //发生时间
    private String title; //标题
	private String milePost; //桩号
	private Integer handleStatus; //处理状态,是否处理(0:未处理,1:已确认,2:误报)
	private Long handleTime; //处理时间
	private Integer roadNo;//路段编号
	private String directionNo;//方向
	private String handleDesc;//处理描述
    private Integer alarmConclusion;//告警结论
    private String dealConclusion;//处置结论
    private String deviceId;
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getLng() {
		return lng;
	}
	public void setLng(String lng) {
		this.lng = lng;
	}
	public String getLat() {
		return lat;
	}
	public void setLat(String lat) {
		this.lat = lat;
	}
	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	public String getDetail() {
		return detail;
	}
	public void setDetail(String detail) {
		this.detail = detail;
	}
	public String getEventId() {
		return eventId;
	}
	public void setEventId(String eventId) {
		this.eventId = eventId;
	}
	public Long getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getMilePost() {
		return milePost;
	}
	public void setMilePost(String milePost) {
		this.milePost = milePost;
	}
	public Integer getHandleStatus() {
		return handleStatus;
	}
	public void setHandleStatus(Integer handleStatus) {
		this.handleStatus = handleStatus;
	}
	public Long getHandleTime() {
		return handleTime;
	}
	public void setHandleTime(Long handleTime) {
		this.handleTime = handleTime;
	}
	public Integer getRoadNo() {
		return roadNo;
	}
	public void setRoadNo(Integer roadNo) {
		this.roadNo = roadNo;
	}
	public String getDirectionNo() {
		return directionNo;
	}
	public void setDirectionNo(String directionNo) {
		this.directionNo = directionNo;
	}
	public String getHandleDesc() {
		return handleDesc;
	}
	public void setHandleDesc(String handleDesc) {
		this.handleDesc = handleDesc;
	}
	public Integer getAlarmConclusion() {
		return alarmConclusion;
	}
	public void setAlarmConclusion(Integer alarmConclusion) {
		this.alarmConclusion = alarmConclusion;
	}
	public String getDealConclusion() {
		return dealConclusion;
	}
	public void setDealConclusion(String dealConclusion) {
		this.dealConclusion = dealConclusion;
	}
	public String getDeviceId() {
		return deviceId;
	}
	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}
}
