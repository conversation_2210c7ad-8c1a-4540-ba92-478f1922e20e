package com.bt.itsroad.alarm.domain.dto;

public class AlarmMasterDTO {
	private String id; // 报警编号
	private Integer handleStatus;//处理状态,是否处理(0:未处理,1:已确认,2:误报，3：忽略)
	private Integer alarmConclusion; // 告警结论（1准确 2不准确 3无法判断）
	private String alarmEventId;
	private String handleMan;
	private Long createTime;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Integer getHandleStatus() {
		return handleStatus;
	}

	public void setHandleStatus(Integer handleStatus) {
		this.handleStatus = handleStatus;
	}

	public Integer getAlarmConclusion() {
		return alarmConclusion;
	}

	public void setAlarmConclusion(Integer alarmConclusion) {
		this.alarmConclusion = alarmConclusion;
	}

	public String getAlarmEventId() {
		return alarmEventId;
	}

	public void setAlarmEventId(String alarmEventId) {
		this.alarmEventId = alarmEventId;
	}

	public String getHandleMan() {
		return handleMan;
	}

	public void setHandleMan(String handleMan) {
		this.handleMan = handleMan;
	}

	public Long getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}
}
