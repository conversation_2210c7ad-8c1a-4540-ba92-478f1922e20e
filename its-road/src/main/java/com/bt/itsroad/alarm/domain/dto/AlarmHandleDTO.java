package com.bt.itsroad.alarm.domain.dto;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 
 * <AUTHOR>
 * @date 2021-03-24 10:26
 * @Description 告警单条处理参数
 */
public class AlarmHandleDTO {
	@NotBlank(message = "id告警编号（整型数字）不能为空")
	private String id;
	@NotNull(message = "告警发生时间不能为空")
	private Long createTime;
	private String handleDesc;//处理描述
	@Min(value = 0, message = "handleStatus不能小于0")
	@Max(value = 3, message = "handleStatus不能大于3")
	private Integer handleStatus;//处理状态,是否处理(0:未处理,1:已确认,2:误报，3：忽略)
	private String handleMan;//处理人员
    private String eventId; //事件ID
	private Integer alarmConclusion ; //告警结论
	private String dealConclusion;  //处置结论

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Long getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}

	public String getHandleDesc() {
		return handleDesc;
	}

	public void setHandleDesc(String handleDesc) {
		this.handleDesc = handleDesc;
	}

	public Integer getHandleStatus() {
		return handleStatus;
	}

	public void setHandleStatus(Integer handleStatus) {
		this.handleStatus = handleStatus;
	}

	public String getHandleMan() {
		return handleMan;
	}

	public void setHandleMan(String handleMan) {
		this.handleMan = handleMan;
	}

	public String getEventId() {
		return eventId;
	}

	public void setEventId(String eventId) {
		this.eventId = eventId;
	}

	public Integer getAlarmConclusion() {
		return alarmConclusion;
	}

	public void setAlarmConclusion(Integer alarmConclusion) {
		this.alarmConclusion = alarmConclusion;
	}

	public String getDealConclusion() {
		return dealConclusion;
	}

	public void setDealConclusion(String dealConclusion) {
		this.dealConclusion = dealConclusion;
	}
}
