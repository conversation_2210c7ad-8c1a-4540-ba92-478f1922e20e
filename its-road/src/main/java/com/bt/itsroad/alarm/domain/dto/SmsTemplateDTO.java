package com.bt.itsroad.alarm.domain.dto;

import javax.validation.constraints.NotBlank;

public class SmsTemplateDTO {
	private String id;
	@NotBlank(message = "mobile电话号码不能为空")
	private String mobile;
	private String person;//接收短信人员
	private String templateId;
	@NotBlank(message = "content短信内容不能为空")
	private String content;
	private String subAppend;
	private String eventNo;//事件编号
	private String processId;//进展编号
	private String sendId;//发送人账号
	private String sendName;//发送人姓名
	private String type;//短信通知大类型，event:事件，96333:96333，alarm:告警，system:系统
	private String subType;//短信通知子类型，online:9633在线短信,offline:96333离线短信
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getPerson() {
		return person;
	}
	public void setPerson(String person) {
		this.person = person;
	}
	public String getTemplateId() {
		return templateId;
	}
	public void setTemplateId(String templateId) {
		this.templateId = templateId;
	}
	public String getContent() {
		return content;
	}
	public void setContent(String content) {
		this.content = content;
	}
	public String getSubAppend() {
		return subAppend;
	}
	public void setSubAppend(String subAppend) {
		this.subAppend = subAppend;
	}
	public String getEventNo() {
		return eventNo;
	}
	public void setEventNo(String eventNo) {
		this.eventNo = eventNo;
	}
	public String getProcessId() {
		return processId;
	}
	public void setProcessId(String processId) {
		this.processId = processId;
	}
	public String getSendId() {
		return sendId;
	}
	public void setSendId(String sendId) {
		this.sendId = sendId;
	}
	public String getSendName() {
		return sendName;
	}
	public void setSendName(String sendName) {
		this.sendName = sendName;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getSubType() {
		return subType;
	}
	public void setSubType(String subType) {
		this.subType = subType;
	}
	
}
