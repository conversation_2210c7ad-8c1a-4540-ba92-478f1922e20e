package com.bt.itsroad.alarm.domain.dto;

/**
 * 描述：告警推送前端websocket配置
 *
 * <AUTHOR>
 * @since 2023-03-15 17:01
 */
public class AlarmConfigDTO {
    private String id;
    private Integer parentTypeId;
    private String alarmName;// 详细告警名称
    private String alarmValue;// 详细告警枚举值
    private Integer alarmConfigTime;// 告警推送websocket时间配置，单位为s（秒）
    private Long lastPushTime;// 最后推送websocket的修改时间戳，单位s
    private String creator;// 创建者
    private Long createTime;// 创建时间戳，单位s

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getParentTypeId() {
        return parentTypeId;
    }

    public void setParentTypeId(Integer parentTypeId) {
        this.parentTypeId = parentTypeId;
    }

    public String getAlarmName() {
        return alarmName;
    }

    public void setAlarmName(String alarmName) {
        this.alarmName = alarmName;
    }

    public String getAlarmValue() {
        return alarmValue;
    }

    public void setAlarmValue(String alarmValue) {
        this.alarmValue = alarmValue;
    }

    public Integer getAlarmConfigTime() {
        return alarmConfigTime;
    }

    public void setAlarmConfigTime(Integer alarmConfigTime) {
        this.alarmConfigTime = alarmConfigTime;
    }

    public Long getLastPushTime() {
        return lastPushTime;
    }

    public void setLastPushTime(Long lastPushTime) {
        this.lastPushTime = lastPushTime;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }
}
