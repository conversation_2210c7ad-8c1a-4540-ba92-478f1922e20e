package com.bt.itsroad.alarm.service;

import java.io.File;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.bt.itscore.config.OssConfig;
import com.bt.itscore.domain.dto.AlarmMessageDTO;
import com.bt.itscore.domain.dto.AttachDTO;
import com.bt.itscore.domain.dto.DictItemDTO;
import com.bt.itscore.domain.dto.EventDetectionStatDTO;
import com.bt.itscore.domain.dto.IdStringBatchDTO;
import com.bt.itscore.domain.dto.IdStringDTO;
import com.bt.itscore.domain.dto.PageDTO;
import com.bt.itscore.domain.dto.SmsTemplateDTO;
import com.bt.itscore.domain.dto.WdAlarmDTO;
import com.bt.itscore.domain.vo.DictItemVO;
import com.bt.itscore.domain.vo.FacilityVO;
import com.bt.itscore.domain.vo.MonthRoadStatVO;
import com.bt.itscore.domain.vo.ResponseVO;
import com.bt.itscore.domain.vo.UserSimpleVO;
import com.bt.itscore.enums.AlarmTypeEnum;
import com.bt.itscore.enums.OrgRoadEnum;
import com.bt.itscore.exception.FailException;
import com.bt.itscore.utils.AliyunSmsUtils;
import com.bt.itscore.utils.ExcelUtils;
import com.bt.itscore.utils.GsonUtils;
import com.bt.itscore.utils.HttpClientUtils;
import com.bt.itscore.utils.OssUtils;
import com.bt.itscore.utils.ServiceUtils;
import com.bt.itscore.utils.TimeUtils;
import com.bt.itsroad.alarm.constants.AlarmConstants;
import com.bt.itsroad.alarm.domain.dto.AlarmAttachDTO;
import com.bt.itsroad.alarm.domain.dto.AlarmBboxDTO;
import com.bt.itsroad.alarm.domain.dto.AlarmConfigDTO;
import com.bt.itsroad.alarm.domain.dto.AlarmDTO;
import com.bt.itsroad.alarm.domain.dto.AlarmHandleDTO;
import com.bt.itsroad.alarm.domain.dto.AlarmMasterDTO;
import com.bt.itsroad.alarm.domain.dto.AlarmTmpDTO;
import com.bt.itsroad.alarm.domain.dto.AlarmUserSmsDTO;
import com.bt.itsroad.alarm.domain.dto.KioskFacilityDTO;
import com.bt.itsroad.alarm.domain.dto.QueryAlarmDTO;
import com.bt.itsroad.alarm.domain.vo.AlarmAllVO;
import com.bt.itsroad.alarm.domain.vo.AlarmAttachVO;
import com.bt.itsroad.alarm.domain.vo.AlarmRadarCarVO;
import com.bt.itsroad.alarm.domain.vo.AlarmRadarEventVO;
import com.bt.itsroad.alarm.domain.vo.AlarmTypeVO;
import com.bt.itsroad.alarm.domain.vo.AlarmUnhandledVO;
import com.bt.itsroad.alarm.domain.vo.AlarmVO;
import com.bt.itsroad.alarm.domain.vo.EventDetectionStatVO;
import com.bt.itsroad.alarm.domain.vo.KioskDeviceVO;
import com.bt.itsroad.alarm.domain.vo.MonthStatVO;
import com.bt.itsroad.alarm.mapper.AlarmConfigMapper;
import com.bt.itsroad.alarm.mapper.AlarmMergeTmpMapper;
import com.bt.itsroad.alarm.mapper.AlarmUserSmsMapper;
import com.bt.itsroad.alarm.mapper.PedalAlarmFailMapper;
import com.bt.itsroad.alarm.mapper.PedalsAlarmMapper;
import com.bt.itsroad.device.domain.dto.DeviceDTO;
import com.bt.itsroad.device.domain.vo.DeviceVO;
import com.bt.itsroad.device.mapper.DeviceMapper;
import com.bt.itsroad.feign.ItsUserFeignClient;
import com.bt.itsroad.feign.ItsWebSocketFeignClient;
import com.bt.itsroad.sharding.mapper.AlarmMapper;
import com.bt.itsroad.tel.domain.vo.UserVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

@Service("alarmService")
@RefreshScope
public class AlarmService {
	private final static Logger LOGGER = LoggerFactory.getLogger(AlarmService.class);
	@Autowired
	AlarmMapper alarmMapper;
	@Autowired
	private AlarmMergeTmpMapper alarmMergeTmpMapper;
	@Autowired
	DeviceMapper deviceMapper;
	@Autowired
	private AlarmConfigMapper alarmConfigMapper;
	@Autowired
	private OssConfig ossConfig;
	@Autowired
	private ItsWebSocketFeignClient itsWebSocketFeignClient;

	@Autowired
	private PedalsAlarmMapper pedalsAlarmMapper;

	@Autowired
	private PedalAlarmFailMapper pedalAlarmFailMapper;

	@Autowired
	private ItsUserFeignClient itsUserFeignClient;

	@Autowired
	private AlarmUserSmsMapper alarmUserSmsMapper;

	@Autowired
	private RedisTemplate redisTemplate;
	@Autowired
	StringRedisTemplate stringRedisTemplate;

	@Resource(name = "pedalsExecutor")
	private ScheduledThreadPoolExecutor executor;
	// 默认值是false，也就是不开启测试模式（测试环境控制数据处理开关）
	@Value("${alarm.active.test.pedal:false}")
	private boolean pedalAlarmActiveTest;

	@Value("${file.export.path}")
	private String fileExportPath;

	@Value("${use.oss:true}")
	private String useOSS;
	@Value("${local.file.path:/home/<USER>/files/}")
	private String localFilePath;
	@Value("${alarm.receive.url:http://10.20.3.63:8666/receive/}")
	private String alarmReceiveUrl;
	@Value("${alarm.receive.timeout:25000}")
	private int alarmReceiveTimeout;
	@Value("${alarm.receive.switch:0}")
	private int alarmReceiveSWitch;
	// private String
	// fileExportPath="E:\\ykdownload\\";//告警文件导出路径**************调试完修改回去

	@Value("${yk.domain:https://yktest.gxits.cn:8763/s}")
	private String ykDomain;

	public PageInfo<AlarmVO> page(HttpServletRequest request, PageDTO pageDTO, AlarmDTO alarmDTO) {
		String roleStr = (String) request.getAttribute("role");
		if (StringUtils.isBlank(alarmDTO.getPtypeName())) {// 为空的时候，加上告警类型权限过滤
			List<String> roles = Arrays.asList(roleStr.split(";"));
			List<AlarmTypeVO> ownAlarmType = selectOwnAlarmType(roles);
			alarmDTO.setOwnAlarmType(ownAlarmType);
			if (CollectionUtils.isEmpty(ownAlarmType)) {
				PageInfo<AlarmVO> pageInfo = new PageInfo<>();
				pageInfo.setTotal(0);
				return pageInfo;
			}
		}

		// 查询count
		int total = alarmMapper.countAlarm(alarmDTO);
		if (total == 0) {
			return new PageInfo<>();
		}
		Integer page = pageDTO.getPage();
		Integer limit = pageDTO.getLimit();
		int offset = (page - 1) * limit;
		if (page * limit > total || limit > total) {
			limit = (int) (total % limit);
		}
		PageHelper.offsetPage(offset, limit, false);
//		 先查询出告警id
		List<AlarmVO> list = alarmMapper.selectList(alarmDTO);
		long startTime = System.currentTimeMillis() / 1000;
		long endTime = 0;
		for (AlarmVO alarmVO : list) {
			Long createTime = alarmVO.getCreateTime();
			if (createTime != null && createTime < startTime) {
				startTime = createTime;
			}
			if (createTime != null && createTime > endTime) {
				endTime = createTime;
			}
		}

		PageInfo<AlarmVO> pageInfo = new PageInfo<>();
		if (!CollectionUtils.isEmpty(list)) {
			Map<String, Object> map = new HashMap<>();
			map.put("startTime", startTime);
			map.put("endTime", endTime);

			List<String> ids = new ArrayList<>();
			for (AlarmVO alarmVO : list) {
				ids.add(alarmVO.getId());
			}
			map.put("ids", ids);
			// 然后通过id列表批量查询详情
			List<AlarmVO> alarmVOList = alarmMapper.selectDetailList(map);
//			for (AlarmVO alarmVO : alarmVOList) {
//				if (alarmVO.getAlarmEventId() != null) {
//					alarmVO.setDetail(alarmVO.getDetail() + "\n" + alarmVO.getAlarmEventId());
//				}
//			}
			// 基于告警id查询关联到的脚踏告警相关的所有摄像设备
			queryPedalAlarmDeviceList(alarmVOList);
			// 基于告警id查询关联到雷视融合告警对应的车辆
			/*
			 * String ptypeName = alarmDTO.getPtypeName(); if
			 * (StringUtils.isNotBlank(ptypeName) && "雷达告警类型".equals(ptypeName)) {
			 * queryAlarmRadarCarList(alarmVOList); }
			 */
			pageInfo.setList(alarmVOList);

			// 查询附件列表
			map.put("alarmIds", ids);
			List<AlarmVO> attachs = alarmMapper.selectAttachs(map);
			if (!CollectionUtils.isEmpty(attachs)) {
				Map<String, List<AlarmAttachVO>> attachMap = new HashMap<>();
				for (AlarmVO vo : attachs) {
					attachMap.put(vo.getId(), vo.getAttachs());
				}
				for (AlarmVO alarmVO : alarmVOList) {
					alarmVO.setAttachs(attachMap.get(alarmVO.getId()));
				}
			}

		}
		pageInfo.setTotal(total);
		return pageInfo;
	}

	public PageInfo<AlarmVO> pageMaster(HttpServletRequest request, PageDTO pageDTO, AlarmDTO alarmDTO) {
		String roleStr = (String) request.getAttribute("role");
		if (StringUtils.isBlank(alarmDTO.getPtypeName())) {// 为空的时候，加上告警类型权限过滤
			List<String> roles = Arrays.asList(roleStr.split(";"));
			List<AlarmTypeVO> ownAlarmType = selectOwnAlarmType(roles);
			alarmDTO.setOwnAlarmType(ownAlarmType);
			if (CollectionUtils.isEmpty(ownAlarmType)) {
				PageInfo<AlarmVO> pageInfo = new PageInfo<>();
				pageInfo.setTotal(0);
				return pageInfo;
			}
		}

		// 查询count
		int total = alarmMapper.countAlarmMaster(alarmDTO);
		if (total == 0) {
			return new PageInfo<>();
		}
		Integer page = pageDTO.getPage();
		Integer limit = pageDTO.getLimit();
		int offset = (page - 1) * limit;
		if (page * limit > total || limit > total) {
			limit = (int) (total % limit);
		}
		PageHelper.offsetPage(offset, limit, false);
//		 先查询出告警id
		List<AlarmVO> list = alarmMapper.selectMasterList(alarmDTO);
		long startTime = System.currentTimeMillis() / 1000;
		long endTime = 0;
		for (AlarmVO alarmVO : list) {
			Long createTime = alarmVO.getCreateTime();
			if (createTime != null && createTime < startTime) {
				startTime = createTime;
			}
			if (createTime != null && createTime > endTime) {
				endTime = createTime;
			}
		}

		PageInfo<AlarmVO> pageInfo = new PageInfo<>();
		if (!CollectionUtils.isEmpty(list)) {
			Map<String, Object> map = new HashMap<>();
			map.put("startTime", startTime);
			map.put("endTime", endTime);

			List<String> ids = new ArrayList<>();
			for (AlarmVO alarmVO : list) {
				ids.add(alarmVO.getId());
			}
			map.put("ids", ids);
			// 然后通过id列表批量查询详情
			List<AlarmVO> alarmVOList = alarmMapper.selectMasterDetailList(map);
			pageInfo.setList(alarmVOList);

		}
		pageInfo.setTotal(total);
		return pageInfo;
	}

	public List<AlarmVO> selectMasterAlarmList(AlarmDTO alarmDTO) {
		String alarmEventId = alarmDTO.getAlarmEventId();
		if (StringUtils.isBlank(alarmEventId)) {
			alarmDTO.setAlarmEventId(alarmDTO.getId());
		}
		Long createTime = alarmDTO.getCreateTime();
		alarmDTO.setEndTime(createTime + 7200);
		alarmDTO.setStartTime(createTime - 14400);
		List<AlarmVO> list = alarmMapper.selectMasterAlarmList(alarmDTO);
		// 查询附件列表
		if (!CollectionUtils.isEmpty(list)) {
			Map<String, Object> map = new HashMap<>();
			map.put("startTime", alarmDTO.getStartTime());
			map.put("endTime", alarmDTO.getEndTime());

			List<String> ids = new ArrayList<>();
			for (AlarmVO alarmVO : list) {
				ids.add(alarmVO.getId());
			}
			map.put("ids", ids);
			map.put("alarmIds", ids);
			List<AlarmVO> attachs = alarmMapper.selectAttachs(map);
			if (!CollectionUtils.isEmpty(attachs)) {
				Map<String, List<AlarmAttachVO>> attachMap = new HashMap<>();
				for (AlarmVO vo : attachs) {
					attachMap.put(vo.getId(), vo.getAttachs());
				}
				for (AlarmVO alarmVO : list) {
					alarmVO.setAttachs(attachMap.get(alarmVO.getId()));
				}
			}
		}
		return list;
	}

	private void queryAlarmRadarCarList(List<AlarmVO> resList) {
		if (resList.isEmpty()) {
			return;
		}
		List<String> ids = resList.stream().map(x -> x.getEventId()).collect(Collectors.toList());
		List<AlarmRadarCarVO> radarCarList = alarmMapper.selectAlarmRadarCar(ids);
		if (!radarCarList.isEmpty()) {
			Map<String, String> carMap = radarCarList.stream()
					.collect(Collectors.toMap(AlarmRadarCarVO::getEventId, AlarmRadarCarVO::getTargetId));
			resList.stream().forEach(item -> {
				String eventId = item.getEventId();
				// item.setTargetId(carMap.get(eventId));
			});
		}

	}

	@Transactional
	public boolean batchHandleMaster(HttpServletRequest request, List<AlarmMasterDTO> list) {
		String userId = (String) request.getAttribute("userId");
		if (StringUtils.isEmpty(userId)) {
			return false;
		}
		UserSimpleVO userSimpleVO = itsUserFeignClient.selectByUserId(userId);
		if (userSimpleVO == null) {
			LOGGER.error("batchHandleMaster - 用户不存在");
			return false;
		}
		AlarmMasterDTO alarmMasterDTO = list.get(0);
		String alarmEventId = alarmMasterDTO.getAlarmEventId();
		Long createTime = alarmMasterDTO.getCreateTime();
		AlarmDTO alarmDTO = new AlarmDTO();
		alarmDTO.setId(alarmMasterDTO.getId());
		alarmDTO.setCreateTime(createTime);
		alarmDTO.setAlarmEventId(alarmEventId);
		alarmDTO.setEndTime(createTime + 7200);
		alarmDTO.setStartTime(createTime - 14400);
		List<AlarmVO> alarmList = alarmMapper.selectMasterAlarmList(alarmDTO);
		Set<String> ids = new HashSet<>();
		for (AlarmVO alarmVO : alarmList) {
			Integer handleStatus = alarmVO.getHandleStatus();
			if (handleStatus == null || handleStatus == 0) {
				ids.add(alarmVO.getId());
			}
		}
		List<AlarmMasterDTO> params = new ArrayList<>();
		for (AlarmMasterDTO dto : list) {
			if (ids.contains(dto.getId())) {
				params.add(dto);
			}
		}
		String handleMan = userSimpleVO.getUserName();
		int rows = 0;
		if (params.size() >= ids.size()) {
			// 全部处理，更新master表的处理状态为已处理
			AlarmDTO updateAlarm = new AlarmDTO();
			updateAlarm.setAlarmEventId(alarmEventId);
			updateAlarm.setHandleStatus(3);
			updateAlarm.setHandleMan(handleMan);

			for (AlarmVO alarmVO : alarmList) {
				if (alarmVO.getHandleStatus() == 2) {
					updateAlarm.setHandleStatus(2);
					break;
				}
			}
			rows = alarmMapper.updateMaster2(updateAlarm);
		}
		for (AlarmMasterDTO alarmMasterDTO2 : params) {
			alarmMasterDTO2.setHandleMan(handleMan);
			rows += alarmMapper.handleMaster(alarmMasterDTO2);
		}

		// 已处理成功的告警，websocket异步推送到前端，前端不在播报
//		if (success) {
//			handleUpdateAlarmToWebsocket(idList, startTime, endTime);
//		}
		return rows > 0;
	}

	public boolean batchHandle(HttpServletRequest request, AlarmDTO alarmDTO) {
		String userId = (String) request.getAttribute("userId");
		if (StringUtils.isEmpty(userId)) {
			return false;
		}
		String ids = alarmDTO.getIds();
		String createTimes = alarmDTO.getCreateTimes();
		if (StringUtils.isEmpty(ids)) {
			return false;
		}
		if (StringUtils.isEmpty(createTimes)) {
			return false;
		}
		List<String> idList = Arrays.asList(ids.split(","));
		alarmDTO.setIdList(idList);
		String[] createTimeArr = createTimes.split(",");
		long startTime = System.currentTimeMillis() / 1000;
		long endTime = 0;
		for (String tmp : createTimeArr) {
			long createTime = NumberUtils.toLong(tmp);
			if (createTime < startTime) {
				startTime = createTime;
			}
			if (createTime > endTime) {
				endTime = createTime;
			}
		}
		alarmDTO.setStartTime(startTime);
		alarmDTO.setEndTime(endTime);

		boolean success = batchUpdateAlarm(alarmDTO, idList, userId);
		// 已处理成功的告警，websocket异步推送到前端，前端不在播报
		if (success) {
			handleUpdateAlarmToWebsocket(idList, startTime, endTime);
		}
		return success;
	}

	@Transactional
	public boolean batchUpdateAlarm(AlarmDTO alarmDTO, List<String> idList, String userId) {
		List<AlarmDTO> tmpAlarmList = alarmMergeTmpMapper.selectAlarmByIdAndUserId(idList, userId);
		if (!org.springframework.util.CollectionUtils.isEmpty(tmpAlarmList)) {
			boolean updateTmp = alarmMergeTmpMapper.batchHandle(alarmDTO);
			LOGGER.info("Update tmp alarm data size is {}", updateTmp);
		}
		return alarmMapper.batchHandle(alarmDTO);
	}

	@Transactional
	@CacheEvict(value = "its-road", key = "'alarm_'+#alarmHandleDTO.id")
	public boolean handle(HttpServletRequest request, AlarmHandleDTO alarmHandleDTO) {
		String userId = (String) request.getAttribute("userId");
		String id = alarmHandleDTO.getId();
		if (StringUtils.isEmpty(id) || StringUtils.isEmpty(userId)) {
			return false;
		}
		List<String> list = Collections.singletonList(id);
		boolean success = updateAlarm(alarmHandleDTO, userId, list);

		Integer handleStatus = alarmHandleDTO.getHandleStatus();
		AlarmDTO dto = new AlarmDTO();
		dto.setId(id);
		dto.setCreateTime(alarmHandleDTO.getCreateTime());
		dto.setHandleStatus(handleStatus);
		dto.setEventId(alarmHandleDTO.getEventId());
		AlarmVO alarmVO = alarmMapper.selectById(dto);
		if (alarmVO == null) {
			return false;
		}
		updateMaster(alarmVO, dto);
		// 已处理成功的告警，websocket异步推送到前端，前端不在播报
		if (success) {
			handleUpdateAlarmToWebsocket(list, alarmHandleDTO.getCreateTime() - 1, alarmHandleDTO.getCreateTime() + 1);
		}
		return success;
	}

	private void updateMaster(AlarmVO alarmVO, AlarmDTO dto) {
		String id = alarmVO.getId();
		String alarmEventId = alarmVO.getAlarmEventId();
		Long createTime = alarmVO.getCreateTime();
		if (id.equals(alarmEventId)) {
			alarmMapper.updateMaster(dto);
		} else {
			dto.setAlarmEventId(alarmEventId);
			dto.setEndTime(createTime + 7200);
			dto.setStartTime(createTime - 14400);
			List<AlarmVO> alarmList = alarmMapper.selectMasterAlarmList(dto);
			boolean all = true;
			for (AlarmVO vo : alarmList) {
				if (id.equals(vo.getId())) {
					continue;
				}
				if (vo.getHandleStatus() == null || vo.getHandleStatus() == 0) {
					all = false;
				} else if (vo.getHandleStatus() == 2) {
					dto.setHandleStatus(2);
				}
			}
			if (all) {
				alarmMapper.updateMaster(dto);
			}
		}
	}

	@Transactional
	public boolean updateAlarm(AlarmHandleDTO alarmHandleDTO, String userId, List<String> list) {
		List<AlarmDTO> tmpAlarmList = alarmMergeTmpMapper.selectAlarmByIdAndUserId(list, userId);
		if (!org.springframework.util.CollectionUtils.isEmpty(tmpAlarmList)) {
			boolean updateTmp = alarmMergeTmpMapper.handle(alarmHandleDTO);
			LOGGER.info("Update tmp alarm data size is {}", updateTmp);
		}
		LOGGER.info("处理告警》》》： {}", alarmHandleDTO.getId());
		return alarmMapper.handle(alarmHandleDTO);
	}

	private void handleUpdateAlarmToWebsocket(List<String> idList, Long startTime, Long endTime) {
		Map<String, Object> map = new HashMap<>();
		map.put("idList", idList);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		List<AlarmVO> alarmVOList = alarmMapper.selectByIdList(map);
		if (org.springframework.util.CollectionUtils.isEmpty(alarmVOList)) {
			return;
		}
		alarmVOList.forEach(this::asyncSendUpdateAlarmToWebsocket);
	}

	public PageInfo<AlarmVO> pageDangerous(PageDTO pageDTO, AlarmDTO alarmDTO) {
		PageHelper.startPage(pageDTO.getPage(), pageDTO.getLimit());
		List<AlarmVO> list = alarmMapper.selectDangerous(alarmDTO);
		PageInfo<AlarmVO> pageInfo = new PageInfo<>(list);
		return pageInfo;
	}

	private boolean validAlarmData(AlarmDTO dto) {
		if (dto == null) {
			return false;
		}
		int type = NumberUtils.toInt(dto.getType(), -1);

		String detail = dto.getDetail();
		// 告警类型校验
		AlarmTypeEnum alarmTypeEnum = AlarmTypeEnum.getName(type);
		if (alarmTypeEnum == null) {
			LOGGER.error("alarm type 【{}】 invalid, please check AlarmTypeEnum,device id:{},detail:{}", type, dto.getDeviceId(),
					detail);
			return false;
		}
		// 重复告警过滤校验
		AlarmVO alarmVO = alarmMapper.selectById(dto);
		if (alarmVO != null) {
			LOGGER.error("The alarm is repeated: {}", GsonUtils.beanToJson(dto));
			return false;
		}
		return true;
	}

	private String assembleDeviceId(AlarmDTO dto) {
		String deviceId = dto.getDeviceId();
		String cameraCode = dto.getCameraCode();
		if (StringUtils.isBlank(deviceId) && StringUtils.isNotBlank(cameraCode)) {
			LOGGER.info("The pedal alarm data: {}", GsonUtils.beanToJson(dto));
			deviceId = deviceMapper.selectByCameraCode(cameraCode);
		}
		deviceId = (StringUtils.isBlank(deviceId) ? "" : deviceId);
		deviceId = deviceId.trim().replaceAll("\r|\n", "");
		return deviceId;
	}

	boolean assembleByType(int type, String deviceId, AlarmDTO dto) {
		LOGGER.info("addAlarm id{},type{}", dto.getId(), type);
		boolean end = false;
		if (type == AlarmTypeEnum.TYPE_50.getIndex()) {//边坡预警
			// 通过deviceId（边坡工号ID）查询边坡相关信息 facility_slope_r
			assembleSlopeData(dto, deviceId);
		} else {
			assembleAlarmData(dto, deviceId, type, dto.getTypeName());// 非重复，则开始封装告警数据
		}
		if (type >= AlarmConstants.PEDALS_TYPE_MIN && type <= AlarmConstants.PEDALS_TYPE_MAX) {
			boolean isSuccess = validParamsAndFilter(dto, deviceId);
			if (!isSuccess) {// 失败：校验异常或者过滤掉的告警放入到失败表中
				return true;
			}
			// 正常的脚踏告警，封装测试模拟参数
			handlePedalTestAlarm(dto, deviceId);
			if (!AlarmConstants.ALARM_DEAL_CONCLUSION_MSG.equals(dto.getDealConclusion())) {
				LOGGER.info("The pedal alarm is not in test mode.");
				dto.setDetail(dto.getDetail() + "(异常告警)");
			}
		}
		if (StringUtils.isBlank(dto.getAlarmEventId())) {
			dto.setAlarmEventId(dto.getId());
		}
		if (type == AlarmTypeEnum.TYPE_704.getIndex()) {
			dto.setHandleStatus(3);
			dto.setHandleMan("系统");
		}
		return end;
	}

	private boolean aiDeal(int type, String deviceId, AlarmDTO dto) {
		DeviceVO device = deviceMapper.selectDeivceByDeviceId(deviceId);
		Integer sourceId = device.getSourceId();
		String id = dto.getId();
		LOGGER.info("aiDeal - id{},device{},sourceId{}", id, deviceId, sourceId);
		boolean aiDeal = false;
		String res = null;
		if (sourceId != null && sourceId > 6 && sourceId < 10) {
			if (alarmReceiveSWitch == 1) {
				res = requestReceive(type, dto);
				aiDeal = true;
			}
			LOGGER.info("aiDeal - id{},res{}", id, res);
			if (StringUtils.isBlank(res)) {
				return aiDeal;
			}
			try {
				// 算法组返回告警事件ID、告警描述、事件等级
				JsonObject jo = JsonParser.parseString(res).getAsJsonObject();
				String alarmEventId = jo.get("alarmEventId").getAsString();
				int alarmLevel = jo.get("alarmLevel").getAsInt();
				String alarmDesc = jo.get("alarmDesc").getAsString();
				String alarmId = jo.get("alarmId").getAsString();
				long alarmCreateTime = jo.get("alarmCreateTime").getAsLong();
				String alarmType = jo.get("alarmType").getAsString();
				LOGGER.info("alarmId:{},alarmCreateTime:{},alarmEventId:{},alarmLevel:{},alarmType:{},alarmDesc:{}",
						alarmId, alarmCreateTime, alarmEventId, alarmLevel, alarmType, alarmDesc);
				dto.setAlarmType(alarmType);
				if (StringUtils.isNotBlank(alarmEventId)) {
					dto.setAlarmEventId(alarmEventId);
					if (type > 700 && type < 800) {
						dto.setAlarmEventId("7-" + alarmEventId);
					}
				} else {
					dto.setAlarmEventId(id);
				}
				if (StringUtils.isNotBlank(alarmDesc)) {
					dto.setDetail(alarmDesc);
				}
				dto.setAlarmLevel(alarmLevel);
			} catch (Exception e) {
				dto.setAlarmEventId(id);
			}
		}
		return aiDeal;
	}
	private AlarmDTO updateMasterParam(AlarmDTO dto, AlarmVO master) {
		AlarmDTO updateAlarm = new AlarmDTO();
		updateAlarm.setHandleStatus(0);
		Integer oldAlarmLevel = master.getAlarmLevel();
		Integer alarmLevel = dto.getAlarmLevel();
		if (alarmLevel != null) {
			if (oldAlarmLevel == null) {
				oldAlarmLevel = -1;
			}
			if (alarmLevel < oldAlarmLevel) {
				// 更新等级
				updateAlarm.setAlarmLevel(alarmLevel);
				updateAlarm.setType(dto.getType());
			}
		}
		updateAlarm.setId(master.getId());
		updateAlarm.setCreateTime(master.getCreateTime());
		updateAlarm.setEndTime(dto.getCreateTime());
		LOGGER.info("addAlarm - updateAlarm:{}", GsonUtils.beanToJson(updateAlarm));
		return updateAlarm;
	}

	@Transactional
	public boolean add(AlarmDTO dto) {
		LOGGER.info("addAlarm source AlarmDTO: {}", GsonUtils.beanToJson(dto));
		// 1. 校验数据
		if (!validAlarmData(dto)) {
			return false;
		}
		int type = NumberUtils.toInt(dto.getType());
		String id = dto.getId();
		String deviceId = assembleDeviceId(dto);
		String detail = dto.getDetail();
		AlarmTypeEnum alarmTypeEnum = AlarmTypeEnum.getName(type);
		String typeName = alarmTypeEnum.getName();
		dto.setTypeName(typeName);

		// 2. 判断是哪种类型告警
		boolean end = assembleByType(type, deviceId, dto);
		if (end) {
			return true;
		}

		boolean success = false;
		if (type <= 32 || (type > 700 && type < 800 && type != 704 && type != 710)) {// 视频事件告警
			boolean aiDealed = aiDeal(type, deviceId, dto);
			if (!aiDealed) {
				// TODO 通过云控平台规则合并处理
				mergeAlarm(dto);
			}
			success = alarmMapper.add(dto) > 0;
			LOGGER.info("addAlarm - id:{}, success:{}, type:{}", id, success, type);
			// 查询主表master中是否存在alarmEventId的告警，存在更新处理状态和等级（等级升级的情况下更新）
			AlarmVO master = alarmMapper.selectMasterByAlarmEventId(dto);
			if (master == null) {
				alarmMapper.addMaster(dto);
			} else {
				AlarmDTO updateAlarm = updateMasterParam(dto, master);
				alarmMapper.updateMaster(updateAlarm);
			}
		} else {
			// TODO 通过云控平台规则合并处理
			mergeAlarm(dto);
			success = alarmMapper.add(dto) > 0;
			LOGGER.info("addAlarm - id:{}, success:{}, type:{}", id, success, type);
			if (type != AlarmTypeEnum.TYPE_704.getIndex()) {
				alarmMapper.addMaster(dto);
			}
		}

		alarmWebsocketPush(dto, type, alarmTypeEnum.getPname(), detail);
		LOGGER.info("addAlarm - alarmWebsocketPush finish id:{}", id);
		return true;
	}

	private void mergeAlarm(AlarmDTO dto) {
		// 从master表查询（30分钟内）同设备告警是否有相同类型告警  deviceId
		
	}

	private void assembleSlopeData(AlarmDTO dto, String deviceId) {
		DeviceVO vo = deviceMapper.selectSlopeByDeviceId(deviceId);
		if (vo != null) {
			dto.setFacilityNo(vo.getFacilityNo());
			dto.setRoadNo(vo.getRoadNo());
			dto.setRoadName(vo.getRoadName());
			dto.setDirectionNo(vo.getDirectionNo());
			dto.setDirectionName(vo.getDirectionName());
			dto.setLng(vo.getLng());
			dto.setLat(vo.getLat());
			dto.setMilePost(vo.getMilePost());
			dto.setFacilityName(vo.getFacilityName());
			dto.setRoadShortName(vo.getRoadShortName());
		}
	}

	private String requestReceive(int type, AlarmDTO dto) {
		String id = dto.getId();
		AlarmBboxDTO alarmBboxDTO = new AlarmBboxDTO();
		alarmBboxDTO.setId(id);
		alarmBboxDTO.setDevice_id(dto.getDeviceId());
		alarmBboxDTO.setType(type);
		alarmBboxDTO.setCreate_time(dto.getCreateTime());
		AlarmAttachDTO alarmAttachDTO = new AlarmAttachDTO();
		alarmAttachDTO.setAlarmId(id);
		alarmAttachDTO.setCreateTime(dto.getCreateTime());
		AlarmAttachVO attach = alarmMapper.selectAttachImg(alarmAttachDTO);
		if (attach == null) {
			LOGGER.info("alarmMapper.add attach is null, alarmId:{}, factory:{}", id, dto.getFactory());
			return null;
		}
		AttachDTO attachDTO = new AttachDTO();
		attachDTO.setDiskFileName(attach.getDiskFileName());
		AttachDTO anOssUrl = OssUtils.getAnOssUrl(ossConfig, attachDTO);
		if (anOssUrl != null) {
			alarmBboxDTO.setImg_url(anOssUrl.getUrl());
		}
		if (dto.getLeft() == null) {
			dto.setLeft(0);
		}
		if (dto.getTop() == null) {
			dto.setTop(0);
		}
		if (dto.getRight() == null) {
			dto.setRight(0);
		}
		if (dto.getBottom() == null) {
			dto.setBottom(0);
		}
		if (dto.getFactory() == null) {
			dto.setFactory(0);
		}
		alarmBboxDTO.setBbox_left(dto.getLeft());
		alarmBboxDTO.setBbox_right(dto.getRight());
		alarmBboxDTO.setBbox_top(dto.getTop());
		alarmBboxDTO.setBbox_bottom(dto.getBottom());
		alarmBboxDTO.setRoad_no(dto.getRoadNo());
		alarmBboxDTO.setMile_post(dto.getMilePost());
		alarmBboxDTO.setFactory(dto.getFactory());
		LOGGER.info("alarmMapper.add alarmBboxDTO,{}", GsonUtils.beanToJson(alarmBboxDTO));
//		String res = HttpClientUtils.post(5000, "http://10.20.3.63:8666/receive/", null, GsonUtils.beanToJson(alarmBboxDTO));
		String res = HttpClientUtils.post(alarmReceiveTimeout, alarmReceiveUrl, null,
				GsonUtils.beanToJson(alarmBboxDTO));
		LOGGER.info("alarmMapper.add receive:{}", res);
		return res;
	}

	public static void main(String[] args) {
		ResponseVO jsonToBean = null;
		try {
			jsonToBean = GsonUtils.jsonToBean("{\"code2\":1}", ResponseVO.class);
			System.out.println(GsonUtils.beanToJson(jsonToBean));
			System.out.println(jsonToBean.getCode());
			JsonObject jsonObject = JsonParser.parseString("{\"code2\":1}").getAsJsonObject();
			System.out.println(jsonObject.get("code"));
			System.out.println(jsonObject.get("code2"));
			int type = 701;
		} catch (Exception e) {
			System.out.println(jsonToBean);
		}
	}

	private void alarmWebsocketPush(AlarmDTO dto, int type, String pname,
			String detail) {
		// 新增成功，则异步线程推送websocket,推送detail保持原始状态的信息
		boolean isPedalAlarm = (type >= AlarmConstants.PEDALS_TYPE_MIN && type <= AlarmConstants.PEDALS_TYPE_MAX);
		boolean isPedalTestAlarm = isPedalAlarm
				&& AlarmConstants.ALARM_DEAL_CONCLUSION_MSG.equals(dto.getDealConclusion());
		boolean notPushVideoAlarm = (type == AlarmTypeEnum.TYPE_16.getIndex()
				|| type == AlarmTypeEnum.TYPE_17.getIndex() || type == AlarmTypeEnum.TYPE_704.getIndex());
		// 脚踏测试及“路障”、“施工”类型不推送
		if (!isPedalTestAlarm && !notPushVideoAlarm) {
			asyncSendWebsocket(dto, type, pname, detail);
		}
	}

	private boolean validParamsAndFilter(AlarmDTO dto, String deviceId) {
		if (StringUtils.isEmpty(deviceId)) {
			// 脚踏告警未携带设备id，告警无法关联收费站亭，将异常数据入库失败表alarm_pedal_fail
			int add = pedalAlarmFailMapper.add(dto);
			if (add != 0) {
				LOGGER.warn("Insert the error pedal alarm into the fail table,the size: {}.The alarm: {}", add,
						GsonUtils.beanToJson(dto));
			}
			return false;
		}
		// 基于deviceId查询收费站亭，为空，则过滤掉（推送到失败表即可）
		Integer count = pedalsAlarmMapper.existCameraByDeviceId(deviceId);
		if (count == 0) {
			// 支持数据库中已配置的收费站相关脚踏告警
			int add = pedalAlarmFailMapper.add(dto);
			if (add != 0) {
				LOGGER.warn("Insert the non pilot pedal alarm into the fail table,the size: {}.The alarm: {}", add,
						GsonUtils.beanToJson(dto));
			}
			return false;
		}
		// 脚踏告警重复（5s内）过滤处理
		Integer notFilter = pedalAlarmFailMapper.selectNotFilter(deviceId);
		if (notFilter == null) {
			LOGGER.info("当前设备需要过滤5s内重复脚踏告警数据！");
			ZoneId zoneId = ZoneId.systemDefault();
			LocalDateTime now = LocalDateTime.now();
			ZoneOffset nowOffset = zoneId.getRules().getOffset(now);
			Instant nowInstant = now.toInstant(nowOffset);
			long reportTime = nowInstant.getEpochSecond();
			LocalDateTime before = now.plusSeconds(-5);
			ZoneOffset beforeOffset = zoneId.getRules().getOffset(before);
			Instant beforeInstant = before.toInstant(beforeOffset);
			long fiveSecondBefore = beforeInstant.getEpochSecond();
			Integer sameCount = alarmMapper.selectPedalSameCount(deviceId, reportTime, fiveSecondBefore);
			if (sameCount != null && sameCount > 0) {
				LOGGER.info("当前设备在五秒内有重复的脚踏告警告警！");
				int add = pedalAlarmFailMapper.add(dto);
				if (add != 0) {
					LOGGER.warn("Insert the non pilot pedal alarm into the fail table,the size: {}.The alarm: {}", add,
							GsonUtils.beanToJson(dto));
				}
				return false;
			}
		}
		return true;
	}

	private void handlePedalTestAlarm(AlarmDTO dto, String deviceId) {
		// 动态开关，如果该值是true，则全部认为脚踏告警默认置成测试数据
		if (pedalAlarmActiveTest) {
			assemblePedalTestAlarmConclusion(dto, null);
			return;
		}
		Long createTime = dto.getCreateTime();
		Long reportTime = dto.getReportTime();
		long difTime = 0;
		if (createTime != null && reportTime != null) {
			difTime = reportTime - createTime;
		}
		// 默认这一分钟之内的，都属于模拟测试，直到解除状态
		// 视频组时间与inner节点时间不一致(误差超过1min)，则以inner节点时间为主（若是网络时延超过1min的，无法判断，只能当成非测试模式处理）
		if (Math.abs(difTime) > 0 && Math.abs(difTime) > AlarmConstants.ALARM_PEDAL_TEST_DURATION) {
			createTime = null;
		}
		// 查询小程序测试记录数据，校验数据以及启动时间的参数
		KioskFacilityDTO testRecord = pedalsAlarmMapper.selectTestLogByAlarmDevice(createTime, reportTime, deviceId);
		if (testRecord == null) { // 无记录，说明不是测试
			LOGGER.info("The pedal alarm test record is empty. alarmId: {}", dto.getId());
			return;
		}
		// 测试启动时间
		Long startTime = testRecord.getStartTime();
		// 结束测试的时间
		Long endTime = testRecord.getEndTime();
		// 时间校验，优先匹配inner节点上报的时间，不符合的话，再匹配告警原始时间
		long verifyReportTimeSt = -1;
		long verifyReportTimeEd = 0;
		if (reportTime != null && startTime != null) {
			verifyReportTimeSt = reportTime - startTime;
			if (endTime != null) {// 测试已完成状态
				verifyReportTimeEd = endTime - reportTime;
			}
		}

		long verifyCreateTimeSt = -1;
		long verifyCreateTimeEd = 0;
		if (createTime != null && startTime != null) {
			verifyCreateTimeSt = createTime - startTime;
			if (endTime != null) { // 测试已完成状态
				verifyCreateTimeEd = endTime - createTime;
			}
		}
		if (verifyReportTimeEd < 0 && verifyCreateTimeEd < 0) {
			LOGGER.info("The pedal alarm report and create time is not bellow the test end time. alarmId: {}",
					dto.getId());
			return;
		}
		Integer status = testRecord.getTestStatus();
		boolean isTestMode = false;
		if (status != null) {
			if (status == 1) {
				// 测试中,最长支持60s区间
				isTestMode = (verifyReportTimeSt >= 0 && verifyReportTimeSt <= AlarmConstants.ALARM_PEDAL_TEST_DURATION)
						|| (verifyCreateTimeSt >= 0 && verifyCreateTimeSt <= AlarmConstants.ALARM_PEDAL_TEST_DURATION);
			}
			if (status == 2) {
				// 测试完成，在区间时间内自动置成测试模式，区间外，都是按照正常的告警流程处理
				isTestMode = (verifyReportTimeSt >= 0 && verifyReportTimeEd >= 0)
						|| (verifyCreateTimeSt >= 0 && verifyCreateTimeEd >= 0);
			}
		}
		if (isTestMode) {
			assemblePedalTestAlarmConclusion(dto, testRecord);
		}
	}

	private static void assemblePedalTestAlarmConclusion(AlarmDTO dto, KioskFacilityDTO testRecord) {
		// 处理模拟测试
		dto.setDetail(dto.getDetail() + "(测试模式)");
		// 自动设置结论
		dto.setAlarmConclusion(1);// 准确
		dto.setHandleStatus(3);// 忽略
		dto.setDealConclusion(AlarmConstants.ALARM_DEAL_CONCLUSION_MSG);
		dto.setHandleMan(testRecord != null ? testRecord.getTestMan() : null);
		dto.setHandleTime(System.currentTimeMillis() / 1000);
	}

	private void asyncSendWebsocket(AlarmDTO dto, int type, String pTypeName, String detail) {
		new Thread(new Runnable() {
			@Override
			public void run() {
				LOGGER.info("告警websocket推送--------------------");
				AlarmMessageDTO messageDTO = new AlarmMessageDTO();
				messageDTO.setId(dto.getId());
				messageDTO.setDetail(detail);
				messageDTO.setType(type + "");
				messageDTO.setFacilityNo(dto.getFacilityNo());
				messageDTO.setCreateTime(dto.getCreateTime());
				String orgName = "";
				String alarmTypeName = "";
				String typeName = dto.getTypeName();
				if (typeName == null) {
					alarmTypeName = "";
				} else {
					alarmTypeName = "“" + typeName + "”";
				}
				Integer roadNo = dto.getRoadNo();
				if (roadNo != null) {
					orgName = OrgRoadEnum.getName(roadNo);
					if (orgName != null) {
						orgName = orgName + "，";
					} else {
						orgName = "";
					}
				}
				// 检查告警上次推送的时间，未超过预制阈值，则不推送播报
				if (isBelowAlarmLimitTime(dto, pTypeName)) {
					LOGGER.info(
							"If the preset threshold is not exceeded, the broadcast will not be pushed.The alarm id: {}",
							dto.getId());
					return;
				} else {
					dto.setPushStatus(1);
					alarmMapper.updateAlarm(dto);
				}
				generateVoicePrompt(messageDTO, dto, orgName, alarmTypeName, pTypeName);
				itsWebSocketFeignClient.pushAlarm(messageDTO);
				// 如果是ups告警，推送的同时需要进行短信推送，短信推送依赖角色，配了ups告警推送的角色用户，才会被短信通知
				sendAlarmSms(dto, typeName, detail, type);
			}
		}).start();
	}

	private void sendAlarmSms(AlarmDTO dto, String typeName, String detail, int type) {
		// 属于ups告警才会短信通知
		if (type != AlarmTypeEnum.TYPE_854.getIndex()) {
			LOGGER.info("当前告警类型：{}，非ups告警（854）", typeName);
			return;
		}
		IdStringDTO idStringDTO = new IdStringDTO(AlarmConstants.PUSH_UPS_ALARM_MESSAGE_ROLE_ID);
		Map<String, String> headParam = ServiceUtils.getInnerLoginHead();
		String result = HttpClientUtils.post(ykDomain + "/its-user/user/selectByRoleId", headParam,
				new Gson().toJson(idStringDTO));
		if (StringUtils.isBlank(result)) {
			LOGGER.warn("当前ups告警短信推送角色未配置用户，result：{}", result);
			return;
		}
		List<UserVO> userVOList = JSONObject.parseArray(result, UserVO.class);
		if (CollectionUtils.isEmpty(userVOList)) {
			LOGGER.warn("当前ups告警短信推送角色未配置用户");
			return;
		}
		// 权限校验并发送短信
		List<AlarmUserSmsDTO> userSmsDTOList = checkPermissionAndSendSmsMsg(dto, typeName, detail, userVOList);
		// 短信发送记录（批量插入）
		if (CollectionUtils.isEmpty(userSmsDTOList)) {
			LOGGER.warn("当前短信告警通知集合为空");
			return;
		}
		int batchInsert = alarmUserSmsMapper.batchInsert(userSmsDTOList);
		LOGGER.info("批量插入短信告警通知的数量：{}，总共的数量：{}", batchInsert, userSmsDTOList.size());
	}

	private List<AlarmUserSmsDTO> checkPermissionAndSendSmsMsg(AlarmDTO dto, String typeName, String detail,
			List<UserVO> userVOList) {
		List<AlarmUserSmsDTO> userSmsDTOList = new ArrayList<>();
		for (UserVO userVO : userVOList) {
			// 检查是否有设施权限
			if (hasNoFacilityPermission(dto, userVO)) {
				LOGGER.info("该用户无对应的电力设施权限，userId: {}", userVO.getUserId());
				continue;
			}
			String mobile = userVO.getMobile();
			if (StringUtils.isBlank(mobile) || mobile.length() != 11 || !mobile.startsWith("1")) {
				LOGGER.warn("手机号格式校验异常：{}", mobile);
				continue;
			}
			SmsTemplateDTO smsTemplateDTO = new SmsTemplateDTO();
			smsTemplateDTO.setTemplateId(stringRedisTemplate.opsForValue().get("its-event:SmsTemplateCode-UPS_ALARM"));
			smsTemplateDTO.setContent(smsTemplateDTO.createUpsAlarmContent(
					TimeUtils.getTimeString(new Date(dto.getCreateTime() * 1000), TimeUtils.FULL_TIME_3),
					dto.getRoadName() + dto.getMilePost() + dto.getFacilityName(), typeName + "，" + detail));
			smsTemplateDTO.setMobile(mobile);
			boolean sendAnSms = AliyunSmsUtils.sendAnSms(smsTemplateDTO);
			if (!sendAnSms) {
				LOGGER.error("短信发送失败：{}", new Gson().toJson(smsTemplateDTO));
			}
			// 暂存到集合中
			AlarmUserSmsDTO userSmsDTO = new AlarmUserSmsDTO();
			userSmsDTO.setUserId(userVO.getUserId());
			userSmsDTO.setUserName(userVO.getUserName());
			userSmsDTO.setMobile(userVO.getMobile());
			userSmsDTO.setBizId(smsTemplateDTO.getBizId());
			userSmsDTO.setAlarmId(dto.getId());
			userSmsDTO.setSmsSendStatus(smsTemplateDTO.getRet());
			userSmsDTO.setSmsFailReason(smsTemplateDTO.getFailReason());
			userSmsDTOList.add(userSmsDTO);
		}
		return userSmsDTOList;
	}

	private boolean hasNoFacilityPermission(AlarmDTO dto, UserVO userVO) {
		String roleIds = (String) redisTemplate.opsForValue().get(userVO.getUserId() + "-role");
		if (StringUtils.isBlank(roleIds)) {
			LOGGER.warn("该用户未配置角色权限，用户id：{}", userVO.getUserId());
			return true;
		}
		String[] roleIdList = roleIds.split(";");
		if (roleIdList == null || roleIdList.length == 0) {
			LOGGER.warn("该用户角色解析异常，用户id：{}", userVO.getUserId());
			return true;
		}
		StringBuilder facilitySb = new StringBuilder();
		for (String roleId : roleIdList) {
			if (StringUtils.isNotBlank(roleId)) {
				String facility = (String) redisTemplate.opsForValue().get(roleId + "-facility");
				if (StringUtils.isNotBlank(facility)) {
					facilitySb.append(facility);
					facilitySb.append(";");
				}
			}
		}
		String facilityList = facilitySb.toString();
		if (StringUtils.isBlank(facilityList) || !facilityList.contains(dto.getFacilityNo())) {
			LOGGER.warn("该用户角色下绑定的设施未配置，用户id：{}", userVO.getUserId());
			return true;
		}
		return false;
	}

	private void assembleAlarmData(AlarmDTO dto, String deviceId, int type, String typeName) {
		String detail = dto.getDetail();
		String sourceId = dto.getValue();
		Long reportTime = dto.getReportTime();
		if (reportTime == null || reportTime <= 0) {
			dto.setReportTime(System.currentTimeMillis() / 1000);
		}
		if (StringUtils.isNotBlank(deviceId)) {
			DeviceVO vo = new DeviceVO();
			DeviceDTO deviceDTO = new DeviceDTO();

			if (type == AlarmConstants.BAYONET_TYPE) {// 高清卡口
				deviceDTO.setLaneId(deviceId);
				vo = deviceMapper.selectByLaneId(deviceDTO);// 厂家设备参数获取智慧高速云控平台设备信息
			} else {// 其余告警检测
				deviceDTO.setSourceId(NumberUtils.toInt(sourceId));
				deviceDTO.setDeviceId(deviceId);
				vo = deviceMapper.selectDeviceById(deviceDTO);
			}
			if (vo != null) {
				dto.setDeviceId(vo.getDeviceId());
				dto.setFacilityNo(vo.getFacilityNo());
				dto.setRoadNo(vo.getRoadNo());
				dto.setRoadName(vo.getRoadName());
				dto.setDirectionNo(vo.getDirectionNo());
				dto.setDirectionName(vo.getDirectionName());
				dto.setLng(vo.getLng());
				dto.setLat(vo.getLat());
				dto.setMilePost(vo.getMilePost());
				dto.setFacilityName(vo.getFacilityName());
				dto.setRoadShortName(vo.getRoadShortName());
				String roadName = StringUtils.trimToEmpty(vo.getRoadName());
				String milePost = StringUtils.trimToEmpty(vo.getMilePost());
				String facilityName = StringUtils.trimToEmpty(vo.getFacilityName());
				String directionName = StringUtils.trimToEmpty(vo.getDirectionName());
				String deviceShortName = StringUtils.trimToEmpty(vo.getDeviceShortName());
				String deviceName = StringUtils.trimToEmpty(vo.getDeviceName());
				if (StringUtils.isBlank(roadName)) {
					LOGGER.warn("设备信息可能有问题：路段为空，sourceId:{}，deviceId:{}，type:{}，detail:{}", sourceId, deviceId, type,
							detail);
				}
				if (type > AlarmConstants.POWER_TYPE_MIN && type < AlarmConstants.POWER_TYPE_MAX) {
					if (type == AlarmTypeEnum.TYPE_855.getIndex()) {
						// 电气火灾告警
						dto.setDetail(roadName + directionName + milePost + facilityName + "，" + detail + "，请及时处理。");
					} else {
						// 电力监控，告警内容拼接规则：路段+桩号+电力设施名称，告警类型，告警详情
						dto.setDetail(roadName + milePost + facilityName + "，" + typeName + "，" + detail);
					}
				} else if (type > AlarmConstants.LIGHTPROT_TYPE_MIN && type < AlarmConstants.LIGHTPROT_TYPE_MAX) {
					// 防雷设备告警
					dto.setDetail(roadName + directionName + milePost + "，" + detail);
				} else if (type > AlarmConstants.PEDALS_TYPE_MIN && type < AlarmConstants.PEDALS_TYPE_MAX) {
					// 脚踏告警
					dto.setDetail(roadName + facilityName + " " + deviceShortName + detail);
				} else if (type > AlarmConstants.VISIBILITY_TYPE_MIN && type < AlarmConstants.VISIBILITY_TYPE_MAX) {
					// 能见度告警
					dto.setDetail("能见度告警：" + roadName + milePost + deviceName + "能见度" + dto.getVisibilityValue()
							+ "m，低于能见度告警阈值，请关注实时气象状况");
				} else if (type == AlarmTypeEnum.TYPE_603.getIndex()) {
					// 服务区危化品车辆停留超时告警
					dto.setDetail("危化品车辆停留超时告警：" + vo.getRoadName() + vo.getFacilityName() + "，疑似出现危化品车辆停留超时，车牌号码："
							+ dto.getCarNo() + "，运输介质：" + dto.getDangerClass() + "，请关注。");
				} else if (type == AlarmTypeEnum.TYPE_604.getIndex()) {
					// 服务区危化品车辆在场饱和告警
					dto.setDetail("危化品车辆在场饱和告警：" + vo.getRoadName() + vo.getFacilityName() + "，当前危化品车辆在场数量"
							+ dto.getValue() + "辆，请核实并关注。");
				} else if (type == AlarmTypeEnum.TYPE_855.getIndex()) {
					// 电气火灾告警
					dto.setDetail(roadName + directionName + milePost + detail + "，请及时处理。");
				} else {
					dto.setDetail(roadName + facilityName + directionName + milePost + detail);
				}
			} else {
				LOGGER.warn("没查询到设备，deviceId:{}，type:{}，detail:{}", deviceId, type, detail);
			}
		} else {
			if (StringUtils.isNotBlank(dto.getFacilityNo()) && type == AlarmTypeEnum.TYPE_854.getIndex()
					&& StringUtils.isNotBlank(detail) && detail.contains(AlarmConstants.UPS_ALARM_POWER_BACKUP_LABEL)) {
				// 针对ups告警，且属于市电断开判断9002
				FacilityVO facilityVO = deviceMapper.selectFacilityById(dto.getFacilityNo());
				if (facilityVO == null) {
					LOGGER.warn("未查询到对应的电力设施，facilityNo: {}", dto.getFacilityNo());
					return;
				}
				dto.setFacilityNo(facilityVO.getFacilityNo());
				dto.setRoadNo(facilityVO.getRoadNo());
				dto.setRoadName(facilityVO.getRoadName());
				dto.setRoadShortName(facilityVO.getRoadAlias());
				dto.setLng(facilityVO.getLng());
				dto.setLat(facilityVO.getLat());
				dto.setMilePost(facilityVO.getMilePost());
				dto.setFacilityName(facilityVO.getFacilityName());
				dto.setDetail(facilityVO.getRoadName() + facilityVO.getMilePost() + facilityVO.getFacilityName() + "，"
						+ typeName + "，" + detail);
			}
		}
	}

	private boolean isBelowAlarmLimitTime(AlarmDTO dto, String pTypeName) {
		if (AlarmTypeEnum.ALARM_REMIND_CONFIG_TYPE_SET.contains(pTypeName)) {
			String parentTypeName = alarmMapper.findParentTypeNameByFuzz(pTypeName);
			if (StringUtils.isEmpty(parentTypeName)) {
				LOGGER.error("The alarm parent type name is empty.");
				return true;
			}
			String deviceId = dto.getDeviceId();
			String type = dto.getType();
			if (StringUtils.isEmpty(type)) {
				LOGGER.error("The type of alarm is empty.type:{}", type);
				return true;
			}
			AlarmVO alarmVO = null;
			long endTime = System.currentTimeMillis() / 1000 + 1800;
			long startTime = endTime - 86400;
			Map<String, Object> map = new HashMap<>();
			map.put("ptypeName", parentTypeName);
			map.put("type", type);
			map.put("startTime", startTime);
			map.put("endTime", endTime);
			String facilityNo = dto.getFacilityNo();
			if (AlarmTypeEnum.TYPE_854.getIndex() == Integer.parseInt(type) && StringUtils.isBlank(dto.getDeviceId())
					&& StringUtils.isNotBlank(facilityNo)
					&& dto.getDetail().contains(AlarmConstants.UPS_ALARM_POWER_BACKUP_LABEL)) {
				map.put("facilityNo", facilityNo);
				map.put("detail", AlarmConstants.UPS_ALARM_POWER_BACKUP_LABEL);
				alarmVO = alarmMapper.selectLastAlarmByFacilityAndType(map);
			} else {
				if (StringUtils.isBlank(deviceId)) {
					LOGGER.error("The device of alarm is empty.deviceId:{}", deviceId);
					return true;
				}
				map.put("deviceId", deviceId);
				alarmVO = alarmMapper.selectLastAlarmByDeviceAndType(map);
			}
			if (alarmVO == null) {
				LOGGER.info("The alarm is first time occur.");
				return false;
			}
			AlarmConfigDTO alarmConfigDTO = alarmConfigMapper.selectAlarmConfigByValue(type);
			if (alarmConfigDTO == null || alarmConfigDTO.getAlarmConfigTime() == null) {
				LOGGER.error("the alarm config is empty.");
				return true;
			}
			long currentTime = dto.getCreateTime();
			Long lastTime = alarmVO.getCreateTime();
			if (lastTime != null && currentTime - lastTime <= alarmConfigDTO.getAlarmConfigTime()) {
				LOGGER.info("The video event alarm is below limit time,should not need to push alarm to websocket.");
				return true;
			} else {
				LOGGER.info("Push the alarm to websocket.");
				return false;
			}
		}
		return false;
	}

	public boolean attachAdd(AlarmAttachDTO dto) {
		AlarmAttachVO vo = alarmMapper.selectAttach(dto);
		if (vo == null) {
			alarmMapper.attachAdd(dto);
		}
		return true;
	}

	public List<AttachDTO> ossUrl(List<AttachDTO> dtos) {
		if ("true".equals(useOSS)) {
			return OssUtils.getUrl(ossConfig, dtos);
		}
		if (CollectionUtils.isEmpty(dtos)) {
			return dtos;
		}
		for (AttachDTO attachDTO : dtos) {
			attachDTO.setUrl(localFilePath + attachDTO.getDiskFileName());
		}
		return dtos;
	}

	@Transactional
	public boolean reback(AlarmDTO dto) {
		alarmMapper.reback(dto);
		alarmMapper.rebackMaster(dto);
		return true;
	}

	// 当天告警总数+上月同比
	@SuppressWarnings("rawtypes")
	public Map countEventTotal(Map<String, Object> todayMap, Map<String, Object> lastMothDayMap) {
		Integer totalEvents = alarmMapper.countAlertTotal(todayMap);
		Integer lastMothEvents = alarmMapper.countAlertTotal(lastMothDayMap);
		Map<String, Object> countMap = new HashMap<>();
		countMap.put("alarmCount", totalEvents);
		Object index = "";
		if (lastMothEvents == 0) {
			index = 0;
		} else if (totalEvents - lastMothEvents == 0) {
			index = 0;
		} else {
			index = ((totalEvents - lastMothEvents) / (double) lastMothEvents) * 100;
		}
		countMap.put("index", index);
		return countMap;
	}

	// 为了业务合理，只查询1个月内的所有告警，并限制10000条
	public List<AlarmAllVO> selectAll(AlarmDTO alarmDTO) {
		alarmDTO.setCreateTime(TimeUtils.beforeMonths(1).getTime() / 1000);
		return alarmMapper.selectAll(alarmDTO);
	}

	@Cacheable(value = "its-road", key = "'alarm_'+#alarmDTO.id", unless = "#result == null")
	public AlarmVO selectById(AlarmDTO alarmDTO) {
		String id = alarmDTO.getId();
		LOGGER.info("数据库查询告警》》》：{}", id);
		AlarmVO alarmVO = alarmMapper.selectById(alarmDTO);
		if (alarmVO == null) {
			return null;
		}
		String typeStr = alarmVO.getType();
		if (NumberUtils.isNumber(typeStr)) {
			int type = Integer.parseInt(typeStr);
			if (type >= AlarmConstants.PEDALS_TYPE_MIN && type <= AlarmConstants.PEDALS_TYPE_MAX) {
				alarmDTO.setDeviceId(alarmVO.getDeviceId());
				List<KioskDeviceVO> selectAlarmDevice = pedalsAlarmMapper.selectAlarmDevice(alarmDTO);
				alarmVO.setKioskDeviceList(selectAlarmDevice);
			}
		}
		return alarmVO;
	}

	/**
	 * 根据视频事件告警、天气预警组装不同的语音播报提示
	 */
	private void generateVoicePrompt(AlarmMessageDTO messageDTO, AlarmDTO dto, String orgName, String alarmTypeName,
			String pTypeName) {
		String roadShortName = dto.getRoadShortName() != null ? dto.getRoadShortName() : "";
		switch (pTypeName) {
		case AlarmTypeEnum.VIDEO_EVENT_ALARM:
			// 视频事件告警
//				String directionName1 = dto.getDirectionName() != null ? dto.getDirectionName() : "";
//				if (directionName1.contains("上行")) {
//					directionName1 = "上行";
//				} else if (directionName1.contains("下行")) {
//					directionName1 = "下行";
//				} else if (directionName1.contains("双向")) {
//					directionName1 = "双向";
//				}
			String milePost1 = dto.getMilePost() != null ? dto.getMilePost() : "";
//				String videoVp = orgName + roadShortName1 + "，" + directionName1 + "，" +
//						milePost1 + "，" + "有" + alarmTypeName + "事件";
			String videoVp = orgName + roadShortName + "，" + milePost1 + "，" + "有" + alarmTypeName + "事件";
			messageDTO.setVoicePrompt(videoVp);
			break;
		case AlarmTypeEnum.WEATHER_EVENT_ALARM:
			// 天气预警
			String weatherVp = orgName + roadShortName + "，" + "有" + alarmTypeName + "天气预警";
			messageDTO.setVoicePrompt(weatherVp);
			break;
		case AlarmTypeEnum.PEDALS_EVENT_ALARM:
			// 脚踏告警
			String facilityName = dto.getFacilityName() != null ? "，" + dto.getFacilityName() : "";
			String pedalsVp = orgName + roadShortName + facilityName + "，有" + alarmTypeName;
			messageDTO.setVoicePrompt(pedalsVp);
			break;
		case AlarmTypeEnum.UPS_ALARM:
			// UPS告警
			String upsFacilityName = dto.getFacilityName() != null ? "，" + dto.getFacilityName() : "";
			String milePost = dto.getMilePost() != null ? dto.getMilePost() : "";
			String upsVp = orgName + roadShortName + upsFacilityName + "," + milePost + "，有" + alarmTypeName;
			messageDTO.setVoicePrompt(upsVp);
			break;
		case AlarmTypeEnum.BLOCKING_ALARM:
			// 拥堵告警
			messageDTO.setVoicePrompt(orgName + "，有" + pTypeName);
			break;
		default:
			messageDTO.setVoicePrompt(orgName + "有" + alarmTypeName + pTypeName);
		}

		LOGGER.info("voicePrompt:{}", messageDTO.getVoicePrompt());
	}

	public boolean updateAlarm(AlarmDTO alarmDTO) {
		int updateAlarm = alarmMapper.updateAlarm(alarmDTO);
		return updateAlarm > 0;
	}

	private void asyncSendUpdateAlarmToWebsocket(AlarmVO dto) {
		executor.execute(() -> {
			LOGGER.info("更新告警websocket推送--------------------");
			AlarmMessageDTO messageDTO = new AlarmMessageDTO();
			messageDTO.setId(dto.getId());
			messageDTO.setDetail(dto.getDetail());
			messageDTO.setType(dto.getType());
			messageDTO.setFacilityNo(dto.getFacilityNo());
			messageDTO.setWebsocketType(AlarmConstants.ALARM_PEDAL_WEBSOCKET_TYPE);
			messageDTO.setCreateTime(dto.getCreateTime());
			itsWebSocketFeignClient.pushAlarm(messageDTO);
		});
	}

	public PageInfo<AlarmVO> oneClickMerge(HttpServletRequest request, PageDTO pageDTO, AlarmDTO alarmDTO) {
		String userId = (String) request.getAttribute("userId");
		alarmDTO.setHandleStatus(0);
		Integer page = pageDTO.getPage();
		Integer limit = pageDTO.getLimit();
		if (page == null || page <= 0) {
			pageDTO.setPage(1);
		}
		if (limit == null || limit < 20) { // 默认20条分页
			pageDTO.setLimit(20);
		}
		PageInfo<AlarmVO> pageInfo = new PageInfo<>();
		// 告警类型都支持一键合并
		String pTypeName = alarmDTO.getPtypeName();
		if (StringUtils.isEmpty(pTypeName)) {
			return pageInfo;
		}
		String parentFullName = alarmMapper.findParentTypeNameByFuzz(pTypeName);
		alarmDTO.setPtypeName(parentFullName);

		Integer isMerge = alarmDTO.getIsMerge();
		if (isMerge != null && isMerge == 1) {// 查询原始数据表并批量插入到tmp表中
			mergeDataAndInsertTmpTable(alarmDTO, userId);
			queryAlarmsByTmpTable(pageDTO, alarmDTO, pageInfo, userId);
		} else {
			queryAlarmsByTmpTable(pageDTO, alarmDTO, pageInfo, userId);
		}
		List<AlarmVO> list = pageInfo.getList();
		if (!CollectionUtils.isEmpty(list)) {
			// 查询附件列表
			long startTime = System.currentTimeMillis() / 1000;
			long endTime = 0;
			List<String> alarmIds = new ArrayList<>();
			for (AlarmVO alarmVO : list) {
				Long createTime = alarmVO.getCreateTime();
				if (createTime != null && startTime > createTime) {
					startTime = createTime;
				}
				if (createTime != null && endTime < createTime) {
					endTime = createTime;
				}
				alarmIds.add(alarmVO.getId());
			}
			Map<String, Object> map = new HashMap<>();
			map.put("startTime", startTime);
			map.put("endTime", endTime);
			map.put("alarmIds", alarmIds);
			List<AlarmVO> attachs = alarmMapper.selectAttachs(map);
			if (!CollectionUtils.isEmpty(attachs)) {
				Map<String, List<AlarmAttachVO>> attachMap = new HashMap<>();
				for (AlarmVO vo : attachs) {
					attachMap.put(vo.getId(), vo.getAttachs());
				}
				for (AlarmVO alarmVO : list) {
					alarmVO.setAttachs(attachMap.get(alarmVO.getId()));
				}
			}
		}

		return pageInfo;
	}

	@Transactional
	public void mergeDataAndInsertTmpTable(AlarmDTO alarmDTO, String userId) {
		// 先清空历史数据
		int deleteSize = alarmMergeTmpMapper.deleteByUserId(userId);
		LOGGER.info("Number of deleted items：{}", deleteSize);
		// 封装大于size条的查询条件，此处是2
		List<QueryAlarmDTO> paramsList = getQueryAlarmParams(alarmDTO, 2, userId);
		if (CollectionUtils.isEmpty(paramsList)) {
			LOGGER.info("封装deviceId和type分组参数，结果参数集合为：0条");
			return;
		}

		if (paramsList.size() > AlarmConstants.MAX_ALARM_DEVICE_TYPE_LIMIT) {
			paramsList = paramsList.subList(0, AlarmConstants.MAX_ALARM_DEVICE_TYPE_LIMIT);
		}

		// 统计总数量
		List<AlarmTmpDTO> totalIds = alarmMapper.countByStatusAndDeviceAndType(alarmDTO, paramsList);
		int countTotal = totalIds.size();
		LOGGER.info("Total number of statistics：{}", countTotal);
		AtomicInteger sort = new AtomicInteger(0);
		handleTmpAlarmOrder(totalIds, sort);
		int size = alarmMergeTmpMapper.batchAdd(totalIds, userId);

		LOGGER.info("Total number of inserted items：{}", size);
	}

	private void handleTmpAlarmOrder(List<AlarmTmpDTO> resultList, AtomicInteger sort) {
		for (AlarmTmpDTO dto : resultList) {
			dto.setSort(sort.addAndGet(1));
		}
	}

	private void queryAlarmsByTmpTable(PageDTO pageDTO, AlarmDTO alarmDTO, PageInfo<AlarmVO> pageInfo, String userId) {
		// 封装大于size条的查询条件，此处是1
		List<QueryAlarmDTO> paramsList = getQueryAlarmParams(alarmDTO, 1, userId);
		// 统计总数量
		int offset = (pageDTO.getPage() - 1) * pageDTO.getLimit();
		int countTotal = alarmMergeTmpMapper.countByStatusAndDeviceAndType(alarmDTO, paramsList, userId);
		List<AlarmVO> resList = alarmMergeTmpMapper.findByStatusAndDeviceAndType(offset, pageDTO.getLimit(), alarmDTO,
				paramsList, userId);
		queryPedalAlarmDeviceList(resList);
		pageInfo.setTotal(countTotal);
		pageInfo.setList(resList);
	}

	private void queryPedalAlarmDeviceList(List<AlarmVO> resList) {
		if (org.springframework.util.CollectionUtils.isEmpty(resList)) {
			return;
		}
		resList.forEach(item -> {
			String type = item.getType();
			if (!StringUtils.isEmpty(type) && NumberUtils.isDigits(type)) {
				int typeInt = Integer.parseInt(type);
				if (typeInt >= AlarmConstants.PEDALS_TYPE_MIN && typeInt <= AlarmConstants.PEDALS_TYPE_MAX) {
					AlarmDTO dto = new AlarmDTO();
					dto.setId(item.getId());
					dto.setCreateTime(item.getCreateTime());
					dto.setDeviceId(item.getDeviceId());
					item.setKioskDeviceList(pedalsAlarmMapper.selectAlarmDevice(dto));
				}
			}
		});
	}

	private List<QueryAlarmDTO> getQueryAlarmParams(AlarmDTO alarmDTO, int size, String userId) {
		List<AlarmVO> alarmVOList = null;
		if (size == 1) {
			alarmVOList = alarmMergeTmpMapper.selectAlarmType(alarmDTO, userId);
		} else {
			alarmVOList = alarmMapper.selectAlarmType(alarmDTO);
		}
		List<QueryAlarmDTO> list = new ArrayList<>();
		alarmVOList.forEach(item -> {
			// 合并条数大于设置的值
			if (item.getLocalCount() != null && item.getLocalCount() >= size) {
				QueryAlarmDTO queryAlarmDTO = new QueryAlarmDTO();
				queryAlarmDTO.setDeviceId(item.getDeviceId());
				queryAlarmDTO.setType(item.getType());
				list.add(queryAlarmDTO);
			}
		});
		return list;
	}

	public List<AlarmUnhandledVO> unhandledAlarm(AlarmDTO alarmDTO) {
		alarmDTO.setStartTime(TimeUtils.monthLong(-1));
		alarmDTO.setEndTime(System.currentTimeMillis() / 1000);
		List<AlarmUnhandledVO> list = alarmMapper.selectUnhandledAlarmNum(alarmDTO);
		if (CollectionUtils.isEmpty(list)) {
			return list;
		}
		Map<String, Object> map = new HashMap<>();
		map.put("ptypeNames", alarmDTO.getPtypeNames());
		map.put("types", list);
		List<AlarmUnhandledVO> unhandledAlarmType = alarmMapper.unhandledAlarmType(map);

		if (CollectionUtils.isEmpty(unhandledAlarmType)) {
			return unhandledAlarmType;
		}
		List<AlarmUnhandledVO> ret = new ArrayList<>();
		Map<String, AlarmUnhandledVO> alarmUnhandledMap = new HashMap<>();
		for (AlarmUnhandledVO alarmUnhandledVO : list) {
			alarmUnhandledMap.put(alarmUnhandledVO.getType() + "", alarmUnhandledVO);
		}

		unhandledAlarmType.stream()
				.collect(Collectors.groupingBy(AlarmUnhandledVO::getPtypeName, LinkedHashMap::new, Collectors.toList()))
				.forEach((ptypeName, ptypeNamelist) -> {
					AlarmUnhandledVO vo = new AlarmUnhandledVO();
					vo.setPtypeName(ptypeName);
					int count = 0;
					for (AlarmUnhandledVO tmp : ptypeNamelist) {
						Integer unhandledCount = alarmUnhandledMap.get(tmp.getType() + "").getUnhandledCount();
						unhandledCount = (unhandledCount == null ? 0 : unhandledCount);
						count += unhandledCount;
					}
					vo.setUnhandledCount(count);
					ret.add(vo);
				});

		return ret;
	}

	// 告警列表导出为Excel
	public void exportAlarmList(AlarmDTO dto, HttpServletRequest request, HttpServletResponse response) {
		String roleStr = (String) request.getAttribute("role");
		// 0.限制导出时间为一个月内
		Calendar calendar = Calendar.getInstance();
		// 如果 endTime 和 startTime 的时间间隔超过一个月，则抛出异常
		calendar.setTimeInMillis(dto.getEndTime() * 1000);
		calendar.add(Calendar.MONTH, -1); // 1个月前
		Long threeMonthsAgo = calendar.getTimeInMillis() / 1000;
		if (dto.getStartTime() < threeMonthsAgo) {
			LOGGER.info("exportAlarmList导出时间间隔不得超出一个月");
			throw new FailException("导出时间间隔不得超出一个月");
		}

		// 1.首先根据条件查询告警id
		LOGGER.info("exportAlarm开始查询告警信息");
		List<AlarmVO> alarmlist = alarmMapper.selectList(dto);
		// 1.1通过id查询告警详情
		if (!CollectionUtils.isEmpty(alarmlist)) {
			IdStringBatchDTO idBatch = new IdStringBatchDTO();
			List<String> ids = new ArrayList<>();
			for (AlarmVO alarmVO : alarmlist) {
				ids.add(alarmVO.getId());
			}
			idBatch.setIds(ids);
			Map<String, Object> map = new HashMap<>();
			map.put("startTime", dto.getStartTime());
			map.put("endTime", dto.getEndTime());
			map.put("ids", ids);
			// 1.2然后通过id列表批量查询告警详情
			List<AlarmVO> alarmVOList = alarmMapper.selectDetailList(map);
			LOGGER.info("exportAlarm完成查询告警信息");
			// 查询当前该类告警的字典项
			Map<String, String> alarmTypeMap = new HashMap<>();
			DictItemDTO alarmTypeQueryDto = new DictItemDTO();
			// alarmTypeQueryDto.setTypeId(typeId);
			alarmTypeQueryDto.setTypeName(dto.getPtypeName());
			List<DictItemVO> alarmTypeItemList = itsUserFeignClient.selectDictItem(alarmTypeQueryDto);
			// 缓存到hashmap中
			for (DictItemVO dictItemVO : alarmTypeItemList) {
				alarmTypeMap.put(dictItemVO.getValue(), dictItemVO.getName());
			}
			LOGGER.info("exportAlarm开始生成Excel");
			// 2.构造Excel的基础文件结构
			String[] titles = { "序号", "告警类型", "发生时间", "告警位置", "告警详情", "处理状态", "处理方式", "处理时间", "处理人", "处置结论", "告警结论" };
			// 3.生成文件名

			String fileName = dto.getPtypeName().substring(0, dto.getPtypeName().length() - 2); // 除去"类型"
			String excelTitle = fileName;

			if (dto.getStartTime() != null && dto.getEndTime() != null) {
				// 查询条件包含时间
				fileName = fileName + "-" + TimeUtils.getTimeString(TimeUtils.DATE_TIME_6, dto.getStartTime() * 1000)
						+ "至" + TimeUtils.getTimeString(TimeUtils.DATE_TIME_6, dto.getEndTime() * 1000);
				fileName = fileName.replaceAll(" ", "");
				excelTitle = fileName;
			}
			String filePath = fileExportPath + File.separator + fileName + "##" + UUID.randomUUID().toString()
					+ "%%.xls"; // 文件路径
			LOGGER.info("文件路径：" + filePath);
			ExcelUtils tool = new ExcelUtils();
			int cellWidth[] = { 15, 15, 15, 30, 50, 15, 15, 15, 15, 15, 50 };
			tool.init(filePath, "sheet1");
			tool.writeTitle(excelTitle, 0, 0, 10, 0); // 标题
			tool.writeHeadStr(titles, cellWidth, 1);

			// 写入各行数据
			List<String> rowData = new ArrayList<String>();
			int size = alarmVOList.size();
			if (size == 0) {
				return;
			}
			for (int i = 0; i < size; i++) {
				AlarmVO alarmVO = alarmVOList.get(i);
				rowData.clear();

				rowData.add(i + 1 + ""); // 序号
				String alarmtypename = alarmTypeMap.get(alarmVO.getType()); // 根据value找name
				rowData.add(alarmtypename); // 告警类型
				rowData.add(TimeUtils.getTimeString(TimeUtils.FULL_TIME, alarmVO.getCreateTime() * 1000)); // 发生时间
				String alarmPlace = alarmVO.getRoadName() + alarmVO.getDirectionName() + alarmVO.getMilePost();
				rowData.add(alarmPlace); // 告警位置
				rowData.add(alarmVO.getDetail()); // 告警详情
				// 处理状态
				String handlestatus = "";

				int handleway = alarmVO.getHandleStatus();
				String handlewayStr = "";
				if (handleway == 0) {
					handlestatus = "未处理";
					handlewayStr = "--";
				} else {
					handlestatus = "已处理";

					if (handleway == 2) {
						handlewayStr = "升级事件";
					} else if (handleway == 3) {
						handlewayStr = "忽略";
					}

				}
				rowData.add(handlestatus); // 处理状态
				rowData.add(handlewayStr); // 处理方式

				if (alarmVO.getHandleStatus() != 0 && alarmVO.getHandleTime() != null) { // 不是未处理且处理时间非空
					rowData.add(TimeUtils.getTimeString(TimeUtils.FULL_TIME, alarmVO.getHandleTime() * 1000)); // 处理时间
				} else {
					rowData.add("--");
				}
				if (alarmVO.getHandleMan() != null) {
					rowData.add(alarmVO.getHandleMan()); // 处理人
				} else {
					rowData.add("--"); // 处理人
				}
				if (alarmVO.getDealConclusion() != null) {
					rowData.add(alarmVO.getDealConclusion()); // 处置结论
				} else {
					rowData.add("--");
				}

				String alarmconclusionStr = "--";
				if (alarmVO.getAlarmConclusion() != null) {
					int alarmconclusion = alarmVO.getAlarmConclusion();
					if (alarmconclusion == 1) {
						alarmconclusionStr = "准确";
					} else if (alarmconclusion == 2) {
						alarmconclusionStr = "不准确";
					} else if (alarmconclusion == 3) {
						alarmconclusionStr = "无法判断";
					}
				}
				rowData.add(alarmconclusionStr); // 告警结论

				tool.writeContentRow(rowData, 2 + i); // 写入一行数据
			}

			tool.writeBook(false); // 写入整个Excel表
			LOGGER.info("完成单个告警excel写入");
			tool.downloadFile(filePath, response);
			ExcelUtils.deleteFile(filePath);
			return;
		} else {
			LOGGER.info("[exportExcel]没有需要导出的告警");
			return; // 查询为空
		}

	}

	public boolean wdAlarmAdd(WdAlarmDTO dto) {
		if (dto == null) {
			return false;
		}
		LOGGER.info("气象告警数据：{}", GsonUtils.beanToJson(dto));
		// 重复告警过滤校验
		WdAlarmDTO wdAlarmDTO = alarmMapper.selectWdAlarm(dto.getId());
		if (wdAlarmDTO != null) {
			LOGGER.error("气象告警数据重复: {}", GsonUtils.beanToJson(dto));
			return false;
		}
		// 新增告警数据
		boolean res = alarmMapper.wdAlarmAdd(dto) > 0;
		// 能见度告警推送
		if (dto.getType() != null && dto.getType() == 2) {
			LocalDateTime now = LocalDateTime.now();
			LocalDateTime before = now.minusHours(1);
			long startTime = before.toInstant(ZoneOffset.of("+8")).getEpochSecond();
			long endTime = now.toInstant(ZoneOffset.of("+8")).getEpochSecond();
			int visiAlarmCount = alarmMapper.visiAlarmCount(startTime, endTime, dto.getDeviceId());
			// 如果一小时内没有重复的能见度告警，推送至云控
			if (visiAlarmCount == 0) {
				LOGGER.info("推送该能见度告警，id：{}", dto.getId());
				AlarmDTO alarmDTO = new AlarmDTO();
				String id = UUID.randomUUID().toString();
				alarmDTO.setId(id);
				alarmDTO.setIds(id);
				alarmDTO.setDeviceId(dto.getDeviceId());
				alarmDTO.setType(String.valueOf(AlarmTypeEnum.TYPE_120.getIndex()));
				alarmDTO.setCreateTime(dto.getAlarmTime());
				alarmDTO.setVisibilityValue(dto.getVisibilityValue());
				DeviceDTO deviceDTO = new DeviceDTO();
				deviceDTO.setDeviceId(dto.getDeviceId());
				DeviceVO vo = deviceMapper.selectDeviceById(deviceDTO);
				if (vo != null) {
					String roadName = StringUtils.trimToEmpty(vo.getRoadName());
					String milePost = StringUtils.trimToEmpty(vo.getMilePost());
					String deviceName = StringUtils.trimToEmpty(vo.getDeviceName());
					alarmDTO.setDetail("能见度告警：" + roadName + milePost + deviceName + "能见度" + dto.getVisibilityValue()
							+ "米，低于能见度告警阈值，请关注实时气象状况");
				}
				add(alarmDTO);
				alarmMapper.visiAlarmPush(dto.getId());
			} else {
				LOGGER.info("此设备一小时内已有推送的能见度告警，id：{}", dto.getId());
			}
		}
		return true;
	}

	public List<AlarmTypeVO> selectAllAlarmType() {
		return alarmMapper.selectAllAlarmType();
	}

	public List<AlarmTypeVO> selectOwnAlarmType(List<String> roles) {
		Map<String, Object> map = new HashMap<>();
		map.put("roles", roles);
		return alarmMapper.selectOwnAlarmType(map);
	}

	public AlarmRadarEventVO selectAlarmRadarEventById(IdStringDTO dto) {
		return alarmMapper.selectAlarmRadarEventById(dto);
	}

	public List<EventDetectionStatVO> countDetectByOrg(EventDetectionStatDTO eDStatDTO) {
		return alarmMapper.countDetectByOrg(eDStatDTO);
	}

	public List<MonthRoadStatVO> countMonthEDByRoad(EventDetectionStatDTO eDStatDTO) {
		return alarmMapper.countMonthEDByRoad(eDStatDTO);
	}

	public List<MonthStatVO> leastYearEDPreTrend(EventDetectionStatDTO eDStatDTO) {
		return alarmMapper.leastYearEDPreTrend(eDStatDTO);
	}

	public List<MonthRoadStatVO> countEDByRoadDay(EventDetectionStatDTO eDStatDTO) {
		return alarmMapper.countEDByRoadDay(eDStatDTO);
	}

	public AlarmVO selectRadarAlarmById(AlarmDTO alarmDTO) {
		AlarmVO alarmVO = alarmMapper.selectById(alarmDTO);
		if (alarmVO == null) {
			return null;
		}
		// 查询事件所有附件信息
		if (alarmVO != null) {
			String id = alarmVO.getId();
			Map<String, Object> map = new HashMap<>();
			map.put("startTime", alarmVO.getCreateTime());
			map.put("endTime", System.currentTimeMillis() / 1000);
			List<String> ids = new ArrayList<>();
			ids.add(id);
			map.put("alarmIds", ids);
			List<AlarmVO> attachs = alarmMapper.selectAttachs(map);
			if (!attachs.isEmpty()) {
				for (AlarmVO vo : attachs) {
					if (id.equals(vo.getId())) {
						alarmVO.setAttachs(vo.getAttachs());
						break;
					}
				}
			}
		}
		return alarmVO;
	}

}
