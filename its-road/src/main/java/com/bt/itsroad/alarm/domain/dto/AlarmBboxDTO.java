package com.bt.itsroad.alarm.domain.dto;

public class AlarmBboxDTO {
	private String id;
	private String device_id;
	private Integer type;
	private Long create_time;
	private String img_url;
	private Integer bbox_left;
	private Integer bbox_top;
	private Integer bbox_right;
	private Integer bbox_bottom;
	private Integer road_no;
	private String mile_post;
	private Integer factory;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getDevice_id() {
		return device_id;
	}

	public void setDevice_id(String device_id) {
		this.device_id = device_id;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Long getCreate_time() {
		return create_time;
	}

	public void setCreate_time(Long create_time) {
		this.create_time = create_time;
	}

	public String getImg_url() {
		return img_url;
	}

	public void setImg_url(String img_url) {
		this.img_url = img_url;
	}

	public Integer getBbox_left() {
		return bbox_left;
	}

	public void setBbox_left(Integer bbox_left) {
		this.bbox_left = bbox_left;
	}

	public Integer getBbox_top() {
		return bbox_top;
	}

	public void setBbox_top(Integer bbox_top) {
		this.bbox_top = bbox_top;
	}

	public Integer getBbox_right() {
		return bbox_right;
	}

	public void setBbox_right(Integer bbox_right) {
		this.bbox_right = bbox_right;
	}

	public Integer getBbox_bottom() {
		return bbox_bottom;
	}

	public void setBbox_bottom(Integer bbox_bottom) {
		this.bbox_bottom = bbox_bottom;
	}

	public Integer getRoad_no() {
		return road_no;
	}

	public void setRoad_no(Integer road_no) {
		this.road_no = road_no;
	}

	public String getMile_post() {
		return mile_post;
	}

	public void setMile_post(String mile_post) {
		this.mile_post = mile_post;
	}

	public Integer getFactory() {
		return factory;
	}

	public void setFactory(Integer factory) {
		this.factory = factory;
	}
}
