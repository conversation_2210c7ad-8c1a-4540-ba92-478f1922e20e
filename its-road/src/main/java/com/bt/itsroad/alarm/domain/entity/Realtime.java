package com.bt.itsroad.alarm.domain.entity;

public class Realtime {
    private String temperature;
    private String humidity;
    private String skycon;
    private String visibility;
    private WindVo wind;

public String getTemperature() {
    return temperature;
}

public void setTemperature(String temperature) {
    this.temperature = temperature;
}

public String getHumidity() {
    return humidity;
}

public void setHumidity(String humidity) {
    this.humidity = humidity;
}

public String getSkycon() {
    return skycon;
}

public void setSkycon(String skycon) {
    this.skycon = skycon;
}

public String getVisibility() {
    return visibility;
}

public void setVisibility(String visibility) {
    this.visibility = visibility;
}

public WindVo getWind() {
    return wind;
}

public void setWind(WindVo wind) {
    this.wind = wind;
}
}
