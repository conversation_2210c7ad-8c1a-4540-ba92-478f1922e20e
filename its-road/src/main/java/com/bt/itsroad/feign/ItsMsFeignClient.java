package com.bt.itsroad.feign;

import com.bt.itscore.domain.dto.CommandDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.bt.itscore.domain.dto.PgisDTO;

@FeignClient(name = "its-ms")
public interface ItsMsFeignClient {
	@PostMapping("/ms/getPgisData")
	public boolean getPgisData(@RequestBody PgisDTO dto);

	@PostMapping("/ms/produce")
	public void produce(@RequestBody CommandDTO commandDTO);

}
