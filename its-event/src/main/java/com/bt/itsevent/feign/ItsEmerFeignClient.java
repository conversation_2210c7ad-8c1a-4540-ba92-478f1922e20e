package com.bt.itsevent.feign;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import com.bt.itscore.domain.dto.IdStringDTO;
import com.bt.itscore.domain.vo.ResponseVO;

@FeignClient(name = "its-emer")
public interface ItsEmerFeignClient {

    @PostMapping("/emerPlan/appraisePermission")
    ResponseVO appraisePermission(IdStringDTO dto);
    
    @PostMapping("/emerPlan/match")
    List<EmerPlanMatchVO> emerPlanMatch(EmerPlanMatchDTO dto);
    
    @PostMapping("/emerPerson/match")
    List<EmerPersonMatchVO> emerPersonMatch(EmerPersonMatchDTO dto);
    
    @PostMapping("/emerDuty/selectTodayByEventId")
    List<TodayEmerDutyVO> emerDutySelectTodayByEventId(EmerDutyTodayDTO dto);
    
    @PostMapping("/emerDuty/selectTodayByRoadNo")
    List<TodayEmerDutyVO> emerDutySelectTodayByRoadNo(EmerDutyTodayDTO dto);
}
