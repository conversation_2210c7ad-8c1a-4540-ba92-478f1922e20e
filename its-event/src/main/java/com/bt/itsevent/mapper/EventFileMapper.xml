<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itsevent.mapper.EventFileMapper">

	<resultMap type="com.bt.itsevent.domain.vo.EventFileVO" id="eventFileVOMap">
		<result column="event_id" property="eventId"/>
		<result column="revisit" property="revisit"/>
		<result column="event_no" property="eventNo"/>
		<result column="event_type" property="eventType"/>
		<result column="event_two_type" property="eventTwoType"/>
		<result column="event_three_type" property="eventThreeType"/>
		<result column="event_four_type" property="eventFourType"/>
		<result column="level" property="level"/>
		<result column="report_time" property="reportTime"/>
		<result column="finish_time" property="finishTime"/>
		<result column="brief_desc" property="briefDesc"/>
		<result column="appraise_status" property="appraiseStatus"/>
		<result column="address" property="address"/>
		<result column="client" property="client"/>
		<result column="client_plate" property="clientPlate"/>
		<result column="telephone" property="telephone"/>
		<result column="source" property="source"/>
		<result column="source_id" property="sourceId"/>
		<result column="final_report" property="finalReport"/>
		<result column="complaint_type" property="complaintType"/>
		<result column="org2_man" property="org2Man"/>
		<result column="org2_name" property="org2Name"/>
		<result column="org_name" property="companyName"/>
		<result column="build_result" property="buildResult"/>
		<result column="org_users" property="orgUsers"/>
		<result column="finish_type" property="finishType"/>
		<result column="finish_platform" property="finishPlatform"/>
		<result column="analysis_time" property="analysisTime"/>
		<result column="emer_plan_level_id" property="emerPlanLevelId"/>
		<result column="countdown_time" property="countdownTime"/>
		<result column="report_source" property="reportSource"/>
		<result column="report_source_key" property="reportSourceKey"/>
		<result column="record_man" property="recordMan"/>
		<result column="business_type_name" property="businessTypeName"/>
		<result column="deal_result" property="dealResult"/>
		<result column="user2_id" property="user2Id"/>
		<result column="report_man_tel" property="reportManTel"/>
	</resultMap>

	<resultMap type="com.bt.itsevent.domain.vo.EventFileExportVO" id="eventFileExportVOMap">
		<result column="event_id" property="eventId"/>
		<result column="event_no" property="eventNo"/>
		<result column="event_type" property="eventType"/>
		<result column="event_two_type" property="eventTwoType"/>
		<result column="event_three_type" property="eventThreeType"/>
		<result column="event_four_type" property="eventFourType"/>
		<result column="level" property="level"/>
		<result column="order_create_time" property="orderCreateTime"/>
		<result column="report_time" property="reportTime"/>
		<result column="finish_time" property="finishTime"/>
		<result column="final_report_time" property="finalReportTime"/>
		<result column="brief_desc" property="briefDesc"/>
		<result column="road_name" property="roadName"/>
		<result column="parent_road" property="parentRoad"/>
		<result column="mile_post" property="milePost"/>
		<result column="direction_name" property="directionName"/>
		<result column="death_man" property="deathMan"/>
		<result column="miss_man" property="missMan"/>
		<result column="injure_man" property="injureMan"/>
		<result column="income_lose" property="incomeLose"/>
		<result column="congestion_length" property="congestionLength"/>
		<result column="deal_result" property="dealResult"/>
		<result column="record_man" property="recordMan"/>
		<result column="client" property="client"/>
		<result column="car_plate" property="carPlate"/>
		<result column="telephone" property="telephone"/>
		<result column="source" property="source"/>
		<result column="final_report" property="finalReport"/>
		<result column="occupied_lane" property="occupiedLane"/>
		<result column="progress_desc" property="progressDesc"/>
		<result column="create_user_name" property="createUserName"/>
		<result column="weather" property="weather"/>
		<result column="accident_type" property="accidentType"/>
		<result column="other_type_cause" property="otherTypeCause"/>
		<result column="dange_flag" property="dangerFlag"/>
		<result column="accident_cause" property="accidentCause"/>
		<result column="other_cause" property="otherCause"/>
		<result column="road_loss_cause" property="roadLossCause"/>
		<result column="report_source_key" property="reportSourceKey"/>
		<result column="business_type" property="businessType"/>
		<result column="distribute_time" property="distributeTime"/>
	</resultMap>

    <select id="selectEventFileDetailByIds" parameterType="java.lang.String" resultMap="eventFileVOMap">
    	SELECT e.id event_id,e.event_no,e.event_type,e.event_two_type,e.event_three_type,e.event_four_type,e.level,e.brief_desc,e.appraise_status,e.mile_post,e.deal_result,
			       e.report_man client,e.report_man_tel telephone,e.car_plate clientPlate,e.source,e.final_report,e.finish_type,e.report_source,e.report_source_key,e.record_man,
		       CONCAT(IFNULL(t2.org_name,''),IFNULL(t3.road_name,''),IFNULL(t4.direction_name,''),IFNULL(e.mile_post,'')) address
		    FROM event e
		LEFT JOIN organization t2 on e.org_id=t2.org_id
		LEFT JOIN road t3 on e.road_no=t3.road_no
		LEFT JOIN direction t4 on e.direction_no=t4.direction_no
		WHERE e.id IN 
		<foreach collection="list" item="id" separator="," open="(" close=")">#{id}</foreach>
    </select>
    <select id="selectList" parameterType="com.bt.itsevent.domain.dto.EventFileDTO" resultMap="eventFileVOMap">
		SELECT DISTINCT event_id,report_time,finish_time,t5.analysis_time,
		       (case when ISNULL(t5.analysis_time)=1 then 2000000000 when t5.analysis_time=0 then 2000000000 when ISNULL(t1.finish_time)=1 then 2000000000
				when ISNULL(t1.event_analyse)=0 then 2000000000 else t1.finish_time+IFNULL(t5.analysis_time,0)*86400-unix_timestamp(now()) end) countdown_time
		FROM (
			SELECT DISTINCT id event_id,report_time,finish_time,emer_plan_level_id,event_analyse
				FROM event WHERE deal_status=100 and del_status=0 AND event_type=1
			<if test="eventTwoType != null &amp;&amp; eventTwoType > 0"> AND event_two_type=#{eventTwoType} </if>
			<if test="eventThreeType != null  &amp;&amp; eventThreeType > 0"> AND event_three_type=#{eventThreeType} </if>
			<if test="eventFourType != null &amp;&amp; eventFourType > 0"> AND event_four_type=#{eventFourType} </if>
			<if test="level != null &amp;&amp; level > 0"> AND level=#{level} </if>
			<if test="appraiseStatus != null  "> AND appraise_status=#{appraiseStatus} </if>
			<if test="source != null and source >= 0 "> AND source=#{source}</if>
			<if test="reportSourceKey != null  "> AND report_source_key=#{reportSourceKey} </if>
			<if test="roads != null and roads.size() > 0 "> AND (road_no IN
				<foreach collection="roads" item="road" open="(" separator="," close=")">#{road}</foreach>
				<if test="organizations != null and organizations.size()>0 "> OR org_id IN
				<foreach collection="organizations" item="orgId" open="(" separator="," close=")">#{orgId}</foreach>
				</if>
				)
			</if>
			<if test="roads == null or roads.size() == 0 ">
				<if test="organizations != null and organizations.size()>0 "> AND org_id IN
					<foreach collection="organizations" item="orgId" open="(" separator="," close=")">#{orgId}</foreach>
				</if>
			</if>

		    <if test="startTime != null "> AND report_time >= #{startTime}</if>
			<if test="endTime != null  "> AND report_time &lt;= #{endTime}</if>
			<if test="finalReport != null  "> AND final_report=#{finalReport}</if>
			<if test="eventNo != null and eventNo != '' "> AND event_no LIKE CONCAT('%',#{eventNo},'%')</if>
			<if test="milePost != null and milePost != '' "> AND mile_post LIKE CONCAT('%',#{milePost},'%')</if>
			<if test="briefDesc != null and briefDesc != '' "> AND brief_desc LIKE CONCAT('%',#{briefDesc},'%')</if>
			<if test="reportManTel != null and reportManTel != '' "> AND report_man_tel LIKE CONCAT('%',#{reportManTel},'%')</if>
			<if test="recordMan != null and recordMan != '' "> AND record_man LIKE CONCAT('%',#{recordMan},'%')</if>
			AND (
			<!-- 角色权限的和填报人能查看的归档事件 -->
				org_id IN (SELECT DISTINCT org_id FROM role_event re WHERE re.role_id IN 
					<foreach collection="roleIds" item="roleId" separator="," open="(" close=")">#{roleId}</foreach> )
				OR record_man_id=#{userId} OR app_record_man_id=#{userId}
			)
			<!-- 应急用户能查看的归档事件 -->
			UNION ALL
			SELECT DISTINCT e.id event_id,e.report_time,e.finish_time,e.emer_plan_level_id,e.event_analyse
				FROM event e,event_emer_user eu WHERE e.event_type=1 AND e.deal_status=100 and e.del_status=0 AND e.id=eu.event_id  AND eu.user_id=#{userId}
			<if test="eventTwoType != null &amp;&amp; eventTwoType > 0"> AND e.event_two_type=#{eventTwoType} </if>
			<if test="eventThreeType != null  &amp;&amp; eventThreeType > 0"> AND e.event_three_type=#{eventThreeType} </if>
			<if test="eventFourType != null &amp;&amp; eventFourType > 0"> AND e.event_four_type=#{eventFourType} </if>
			<if test="level != null &amp;&amp; level > 0"> AND e.level=#{level} </if>
			<if test="appraiseStatus != null  "> AND e.appraise_status=#{appraiseStatus} </if>
			<if test="source != null and source >= 0 "> AND e.source=#{source}</if>
			<if test="reportSourceKey != null  "> AND e.report_source_key=#{reportSourceKey} </if>
			<if test="roads != null and roads.size() > 0 "> AND (e.road_no IN
				<foreach collection="roads" item="road" open="(" separator="," close=")">#{road}</foreach>
				<if test="organizations != null and organizations.size()>0 "> OR e.org_id IN
				<foreach collection="organizations" item="orgId" open="(" separator="," close=")">#{orgId}</foreach>
				</if>
				)
			</if>
			<if test="roads == null or roads.size() == 0 ">
				<if test="organizations != null and organizations.size()>0 "> AND e.org_id IN
					<foreach collection="organizations" item="orgId" open="(" separator="," close=")">#{orgId}</foreach>
				</if>
			</if>

		    <if test="startTime != null "> AND e.report_time >= #{startTime}</if>
			<if test="endTime != null  "> AND e.report_time &lt;= #{endTime}</if>
			<if test="finalReport != null  "> AND e.final_report=#{finalReport}</if>
			<if test="eventNo != null and eventNo != '' "> AND e.event_no LIKE CONCAT('%',#{eventNo},'%')</if>
			<if test="milePost != null and milePost != '' "> AND e.mile_post LIKE CONCAT('%',#{milePost},'%')</if>
			<if test="briefDesc != null and briefDesc != '' "> AND e.brief_desc LIKE CONCAT('%',#{briefDesc},'%')</if>
			<if test="reportManTel != null and reportManTel != '' "> AND e.report_man_tel LIKE CONCAT('%',#{reportManTel},'%')</if>
			<if test="recordMan != null and recordMan != '' "> AND e.record_man LIKE CONCAT('%',#{recordMan},'%')</if>
			
		) t1
		LEFT JOIN (SELECT emer_plan_level_id,analysis_time FROM emer_plan_level_analysis GROUP BY emer_plan_level_id,analysis_time ) t5 on t1.emer_plan_level_id=t5.emer_plan_level_id
		ORDER BY countdown_time asc,report_time desc
	</select>

	<select id="selectTsJyList" parameterType="com.bt.itsevent.domain.dto.EventFileDTO" resultMap="eventFileVOMap">
		SELECT DISTINCT t1.event_id,t2.revisit,t1.event_no,t1.finish_platform,t1.event_type,t1.source,t1.source_id,t1.report_time,t1.brief_desc,t1.build_result,t1.complaint_type,t1.event_two_type,t1.event_three_type,t1.event_four_type,
		IFNULL(t2.org2_name,t1.short_name) org2_name,t2.org2_man,t2.org_users,t1.org_name,t1.business_type_name,t2.user2_id,t1.report_man_tel,t1.report_source_key
		FROM (
		SELECT e.id event_id,e.event_no,e.finish_platform,e.event_type,e.source,e.source_id,e.report_time,e.brief_desc,e.complaint_type,e.event_two_type,e.event_three_type,e.event_four_type,t3.short_name,t3.org_name,ts.build_result,di.business_type_name,
		       e.report_man_tel,e.report_source_key
		FROM `event` e
		INNER JOIN organization t3 ON e.org_id = t3.org_id
		LEFT JOIN event_ts ts ON e.id=ts.event_id
		LEFT JOIN ( SELECT `value` AS business_type, `name` AS business_type_name FROM dict_item WHERE type_id = 126 ) AS di ON di.business_type = e.business_type
		<if test="keyword != null and keyword != '' ">
			LEFT JOIN (
			SELECT t1.event_id,GROUP_CONCAT(t1.user_name SEPARATOR ',') username
			FROM (
			SELECT t1.event_id,t1.user2_id,t2.user_name
			FROM `event_ts` t1,`user` t2 where t1.user1_id=t2.user_id
			UNION
			SELECT t1.event_id,t1.user2_id,t2.user_name
			FROM `event_ts` t1,`user` t2 where t1.user2_id=t2.user_id
			UNION
			SELECT t1.event_id,t1.user2_id,t2.user_name
			FROM `event_ts` t1,`user` t2 where t1.user3_id=t2.user_id
			UNION
			SELECT t1.event_id,t1.user2_id,t2.user_name
			FROM `event_ts` t1,`user` t2 where t1.user4_id=t2.user_id
			) t1 GROUP BY t1.event_id
			) AS dealuser ON e.id=dealuser.event_id
		</if>
		WHERE e.deal_status=100 AND e.del_status=0
		<if test="eventType != null and eventType > 0"> AND e.event_type=#{eventType} </if>
		<if test="eventType == null "> AND (e.event_type=2 OR e.event_type=3) </if>
		<if test="eventTwoType != null and eventTwoType > 0"> AND e.event_two_type=#{eventTwoType} </if>
		<if test="eventThreeType != null and eventThreeType > 0 "> AND e.event_three_type=#{eventThreeType} </if>
		<if test="eventFourType != null and eventFourType > 0"> AND e.event_four_type=#{eventFourType} </if>
		<if test="startTime != null "> AND e.report_time &gt;=#{startTime}</if>
		<if test="endTime != null  "> AND e.report_time &lt;=#{endTime}</if>
		<if test="finalReport != null  "> AND e.final_report=#{finalReport}</if>
		<if test="complaintType != null  "> AND e.complaint_type=#{complaintType}</if>
		<if test="keyword != null and keyword != '' ">
			AND (
			e.event_no LIKE CONCAT('%',#{keyword},'%') OR e.brief_desc LIKE CONCAT('%',#{keyword},'%') OR e.report_man LIKE CONCAT('%',#{keyword},'%')
			OR e.record_man LIKE CONCAT('%',#{keyword},'%') OR e.report_man_tel LIKE CONCAT('%',#{keyword},'%')
			OR dealuser.username LIKE CONCAT('%',#{keyword},'%')
			)
		</if>
		AND (e.org_id IN (SELECT DISTINCT org_id FROM role_event re WHERE re.role_id IN
		<foreach collection="roleIds" item="roleId" separator="," open="(" close=")">#{roleId}</foreach>
		)
		OR ts.user2_id=#{userId}
		)
		<if test="organizations != null and organizations.size()>0 ">
			AND e.org_id IN <foreach collection="organizations" item="org" open="(" separator="," close=")">#{org}</foreach>
		</if>
		) t1 left JOIN (
		select t1.event_id,t1.revisit,t1.org2_name,t2.user_name org2_man,org_users,t1.user2_id
		from (
		select t1.event_id,t1.revisit,t1.org2_name,t1.user2_id,GROUP_CONCAT(CONCAT(t1.short_name,'-',t1.user_name) SEPARATOR ',') org_users
		from (
		SELECT t1.event_id,t1.revisit,t1.org2_name,t1.user2_id,t2.user_name,t3.short_name
		FROM `event_ts` t1,user t2,organization t3 where t1.user1_id=t2.user_id and t2.org_id=t3.org_id
		UNION
		SELECT t1.event_id,t1.revisit,t1.org2_name,t1.user2_id,t2.user_name,t3.short_name
		FROM `event_ts` t1,user t2,organization t3 where t1.user3_id=t2.user_id and t2.org_id=t3.org_id
		UNION
		SELECT t1.event_id,t1.revisit,t1.org2_name,t1.user2_id,t2.user_name,t3.short_name
		FROM `event_ts` t1,user t2,organization t3 where t1.user4_id=t2.user_id and t2.org_id=t3.org_id
		) t1  GROUP BY t1.event_id,t1.revisit,t1.org2_name,t1.user2_id
		) t1 LEFT JOIN `user` t2 on t1.user2_id=t2.user_id
		) t2 on t1.event_id=t2.event_id
		ORDER BY t1.report_time DESC
	</select>
	
	 <select id="checkEventStatus" parameterType="java.lang.String" resultType="java.lang.Integer">
	     SELECT IFNULL(active_status,1) active_status FROM `event` where id=#{eventId}
	 </select>
	
	<select id="getEventRecord" parameterType="java.lang.String" resultType="com.bt.itsevent.domain.vo.EventRecordVO">
	     SELECT id eventId,event_analyse reason,fill_man operator,fill_time recordTime FROM `event` where id=#{eventId} LIMIT 1 
	</select>
	 
	<select id="getEventEntity" parameterType="java.lang.String" resultType="com.bt.itsevent.domain.vo.EventEntityVO">
	     SELECT t1.id eventId,t1.emer_plan_id  emerPlanId,IFNULL(t1.finish_time,0) finishTime,IFNULL(t1.confirm_time,0) confirmTime,IFNULL(t1.distribute_time,0) distributeTime,order_create_time orderCreateTime,
		   		IFNULL(t1.final_report_time,0) finalReportTime,t1.level,t1.event_type oneType,t1.event_two_type twoType,t1.event_three_type threeType,t1.event_four_type fourType,t3.road_name roadName,t1.suggestion
		 FROM `event` t1 LEFT JOIN road t3 on t1.road_no=t3.road_no
		 where t1.id=#{eventId} LIMIT 1 
	</select>
	 
	 <select id="getEventDealAvgTime" parameterType="java.lang.String" resultType="java.lang.Double">
	    select AVG(IFNULL(final_report_time-order_create_time,0)) avg_time FROM `event` where emer_plan_id=#{emerPlanId} and deal_status=100
	</select>
	
	<resultMap type="com.bt.itsevent.domain.vo.EventEntityVO" id="eventEntityMap">
		<result column="event_id" property="eventId"/>
		<result column="progress_desc" property="progressDesc"/>
		<result column="occur_time" property="occurTime"/>
		<result column="card_type" property="cardType"/>
		<result column="user_name" property="userName"/>
		<result column="card_pass" property="cardPass"/>
		
	</resultMap>
	
	<select id="getTimeList" parameterType="java.lang.String" resultMap="eventEntityMap">
	    SELECT event_id,occur_time,progress_desc,card_type,user_name,card_pass
		FROM `event_progress` LEFT JOIN `user` on create_user_id=user_id
		where event_id=#{eventId}
	</select>
	
	
	<resultMap type="com.bt.itsevent.domain.vo.EventPlanAppraise2VO" id="planAppraise2VOMap">
		<result column="event_id" property="eventId"/>
		<result column="emer_plan_name" property="emerPlanName"/>
		<collection property="planStepList" ofType="com.bt.itsevent.domain.vo.EventPlanStepAppraise2VO">
			<result column="step" property="step"/>
			<result column="title" property="title"/>
    	</collection>
	</resultMap>
	
	<select id="selecEventPlanAppraise2VO" parameterType="java.lang.String" resultMap="planAppraise2VOMap">
		select t1.id event_id,t1.name emer_plan_name,t2.step,t2.title
		from (
			SELECT t1.id,t1.emer_plan_id,t2.name
			FROM event t1 ,emer_plan t2 where t1.emer_plan_id=t2.id  and t1.id=#{eventId}
		) t1 
		LEFT JOIN event_plan_step t2 on t1.emer_plan_id=t2.emer_plan_id
		order by t2.step
	</select>

	<select id="selectListForExport" parameterType="com.bt.itsevent.domain.dto.EventFileDTO" resultMap="eventFileExportVOMap">
	SELECT tmp.id event_id,e.event_no,e.event_type,e.event_two_type,e.event_three_type,e.event_four_type,e.level,e.report_time,e.finish_time,e.brief_desc,
		       t3.road_name,t3.parent_road,e.mile_post,t4.direction_name,e.death_man,e.miss_man,e.injure_man,e.income_lose,e.congestion_length,
		       e.report_man client,e.report_man_tel telephone,e.car_plate,e.source,e.final_report,e.final_report_time,e.order_create_time,e.deal_result,e.record_man,tmp.occupied_lane,
		       e.weather,e.accident_type,e.other_type_cause,e.dange_flag,e.accident_cause,e.other_cause,e.road_loss_cause,e.report_source_key,e.business_type,e.distribute_time
	FROM (
	SELECT t1.id, GROUP_CONCAT(DISTINCT t5.occupied_lane SEPARATOR ';') occupied_lane FROM (
		SELECT id
			FROM event WHERE deal_status=100 AND del_status=0 AND event_type=1
		<if test="eventTwoType != null &amp;&amp; eventTwoType > 0"> AND event_two_type=#{eventTwoType} </if>
		<if test="eventThreeType != null  &amp;&amp; eventThreeType > 0"> AND event_three_type=#{eventThreeType} </if>
		<if test="eventFourType != null &amp;&amp; eventFourType > 0"> AND event_four_type=#{eventFourType} </if>
		<if test="level != null &amp;&amp; level > 0"> AND level=#{level} </if>
		<if test="appraiseStatus != null  "> AND appraise_status=#{appraiseStatus} </if>
		<if test="source != null and source >= 0 "> AND source=#{source}</if>
		<if test="reportSourceKey != null  "> AND report_source_key=#{reportSourceKey} </if>
		<if test="roads != null and roads.size() > 0 "> AND (road_no IN
			<foreach collection="roads" item="road" open="(" separator="," close=")">#{road}</foreach>
			<if test="organizations != null and organizations.size()>0 "> OR org_id IN
			<foreach collection="organizations" item="orgId" open="(" separator="," close=")">#{orgId}</foreach>
			</if>
			)
		</if>
		<if test="roads == null or roads.size() == 0 ">
			<if test="organizations != null and organizations.size()>0 "> AND org_id IN
				<foreach collection="organizations" item="orgId" open="(" separator="," close=")">#{orgId}</foreach>
			</if>
		</if>
	    <if test="startTime != null "> AND report_time >= #{startTime}</if>
		<if test="endTime != null  "> AND report_time &lt;= #{endTime}</if>
		<if test="finalReport != null  "> AND final_report=#{finalReport}</if>
		<if test="eventNo != null and eventNo != '' "> AND event_no LIKE CONCAT('%',#{eventNo},'%')</if>
		<if test="milePost != null and milePost != '' "> AND mile_post LIKE CONCAT('%',#{milePost},'%')</if>
		<if test="briefDesc != null and briefDesc != '' "> AND brief_desc LIKE CONCAT('%',#{briefDesc},'%')</if>
		<if test="reportManTel != null and reportManTel != '' "> AND report_man_tel LIKE CONCAT('%',#{reportManTel},'%')</if>
		<if test="recordMan != null and recordMan != '' "> AND record_man LIKE CONCAT('%',#{recordMan},'%')</if>
		AND (
			org_id IN (SELECT DISTINCT org_id FROM role_event re WHERE re.role_id IN
				<foreach collection="roleIds" item="roleId" separator="," open="(" close=")">#{roleId}</foreach> )
			OR record_man_id=#{userId} OR app_record_man_id=#{userId}
		)
		
		UNION ALL
			SELECT e.id
				FROM event e,event_emer_user eu WHERE e.event_type=1 AND e.deal_status=100 AND e.del_status=0 AND e.id=eu.event_id AND eu.user_id=#{userId}
			<if test="eventTwoType != null &amp;&amp; eventTwoType > 0"> AND e.event_two_type=#{eventTwoType} </if>
			<if test="eventThreeType != null  &amp;&amp; eventThreeType > 0"> AND e.event_three_type=#{eventThreeType} </if>
			<if test="eventFourType != null &amp;&amp; eventFourType > 0"> AND e.event_four_type=#{eventFourType} </if>
			<if test="level != null &amp;&amp; level > 0"> AND e.level=#{level} </if>
			<if test="appraiseStatus != null  "> AND e.appraise_status=#{appraiseStatus} </if>
			<if test="source != null and source >= 0 "> AND e.source=#{source}</if>
			<if test="reportSourceKey != null  "> AND e.report_source_key=#{reportSourceKey} </if>
			<if test="roads != null and roads.size() > 0 "> AND (e.road_no IN
				<foreach collection="roads" item="road" open="(" separator="," close=")">#{road}</foreach>
				<if test="organizations != null and organizations.size()>0 "> OR e.org_id IN
				<foreach collection="organizations" item="orgId" open="(" separator="," close=")">#{orgId}</foreach>
				</if>
				)
			</if>
			<if test="roads == null or roads.size() == 0 ">
				<if test="organizations != null and organizations.size()>0 "> AND e.org_id IN
					<foreach collection="organizations" item="orgId" open="(" separator="," close=")">#{orgId}</foreach>
				</if>
			</if>
		    <if test="startTime != null "> AND e.report_time >= #{startTime}</if>
			<if test="endTime != null  "> AND e.report_time &lt;= #{endTime}</if>
			<if test="finalReport != null  "> AND e.final_report=#{finalReport}</if>
			<if test="eventNo != null and eventNo != '' "> AND e.event_no LIKE CONCAT('%',#{eventNo},'%')</if>
			<if test="milePost != null and milePost != '' "> AND e.mile_post LIKE CONCAT('%',#{milePost},'%')</if>
			<if test="briefDesc != null and briefDesc != '' "> AND e.brief_desc LIKE CONCAT('%',#{briefDesc},'%')</if>
			<if test="reportManTel != null and reportManTel != '' "> AND e.report_man_tel LIKE CONCAT('%',#{reportManTel},'%')</if>
			<if test="recordMan != null and recordMan != '' "> AND e.record_man LIKE CONCAT('%',#{recordMan},'%')</if>
	) t1 LEFT JOIN event_occupied_lane t5 ON t1.id=t5.event_id
	GROUP BY t1.id
	) AS tmp
	INNER JOIN event e ON tmp.id=e.id
	LEFT JOIN organization t2 on e.org_id=t2.org_id
	LEFT JOIN road t3 on e.road_no=t3.road_no
	LEFT JOIN direction t4 on e.direction_no=t4.direction_no
	ORDER BY e.report_time
	</select>

	<select id="selectEventId" parameterType="com.bt.itsevent.domain.dto.EventFileDTO" resultType="java.lang.String">
	SELECT DISTINCT id FROM (
		SELECT id FROM event WHERE event_type=1 AND deal_status=100 AND del_status=0
			<if test="eventTwoType != null &amp;&amp; eventTwoType > 0"> AND event_two_type=#{eventTwoType} </if>
			<if test="eventThreeType != null  &amp;&amp; eventThreeType > 0"> AND event_three_type=#{eventThreeType} </if>
			<if test="eventFourType != null &amp;&amp; eventFourType > 0"> AND event_four_type=#{eventFourType} </if>
			<if test="level != null &amp;&amp; level > 0"> AND level=#{level} </if>
			<if test="appraiseStatus != null  "> AND appraise_status=#{appraiseStatus} </if>
			<if test="source != null and source >= 0 "> AND source=#{source}</if>
			<if test="reportSourceKey != null  "> AND report_source_key=#{reportSourceKey} </if>
			<if test="roads != null and roads.size() > 0 "> AND (road_no IN
				<foreach collection="roads" item="road" open="(" separator="," close=")">#{road}</foreach>
				<if test="organizations != null and organizations.size()>0 "> OR org_id IN
				<foreach collection="organizations" item="orgId" open="(" separator="," close=")">#{orgId}</foreach>
				</if>
				)
			</if>
			<if test="roads == null or roads.size() == 0 ">
				<if test="organizations != null and organizations.size()>0 "> AND org_id IN
					<foreach collection="organizations" item="orgId" open="(" separator="," close=")">#{orgId}</foreach>
				</if>
			</if>
		    <if test="startTime != null "> AND report_time >= #{startTime}</if>
			<if test="endTime != null  "> AND report_time &lt;= #{endTime}</if>
			<if test="finalReport != null  "> AND final_report=#{finalReport}</if>
			<if test="eventNo != null and eventNo != '' "> AND event_no LIKE CONCAT('%',#{eventNo},'%')</if>
			<if test="milePost != null and milePost != '' "> AND mile_post LIKE CONCAT('%',#{milePost},'%')</if>
			<if test="briefDesc != null and briefDesc != '' "> AND brief_desc LIKE CONCAT('%',#{briefDesc},'%')</if>
			<if test="reportManTel != null and reportManTel != '' "> AND report_man_tel LIKE CONCAT('%',#{reportManTel},'%')</if>
			<if test="recordMan != null and recordMan != '' "> AND record_man LIKE CONCAT('%',#{recordMan},'%')</if>
		AND (
			org_id IN (SELECT DISTINCT org_id FROM role_event re WHERE re.role_id IN
				<foreach collection="roleIds" item="roleId" separator="," open="(" close=")">#{roleId}</foreach> )
			OR record_man_id=#{userId} OR app_record_man_id=#{userId}
		)

		UNION ALL
			SELECT e.id
				FROM event e,event_emer_user eu WHERE e.event_type=1 AND e.deal_status=100 AND e.del_status=0 AND e.id=eu.event_id  AND eu.user_id=#{userId}
			<if test="eventTwoType != null &amp;&amp; eventTwoType > 0"> AND e.event_two_type=#{eventTwoType} </if>
			<if test="eventThreeType != null  &amp;&amp; eventThreeType > 0"> AND e.event_three_type=#{eventThreeType} </if>
			<if test="eventFourType != null &amp;&amp; eventFourType > 0"> AND e.event_four_type=#{eventFourType} </if>
			<if test="level != null &amp;&amp; level > 0"> AND e.level=#{level} </if>
			<if test="appraiseStatus != null  "> AND e.appraise_status=#{appraiseStatus} </if>
			<if test="source != null and source >= 0 "> AND e.source=#{source}</if>
			<if test="reportSourceKey != null  "> AND e.report_source_key=#{reportSourceKey} </if>
			<if test="roads != null and roads.size() > 0 "> AND (e.road_no IN
				<foreach collection="roads" item="road" open="(" separator="," close=")">#{road}</foreach>
				<if test="organizations != null and organizations.size()>0 "> OR e.org_id IN
				<foreach collection="organizations" item="orgId" open="(" separator="," close=")">#{orgId}</foreach>
				</if>
				)
			</if>
			<if test="roads == null or roads.size() == 0 ">
				<if test="organizations != null and organizations.size()>0 "> AND e.org_id IN
					<foreach collection="organizations" item="orgId" open="(" separator="," close=")">#{orgId}</foreach>
				</if>
			</if>
		    <if test="startTime != null "> AND e.report_time >= #{startTime}</if>
			<if test="endTime != null  "> AND e.report_time &lt;= #{endTime}</if>
			<if test="finalReport != null  "> AND e.final_report=#{finalReport}</if>
			<if test="eventNo != null and eventNo != '' "> AND e.event_no LIKE CONCAT('%',#{eventNo},'%')</if>
			<if test="milePost != null and milePost != '' "> AND e.mile_post LIKE CONCAT('%',#{milePost},'%')</if>
			<if test="briefDesc != null and briefDesc != '' "> AND e.brief_desc LIKE CONCAT('%',#{briefDesc},'%')</if>
			<if test="reportManTel != null and reportManTel != '' "> AND e.report_man_tel LIKE CONCAT('%',#{reportManTel},'%')</if>
			<if test="recordMan != null and recordMan != '' "> AND e.record_man LIKE CONCAT('%',#{recordMan},'%')</if>
	) tmp 
	</select>

	<select id="selectProgress" parameterType="java.lang.String" resultType="com.bt.itsevent.domain.vo.ProgressVO">
		SELECT id, (event_id)eventId, (occur_time)occurTime, (progress_desc)progressDesc, (create_user_id)createUserId FROM event_progress WHERE
		event_id in
		<foreach collection="eventIdList" item="eventIdList" separator="," open="(" close=")">#{eventIdList}</foreach>
	</select>

	<select id="selectProgressUser" parameterType="java.lang.Integer" resultType="com.bt.itsevent.domain.vo.ProgressUserVO">
		SELECT (event_progress_id)eventProgressId, (user_name)userName FROM event_progress_user WHERE
		event_progress_id in
		<foreach collection="progressIdList" item="progressIdList" separator="," open="(" close=")">#{progressIdList}</foreach>
	</select>

	<select id="selectEventVisit" parameterType="java.util.Map" resultType="com.bt.itsevent.domain.vo.EventVisitVO">
	SELECT eventId,SUM(appraisal) AS appraisal FROM (
		SELECT id AS eventId,0 appraisal FROM `event` e 
		UNION ALL
		SELECT event_id AS eventId,1 appraisal FROM event_visit 
		WHERE event_id IN
		<foreach collection="list" item="v" separator="," open="(" close=")">#{v.eventId}</foreach>
		) t GROUP BY eventId
	</select>

	<select id="countEventStatics" parameterType="com.bt.itsevent.domain.dto.EventFileDTO" resultType="int">
		SELECT COUNT(*)
			FROM event e
			WHERE e.event_type=1 AND e.deal_status=100 AND e.del_status=0 AND e.finish_type IS NULL
				AND e.level IN 
				<foreach collection="levels" item="levelItem" separator="," open="(" close=")">#{levelItem}</foreach>
			AND e.org_id IN (SELECT DISTINCT org_id FROM role_event re WHERE re.role_id IN
			<foreach collection="roleIds" item="roleId" separator="," open="(" close=")">#{roleId}</foreach> )
			<if test="roads != null and roads.size() != 0">
			AND (e.road_no IN <foreach collection="roads" item="roadItem" open="(" close=")" separator=",">#{roadItem}</foreach>
			<if test="organizations != null and organizations.size() > 0">
				OR e.org_id IN <foreach collection="organizations" item="orgItem" open="(" close=")" separator=",">#{orgItem}</foreach>
			</if>
			)
			</if>

		<if test="roads == null or roads.size() == 0">
			<if test="organizations != null and organizations.size() != 0">
				AND e.org_id IN <foreach collection="organizations" item="orgItem" open="(" close=")" separator=",">#{orgItem}</foreach>
			</if>
		</if>
		<if test="startTime != null "> and e.report_time &gt;=#{startTime}</if>
		<if test="endTime != null  "> and e.report_time &lt;=#{endTime}</if>
	</select>

	<select id="selectEventStatics" parameterType="com.bt.itsevent.domain.dto.EventFileDTO" resultType="com.bt.itsevent.domain.vo.EventStaticsVO">
		SELECT t1.id id, t1.report_time reportTime, t1.road_name roadName, t1.direction_name direction, t1.mile mile, t1.post post, t1.rt,
			t1.im injureMan, t1.dm deathMan, t1.facility_name facilityName, t1.weather weather,
		    t1.cause accidentCause, t1.event_analyse eventAnalyse,t1.suggestion suggestion, t1.user_name finishUserName
		from (
				SELECT e.id, FROM_UNIXTIME(e.report_time, '%Y年%m月%d日%H时%i分') AS report_time, report_time AS rt, r.road_name, d.direction_name, substring_index(e.mile_post, '+', 1) mile,
					substring_index(substring_index(e.mile_post, '+', -1), '+', 1) post,
					(CASE e.injure_man WHEN '-1' THEN '不详' WHEN '-2' THEN '有' WHEN NULL THEN '0' ELSE e.injure_man END) AS im,
					(CASE e.death_man WHEN '-1' THEN '不详' WHEN '-2' THEN '有' WHEN NULL THEN '0' ELSE e.death_man END) AS dm,
					f.facility_name, diw.`name` AS weather,
					(CASE e.accident_cause WHEN '999' THEN e.other_cause ELSE dia.`name` END) AS cause,	e.suggestion, u.user_name,e.event_analyse
				FROM event e
					  LEFT JOIN road r ON e.road_no=r.road_no
					  LEFT JOIN direction d ON e.direction_no=d.direction_no
					  LEFT JOIN facility f ON e.facility_no=f.facility_no
					  LEFT JOIN dict_item diw ON e.weather=diw.`value` AND diw.type_id='61'
					  LEFT JOIN dict_item dia ON e.accident_cause=dia.`value` AND dia.type_id='100'
					  LEFT JOIN `user` u ON e.finish_user_id=u.user_id
				WHERE e.event_type=1 AND e.deal_status=100 AND e.del_status=0 AND e.finish_type IS NULL
				AND e.level IN 
				<foreach collection="levels" item="levelItem" separator="," open="(" close=")">#{levelItem}</foreach>
				AND e.org_id IN (SELECT DISTINCT org_id FROM role_event re WHERE re.role_id IN
				<foreach collection="roleIds" item="roleId" separator="," open="(" close=")">#{roleId}</foreach> )
				<if test="roads != null and roads.size() != 0">
				AND (e.road_no IN <foreach collection="roads" item="roadItem" open="(" close=")" separator=",">#{roadItem}</foreach>
				<if test="organizations != null and organizations.size() > 0">
					OR e.org_id IN <foreach collection="organizations" item="orgItem" open="(" close=")" separator=",">#{orgItem}</foreach>
				</if>
				)
				</if>

			<if test="roads == null or roads.size() == 0">
				<if test="organizations != null and organizations.size() != 0">
					AND e.org_id IN <foreach collection="organizations" item="orgItem" open="(" close=")" separator=",">#{orgItem}</foreach>
				</if>
			</if>
			<if test="startTime != null "> and e.report_time &gt;=#{startTime}</if>
			<if test="endTime != null  "> and e.report_time &lt;=#{endTime}</if>
		) t1
		ORDER BY t1.rt DESC
	</select>
	<!-- 根据事件编号查询进展，拼接事故简述，返回：progressBrief -->
	<select id="selectProgressBrief" parameterType="java.lang.String" resultType="com.bt.itsevent.domain.vo.EventStaticsVO">
		SELECT t1.*,t2.*,t3.*,t4.*,tt1.eventType FROM (		
			SELECT e.id, CASE WHEN e.deal_result IS NOT NULL THEN CONCAT(GROUP_CONCAT(ep.progress_desc SEPARATOR '\n'), '\n【处理结果】', e.deal_result) ELSE GROUP_CONCAT(ep.	progress_desc SEPARATOR '\n') END AS progressBrief
			FROM event e
		 	LEFT JOIN event_progress ep 
		 	ON e.id=ep.event_id
		 	WHERE e.id IN <foreach collection="list" item="id" separator="," open="(" close=")">#{id}</foreach> 
		  	AND ep.card_type IN (1, 5, 10)
		  	GROUP BY e.id)t1
		  	<!-- 根据事件编号查询事件类型名称，返回：eventType -->
		  	LEFT JOIN (
		  	SELECT e.*,et.`name` AS eventType FROM (
			SELECT id,IF(event_four_type IS NOT NULL,event_four_type,IF(event_three_type IS NOT NULL,event_three_type,IF(event_two_type IS NOT NULL,event_two_type,event_type))) AS event_type_no
		  	FROM `event` 
		  	WHERE id IN <foreach collection="list" item="id" separator="," open="(" close=")">#{id}</foreach>)e
		  	LEFT JOIN event_type et
			ON e.event_type_no=et.id
		  	)tt1 ON t1.id=tt1.id
		  	<!-- 根据事件编号查询车道号，返回：lane -->
			LEFT JOIN (
			SELECT e.id, GROUP_CONCAT(dio.`name`) AS lane
			FROM event e
			LEFT JOIN event_occupied_lane eol 
			ON e.id=eol.event_id
			LEFT JOIN dict_item dio ON eol.occupied_lane=dio.`value`
			WHERE e.id IN <foreach collection="list" item="id" separator="," open="(" close=")">#{id}</foreach> AND dio.type_id='70'
			GROUP BY e.id)t2 ON t1.id=t2.id
			<!-- 根据事件编号查询车辆信息，返回：carPlate、carCategory、pCarType、gCarType -->
			LEFT JOIN (
			SELECT e.id, GROUP_CONCAT(ec.car_plate) AS carPlate, GROUP_CONCAT(DISTINCT CASE ec.car_category WHEN '1' THEN '客车' WHEN '2' THEN '货车' ELSE '' END) AS carCategory, 
			GROUP_CONCAT(DISTINCT CASE ec.car_category WHEN '1' THEN dic.`name` END) AS pCarType,
			GROUP_CONCAT(DISTINCT CASE ec.car_category WHEN '2' THEN dic.`name` END) AS gCarType
			FROM event e
			LEFT JOIN event_car ec 
			ON e.id=ec.event_id 
			LEFT JOIN dict_item dic ON ec.car_type=dic.`value` 
			WHERE e.id IN <foreach collection="list" item="id" separator="," open="(" close=")">#{id}</foreach> AND dic.type_id='37'
			GROUP BY e.id)t3 ON t1.id=t3.id
			<!-- 根据事件编号查询货物信息、道路信息-->
			LEFT JOIN (
			SELECT e.id, did.name AS roadCondition, ed.accident_car_num AS carNum, ed.secondary_accidents AS secondAccidentNum,
			ed.accident_num AS accidentNum, ed.goods_car AS gGoodsCarNum, ed.empty_car AS gEmptyCarNum, ed.goods AS goods,
			di.name roadType
			FROM event e
			LEFT JOIN event_dd ed ON e.id=ed.event_id
			LEFT JOIN dict_item di ON ed.facility_type=di.`value` 
			LEFT JOIN dict_item did ON ed.road_condition=did.`value` 
			WHERE e.id IN <foreach collection="list" item="id" separator="," open="(" close=")">#{id}</foreach> AND di.type_id='113' AND did.type_id='115')t4 ON t1.id=t4.id
	</select>

	<insert id="addEventTimeoutAudit" parameterType="com.bt.itsevent.domain.dto.EventTimeoutAuditDTO">
	INSERT INTO event_timeout_audit (event_id,reason,create_user_id,create_user_name,create_time) VALUES 
	(
		#{eventId},
		#{reason},
		#{createUserId},
		#{createUserName},
		#{createTime}
	)
	</insert>

	<resultMap type="com.bt.itsevent.domain.vo.EventTimeoutAuditVO" id="EventTimeoutAuditMap">
		<id column="id" property="id"/>
		<result column="event_id" property="eventId"/>
		<result column="event_type" property="eventType"/>
		<result column="event_no" property="eventNo"/>
		<result column="event_two_type" property="eventTwoType"/>
		<result column="event_three_type" property="eventThreeType"/>
		<result column="event_four_type" property="eventFourType"/>
		<result column="report_time" property="reportTime"/>
		<result column="finish_time" property="finishTime"/>
		<result column="final_report_time" property="finalReportTime"/>
		<result column="order_create_time" property="orderCreateTime"/>
		<result column="distribute_time" property="distributeTime"/>
		<result column="org_name" property="orgName"/>
		<result column="reason" property="reason"/>
		<result column="create_user_id" property="createUserId"/>
		<result column="create_user_name" property="createUserName"/>
		<result column="audit_status" property="auditStatus"/>
		<result column="auditor" property="auditor"/>
		<result column="auditor_id" property="auditorId"/>
		<result column="create_time" property="createTime"/>
		<result column="audit_opinion" property="auditOpinion"/>
		<result column="attach_num" property="attachNum"/>
	</resultMap>
	<select id="selectEventTimeoutAudit" parameterType="com.bt.itsevent.domain.dto.EventTimeoutAuditDTO" resultMap="EventTimeoutAuditMap">
	SELECT eta.id ,eta.event_id ,e.event_no ,e.event_type ,e.event_two_type ,e.event_three_type ,e.event_four_type 
		,e.report_time ,e.finish_time ,eta.create_time ,e.final_report_time ,e.order_create_time ,o.org_name, e.distribute_time
		,eta.reason ,eta.create_user_id ,eta.create_user_name ,eta.audit_status ,eta.auditor ,eta.auditor_id ,eta.audit_opinion
		FROM event_timeout_audit eta, event e
		LEFT JOIN organization o ON e.org_id = o.org_id
		WHERE eta.event_id = e.id AND e.del_status=0
			<if test="eventNo != null and eventNo != ''"> AND e.event_no = #{eventNo} </if>
			<if test="auditStatus != null"> AND eta.audit_status = #{auditStatus} </if>
			<if test="startTime != null"> AND eta.create_time >= #{startTime} </if>
			<if test="endTime != null"> AND eta.create_time &lt;= #{endTime} </if>
			AND e.org_id IN (SELECT DISTINCT org_id FROM role_event re WHERE re.role_id IN 
				<foreach collection="roleIds" item="roleId" separator="," open="(" close=")">#{roleId}</foreach> )
		ORDER BY eta.audit_status ASC, eta.create_time DESC
	</select>

	<update id="confirmEventTimeoutAudit" parameterType="com.bt.itsevent.domain.dto.EventTimeoutAuditDTO">
	UPDATE event_timeout_audit SET auditor = #{auditor}, auditor_id = #{auditorId}, audit_status = #{auditStatus}, audit_opinion = #{auditOpinion}
		WHERE id = #{id}
	</update>

	<update id="updateEventDdStatDealTime" parameterType="com.bt.itsevent.domain.dto.EventTimeoutAuditDTO">
	UPDATE event_dd ed ,event_timeout_audit eta SET ed.stat_deal_time = 1, ed.nostat_deal_reason = eta.reason 
		WHERE ed.event_id = eta.event_id AND id = #{id}
	</update>
    <update id="updateSummary" parameterType="java.lang.String">
		UPDATE event
		SET event_analyse=#{eventAnalyse},suggestion = #{suggestion}
		WHERE id = #{eventId}
	</update>

    <select id="selectEventTimeoutAuditById" parameterType="com.bt.itscore.domain.dto.IdIntegerDTO" resultMap="EventTimeoutAuditMap">
	SELECT eta.id ,eta.event_id ,e.event_no ,e.event_type ,e.event_two_type ,e.event_three_type ,e.event_four_type 
		,e.report_time ,e.finish_time ,eta.create_time ,e.final_report_time ,e.order_create_time
		,eta.reason ,eta.create_user_id ,eta.create_user_name ,eta.audit_status ,eta.auditor ,eta.auditor_id  
		FROM event_timeout_audit eta, event e
		WHERE eta.event_id = e.id AND eta.id = #{id} AND e.del_status=0
	</select>

	<select id="selectEventTimeoutAuditByEventId" parameterType="com.bt.itscore.domain.dto.IdStringDTO" resultMap="EventTimeoutAuditMap">
	SELECT eta.id ,eta.event_id ,e.event_no ,e.event_type ,e.event_two_type ,e.event_three_type ,e.event_four_type 
		,e.report_time ,e.finish_time ,eta.create_time ,e.final_report_time ,e.order_create_time
		,eta.reason ,eta.create_user_id ,eta.create_user_name ,eta.audit_status ,eta.auditor ,eta.auditor_id  
		FROM event_timeout_audit eta, event e
		WHERE eta.event_id = e.id AND eta.event_id = #{id} AND e.del_status=0
	</select>

	<select id="statEventTimeoutAudit" parameterType="com.bt.itsevent.domain.dto.EventTimeoutAuditDTO" resultType="int">
	SELECT COUNT(*)
		FROM event_timeout_audit eta, event e
	WHERE eta.create_time>#{createTime} AND eta.event_id = e.id AND eta.audit_status = 0 AND e.del_status=0
		AND org_id IN (SELECT DISTINCT org_id FROM role_event re WHERE re.role_id IN 
			<foreach collection="roleIds" item="roleId" separator="," open="(" close=")">#{roleId}</foreach> )
	</select>

	<insert id="addAttach" parameterType="com.bt.itscore.domain.dto.AttachDTO" keyProperty="id" useGeneratedKeys="true">
		INSERT INTO event_timeout_audit_attach (file_name,disk_file_name,file_size,content_type,digest,disk_directory,create_time) VALUES
		(#{fileName}, #{diskFileName}, #{fileSize}, #{contentType}, #{digest},#{diskDirectory},#{createTime})
	</insert>
	<delete id="deleteAttach" parameterType="com.bt.itscore.domain.dto.AttachDTO">
		DELETE FROM event_timeout_audit_attach WHERE id=#{id}
	</delete>

	<update id="updateEventTimeoutAuditAttach" parameterType="com.bt.itsevent.domain.dto.EventTimeoutAuditDTO">
	UPDATE event_timeout_audit_attach SET event_id=#{eventId} WHERE id IN 
	<foreach collection="attachs" item="a" open="(" close=")" separator=",">#{a}</foreach>
	</update>

	<delete id="deleteEventTimeoutAuditAttach" parameterType="com.bt.itsevent.domain.dto.EventTimeoutAuditDTO">
        DELETE FROM event_timeout_audit_attach
        WHERE event_id=#{eventId}
		<if test="attachs.size() > 0">
			AND id NOT IN
			<foreach collection="attachs" item="a" open="(" close=")" separator=",">#{a}</foreach>
		</if>
    </delete>

	<select id="selectEventTimeoutAuditAttachByEventIds" parameterType="java.util.Map" resultMap="EventTimeoutAuditMap">
	SELECT event_id, COUNT(*) AS attach_num FROM event_timeout_audit_attach where event_id IN 
	<foreach collection="eventIds" item="eventId" open="(" close=")" separator=",">#{eventId}</foreach>
	GROUP BY event_id
	</select>
	<update id="updateEventTimeoutAudit" parameterType="com.bt.itsevent.domain.dto.EventTimeoutAuditDTO">
		UPDATE event_timeout_audit
		SET
		reason = #{reason},
		update_time = #{updateTime}  <!-- 添加修改时间字段，后续若选择覆盖进展而不是新增进展，
		在这里修改为createtime-->
		WHERE
		event_id = #{eventId}
	</update>

	<update id="updateTsjyResult" parameterType="com.bt.itsevent.domain.dto.EventFileDTO">
		UPDATE event_ts
		SET build_result=#{buildResult},build_deal_advice = #{buildDealAdvice}
		WHERE event_id = #{eventId}
	</update>

</mapper>