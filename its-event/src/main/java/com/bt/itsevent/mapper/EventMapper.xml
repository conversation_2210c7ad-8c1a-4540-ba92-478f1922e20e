<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itsevent.mapper.EventMapper">

	<resultMap type="com.bt.itsevent.domain.vo.EventAttachVO" id="EventAttachMap">
		<id column="id" property="id"/>
		<result column="event_id" property="eventId"/>
		<result column="file_name" property="fileName"/>
		<result column="disk_file_name" property="diskFileName"/>
		<result column="file_size" property="fileSize"/>
		<result column="content_type" property="contentType"/>
		<result column="digest" property="digest"/>
		<result column="disk_directory" property="diskDirectory"/>
		<result column="create_time" property="createTime"/>
	</resultMap>

	<resultMap type="com.bt.itsevent.domain.vo.ProgressVO" id="ProgressMap">
		<id column="id" property="id"/>
		<result column="event_no" property="eventNo"/>
		<result column="occur_time" property="occurTime"/>
		<result column="progress_desc" property="progressDesc"/>
		<result column="create_user_id" property="createUserId"/>
		<result column="theme" property="theme"/>
		<result column="pid" property="pid"/>
		<result column="create_time" property="createTime"/>
		<collection property="attachs" ofType="com.bt.itsevent.domain.vo.ProgressAttachVO"
			select="selectProgressAttach" column="id">
    	</collection>
	</resultMap>
	<resultMap type="com.bt.itsevent.domain.vo.ProgressAttachVO" id="ProgressAttachMap">
		<id column="attach_id" property="id"/>
		<result column="event_progress_id" property="eventProgressId"/>
		<result column="file_name" property="fileName"/>
		<result column="disk_file_name" property="diskFileName"/>
		<result column="file_size" property="fileSize"/>
		<result column="content_type" property="contentType"/>
		<result column="digest" property="digest"/>
		<result column="disk_directory" property="diskDirectory"/>
		<result column="create_time" property="createTime"/>
	</resultMap>
	<resultMap type="com.bt.itsevent.domain.vo.EventOrgVO" id="EventOrgMap">
		<id column="event_no" property="eventNo"/>
		<result column="org_id" property="orgId"/>
		<result column="org_name" property="orgName"/>
	</resultMap>

	<resultMap type="com.bt.itsevent.domain.vo.EventHeatVO" id="EventHeatMap">
		<result column="source_id" property="sourceId"/>
		<result column="brief_desc" property="briefDesc"/>
		<result column="mile_post" property="milePost"/>
		<result column="lng" property="lng"/>
		<result column="lat" property="lat"/>
		<result column="three_type_name" property="threeTypeName"/>
	</resultMap>

	<select id="selectEventOrg" parameterType="com.bt.itsevent.domain.dto.EventQueryDTO" resultMap="EventOrgMap">
		SELECT eo.event_no,eo.org_id,o.org_name FROM event_organization eo,organization o WHERE eo.org_id=o.org_id AND eo.event_no=#{eventNo}	
	</select>
	<select id="selectProgressAttach" parameterType="com.bt.itsevent.domain.dto.ProgressDTO" resultMap="ProgressAttachMap">
		SELECT a.id attach_id,a.event_progress_id,a.disk_file_name,a.file_name,a.file_size,a.content_type,a.digest,a.disk_directory,a.create_time FROM progress_attach a 
		WHERE a.event_progress_id=#{id}	
	</select>
	<select id="selectEventAttachs" parameterType="com.bt.itscore.domain.dto.IdStringDTO" resultMap="EventAttachMap">
		SELECT id,event_id,file_name,disk_file_name,file_size,content_type,digest,disk_directory,create_time FROM event_attach WHERE event_id=#{id}
	</select>

	<select id="selectEventAttachById" parameterType="List" resultMap="EventAttachMap">
		select * from event_attach WHERE id IN
		<foreach collection="list" item="id" index="index" open="(" close=")" separator=",">#{id}</foreach>
	</select>

	<select id="selectMyProgressUser" parameterType="com.bt.itsevent.domain.dto.EventQueryDTO" resultType="com.bt.itsevent.domain.vo.ProgressMyUserVO">
        SELECT ep.id proId,ep.card_type cardType,epu.card_pass cardPass,epu.user_id userId FROM event_progress ep,event_progress_user epu
        WHERE ep.id = epu.event_progress_id AND  event_id=#{id} AND (ep.card_type = 1 or ep.card_type = 15)
	    AND (epu.card_pass is null or epu.card_pass = 0)
	</select>

	<select id="selectEventOrder" parameterType="com.bt.itsevent.domain.dto.EventQueryDTO" resultType="com.bt.itsevent.domain.vo.ProgressMyUserVO">
        SELECT ep.id proId,ep.card_type cardType,epu.card_pass cardPass,epu.user_id userId FROM event_progress ep,event_progress_user epu
        WHERE ep.id = epu.event_progress_id AND  event_id=#{id} AND epu.user_id = #{userId} AND (ep.card_type = 1 or ep.card_type = 15)
	    AND (epu.card_pass is null or epu.card_pass = 0)
	</select>

	<select id="selectProgress" parameterType="com.bt.itsevent.domain.dto.EventQueryDTO" resultMap="ProgressMap">
		SELECT p.*,a.id attach_id,a.event_progress_id,a.disk_file_name,a.file_name,a.file_size,a.content_type,a.digest,a.disk_directory,a.create_time FROM event_progress p 
		LEFT JOIN progress_attach a ON p.id=a.event_progress_id
		WHERE p.event_no=#{eventNo}
	</select>

	<insert id="add" parameterType="com.bt.itsevent.domain.dto.EventDTO" keyProperty="id" useGeneratedKeys="true">
	INSERT INTO event (source,uuid,event_type,two_type,three_type,deal_status,revisit_status,event_no,report_man,report_man_tel,report_time,brief_desc,
	car_plate,car_type,car_man,road_no,direction_no,mile_post,lng,lat,address)
	VALUES (
	#{source},
	#{uuid},
	#{eventType},
	#{twoType},
	#{threeType},
	#{dealStatus},
	#{revisitStatus},
	#{eventNo},
	#{reportMan},
	#{reportManTel},
	#{reportTime},
	#{briefDesc},
	
	#{carPlate},
	#{carType},
	#{carMan},
	#{roadNo},
	#{directionNo},
	#{milePost},
	#{lng},
	#{lat},
	#{address}
	)
	</insert>

	<insert id="addTs" parameterType="com.bt.itsevent.domain.dto.EventDTO" keyProperty="id" useGeneratedKeys="true">
	INSERT INTO event (source,uuid,event_type,two_type,three_type,deal_status,revisit_status,event_no,report_man,report_man_tel,report_time,brief_desc,
	car_plate,complaint_type,complaint_target)
	VALUES (
	#{source},
	#{uuid},
	#{eventType},
	#{twoType},
	#{threeType},
	#{dealStatus},
	#{revisitStatus},
	#{eventNo},
	#{reportMan},
	#{reportManTel},
	#{reportTime},
	#{briefDesc},
	
	#{carPlate},
	#{complaintType},
	#{complaintTarget}
	)
	</insert>

	<update id="updateEventNo" parameterType="com.bt.itsevent.domain.dto.EventDTO">
	UPDATE event SET event_no=#{eventNo} WHERE uuid=#{uuid}
	</update>

	<delete id="delete" parameterType="com.bt.itscore.domain.dto.IdStringDTO">
	UPDATE event SET del_status=1,event_no=CONCAT('del-',event_no, '-', UNIX_TIMESTAMP()) WHERE id=#{id}
	</delete>

	<update id="batchDeleteAttach" parameterType="com.bt.itsevent.domain.dto.EventDTO">
	UPDATE event_attach SET event_no='' WHERE event_no=#{eventNo}
	</update>

	<update id="batchUpdateAttach" parameterType="com.bt.itsevent.domain.dto.EventDTO">
	UPDATE event_attach SET event_no=#{eventNo} WHERE id IN 
	<foreach collection="attachs" item="a" index="index" open="(" close=")" separator=",">#{a.id}</foreach>
	</update>

	<delete id="deleteEventOrg" parameterType="com.bt.itsevent.domain.dto.EventDTO">
	DELETE FROM event_organization WHERE event_no=#{eventNo}
	</delete>

	<insert id="batchAddEventOrg" parameterType="com.bt.itsevent.domain.dto.EventDTO">
	INSERT INTO event_organization (event_no,org_id) VALUES
	<foreach collection="orgIds" item="orgId" index="index" open="" close="" separator=",">(#{eventNo},#{orgId})</foreach>
	</insert>

	<select id="selectMyEvent" parameterType="com.bt.itsevent.domain.dto.EventQueryDTO" resultMap="EventProgressMap">
		SELECT t2.*,e.source,e.id,e.event_no,e.`level`,e.event_three_type,e.event_four_type,e.deal_status,e.event_status,e.report_time,
		e.car_plate,e.brief_desc,e.record_man,e.record_man_tel,e.report_man,e.report_man_tel,e.road_no,e.direction_no,e.mile_post,e.source_id,
		e.lng,e.lat,e.emer_plan_level_id,e.distribute_time FROM (
		  SELECT t.id,SUM(t.new_event) new_event,SUM(t.pz_carPass) pz_carPass,SUM(t.new_proId) new_proId,
				SUM(t.pz_proId) pz_proId,sum(t.start_plan) start_plan,sum(t.cancel_plan) cancel_plan FROM (
			SELECT e.id,0 new_event,0 pz_carPass,0 new_proId,0 pz_proId,0 start_plan,0 cancel_plan FROM `event` e
				WHERE 100 > e.deal_status AND e.del_status=0 AND e.event_type=1 AND (e.record_man_id=#{userId} OR e.app_record_man_id=#{userId})
					AND e.report_time >= #{startTime}
					AND  #{endTime}>= e.report_time
			UNION ALL
			SELECT e.id,0 new_event,0 pz_carPass,0 new_proId,0 pz_proId,0 start_plan,0 cancel_plan FROM `event` e
				WHERE 100 > e.deal_status AND e.del_status=0 AND e.event_type=1
					AND e.report_time >= #{startTime}
					AND  #{endTime}>= e.report_time
					AND e.org_id IN (SELECT DISTINCT org_id FROM role_event re WHERE
					re.role_id IN <foreach collection="roleIds" item="roleId" separator="," open="(" close=")">#{roleId}</foreach> )
			UNION ALL
				SELECT DISTINCT e.id,0 new_event,0 pz_carPass,0 new_proId,0 pz_proId,0 start_plan,0 cancel_plan FROM `event` e,event_emer_user eu
				WHERE e.id=eu.event_id  AND eu.user_id=#{userId} AND e.del_status=0 AND e.event_type=1 AND 100 > e.deal_status
				AND e.report_time >= #{startTime}
				AND  #{endTime}>= e.report_time
			UNION ALL
			SELECT e.id,1 new_event,0 pz_carPass,ep.id new_proId,0 pz_proId,0 start_plan,0 cancel_plan FROM `event` e
				LEFT JOIN event_progress ep ON e.id=ep.event_id
				LEFT JOIN event_progress_user epu ON ep.id=epu.event_progress_id
				WHERE 100 > e.deal_status AND e.del_status=0 AND e.event_type=1
					AND e.report_time >= #{startTime}
					AND  #{endTime}>= e.report_time
					AND epu.user_id=#{userId} AND ep.card_type=1 AND (epu.card_pass=0 OR epu.card_pass IS NULL) AND epu.del_status=0
			UNION ALL
			SELECT e.id,5 new_event,0 pz_carPass,ep.id new_proId,0 pz_proId,0 start_plan,0 cancel_plan FROM `event` e
				LEFT JOIN event_progress ep ON e.id=ep.event_id
				LEFT JOIN event_progress_user epu ON ep.id=epu.event_progress_id
				WHERE 100 > e.deal_status AND e.del_status=0 AND e.event_type=1
					AND e.report_time >= #{startTime}
					AND  #{endTime}>= e.report_time
					AND epu.user_id=#{userId} AND ep.card_type=1 AND epu.card_pass=1 AND epu.del_status=0
			UNION ALL
			SELECT e.id,0 new_event,1 pz_carPass,0 new_proId,ep.id pz_proId,0 start_plan,0 cancel_plan FROM `event` e
				LEFT JOIN event_progress ep ON e.id=ep.event_id
				LEFT JOIN event_progress_user epu ON ep.id=epu.event_progress_id
				WHERE 100 > e.deal_status AND e.del_status=0 AND e.event_type=1
					AND e.report_time >= #{startTime}
					AND  #{endTime}>= e.report_time
					AND epu.user_id=#{userId} AND (ep.card_type=15 AND ep.card_pass=0) AND epu.del_status=0
			UNION ALL
			SELECT e.id,0 new_event,0 pz_carPass,ep.id new_proId,0 pz_proId,-1 start_plan,0 cancel_plan FROM `event` e
				LEFT JOIN event_progress ep ON e.id=ep.event_id
				LEFT JOIN event_progress_user epu ON ep.id=epu.event_progress_id
				WHERE 100 > e.deal_status AND e.del_status=0 AND e.event_type=1 AND ep.card_valid=1
					AND e.report_time >= #{startTime}
					AND  #{endTime}>= e.report_time
					AND epu.user_id=#{userId} AND ep.card_type=20 AND (ep.card_pass=0 OR ep.card_pass IS NULL) AND epu.del_status=0
			UNION ALL
			SELECT e.id,0 new_event,0 pz_carPass,ep.id new_proId,0 pz_proId,ep.card_pass AS start_plan,0 cancel_plan FROM `event` e
				LEFT JOIN event_progress ep ON e.id=ep.event_id
				LEFT JOIN event_progress_user epu ON ep.id=epu.event_progress_id
				WHERE 100 > e.deal_status AND e.del_status=0 AND e.event_type=1 AND ep.card_valid=1
					AND e.report_time >= #{startTime}
					AND  #{endTime}>= e.report_time
					AND epu.user_id=#{userId} AND ep.card_type=20 AND ep.card_pass > 0 AND epu.del_status=0
			UNION ALL
			SELECT e.id,0 new_event,0 pz_carPass,ep.id new_proId,0 pz_proId,0 start_plan,-1 cancel_plan FROM `event` e
				LEFT JOIN event_progress ep ON e.id=ep.event_id
				LEFT JOIN event_progress_user epu ON ep.id=epu.event_progress_id
				WHERE 100 > e.deal_status AND e.del_status=0 AND e.event_type=1 AND ep.card_valid=1
					AND e.report_time >= #{startTime}
					AND  #{endTime}>= e.report_time
					AND epu.user_id=#{userId} AND ep.card_type=25 AND (ep.card_pass=0 OR ep.card_pass IS NULL)
					AND epu.del_status=0
			UNION ALL
			SELECT e.id,0 new_event,0 pz_carPass,ep.id new_proId,0 pz_proId,0 start_plan,ep.card_pass AS cancel_plan FROM `event` e
				LEFT JOIN event_progress ep ON e.id=ep.event_id
				LEFT JOIN event_progress_user epu ON ep.id=epu.event_progress_id
				WHERE 100 > e.deal_status AND e.del_status=0 AND e.event_type=1 AND ep.card_valid=1
					AND e.report_time >= #{startTime}
					AND  #{endTime}>= e.report_time
					AND epu.user_id=#{userId} AND ep.card_type=25 AND ep.card_pass > 0 AND epu.del_status=0
			) t GROUP BY id
		) t2
		LEFT JOIN `event` e ON t2.id=e.id
		<where>
			<if test="startTime != null and startTime != '' ">
				AND e.report_time >= #{startTime}
				AND  #{endTime}>= e.report_time
			</if>
			<if test="keyword != null and keyword != '' ">
				AND (e.event_no LIKE CONCAT('%',#{keyword},'%')
				OR e.car_plate LIKE CONCAT('%',#{keyword},'%')
				OR e.brief_desc LIKE CONCAT('%',#{keyword},'%')
				OR e.report_man LIKE CONCAT('%',#{keyword},'%')
				OR e.report_man_tel LIKE CONCAT('%',#{keyword},'%')
				OR e.record_man LIKE CONCAT('%',#{keyword},'%')
				OR e.record_man_tel LIKE CONCAT('%',#{keyword},'%'))
			</if>
		</where> ORDER BY e.report_time DESC
	</select>

    <update id="updateSg" parameterType="com.bt.itsevent.domain.dto.EventDTO">
    update event set
        source=#{source},
        report_man=#{reportMan},
        report_man_tel=#{reportManTel},
        report_time=#{reportTime},
        brief_desc=#{briefDesc},
        car_plate=#{carPlate},
        car_type=#{carType},
        car_man=#{carMan},
        road_no=#{roadNo},
        direction_no=#{directionNo},
        mile_post=#{milePost},
        lng=#{lng},
        lat=#{lat},
        address=#{address}
    where uuid=#{uuid}
    </update>

    <update id="updateTs" parameterType="com.bt.itsevent.domain.dto.EventDTO">
    update event set
        source=#{source},
        report_man=#{reportMan},
        report_man_tel=#{reportManTel},
        report_time=#{reportTime},
        brief_desc=#{briefDesc},
        car_plate=#{carPlate},
        car_type=#{carType},
        car_man=#{carMan},
        road_no=#{roadNo},
        direction_no=#{directionNo},
        mile_post=#{milePost},
        complaint_type=#{complaintType},
        complaint_target=#{complaintTarget},
        deal_advice=#{dealAdvice},
        kf_advice=#{kfAdvice},
        supervise_advice=#{superviseAdvice},
        deal_result=#{dealResult},
        address=#{address}
    where uuid=#{uuid}
    </update>

    <update id="updateJy" parameterType="com.bt.itsevent.domain.dto.EventDTO">
    update event set
        report_man=#{reportMan},
        report_man_tel=#{reportManTel},
        brief_desc=#{briefDesc},
        car_plate=#{carPlate},
        complaint_type=#{complaintType},
        complaint_target=#{complaintTarget},
        deal_advice=#{dealAdvice},
        kf_advice=#{kfAdvice},
        supervise_advice=#{superviseAdvice},
        deal_result=#{dealResult}
    where uuid=#{uuid}
    </update>

	<insert id="addAttach" parameterType="com.bt.itscore.domain.dto.AttachDTO" keyProperty="id" useGeneratedKeys="true">
		INSERT INTO event_attach (file_name,disk_file_name,file_size,content_type,digest,disk_directory,create_time) VALUES
		(#{fileName}, #{diskFileName}, #{fileSize}, #{contentType}, #{digest},#{diskDirectory},#{createTime})
	</insert>
	<delete id="deleteAttach" parameterType="com.bt.itscore.domain.dto.AttachDTO">
		DELETE FROM event_attach WHERE id=#{id}
	</delete>

	<resultMap type="com.bt.itsevent.domain.vo.EventStatVO" id="EventStatMap">
	    <result column="event_type" property="eventType"/>
	    <result column="count" property="count"/>
	</resultMap>
	<select id="statNoDeal" parameterType="com.bt.itsevent.domain.dto.EventQueryDTO" resultMap="EventStatMap">
	    SELECT r.event_type,COUNT(r.event_type) count FROM (
	    SELECT DISTINCT e.* FROM event e,event_organization eo WHERE e.deal_status &lt;> 100 AND e.del_status=0
	    AND eo.org_id IN (SELECT DISTINCT org_id FROM role_event re WHERE re.role_id IN
	        <foreach collection="roleIds" item="roleId" separator="," open="(" close=")">#{roleId}</foreach>
	    )
	    AND e.id=eo.event_id
	    ) r	GROUP BY r.event_type
	</select>
	
	<select id="countEvent" parameterType="com.bt.itsevent.domain.dto.EventCountDTO" resultType="com.bt.itsevent.domain.vo.EventCountVO">
	SELECT
		COUNT(DISTINCT(CASE WHEN 3 > e.deal_status
		THEN e.id ELSE NULL END)) AS unSure,
		COUNT(DISTINCT (CASE WHEN  e.deal_status =3
		THEN e.id ELSE NULL END)) AS inProgress,
		COUNT(DISTINCT (CASE WHEN  e.deal_status = 100
		THEN e.id ELSE NULL END)) AS finish
	FROM(
		select e.id,e.del_status,e.deal_status,e.record_man_id,e.report_time
		from event e where e.event_type=1 and e.del_status = 0 and e.record_man_id = #{userId}
		<if test="startTime != null and startTime != '' ">
			AND e.report_time >= #{startTime}
		</if>
		<if test="endTime != null and endTime != '' ">
			AND  #{endTime} >= e.report_time
		</if>
		UNION all
		select e.id,e.del_status,e.deal_status ,e.record_man_id,e.report_time
		from event e
		right join event_progress ep on e.id = ep.event_id
		right join event_progress_user epu on ep.id = epu.event_progress_id
		where e.event_type=1 and e.del_status = 0 and epu.user_id = #{userId}
		<if test="startTime != null and startTime != '' ">
			AND e.report_time >= #{startTime}
		</if>
		<if test="endTime != null and endTime != '' ">
			AND  #{endTime} >= e.report_time
		</if>
	) as e
    </select>

	<select id="countEventByOrg" parameterType="com.bt.itsevent.domain.dto.EventCountDTO" resultType="com.bt.itsevent.domain.vo.EventCountByOrgVO">
	    SELECT COALESCE(sum(CASE WHEN e.event_four_type = 26 THEN 1 ELSE 0 END),0) traffic,
	    COALESCE(sum(CASE WHEN e.event_three_type =11 THEN 1 ELSE 0 END),0) rescue,
	    COALESCE(sum(CASE WHEN e.event_three_type = 18 THEN 1 ELSE 0 END),0) consult,
	    count(id) totalAll
	    FROM event e
	    WHERE e.deal_status > 2 AND e.event_type=1 AND e.del_status = 0
	    <if test="startTime != null and startTime != '' ">
	        AND e.report_time >= #{startTime}
	    </if>
	    <if test="endTime != null and endTime != '' ">
	        AND  #{endTime} >= e.report_time
	    </if>
	    <if test="orgId != null and orgId != ''">
	        AND e.org_id = #{orgId}
	    </if>
	</select>

	<select id="countEventTypeByOrg" parameterType="com.bt.itsevent.domain.dto.EventCountDTO" resultType="java.util.Map">
	    SELECT COALESCE(sum(CASE WHEN e.event_three_type = 11 THEN 1 ELSE 0 END),0) 车辆救援,
	    COALESCE(sum(CASE WHEN e.event_three_type =71 THEN 1 ELSE 0 END),0) 道路拥堵,
	    COALESCE(sum(CASE WHEN e.event_three_type = 72 THEN 1 ELSE 0 END),0) 行人事件,
	    COALESCE(sum(CASE WHEN e.event_three_type = 73 THEN 1 ELSE 0 END),0) 异常停车,
	    COALESCE(sum(CASE WHEN e.event_three_type = 74 THEN 1 ELSE 0 END),0) 车辆逆行,
	    COALESCE(sum(CASE WHEN e.event_three_type = 75 THEN 1 ELSE 0 END),0) 道路遗撒,
	    COALESCE(sum(CASE WHEN e.event_three_type = 76 THEN 1 ELSE 0 END),0) 极端天气,
	    COALESCE(sum(CASE WHEN e.event_three_type = 77 THEN 1 ELSE 0 END),0) 隧道事件,
	    COALESCE(sum(CASE WHEN e.event_three_type = 78 THEN 1 ELSE 0 END),0) 服务区事件,
	
	    COALESCE(sum(CASE WHEN e.event_three_type = 8 THEN 1 ELSE 0 END),0) 自然灾害,
	    COALESCE(sum(CASE WHEN e.event_three_type = 25 THEN 1 ELSE 0 END),0) 事故灾害,
	    COALESCE(sum(CASE WHEN e.event_three_type = 10 THEN 1 ELSE 0 END),0) 公共卫生,
	    COALESCE(sum(CASE WHEN e.event_three_type = 9 THEN 1 ELSE 0 END),0) 社会安全,
	    COALESCE(sum(CASE WHEN e.event_three_type = 34 THEN 1 ELSE 0 END),0) 服务区突发事件,
	    COALESCE(sum(CASE WHEN e.event_three_type = 43 THEN 1 ELSE 0 END),0) 收费站突发事件,
	    COALESCE(sum(CASE WHEN e.event_three_type = 51 THEN 1 ELSE 0 END),0) 隧道突发事件,
	    COALESCE(sum(CASE WHEN e.event_three_type = 61 THEN 1 ELSE 0 END),0) 桥梁突发事件
	    FROM event e
	    WHERE e.event_type=1 AND e.del_status = 0 AND e.deal_status > 2
	    <if test="startTime != null and startTime != '' ">
	        AND e.report_time >= #{startTime}
	    </if>
	    <if test="endTime != null and endTime != '' ">
	        AND  #{endTime} >= e.report_time
	    </if>
	    <if test="orgIds != null">
	        AND e.org_id in
	        <foreach collection="orgIds" item="orgIdItem" open="(" close=")" separator=",">#{orgIdItem}</foreach>
	    </if>
	</select>

	<select id="countEventRoadByOrg" parameterType="com.bt.itsevent.domain.dto.EventCountDTO" resultType="java.util.HashMap">
	    SELECT
	    <trim prefix="" suffixOverrides=",">
	    <if test="roadVOS != null">
	        <foreach collection="roadVOS" item="roadItem" open="" close="" separator=",">
	            COALESCE(sum(CASE WHEN e.road_no = #{roadItem.roadNo} THEN 1 ELSE 0 END),0)  #{roadItem.roadName}
	        </foreach>
	    </if>
	    </trim>
	    FROM event e
	    WHERE e.event_type=1 AND e.del_status = 0 AND e.deal_status > 2
	    <if test="startTime != null and startTime != '' ">
	        AND e.report_time >= #{startTime}
	    </if>
	    <if test="endTime != null and endTime != '' ">
	        AND  #{endTime} >= e.report_time
	    </if>
	    <if test="orgIds != null">
	        AND e.org_id in
	        <foreach collection="orgIds" item="orgIdItem" open="(" close=")" separator=",">#{orgIdItem}</foreach>
	    </if>
	</select>

	<select id="countEventTodayType"  resultType="com.bt.itsevent.domain.vo.EventTodayTypeVO">
	    SELECT source_id as sourceId, COUNT(if( e.event_three_type = 11,1,NULL)) rescueTotal,
	    COUNT(if( e.event_three_type = 72,1,NULL)) passgerTotal,
	    COUNT(if( e.event_three_type = 26,1,NULL)) accidentTotal,
	    COUNT(if( e.event_three_type = 75,1,NULL)) lsaacTotal
	    FROM event e WHERE e.del_status = 0 AND e.deal_status > 2
	    AND to_days(FROM_UNIXTIME(e.report_time)) = to_days(now()) AND e.source_id IS NOT NULL
	    GROUP BY source_id;
	</select>

	<select id="countYanHaiEvent"  resultType="map">
           select CONCAT(u.bhProcessing,'/',u.bhTotalAll) as '北海',CONCAT(u.qzProcessing,'/',u.qzTotalAll) as '钦州',
CONCAT(u.fcProcessing,'/',u.fcTotalAll) as '防城',CONCAT(u.nnProcessing,'/',u.nnTotalAll) as '南宁'
from( SELECT
	COUNT( IF ( ( e.deal_status = 3 AND e.org_id = '60381fcb-1138-11ec-a11a-7c8ae1d066a4' ), 1, NULL ) ) bhProcessing,
	COUNT( IF ( ( e.deal_status > 2 AND e.org_id = '60381fcb-1138-11ec-a11a-7c8ae1d066a4' ), 1, NULL ) ) bhTotalAll,
	COUNT( IF ( ( e.deal_status = 3 AND e.org_id = '4a5c49a9-8152-4d38-9666-e536725c6670' ), 1, NULL ) ) qzProcessing,
	COUNT( IF ( ( e.deal_status > 2 AND e.org_id = '4a5c49a9-8152-4d38-9666-e536725c6670' ), 1, NULL ) ) qzTotalAll,
	COUNT( IF ( ( e.deal_status = 3 AND e.org_id = 'c8d11e42-3cd2-48e0-9bae-b1d462c7ed26' ), 1, NULL ) ) fcProcessing,
	COUNT( IF ( ( e.deal_status > 2 AND e.org_id = 'c8d11e42-3cd2-48e0-9bae-b1d462c7ed26' ), 1, NULL ) ) fcTotalAll,
	COUNT( IF ( ( e.deal_status = 3 AND e.org_id = '681bbd49-1138-11ec-a11a-7c8ae1d066a4' ), 1, NULL ) ) nnProcessing,
	COUNT( IF ( ( e.deal_status > 2 AND e.org_id = '681bbd49-1138-11ec-a11a-7c8ae1d066a4' ), 1, NULL ) ) nnTotalAll
FROM event e WHERE e.del_status = 0 AND e.event_type = 1
AND to_days( FROM_UNIXTIME( e.report_time ) ) = to_days( now( ) ) ) u
	</select>
	
	<select id="countTypeTop5" parameterType="java.util.Map" resultType="com.bt.itsevent.domain.vo.EventTypeTop5VO">
	    SELECT  case when e.org_id = '60381fcb-1138-11ec-a11a-7c8ae1d066a4' THEN 1
	    when e.org_id = '4a5c49a9-8152-4d38-9666-e536725c6670' THEN 2
	    when e.org_id = 'c8d11e42-3cd2-48e0-9bae-b1d462c7ed26' THEN 3
	    when e.org_id = '681bbd49-1138-11ec-a11a-7c8ae1d066a4' THEN 4
	    else 5 end as sourceId,
	    <foreach collection="map.entrySet()" item="value" index="key" separator=",">
	        COUNT(if( et.name = #{value},1,NULL)) #{key}
	    </foreach>
	    FROM event e,event_type et WHERE e.event_three_type = et.id
	    AND to_days(FROM_UNIXTIME(e.report_time)) = to_days(now())
	    AND e.del_status = 0 AND e.deal_status > 2 AND e.source_id IS NOT NULL
	    AND e.org_id in ('60381fcb-1138-11ec-a11a-7c8ae1d066a4','681bbd49-1138-11ec-a11a-7c8ae1d066a4',
	    '4a5c49a9-8152-4d38-9666-e536725c6670','c8d11e42-3cd2-48e0-9bae-b1d462c7ed26')
	    GROUP BY sourceId
	</select>
	
	<select id="selectTypeTop5" resultType="String">
	    SELECT decount.name FROM
	    (SELECT et.`name` name,
	    count(e.event_three_type) AS num
	    FROM event e,event_type et WHERE e.event_three_type = et.id
	    AND e.del_status = 0 AND e.deal_status > 2 AND e.source_id IS NOT NULL
	    AND e.source_id = 1
	    AND to_days(FROM_UNIXTIME(e.report_time)) = to_days(now())
	    GROUP BY et.`name` ORDER BY num DESC limit 5 )
	    AS decount
	</select>
	
	<update id="finish" parameterType="com.bt.itsevent.domain.dto.EventResultDTO">
	    UPDATE event SET deal_status=100, deal_result=#{dealResult}, complaint_type=#{complaintType}, finish_time=#{finishTime} WHERE event_no=#{eventNo}
	</update>
	
	<select id="allAttach" parameterType="com.bt.itscore.domain.dto.IdStringDTO" resultMap="EventAttachMap">
	    SELECT ea.file_name,ea.disk_file_name,ea.content_type,CONCAT('event/',ea.disk_directory) disk_directory FROM `event` e , event_attach ea WHERE e.id=ea.event_id AND e.id=#{id}
	        UNION ALL
	    SELECT pa.file_name,pa.disk_file_name,pa.content_type,CONCAT('progress/',pa.disk_directory) disk_directory  FROM `event_progress` ep , progress_attach pa WHERE ep.id=pa.event_progress_id AND ep.event_id=#{id}
	        UNION ALL
	    SELECT pa.file_name,pa.disk_file_name,pa.content_type,CONCAT('progress/',pa.disk_directory) disk_directory  FROM `event_progress_scene` eps , progress_attach pa WHERE eps.id=pa.progress_scene_id AND eps.event_id=#{id}
	</select>
	
	<insert id="addOccupiedLane" parameterType="com.bt.itsevent.domain.dto.EmerRescueDTO">
	    INSERT INTO event_occupied_lane (event_id,occupied_lane) VALUES
	    <foreach collection="occupiedLanes" item="occupiedLane" separator=",">(#{id},#{occupiedLane})</foreach>
	</insert>

	<insert id="addEmerRescue" parameterType="com.bt.itsevent.domain.dto.EmerRescueDTO">
	    INSERT INTO event (id,event_no,brief_desc,event_type,event_two_type,event_three_type,
	    event_four_type,report_time,`level`,source,road_no,
	    direction_no,mile_post,
	    <if test="deathMan != null">death_man,</if>
	    <if test="missMan != null">miss_man,</if>
	    <if test="injureMan != null">injure_man,</if>
	    <if test="incomeLose != null">income_lose,</if>
	    <if test="appRecordManId != null">app_record_man_id,</if>
	    <if test="weather != null">weather,</if>
	    <if test="address != null">address,</if>
	    <if test="facilityNo != null">facility_no,</if>
	    <if test="accidentType != null">accident_type,</if>
	    <if test="otherTypeCause != null">other_type_cause,</if>
	    <if test="dangeFlag != null">dange_flag,</if>
	    <if test="accidentCause != null">accident_cause,</if>
	    <if test="otherCause != null">other_cause,</if>
	    <if test="roadLossCause != null">road_loss_cause,</if>
	    <if test="congestionLength != null">congestion_length,</if>
	    report_source,report_source_key,
	    report_man,report_man_tel,car_plate,create_time,order_create_time,del_status,deal_status,
	    record_man_id,record_man,record_man_tel,lng,lat,
	    event_status,org_id,source_id,emer_plan_id,emer_plan_level_id,
	    accident_pattern
	    ) VALUES
	    (
	    #{id},
	    #{eventNo},
	    #{briefDesc},
	    #{eventType},
	    #{eventTwoType},
	    #{eventThreeType},
	
	    #{eventFourType},
	    #{reportTime},
	    #{level},
	    #{source},
	    #{roadNo},
	
	    #{directionNo},
	    #{milePost},
	    <if test="deathMan != null">#{deathMan},</if>
	    <if test="missMan != null">#{missMan},</if>
	    <if test="injureMan != null">#{injureMan},</if>
	    <if test="incomeLose != null">#{incomeLose},</if>
		<if test="appRecordManId != null">#{appRecordManId},</if>
	    <if test="weather != null">#{weather},</if>
	    <if test="address != null">#{address},</if>
	    <if test="facilityNo != null">#{facilityNo},</if>
	    <if test="accidentType != null">#{accidentType},</if>
	    <if test="otherTypeCause != null">#{otherTypeCause},</if>
	    <if test="dangeFlag != null">#{dangeFlag},</if>
	    <if test="accidentCause != null">#{accidentCause},</if>
	    <if test="otherCause != null">#{otherCause},</if>
	    <if test="roadLossCause != null">#{roadLossCause},</if>
	    <if test="congestionLength != null">#{congestionLength},</if>
	    #{reportSource},
	    #{reportSourceKey},
	    #{reportMan},
	    #{reportManTel},
	    #{carPlate},
	    #{createTime},
	    #{createTime},
	    #{delStatus},
	    #{dealStatus},
	    #{recordManId},
	    #{recordMan},
	    #{recordManTel},
	    #{lng},
	    #{lat},
	
	    #{eventStatus},
	    #{orgId},
	    #{sourceId},
	    #{emerPlanId},
	    #{emerPlanLevelId},

	    #{accidentPattern}
	    )
	</insert>

	<update id="updateEmerRescueStepOne" parameterType="com.bt.itsevent.domain.dto.EmerRescueDTO">
	  UPDATE event SET
	    brief_desc=#{briefDesc},
	    event_type=#{eventType},
	    event_two_type=#{eventTwoType},
	    event_three_type=#{eventThreeType},
	    event_four_type=#{eventFourType},
	    report_time=#{reportTime},
	    `level`=#{level},
	    <if test="source != null">source=#{source},</if>
	    road_no=#{roadNo},
	    direction_no=#{directionNo},
	    mile_post=#{milePost},
	    <if test="deathMan != null">death_man=#{deathMan},</if>
	    <if test="missMan != null">miss_man=#{missMan},</if>
	    <if test="injureMan != null">injure_man=#{injureMan},</if>
	    <if test="incomeLose != null">income_lose=#{incomeLose},</if>
	    <if test="weather != null">weather=#{weather},</if>
	    <if test="address != null">address=#{address},</if>
	    <if test="facilityNo != null">facility_no=#{facilityNo},</if>
	   	<if test="accidentType != null">accident_type=#{accidentType},</if>
	   	<if test="otherTypeCause != null">other_type_cause=#{otherTypeCause},</if>
	   	<if test="dangeFlag != null">dange_flag=#{dangeFlag},</if>
	   	<if test="accidentCause != null">accident_cause=#{accidentCause},</if>
	   	<if test="otherCause != null">other_cause=#{otherCause},</if>
	   	<if test="roadLossCause != null">road_loss_cause=#{roadLossCause},</if>
	   	report_source=#{reportSource},
	   	report_source_key=#{reportSourceKey},
	    report_man=#{reportMan},
	    report_man_tel=#{reportManTel},
	    car_plate=#{carPlate},
	    update_time=#{createTime},
	    record_man_id=#{recordManId},
	    record_man=#{recordMan},
	    record_man_tel=#{recordManTel},
	    lng=#{lng},
	    lat=#{lat},
	    <if test="orgId != null">org_id=#{orgId},</if>
	    <if test="sourceId != null">source_id=#{sourceId},</if>
	    emer_plan_id=#{emerPlanId},
	    emer_plan_level_id=#{emerPlanLevelId},
	    congestion_length=#{congestionLength}
	  WHERE id=#{id}
	</update>
	
	<update id="batchUpdateAttachs" parameterType="com.bt.itsevent.domain.dto.EmerRescueDTO">
		UPDATE event_attach SET event_id=null WHERE event_id=#{id};
		<if test="attachs != null and attachs.size() > 0 ">
	    UPDATE event_attach SET event_id=#{id} WHERE id IN
	    <foreach collection="attachs" item="a" open="(" close=")" separator=",">#{a}</foreach>
	    </if>
	</update>

    <insert id="addEventOrg" parameterType="com.bt.itsevent.domain.dto.EmerRescueDTO">
    INSERT INTO event_organization (event_id,org_id) VALUES (#{id},#{orgId})
    </insert>
    <resultMap type="com.bt.itsevent.domain.vo.EmerResuceVO" id="EmerResuceMap">
        <id column="id" property="id"/>
        <result column="level" property="level"/>
        <result column="event_three_type" property="eventThreeType"/>
        <result column="event_four_type" property="eventFourType"/>
        <result column="deal_status" property="dealStatus"/>
        <result column="event_status" property="eventStatus"/>
        <result column="report_time" property="reportTime"/>
        <result column="brief_desc" property="briefDesc"/>
        <result column="road_no" property="roadNo"/>
        <result column="direction_no" property="directionNo"/>
        <result column="mile_post" property="milePost"/>
        <result column="lng" property="lng"/>
        <result column="lat" property="lat"/>
        <result column="report_man" property="reportMan"/>
        <result column="report_man_tel" property="reportManTel"/>
        <result column="car_plate" property="carPlate"/>
        <result column="source" property="source"/>
        <result column="final_report" property="finalReport"/>
        <result column="source_id" property="sourceId"/>
        <result column="org_id" property="orgId"/>
        <result column="event_analyse" property="eventAnalyse"/>
		<result column="report_source_key" property="reportSourceKey"/>
    </resultMap>

	<resultMap type="com.bt.itsevent.domain.vo.EmerProgressVO" id="EventProgressMap">
	    <id column="id" property="id"/>
	    <result column="source" property="source"/>
	    <result column="level" property="level"/>
	    <result column="event_three_type" property="eventThreeType"/>
	    <result column="event_four_type" property="eventFourType"/>
	    <result column="deal_status" property="dealStatus"/>
	    <result column="event_status" property="eventStatus"/>
	    <result column="report_time" property="reportTime"/>
	    <result column="brief_desc" property="briefDesc"/>
	    <result column="road_no" property="roadNo"/>
	    <result column="direction_no" property="directionNo"/>
	    <result column="mile_post" property="milePost"/>
	    <result column="lng" property="lng"/>
	    <result column="lat" property="lat"/>
	    <result column="report_man" property="reportMan"/>
	    <result column="report_man_tel" property="reportManTel"/>
	    <result column="car_plate" property="carPlate"/>
	    <result column="new_event" property="newEvent"/>
	    <result column="pz_carPass" property="pzCarPass"/>
	    <result column="new_proId" property="newProId"/>
	    <result column="pz_proId" property="pzProId"/>
	    <result column="start_plan" property="startPlan"/>
	    <result column="cancel_plan" property="cancelPlan"/>
	    <result column="emer_plan_level_id" property="emerPlanLevelId"/>
	    <result column="distribute_time" property="initReportTime"/>
	    <result column="analysis_time" property="analysisTime"/>
	    <result column="finish_time" property="finishTime"/>
	    <result column="source_id" property="sourceId"/>
	</resultMap>

	<select id="selectEmerRescue" parameterType="com.bt.itsevent.domain.dto.EventQueryDTO" resultMap="EmerResuceMap">
	SELECT DISTINCT res.* from (
		SELECT DISTINCT e.id,e.event_no,e.`level`,e.event_two_type,e.event_three_type,e.event_four_type,e.deal_status,e.event_status,e.report_time,e.car_plate,e.brief_desc,e.record_man,e.record_man_tel,e.report_man,e.report_man_tel,e.road_no,e.direction_no,e.mile_post,e.lng,e.lat,e.source,e.final_report,e.org_id,e.source_id,e.event_analyse,e.report_source_key FROM `event` e
			WHERE e.event_type=1 AND (e.record_man_id=#{userId} OR e.app_record_man_id=#{userId}) AND e.del_status=0
			<if test="startTime != null and startTime != '' ">
				AND e.report_time >= #{startTime}
				AND #{endTime}>= e.report_time
			</if>
			<if test="dealStatus != null and dealStatus == 0"> AND e.deal_status > 0 AND e.deal_status &lt; 100 </if>
			<if test="dealStatus != null and dealStatus > 0"> AND e.deal_status = #{dealStatus} </if>
		UNION ALL
			SELECT DISTINCT e.id,e.event_no,e.`level`,e.event_two_type,e.event_three_type,e.event_four_type,e.deal_status,e.event_status,e.report_time,e.car_plate,e.brief_desc,e.record_man,e.record_man_tel,e.report_man,e.report_man_tel,e.road_no,e.direction_no,e.mile_post,e.lng,e.lat,e.source,e.final_report,e.org_id,e.source_id,e.event_analyse,e.report_source_key FROM `event` e,event_emer_user eu
			WHERE e.event_type=1 AND e.id=eu.event_id  AND eu.user_id=#{userId} AND e.del_status=0
			<if test="startTime != null and startTime != '' ">
				AND e.report_time >= #{startTime}
				AND #{endTime}>= e.report_time
			</if>
			<if test="dealStatus != null and dealStatus == 0"> AND e.deal_status > 0 AND e.deal_status &lt; 100 </if>
			<if test="dealStatus != null and dealStatus > 0"> AND e.deal_status = #{dealStatus} </if>
		UNION ALL
			SELECT DISTINCT e.id,e.event_no,e.`level`,e.event_two_type,e.event_three_type,e.event_four_type,e.deal_status,e.event_status,e.report_time,e.car_plate,e.brief_desc,e.record_man,e.record_man_tel,e.report_man,e.report_man_tel,e.road_no,e.direction_no,e.mile_post,e.lng,e.lat,e.source,e.final_report,e.org_id,e.source_id,e.event_analyse,e.report_source_key FROM `event` e
			  WHERE e.event_type=1 AND e.del_status=0
			<if test="startTime != null and startTime != '' ">
				AND e.report_time >= #{startTime}
				AND #{endTime}>= e.report_time
			</if>
			  <if test="dealStatus != null and dealStatus == 0"> AND e.deal_status > 0 AND e.deal_status &lt; 100 </if>
			  <if test="dealStatus != null and dealStatus > 0"> AND e.deal_status = #{dealStatus} </if>
				AND e.org_id IN (SELECT DISTINCT org_id FROM role_event re WHERE re.role_id IN 
				<foreach collection="roleIds" item="roleId" separator="," open="(" close=")">#{roleId}</foreach>
				)
	) res
	<where>
	<if test="eventTwoType != null and eventTwoType > 0 ">
	AND event_two_type=#{eventTwoType}
	</if>
	<if test="eventThreeType != null and eventThreeType > 0 ">
	AND event_Three_Type=#{eventThreeType}
	</if>
	<if test="eventFourType != null and eventFourType > 0 ">
	AND event_four_Type=#{eventFourType}
	</if>
	<if test="roadNo != null and roadNo > 0">
	AND road_no=#{roadNo}
	</if>
	<if test="startTime != null and startTime != '' ">
		AND report_time >= #{startTime}
		AND  #{endTime}>= report_time
	</if>
	<if test="keyword != null and keyword != '' ">
	AND (event_no LIKE CONCAT('%',#{keyword},'%')
		OR car_plate LIKE CONCAT('%',#{keyword},'%')
		OR brief_desc LIKE CONCAT('%',#{keyword},'%')
		OR report_man LIKE CONCAT('%',#{keyword},'%')
		OR report_man_tel LIKE CONCAT('%',#{keyword},'%')
		OR record_man LIKE CONCAT('%',#{keyword},'%')
		OR record_man_tel LIKE CONCAT('%',#{keyword},'%'))
	</if>
	</where> ORDER BY deal_status ASC,report_time DESC
</select>

<!-- 查询某个事件的顺序序号 -->
<select id="selectSeqNo" parameterType="com.bt.itsevent.domain.dto.EventQueryDTO" resultType="com.bt.itsevent.domain.vo.EmerResuceVO">
	SELECT DISTINCT res.id,res.deal_status,res.report_time from (
		SELECT DISTINCT e.id,e.deal_status,e.report_time FROM `event` e
		WHERE e.event_type=1 AND (e.record_man_id=#{userId} OR e.app_record_man_id=#{userId}) AND e.del_status=0
		<if test="dealStatus != null and dealStatus == 0 "> AND e.deal_status > 0 AND e.deal_status &lt; 100 </if>
		<if test="dealStatus != null and dealStatus > 0 "> AND deal_status =#{dealStatus} </if>
		UNION ALL
		SELECT DISTINCT e.id,e.deal_status,e.report_time FROM `event` e,event_emer_user eu
		WHERE e.event_type=1 AND e.id=eu.event_id AND eu.user_id=#{userId} AND e.del_status=0 
		<if test="dealStatus != null and dealStatus == 0 "> AND e.deal_status > 0 AND e.deal_status &lt; 100 </if>
		<if test="dealStatus != null and dealStatus > 0 "> AND deal_status =#{dealStatus} </if>
		UNION ALL
		SELECT DISTINCT e.id,e.deal_status,e.report_time FROM `event` e 
		WHERE e.event_type=1 AND e.del_status=0
		<if test="dealStatus != null and dealStatus == 0 "> AND e.deal_status > 0 AND e.deal_status &lt; 100 </if>
		<if test="dealStatus != null and dealStatus > 0 "> AND deal_status =#{dealStatus} </if>
		AND e.org_id IN (SELECT DISTINCT org_id FROM role_event re WHERE re.role_id IN 
		<foreach collection="roleIds" item="roleId" separator="," open="(" close=")">#{roleId}</foreach>
		)
	) res
	<where>
	<if test="startTime != null and startTime != '' ">
		AND res.report_time >= #{startTime}
		AND  #{endTime}>= res.report_time
	</if>
	</where>
	ORDER BY deal_status ASC,report_time DESC LIMIT 100
</select>
<resultMap type="com.bt.itsevent.domain.vo.EventTypeVO" id="EventTypeMap">
<result column="pid" property="pid"/>
<result column="id" property="value"/>
<result column="name" property="label"/>
<result column="often" property="often"/>
</resultMap>
<select id="selectEmerRescueType" resultMap="EventTypeMap">
SELECT pid, id, name, often FROM event_type WHERE flag='DD' AND status = 1
</select>
<select id="selectEventType" parameterType="com.bt.itsevent.domain.dto.EventTypeDTO" resultMap="EventTypeMap">
SELECT pid, id, name, often FROM event_type 
WHERE status = 1
<if test="flag != null and flag != ''">
AND flag=#{flag}
</if>
</select>

<resultMap id="BusinessTypeMap" type="com.bt.itsevent.domain.vo.BusinessTypeVO">
	<result column="id" property="id"/>
	<result column="parent_id" property="parentId"/>
	<result column="name" property="name"/>
	<result column="value" property="value"/>
</resultMap>
<select id="selectBusinessType"	 resultMap="BusinessTypeMap">
	SELECT id, parent_id, value, name FROM dict_item
	WHERE 1 = 1
	<if test="typeId != null and typeId != ''">
		AND type_id=#{typeId}
	</if>
</select>

<resultMap type="com.bt.itsevent.domain.vo.EventDetailVO" id="EmerRescueDetailMap">
<id column="id" property="id"/>
<result column="busi_id" property="busiId"/>
<result column="brief_desc" property="briefDesc"/>
<result column="deal_result" property="dealResult"/>
<result column="source_id" property="sourceId"/>
<result column="source" property="source"/>
<result column="level" property="level"/>
<result column="event_type" property="eventType"/>
<result column="event_two_type" property="eventTwoType"/>
<result column="event_three_type" property="eventThreeType"/>
<result column="event_three_type_name" property="eventThreeTypeName"/>
<result column="event_four_type" property="eventFourType"/>
<result column="event_four_type_name" property="eventFourTypeName"/>
<result column="deal_status" property="dealStatus"/>
<result column="del_status" property="delStatus"/>
<result column="event_status" property="eventStatus"/>
<result column="event_no" property="eventNo"/>
<result column="report_man" property="reportMan"/>
<result column="report_man_tel" property="reportManTel"/>
<result column="record_man_id" property="recordManId"/>
<result column="record_man" property="recordMan"/>
<result column="record_man_tel" property="recordManTel"/>
<result column="order_create_time" property="orderCreateTime"/>
<result column="report_time" property="reportTime"/>
<result column="distribute_time" property="distributeTime"/>
<result column="car_plate" property="carPlate"/>
<result column="car_type" property="carType"/>
<result column="car_man" property="carMan"/>
<result column="road_no" property="roadNo"/>
<result column="road_name" property="roadName"/>
<result column="direction_no" property="directionNo"/>
<result column="direction_name" property="directionName"/>
<result column="mile_post" property="milePost"/>
<result column="ggj_mile_post" property="ggjMilePost"/>
<result column="lng" property="lng"/>
<result column="lat" property="lat"/>
<result column="death_man" property="deathMan"/>
<result column="miss_man" property="missMan"/>
<result column="injure_man" property="injureMan"/>
<result column="lat" property="lat"/>
<result column="emer_plan_id" property="emerPlanId"/>
<result column="org_id" property="orgId"/>
<result column="org_name" property="orgName"/>
<result column="short_name" property="shortName"/>
<result column="event_address" property="eventAddress"/>
<result column="ggj_road_no" property="ggjRoadNo"/>
<result column="final_report" property="finalReport"/>
<result column="income_lose" property="incomeLose"/>
<result column="congestion_length" property="congestionLength"/>
<result column="emer_plan_level_id" property="emerPlanLevelId"/>
<result column="weather" property="weather"/>
<result column="address" property="address"/>
<result column="facility_no" property="facilityNo"/>
<result column="facility_type_no" property="facilityTypeNo"/>
<result column="facility_name" property="facilityName"/>
<result column="accident_type" property="accidentType"/>
<result column="other_type_cause" property="otherTypeCause"/>
<result column="dange_flag" property="dangeFlag"/>
<result column="accident_cause" property="accidentCause"/>
<result column="other_cause" property="otherCause"/>
<result column="road_loss_cause" property="roadLossCause"/>
<result column="report_source" property="reportSource"/>
<result column="report_source_key" property="reportSourceKey"/>
<result column="finish_time" property="finishTime"/>
<result column="accident_car_num" property="accidentCarNum"/>
<result column="accident_num" property="accidentNum"/>
<result column="secondary_accidents" property="secondaryAccidents"/>
<result column="road_condition" property="roadCondition"/>
<result column="facility_type" property="facilityType"/>
<result column="goods_car" property="goodsCar"/>
<result column="empty_car" property="emptyCar"/>
<result column="goods" property="goods"/>
<result column="report_source_str" property="reportSourceStr"/>
<result column="report_time_str" property="reportTimeStr"/>
<result column="location_str" property="location"/>
<result column="event_desc_str" property="eventDesc"/>
<result column="business_type" property="businessType"/>
<result column="final_report_time" property="finalReportTime"/>
<result column="car_lane" property="carLane"/>
<result column="finish_type" property="finishType"/>
<result column="accident_pattern" property="accidentPattern"/>
<result column="reach_time" property="reachTime"/>
<result column="deal_response_time" property="dealResponseTime"/>
<result column="unblock" property="unblock"/>
<result column="unblock_time" property="unblockTime"/>
<result column="audit_status" property="auditStatus"/>
<result column="push_amap" property="pushAmap"/>
	<result column="integral_order_id" property="integralOrderId"/>
	<result column="rescue_status" property="rescueStatus"/>
<collection property="emerRoles" ofType="com.bt.itsevent.domain.vo.EmerRoleVO">
	<id column="emer_role_id" property="id"/>
	<result column="role_name" property="roleName"/>
</collection>
<collection property="occupiedLanes" ofType="com.bt.itsevent.domain.vo.OccupiedLaneVO">
	<id column="occupied_lane" property="occupiedLane"/>
</collection>
<collection property="attachs" ofType="com.bt.itsevent.domain.vo.EventAttachVO">
	<id column="attach_id" property="id"/>
	<id column="disk_file_name" property="diskFileName"/>
	<id column="content_type" property="contentType"/>
</collection>
<collection property="roadLoss" ofType="com.bt.itsevent.domain.vo.EventRoadLossVO">
	<id column="type_code" property="typeCode"/>
	<id column="value" property="value"/>
</collection>
<collection property="carOwners" ofType="com.bt.itsevent.domain.vo.EventCarVO">
	<id column="ec_id" property="id"/>
	<result column="car_owner" property="carOwner"/>
	<result column="car_owner_tel" property="carOwnerTel"/>
	<result column="car_category" property="carCategory"/>
	<result column="ec_car_type" property="carType"/>
	<result column="car_axles" property="carAxles"/>
	<result column="ec_car_plate" property="carPlate"/>
	<result column="ec_car_man" property="carMan"/>
</collection>
</resultMap>
<select id="selectEmerRescueByEventId" parameterType="com.bt.itscore.domain.dto.IdStringDTO" resultMap="EmerRescueDetailMap">
SELECT e.*,ed.*,eer.emer_role_id,eer.role_name,o.org_name,o.short_name,eol.occupied_lane,r.road_name,d.direction_name,ea.disk_file_name,ea.content_type,ea.id as attach_id,rl.type_code,rl.`value`,
	et.`name` event_three_type_name,et4.`name` event_four_type_name,f.facility_type_no,f.facility_name
	,ec.id ec_id,ec.car_owner,ec.car_owner_tel,ec.car_category,ec.car_type ec_car_type,ec.car_axles,ec.car_plate ec_car_plate,ec.car_man ec_car_man
	,ed.report_source_str ,ed.report_time_str, ed.location_str, ed.event_desc_str,r.car_lane,e.finish_type, ed.push_amap, ed.integral_order_id, ed.rescue_status
	 FROM `event` e LEFT JOIN
	(SELECT a.event_id,a.emer_role_id,r.role_name FROM event_emer_role a,emer_role r WHERE a.emer_role_id=r.id) eer
	ON e.id=eer.event_id LEFT JOIN organization o ON e.org_id=o.org_id
	LEFT JOIN event_occupied_lane eol ON e.id=eol.event_id
	LEFT JOIN event_attach ea on ea.event_id = e.id
	LEFT JOIN road r ON e.road_no=r.road_no
	LEFT JOIN direction d ON e.direction_no=d.direction_no
	LEFT JOIN event_type et ON e.event_three_type=et.id
	LEFT JOIN event_type et4 ON e.event_four_type=et4.id
	LEFT JOIN event_road_loss rl ON rl.event_id=e.id
	LEFT JOIN facility f ON e.facility_no=f.facility_no
	LEFT JOIN event_car ec ON e.id=ec.event_id
	LEFT JOIN event_dd ed ON e.id=ed.event_id
WHERE e.id=#{id}
</select>

<resultMap type="com.bt.itsevent.domain.vo.EventDDVO" id="EventDDMap">
	<id column="event_id" property="eventId"/>
	<result column="accident_num" property="accidentNum"/>
	<result column="secondary_accidents" property="secondaryAccidents"/>
	<result column="accident_car_num" property="accidentCarNum"/>
	<result column="road_condition" property="roadCondition"/>
	<result column="goods_car" property="goodsCar"/>
	<result column="empty_car" property="emptyCar"/>
	<result column="goods" property="goods"/>
	<result column="facility_type" property="facilityType"/>
	<result column="report_source_str" property="reportSource"/>
	<result column="report_time_str" property="reportTime"/>
	<result column="location_str" property="location"/>
	<result column="event_desc_str" property="eventDesc"/>
	<result column="update_time" property="updateTime"/>
	<result column="reach_time" property="reachTime"/>
</resultMap>
<select id="selectEventDD" parameterType="com.bt.itscore.domain.dto.IdStringDTO" resultMap="EventDDMap">
SELECT * FROM event_dd WHERE event_id=#{id}
</select>

<insert id="addEventDD" parameterType="com.bt.itsevent.domain.dto.EventDDDTO">
INSERT IGNORE INTO event_dd (event_id,accident_car_num,secondary_accidents,accident_num,road_condition
	,facility_type,goods_car,empty_car,goods,integral_order_id,rescue_status)
VALUES (#{eventId},#{accidentCarNum},#{secondaryAccidents},#{accidentNum},#{roadCondition}
	,#{facilityType},#{goodsCar},#{emptyCar},#{goods},#{integralOrderId},#{rescueStatus})
</insert>

<update id="updateEventDD" parameterType="com.bt.itsevent.domain.dto.EventDDDTO">
UPDATE event_dd
	<set>
		<if test = "accidentCarNum!=null"> accident_car_num=#{accidentCarNum},</if>
		<if test = "secondaryAccidents!=null"> secondary_accidents=#{secondaryAccidents},</if>
		<if test = "accidentNum!=null">	accident_num=#{accidentNum},</if>
		<if test = "roadCondition!=null"> road_condition=#{roadCondition},</if>
		<if test = "facilityType!=null">facility_type=#{facilityType},</if>
		<if test = "goodsCar!=null">goods_car=#{goodsCar},</if>
		<if test = "emptyCar!=null">empty_car=#{emptyCar},</if>
		<if test = "goods!=null">goods=#{goods},</if>
		<if test = "reportTime!=null">report_time_str=#{reportTime},</if>
		<if test = "reportSource!=null">report_source_str=#{reportSource},</if>
		<if test = "location!=null">location_str=#{location},</if>
		<if test = "eventDesc!=null">event_desc_str=#{eventDesc},</if>
		<if test = "cameraId != null"> camera_id=#{cameraId},</if>
		<if test = "rescueStatus != null"> rescue_status=#{rescueStatus},</if>
		update_time=#{updateTime}
	</set>
WHERE event_id=#{eventId}
</update>

<select id="selectEventDetailById" parameterType="com.bt.itsevent.domain.dto.EventQueryDTO" resultMap="EmerRescueDetailMap">
SELECT e.*,eer.*,o.org_name,o.short_name,eol.occupied_lane,r.road_name,d.direction_name,et.`name` event_three_type_name,epu.card_pass canOutSet
FROM `event` e LEFT JOIN
	(SELECT a.event_id,a.emer_role_id,r.role_name FROM event_emer_role a,emer_role r WHERE a.emer_role_id=r.id) eer
	ON e.id=eer.event_id LEFT JOIN organization o ON e.org_id=o.org_id
	LEFT JOIN event_occupied_lane eol ON e.id=eol.event_id
	LEFT JOIN road r ON e.road_no=r.road_no
	LEFT JOIN direction d ON e.direction_no=d.direction_no
	LEFT JOIN event_type et ON e.event_three_type=et.id
	LEFT JOIN event_progress ep on e.id = ep.event_id and ep.card_type = 15
	LEFT JOIN event_progress_user epu on ep.id = epu.event_progress_id AND epu.user_id = #{userId} and epu.card_pass = 1
WHERE e.id=#{id}
</select>


<resultMap type="com.bt.itsevent.domain.vo.EventDetailVO" id="EventBaseInfoMap">
<id column="id" property="id"/>
<id column="busi_id" property="busiId"/>
<result column="brief_desc" property="briefDesc"/>
<result column="source_id" property="sourceId"/>
<result column="source" property="source"/>
<result column="level" property="level"/>
<result column="event_type" property="eventType"/>
<result column="event_two_type" property="eventTwoType"/>
<result column="event_three_type" property="eventThreeType"/>
<result column="event_three_type_name" property="eventThreeTypeName"/>
<result column="event_four_type" property="eventFourType"/>
<result column="deal_status" property="dealStatus"/>
<result column="event_status" property="eventStatus"/>
<result column="event_no" property="eventNo"/>
<result column="report_man" property="reportMan"/>
<result column="report_man_tel" property="reportManTel"/>
<result column="record_man_id" property="recordManId"/>
<result column="record_man" property="recordMan"/>
<result column="record_man_tel" property="recordManTel"/>
<result column="report_time" property="reportTime"/>
<result column="car_plate" property="carPlate"/>
<result column="car_type" property="carType"/>
<result column="car_man" property="carMan"/>
<result column="road_no" property="roadNo"/>
<result column="road_name" property="roadName"/>
<result column="direction_no" property="directionNo"/>
<result column="direction_name" property="directionName"/>
<result column="mile_post" property="milePost"/>
<result column="lng" property="lng"/>
<result column="lat" property="lat"/>
<result column="death_man" property="deathMan"/>
<result column="miss_man" property="missMan"/>
<result column="injure_man" property="injureMan"/>
<result column="lat" property="lat"/>
<result column="emer_plan_id" property="emerPlanId"/>
<result column="org_id" property="orgId"/>
<result column="org_name" property="orgName"/>
<result column="event_address" property="eventAddress"/>
<result column="ggj_road_no" property="ggjRoadNo"/>
<result column="final_report" property="finalReport"/>
<result column="income_lose" property="incomeLose"/>
<result column="congestion_length" property="congestionLength"/>
<result column="facility_no" property="facilityNo"/>
<result column="final_report_time" property="finalReportTime"/>
<result column="finish_time" property="finishTime"/>
</resultMap>
<select id="selectBaseInfoByEventId" parameterType="com.bt.itscore.domain.dto.IdStringDTO" resultMap="EventBaseInfoMap">
SELECT * FROM `event` WHERE id=#{id}
</select>

<select id="selectBaseInfoByEventNo" parameterType="com.bt.itscore.domain.dto.IdStringDTO" resultMap="EventBaseInfoMap">
SELECT * FROM `event` WHERE event_no=#{id}
</select>

<update id="deleteAndUpdateEventNo" parameterType="java.util.Map">
UPDATE `event` SET event_no=#{eventNo},del_status=1 WHERE id=#{id}
</update>

<!-- 更新事件删除状态为0 -->
<update id="updateDelStatus0" parameterType="com.bt.itscore.domain.dto.IdStringDTO">
UPDATE `event` SET del_status=0,event_status=2,deal_status=3,org_id=#{orgId},distribute_time=#{distributeTime} WHERE id=#{id}
</update>

<delete id="clearCarOwners" parameterType="com.bt.itscore.domain.dto.IdStringDTO">
	DELETE FROM event_car WHERE event_id=#{id}
</delete>

<insert id="addCarOwners" parameterType="com.bt.itsevent.domain.dto.EmerRescueDTO">
	INSERT INTO event_car (event_id,car_owner,car_owner_tel,car_category,car_type,car_axles,car_plate,car_man,create_time) VALUES
	<foreach collection="carOwners" item="c" separator=",">
	(#{id},#{c.carOwner},#{c.carOwnerTel},#{c.carCategory},#{c.carType},#{c.carAxles}
	,#{c.carPlate},#{c.carMan},#{serverTime})
	</foreach>
</insert>

<update id="updateEmerRescue" parameterType="com.bt.itsevent.domain.dto.EmerRescueDTO">
	UPDATE event SET
	<trim suffixOverrides=",">
		lng=#{lng},lat=#{lat},mile_post=#{milePost}
		<if test="eventType != null">,event_type=#{eventType}</if>
		<if test="eventTwoType != null">,event_two_type=#{eventTwoType}</if>
		<if test="eventThreeType != null">,event_three_type=#{eventThreeType}</if>
		<if test="eventThreeType != null">,event_four_type=#{eventFourType}</if>
		<if test="reportTime != null">,report_time=#{reportTime}</if>
		<if test="level != null">,level=#{level}</if>
		<if test="source != null">,source=#{source}</if>
		<if test="orgId != null">,org_id=#{orgId}</if>
		<if test="roadNo != null">,road_no=#{roadNo}</if>
		<if test="directionNo != null and directionNo !=''">,direction_no=#{directionNo}</if>
		,report_man=#{reportMan}
		,report_man_tel=#{reportManTel}
		<if test="recordManId != null and recordManId !=''">,record_man_id=#{recordManId}</if>
		<if test="recordMan != null and recordMan !=''">,record_man=#{recordMan}</if>
		<if test="recordManTel != null and recordManTel !=''">,record_man_tel=#{recordManTel}</if>
		,car_plate=#{carPlate}
		,car_type=#{carType}
		,car_man=#{carMan}
		<if test="deathMan != null">,death_man=#{deathMan}</if>
		<if test="missMan != null">,miss_man=#{missMan}</if>
		<if test="injureMan != null">,injure_man=#{injureMan}</if>
		,congestion_length=#{congestionLength}
		<if test="incomeLose != null">,income_lose=#{incomeLose}</if>
		<if test="briefDesc != null">,brief_desc=#{briefDesc}</if>
		<if test="activeStatus != null">,active_status=#{activeStatus}</if>
		<if test="sourceId != null and sourceId > 0">,source_id=#{sourceId}</if>
		<if test="weather != null">,weather=#{weather}</if>
		<if test="address != null">,address=#{address}</if>
		<if test="facilityNo != null">,facility_no=#{facilityNo}</if>
	   	<if test="accidentType != null">,accident_type=#{accidentType}</if>
	   	<if test="otherTypeCause != null">,other_type_cause=#{otherTypeCause}</if>
	   	<if test="dangeFlag != null">,dange_flag=#{dangeFlag}</if>
	   	<if test="accidentCause != null">,accident_cause=#{accidentCause}</if>
	   	<if test="otherCause != null">,other_cause=#{otherCause}</if>
	   	<if test="roadLossCause != null">,road_loss_cause=#{roadLossCause}</if>
	   	<if test="businessType != null and businessType !='' ">,business_type=#{businessType}</if>
	   	,report_source=#{reportSource}
	   	,report_source_key=#{reportSourceKey}
	</trim>
	WHERE id=#{id}
</update>

<delete id="deleteOccupiedLane" parameterType="com.bt.itsevent.domain.dto.EmerRescueDTO">
	DELETE FROM event_occupied_lane WHERE event_id=#{id}
</delete>

<!-- 完结事件 -->
<update id="finishEmerRescue" parameterType="com.bt.itsevent.domain.dto.EventFinishDTO">
UPDATE `event` SET deal_status=100,event_status=8, finish_user_id=#{finishUserId}, deal_result=#{dealResult},finish_time=#{finishTime},finish_platform=1
	<if test="confirmTime != null">,confirm_time=#{confirmTime}</if>
	<if test="distributeTime != null">,distribute_time=#{distributeTime}</if>
	<if test="finishType != null">,finish_type=#{finishType}</if>
	<if test="businessType != null and businessType !='' ">,business_type=#{businessType}</if>
	<if test="finalReportTime != null">,final_report_time=#{finalReportTime}</if>
	<if test="accidentCause != null">,accident_cause=#{accidentCause}</if>
	<if test="otherCause != null">,other_cause=#{otherCause}</if>
	<if test="weather != null">,weather=#{weather}</if>
WHERE id=#{id}
</update>

<!-- 更新事件的应急预案ID -->
<update id="updateEmerPlanId" parameterType="com.bt.itsevent.domain.dto.EventEmerUserDTO">
UPDATE `event` SET emer_plan_id=#{emerPlanId} WHERE id=#{id}
</update>

<!-- 事件关联应急人员 -->
<insert id="emerUser" parameterType="com.bt.itsevent.domain.dto.EventEmerUserDTO">
insert into event_emer_user (event_id,emer_role_id,emer_group_id,user_id,mobile,post,force_remind,sms_remind) values 
<foreach collection="emerUsers" item="emerUser" separator=",">
<foreach collection="emerUser.users" item="user" separator=",">
(
#{id},#{emerUser.emerRoleId},#{emerUser.emerGroupId},#{user.userId},#{user.mobile},#{user.post},#{user.forceRemind},#{user.smsRemind}
)
</foreach>
</foreach>
<if test="emerRoles != null and emerRoles.size() > 0">
;
INSERT INTO event_emer_role (event_id,emer_role_id) VALUES 
<foreach collection="emerRoles" item="emerRole" separator=",">
(
#{id},#{emerRole}
)
</foreach>
</if>
</insert>

<!-- 删除事件关联应急人员 -->
<delete id="deleteEmerUser" parameterType="com.bt.itsevent.domain.dto.EventEmerUserDTO">
DELETE FROM event_emer_user WHERE event_id=#{id};
DELETE FROM event_emer_role WHERE event_id=#{id}
</delete>

<insert id="addRemoveProgressUser" parameterType="java.util.Map">
insert into event_progress_user (event_progress_id,user_id,user_name,mobile,sms_send_status,sms_fail_reason
,biz_id,send_time,job_id,reference_id,robot_call_status,robot_fail_reason,robot_call_time,del_status) values 
<foreach collection="removeProgressUsers" item="u" separator=",">
(#{u.eventProgressId},#{u.userId},#{u.userName},#{u.mobile},#{u.smsSendStatus},#{u.smsFailReason}
,#{u.bizId},#{u.sendTime},#{u.jobId},#{u.referenceId},#{u.robotCallStatus},#{u.robotFailReason},#{u.robotCallTime},1)
</foreach>
</insert>

<!-- 更新事件状态event_status -->
<update id="updateEventStatus" parameterType="java.util.Map">
UPDATE `event` SET event_status=#{eventStatus} WHERE id=#{id} AND #{eventStatus} > event_status
</update>

<!-- 更新事件归档中其他状态信息 -->
<update id="updateEventByOtherStatus" parameterType="java.util.Map">
    UPDATE `event` 
        <set>
		 <if test="activeStatus != null &amp;&amp; activeStatus != '' "> active_status=#{activeStatus}, </if>
		 <if test="appraiseStatus != null &amp;&amp; appraiseStatus != '' "> appraise_status=#{appraiseStatus}, </if>
		 <if test="eventAnalyse != null &amp;&amp; eventAnalyse != '' "> event_analyse=#{eventAnalyse}, </if>
		 <if test="fillMan != null &amp;&amp; fillMan != '' "> fill_man=#{fillMan}, </if>
		 <if test="fillTime != null &amp;&amp; fillTime != '' "> fill_time=#{fillTime}, </if>
		 <if test="evnetFileAnalyse != null &amp;&amp; evnetFileAnalyse != '' "> evnet_file_analyse=#{evnetFileAnalyse}, </if>
		 <if test="suggestion != null &amp;&amp; suggestion != '' "> suggestion=#{suggestion}, </if>
		</set>		
     WHERE id=#{id}
</update>
<!-- 判断事件是否处于归档激活编辑状态 -->
 <select id="checkActiveStatus" parameterType="java.lang.String" resultType="java.lang.Integer">
	     SELECT active_status FROM `event` where id=#{id}
</select>


<!-- 更新事件的排障派单情况 -->
<update id="updateCarOrder" parameterType="com.bt.itsevent.domain.dto.RemoveObstaclesDTO">
UPDATE `event` SET car_order=#{carOrder} WHERE id=#{id}
</update>

<resultMap type="com.bt.itsevent.domain.vo.RemoveObstaclesUserVO" id="RemoveObstaclesUserMap">
<id column="event_id" property="id"/>
<collection property="emerGroups" ofType="com.bt.itsevent.domain.vo.EmerGroupVO">
<id column="emer_group_id" property="emerGroupId"/>
<id column="group_name" property="groupName"/>
<collection property="users" ofType="com.bt.itsevent.domain.dto.ProgressUserDTO">
<id column="user_id" property="userId"/>
<id column="user_name" property="userName"/>
</collection>
</collection>
</resultMap>
<!-- 查询某个事件关联的排障组及人员 -->
<select id="selectRmObstaclesById" parameterType="com.bt.itsevent.domain.dto.RemoveObstaclesDTO" resultMap="RemoveObstaclesUserMap">
SELECT eeu.event_id,eeu.user_id,u.user_name,eeu.emer_group_id,eg.group_name,er.role_name,er.sort,eg.sort AS sort2 FROM emer_group eg,emer_role er,event_emer_user eeu
LEFT JOIN `user` u ON eeu.user_id=u.user_id
WHERE eeu.emer_group_id=eg.id AND eg.role_id=er.id AND er.`value`=2 AND eeu.event_id=#{id} 
ORDER BY sort ASC,sort2 ASC
</select>
<select id="operateAuthorityById" parameterType="java.util.Map" resultType="java.lang.Integer">
SELECT SUM(e.a) FROM (
SELECT COUNT(0) a FROM event_emer_user WHERE event_id=#{id} AND user_id=#{userId}
UNION ALL 
SELECT COUNT(0) a FROM `event` WHERE id=#{id} AND record_man_id=#{userId}
) e
</select>
<select id="viewAuthorityById" parameterType="com.bt.itsevent.domain.dto.EventAuthorityDTO" resultType="java.lang.Integer">
SELECT SUM(c) FROM (
SELECT COUNT(0) c FROM `event` e WHERE e.record_man_id=#{userId} AND e.del_status=0
UNION ALL
SELECT COUNT(0) c FROM event_emer_user WHERE event_id=#{id} AND user_id=#{userId}
UNION ALL
SELECT COUNT(0) c FROM `event` e,event_organization eo 
WHERE e.id=eo.event_id AND e.del_status=0
AND eo.org_id IN (SELECT DISTINCT org_id FROM role_event re WHERE re.role_id IN 
<foreach collection="roleIds" item="roleId" separator="," open="(" close=")">#{roleId}</foreach>
)
) a
</select>

<select id="selectEmerRoles" parameterType="com.bt.itscore.domain.dto.IdStringDTO" resultType="com.bt.itsevent.domain.vo.EmerRoleVO">
<!-- SELECT er.id,er.role_name roleName FROM emer_plan_role epr,emer_role er WHERE epr.emer_role_id=er.id AND epr.emer_plan_id=#{id} -->
SELECT DISTINCT * FROM (
SELECT er.id,er.role_name roleName,er.sort FROM emer_role er WHERE er.del_status=0
) AS t ORDER BY t.sort ASC
</select>

<resultMap type="com.bt.itsevent.domain.vo.EventEmerUserVO" id="EventEmerUserMap">
	<result column="event_id" property="eventId"/>
	<result column="emer_group_id" property="emerGroupId"/>
	<result column="user_id" property="userId"/>
	<result column="user_name" property="userName"/>
	<result column="mobile" property="mobile"/>
	<result column="force_remind" property="forceRemind"/>
	<result column="sms_remind" property="smsRemind"/>
</resultMap>
<select id="selectEmerUserById" parameterType="com.bt.itscore.domain.dto.IdStringDTO" resultMap="EventEmerUserMap">
SELECT event_id,emer_group_id,eeu.user_id,IFNULL(u.user_name,eeu.user_id) AS user_name,IFNULL(u.mobile,eeu.mobile) AS mobile,IFNULL(eeu.force_remind,0) AS force_remind,IFNULL(eeu.sms_remind,0) AS sms_remind FROM event_emer_user eeu
LEFT JOIN `user` u ON eeu.user_id=u.user_id
WHERE eeu.event_id=#{id}
</select>

<resultMap type="com.bt.itsevent.domain.vo.GroupEventEmerUserVO" id="GroupEventEmerUserMap">
	<id column="emer_role_id" property="emerRoleId"/>
	<result column="event_id" property="eventId"/>
	<result column="role_name" property="emerRoleName"/>
	<collection property="emerGroups" ofType="com.bt.itsevent.domain.vo.EventEmerGroupVO">
		<id column="emer_group_id" property="emerGroupId"/>
		<result column="group_name" property="emerGroupName"/>
		<result column="force_remind" property="forceRemind"/>
		<result column="sms_remind" property="smsRemind"/>
		<collection property="emerUsers" ofType="com.bt.itsevent.domain.vo.EmerUserVO">
			<result column="user_id" property="userId"/>
			<result column="user_name" property="userName"/>
			<result column="mobile" property="mobile"/>
			<result column="post" property="post"/>
		</collection>
	</collection>
</resultMap>
<select id="selectGroupEmerUserById" parameterType="com.bt.itscore.domain.dto.IdStringDTO" resultMap="GroupEventEmerUserMap">
	SELECT eeu.event_id,IFNULL(eeu.emer_role_id,'') emer_role_id,er.role_name,IFNULL(eeu.emer_group_id,'') emer_group_id,eg.group_name,eeu.user_id,IFNULL(u.user_name,eeu.user_id) AS user_name,IFNULL(u.mobile,eeu.mobile) AS mobile,eeu.post,eeu.force_remind,eeu.sms_remind FROM event_emer_user eeu
	LEFT JOIN  `user` u ON eeu.user_id=u.user_id
	  LEFT JOIN emer_role er ON eeu.emer_role_id=er.id
	  LEFT JOIN emer_group eg ON eeu.emer_group_id=eg.id
	WHERE eeu.event_id=#{id} ORDER BY er.sort ASC,eg.sort ASC
</select>

<select id="selectEmerPlan" parameterType="com.bt.itscore.domain.dto.IdStringDTO" resultType="com.bt.itsevent.domain.vo.EmerPlanVO">
SELECT `name` FROM emer_plan WHERE id=#{id}
</select>

<delete id="deleteSomeEmerUser" parameterType="java.util.List">
DELETE FROM event_emer_user IN <foreach collection="list" item="u" open="(" close=")" separator=",">
user_id=#{u.userId} AND event_id=#{u.eventId}
</foreach>
</delete>

<insert id="add96333Rescue" parameterType="com.bt.itscore.domain.dto.Event96333DTO">
INSERT INTO event (
  source,id,busi_id,event_no,event_type,
  event_two_type,event_three_type,car_type,deal_status,event_status,
  brief_desc,ggj_deal_type,report_man,report_man_tel,car_plate,
  event_address,report_time,order_create_time,ggj_road_no,mile_post,
  ggj_mile_post,ggj_deal_dept,complaint_type,complaint_target,create_time,
  org_id,road_no,direction_no,source_id,deal_result,deal_org_id,
  deal_org_name, business_type, report_source_key
) VALUES (
  #{source},
  #{id},
  #{busiId},
  #{eventNo},
  #{eventType},
  
  #{eventTwoType},
  #{eventThreeType},
  #{carType},
  #{dealStatus},
  #{eventStatus},

  #{briefDesc},
  #{ggjDealType},
  #{reportMan},
  #{reportManTel},
  #{carPlate},

  #{eventAddress},
  #{reportTime},
  #{orderCreateTime},
  #{ggjRoadNo},
  #{milePost},

  #{ggjMilePost},
  #{ggjDealDept},
  #{complaintType},
  #{complaintTarget},
  #{createTime},

  #{orgId},
  #{roadNo},
  #{directionNo},
  #{sourceId},
  #{dealResult},
  #{dealOrgId},
  #{dealOrgName},
  #{businessType},
  1
)
</insert>

<insert id="batchAddAlarmAttachs" parameterType="com.bt.itsevent.domain.dto.EmerRescueDTO">
INSERT INTO event_attach (event_id,file_name,disk_file_name,file_size,content_type,create_time) VALUES
<foreach collection="alarmAttachs" item="a" separator=",">
(
#{id},
#{a.fileName},
#{a.diskFileName},
#{a.fileSize},
#{a.contentType},
#{a.createTime}
)
</foreach>
</insert>

<select id="checkByEventNo" parameterType="com.bt.itscore.domain.dto.Event96333DTO" resultType="java.lang.Integer">
SELECT COUNT(0) FROM `event` WHERE event_no=#{eventNo}
</select>

<select id="selectByEventNo" parameterType="com.bt.itscore.domain.dto.Event96333DTO" resultType="java.lang.Integer">
SELECT * FROM `event` WHERE event_no=#{eventNo}
</select>

<select id="validRoadMilePost" parameterType="com.bt.itscore.domain.dto.Event96333DTO" resultType="java.lang.Integer">
SELECT COUNT(*) FROM road WHERE road_no=#{roadNo} AND start_mile &lt;= ${mile} AND end_mile >= ${mile}
</select>

<update id="update96333EmerRescue" parameterType="com.bt.itscore.domain.dto.Event96333DTO">
UPDATE `event` SET deal_status=#{dealStatus}, deal_result=#{dealResult} WHERE id=#{id}
</update>

<update id="updateFinalReport" parameterType="com.bt.itsevent.domain.dto.EventConfirmDTO">
UPDATE `event` SET final_report=1 
<if test="accidentCause != null">,accident_cause=#{accidentCause}</if>
<if test="otherCause != null">,other_cause=#{otherCause}</if>
<if test="dealResult != null">,deal_result=#{dealResult}</if>
<if test="finalReportTime != null">,final_report_time=#{finalReportTime}</if>
WHERE id=#{id}
</update>

<select id="selectJyType" resultMap="EventTypeMap">
SELECT pid, id, name, often FROM event_type WHERE (flag='TS' OR flag='JY') AND `level`=3 ORDER BY id ASC
</select>
<resultMap type="com.bt.itsevent.domain.vo.TsJyVO" id="TsJyMap">
	<id column="id" property="id"/>
	<result column="event_type" property="eventType"/>
	<result column="event_three_type" property="eventThreeType"/>
	<result column="event_four_type" property="eventFourType"/>
	<result column="deal_status" property="dealStatus"/>
	<result column="event_status" property="eventStatus"/>
	<result column="report_time" property="reportTime"/>
	<result column="brief_desc" property="briefDesc"/>
	<result column="report_man" property="reportMan"/>
	<result column="report_man_tel" property="reportManTel"/>
	<result column="car_plate" property="carPlate"/>
	<result column="source" property="source"/>
	<result column="deal_org_id" property="dealOrgId"/>
	<result column="deal_org_name" property="dealOrgName"/>
	<result column="event_no" property="eventNo"/>
	<result column="complaint_type" property="complaintType"/>
	<result column="finish_platform" property="finishPlatform"/>
	<result column="user2_id" property="user2Id"/>
	<result column="org_name" property="orgName"/>
	<result column="report_source_key" property="reportSourceKey"/>
</resultMap>

	<select id="selectTsJy" parameterType="com.bt.itsevent.domain.dto.EventQueryDTO" resultMap="TsJyMap">
		SELECT DISTINCT res.* from (
		SELECT DISTINCT e.id,e.event_no,finish_platform,e.event_type,e.event_two_type,e.event_three_type,e.event_four_type,
		e.deal_status,e.event_status,e.report_time,e.car_plate,e.brief_desc,e.record_man,e.record_man_tel,e.report_man,
		e.report_man_tel,e.road_no,e.direction_no,e.mile_post,e.lng,e.lat,e.source,e.step,e.deal_org_id,e.deal_org_name,
		e.complaint_type,ts.user2_id user2_id,o.short_name org_name,e.report_source_key
		FROM `event` e
		LEFT JOIN event_ts ts ON ts.event_id = e.id left join organization o ON e.org_id = o.org_id
		WHERE (e.event_type=2 OR e.event_type=3) AND e.record_man_id=#{userId} AND e.del_status=0 AND e.deal_status > 0
		<if test="startTime != null and startTime != '' ">
			AND e.report_time >= #{startTime}
			AND #{endTime}>= e.report_time
		</if>
		<if test="dealStatus != null and dealStatus == 0 ">
			AND e.deal_status &lt; 100
		</if>
		<if test="dealStatus != null and dealStatus > 0 ">
			AND deal_status =#{dealStatus}
		</if>
		<if test="step != null and step>0"> AND e.step=#{step} </if>
		<if test="step != null and step==0"> AND e.step &lt; 4 </if>
		<if test="dealOrg != null and dealOrg==1"> AND e.step!=2 </if>
		<if test="dealOrg != null and dealOrg==2"> AND e.step=2 </if>
		UNION ALL
		SELECT DISTINCT e.id,e.event_no,finish_platform,e.event_type,e.event_two_type,e.event_three_type,e.event_four_type,
		e.deal_status,e.event_status,e.report_time,e.car_plate,e.brief_desc,e.record_man,e.record_man_tel,e.report_man,
		e.report_man_tel,e.road_no,e.direction_no,e.mile_post,e.lng,e.lat,e.source,e.step,e.deal_org_id,e.deal_org_name,
		e.complaint_type,ts.user2_id user2_id,o.short_name org_name,e.report_source_key
		FROM `event` e,event_ts ts,organization o
		WHERE e.id=ts.event_id AND e.org_id = o.org_id AND (e.event_type=2 OR e.event_type=3) AND ts.user2_id=#{userId} AND e.del_status=0 AND e.deal_status > 0
		<if test="startTime != null and startTime != '' ">
			AND e.report_time >= #{startTime}
			AND #{endTime}>= e.report_time
		</if>
		<if test="step != null and step>0"> AND e.step=#{step} </if>
		<if test="step != null and step==0"> AND e.step &lt; 4 </if>
		<if test="dealOrg != null and dealOrg==1"> AND e.step!=2 </if>
		<if test="dealOrg != null and dealOrg==2"> AND e.step=2 </if>
		UNION ALL
		SELECT DISTINCT e.id,e.event_no,finish_platform,e.event_type,e.event_two_type,e.event_three_type,e.event_four_type,
		e.deal_status,e.event_status,e.report_time,e.car_plate,e.brief_desc,e.record_man,e.record_man_tel,e.report_man,
		e.report_man_tel,e.road_no,e.direction_no,e.mile_post,e.lng,e.lat,e.source,e.step,e.deal_org_id,e.deal_org_name,
		e.complaint_type,ts.user2_id user2_id,o.short_name org_name,e.report_source_key
		FROM `event` e
		left join event_ts ts ON ts.event_id = e.id left join organization o ON e.org_id = o.org_id
		WHERE (e.event_type=2 OR e.event_type=3) AND e.del_status=0 AND e.deal_status > 0
		<if test="startTime != null and startTime != '' ">
			AND e.report_time >= #{startTime}
			AND #{endTime}>= e.report_time
		</if>
		<if test="dealStatus != null and dealStatus == 0 ">
			AND e.deal_status &lt; 100
		</if>
		<if test="dealStatus != null and dealStatus > 0 ">
			AND deal_status =#{dealStatus}
		</if>
		<if test="step != null and step>0"> AND e.step=#{step} </if>
		<if test="step != null and step==0"> AND e.step &lt; 4 </if>
		<if test="dealOrg != null and dealOrg==1"> AND e.step!=2 </if>
		<if test="dealOrg != null and dealOrg==2"> AND e.step=2 </if>
		AND e.org_id IN (SELECT DISTINCT org_id FROM role_event re WHERE re.role_id IN
		<foreach collection="roleIds" item="roleId" separator="," open="(" close=")">#{roleId}</foreach>
		)
		) res
		<where>
			<if test="eventType != null and eventType > 0 ">
				AND res.event_type=#{eventType}
			</if>
			<if test="eventTwoType != null and eventTwoType > 0 ">
				AND res.event_two_type=#{eventTwoType}
			</if>
			<if test="eventThreeType != null and eventThreeType > 0 ">
				AND res.event_Three_Type=#{eventThreeType}
			</if>
			<if test="eventFourType != null and eventFourType > 0 ">
				AND res.event_four_Type=#{eventFourType}
			</if>
			<if test="dealStatus != null and dealStatus > 0 ">
				AND res.deal_status =#{dealStatus}
			</if>
			<if test="dealStatus != null and dealStatus == 0 ">
				AND res.deal_status &lt; 100
			</if>
			<if test="keyword != null and keyword != '' ">
				AND (res.event_no LIKE CONCAT('%',#{keyword},'%')
				OR res.car_plate LIKE CONCAT('%',#{keyword},'%')
				OR res.brief_desc LIKE CONCAT('%',#{keyword},'%')
				OR res.report_man LIKE CONCAT('%',#{keyword},'%')
				OR res.report_man_tel LIKE CONCAT('%',#{keyword},'%')
				OR res.record_man LIKE CONCAT('%',#{keyword},'%')
				OR res.record_man_tel LIKE CONCAT('%',#{keyword},'%')
				OR res.event_no LIKE CONCAT('%',#{keyword},'%'))
			</if>
		</where> ORDER BY res.deal_status ASC,res.report_time DESC
	</select>

<resultMap type="com.bt.itsevent.domain.vo.TsJyDetailVO" id="TsJyDetailMap">
	<id column="id" property="id"/>
	<result column="busi_id" property="busiId"/>
	<result column="event_no" property="eventNo"/>
	<result column="brief_desc" property="briefDesc"/>
	<result column="report_time" property="reportTime"/>
	<result column="event_three_type" property="eventThreeType"/>
	<result column="complaint_type" property="complaintType"/>
	<result column="complaint_target" property="complaintTarget"/>
	<result column="report_man" property="reportMan"/>
	<result column="report_man_tel" property="reportManTel"/>
	<result column="car_plate" property="carPlate"/>
	<result column="source" property="source"/>
	<result column="report_source_key" property="reportSourceKey"/>
	<result column="deal_org_id" property="dealOrgId"/>
	<result column="deal_org_name" property="dealOrgName"/>
	<result column="step" property="step"/>
	<result column="deal_result" property="dealResult"/>
	<result column="deal_status" property="dealStatus"/>
	<result column="source_id" property="sourceId"/>
	<result column="et_id" property="etId"/>
	<result column="user1_id" property="user1Id"/>
	<result column="user2_id" property="user2Id"/>
	<result column="user3_id" property="user3Id"/>
	<result column="user4_id" property="user4Id"/>
	<result column="kf_advice" property="kfAdvice"/>
	<result column="kf_finish_advice" property="kfFinishAdvice"/>
	<result column="build_result" property="buildResult"/>
	<result column="build_deal_advice" property="buildDealAdvice"/>
	<result column="revisit" property="revisit"/>
	<result column="satisfaction" property="satisfaction"/>
	<result column="org_id" property="orgId"/>
	<result column="company1_id" property="company1Id"/>
	<result column="finish_platform" property="finishPlatform"/>
	<result column="business_type" property="businessType"/>
	<result column="name" property="businessTypeName"/>
	<result column="org_name" property="orgName"/>
	<result column="complaint_supplement" property="complaintSupplement"/>
</resultMap>
<select id="selectTsJyByEventId" parameterType="com.bt.itscore.domain.dto.IdStringDTO" resultMap="TsJyDetailMap">
SELECT e.id,e.busi_id,e.event_no,finish_platform,e.brief_desc,e.report_time,e.event_three_type,e.complaint_type,e.complaint_target,e.report_man,e.report_man_tel,e.car_plate,
	e.source,e.report_source_key,e.deal_org_id,e.deal_org_name,e.step,e.deal_result,e.deal_status,e.source_id,
et.id et_id,et.user1_id,et.user2_id,et.user3_id,et.user4_id,et.kf_advice,et.kf_finish_advice,et.build_result,et.build_deal_advice,IF(ev.appraisal, 1, 0) revisit,ev.appraisal AS satisfaction,e.org_id,et.company1_id,
e.business_type,di.name,o.org_name,e.complaint_supplement
FROM `event` e LEFT JOIN event_ts et ON e.id=et.event_id LEFT JOIN event_visit ev ON e.id=ev.event_id
LEFT JOIN dict_item di ON di.value=e.business_type
LEFT JOIN organization o ON e.org_id = o.org_id
WHERE e.id=#{id} AND (e.event_type=2 OR e.event_type=3)
</select>

<insert id="addEventTs" parameterType="com.bt.itsevent.domain.dto.EventTsDTO">
INSERT INTO event_ts (id, event_id, kf_advice, user1_id, company1_id, user2_id, step, is_praise) VALUES (#{etId},#{id},#{kfAdvice},#{user1Id},#{company1Id},#{user2Id},#{step},#{isPraise})
</insert>

<update id="updateEventTs" parameterType="com.bt.itsevent.domain.dto.EventTsDTO">
UPDATE event_ts
<set>
<if test="kfAdvice != null">,kf_advice=#{kfAdvice}</if>
<if test="user1Id != null">,user1_id=#{user1Id}</if>
<if test="user2Id != null">,user2_id=#{user2Id}</if>
<if test="step != null">,step=#{step}</if>
<if test="user3Id != null">,user3_id=#{user3Id}</if>
<if test="buildResult != null">,build_result=#{buildResult}</if>
<if test="buildDealAdvice != null">,build_deal_advice=#{buildDealAdvice}</if>
<if test="kfFinishAdvice != null">,kf_finish_advice=#{kfFinishAdvice}</if>
<if test="revisit != null">,revisit=#{revisit}</if>
<if test="satisfaction != null">,satisfaction=#{satisfaction}</if>
<if test="user4Id != null">,user4_id=#{user4Id}</if>
<if test="dealOrgName != null">,org2_name=#{org2Name}</if>
<if test="isPraise != null">,is_praise=#{isPraise}</if>
</set>
 WHERE id=#{etId} AND event_id=#{id}
</update>

<update id="updateTsStep" parameterType="com.bt.itsevent.domain.dto.EventTsDTO">
UPDATE `event` SET deal_status=3  
<if test="dealOrgId != null">,deal_org_id=#{dealOrgId}</if>
<if test="dealOrgName != null">,deal_org_name=#{dealOrgName}</if>
<if test="step != null">,step=#{step}</if>
<!-- <if test="company2Id != null">,org_id=#{company2Id}</if> -->
<if test="complaintType != null">,complaint_type=#{complaintType}</if>
<if test="businessType != null">,business_type=#{businessType}</if>
 WHERE id=#{id}
</update>

<update id="finishTsJy" parameterType="com.bt.itsevent.domain.dto.EventTsDTO">
UPDATE `event` e, event_ts et SET e.deal_status=100, e.deal_result=#{dealResult}, e.complaint_type=#{complaintType},
 e.finish_time=#{finishTime},e.step=4,e.finish_platform=1,et.build_result=#{buildResult}, et.build_deal_advice=#{buildDealAdvice}
 WHERE e.id=#{id} AND e.id=et.event_id
</update>

<update id="updateTsJyRevisit" parameterType="com.bt.itsevent.domain.dto.EventTsDTO">
UPDATE event_ts SET revisit=#{revisit},satisfaction=#{satisfaction}
 WHERE event_id=#{id}
</update>

<select id="todayAvgDealTime" resultType="int">
SELECT IFNULL(ROUND(AVG((final_report_time-order_create_time))),0) FROM `event` WHERE report_time> UNIX_TIMESTAMP(CAST(SYSDATE()AS DATE)) AND event_type=1 AND deal_status=100 AND del_status=0
</select>

<select id="selectEventHeat" parameterType="java.util.Map" resultMap="EventHeatMap">
	SELECT case when e.org_id = '60381fcb-1138-11ec-a11a-7c8ae1d066a4' THEN 1
	when e.org_id = '4a5c49a9-8152-4d38-9666-e536725c6670' THEN 2
	when e.org_id = 'c8d11e42-3cd2-48e0-9bae-b1d462c7ed26' THEN 3
	when e.org_id = '681bbd49-1138-11ec-a11a-7c8ae1d066a4' THEN 4
	else 5 end as source_id,
	e.brief_desc,e.mile_post,e.lng,e.lat,e.road_no,et.name three_type_name FROM event e ,
	event_type et WHERE e.event_three_type = et.id
	AND e.del_status = 0 AND e.deal_status > 2 AND to_days(FROM_UNIXTIME(e.report_time)) = to_days(now()) AND
	e.org_id in ('60381fcb-1138-11ec-a11a-7c8ae1d066a4','681bbd49-1138-11ec-a11a-7c8ae1d066a4',
	'4a5c49a9-8152-4d38-9666-e536725c6670','c8d11e42-3cd2-48e0-9bae-b1d462c7ed26')
	AND
	<foreach collection="map.entrySet()" open="(" close=")" item="value" index="key" separator="or">
		 et.name = #{value}
	</foreach>
</select>

<select id="countEventTotal" parameterType="java.util.Map" resultType="java.lang.Integer">
	SELECT count(id) FROM `event` e WHERE
	FROM_UNIXTIME(e.report_time,'%Y-%m-%d') = #{compareDay}  AND
	e.del_status = 0 AND deal_status > 2 AND e.source_id = 1
	<if test="complaintType != null">
	AND event_type = 2
	</if>
</select>

<select id="checkEmerplanRoad" parameterType="java.util.Map" resultType="java.lang.String">
	SELECT DISTINCT ep.id from emer_plan ep,emer_plan_road epr WHERE ep.id=epr.emer_plan_id AND epr.road_no=#{roadNo} AND ep.id=#{emerPlanId}
</select>

<select id="nearestFacilityById" parameterType="java.util.Map" resultType="com.bt.itsevent.domain.vo.NearestFacilityTipVO">
SELECT FLOOR(ABS(mp_value-${mpValue})) distance, facility_name AS facilityName, mp_value AS mpValue FROM facility f
WHERE road_no=#{roadNo} AND facility_type_no IN (7,8) AND mp_value &lt;> '' ORDER BY distance ASC,facility_type_no DESC LIMIT 1
</select>

<insert id="addZx" parameterType="com.bt.itsevent.domain.dto.EventZxDTO">
	INSERT INTO `event` (id, source, event_no, event_type, event_two_type, 
	  event_three_type, brief_desc, report_time, report_man, report_man_tel,
	  create_user_id, org_id, create_time, order_create_time, deal_status, record_man, record_man_id,
	  record_man_tel, event_status, other_source, business_type, report_source_key
	  )
	VALUES (
	  #{id}, #{source}, #{eventNo}, 4, 17,
	  18, #{briefDesc}, #{reportTime}, #{reportMan}, #{reportManTel},
	  #{createUserId}, #{orgId}, #{createTime}, #{createTime}, 3, #{recordMan}, #{recordManId},
	  #{recordManTel}, #{eventStatus}, #{otherSource}, #{businessType}, #{reportSourceKey}
	)
</insert>

<insert id="addEventZx" parameterType="com.bt.itsevent.domain.dto.EventZxDTO">
	INSERT INTO event_zx (id, event_id, org1_id, user1_id, user1_name, create_time)
	VALUES (
	  #{zxId}, #{id}, #{org1Id}, #{user1Id}, #{user1Name}, #{createTime}
	)
</insert>

<!-- 完结咨询事件 -->
<update id="finishZx" parameterType="com.bt.itsevent.domain.dto.EventZxDTO">
	UPDATE `event` 
	  SET deal_status=100, event_status=8, finish_user_id=#{finishUserId}, 
	  finish_user_name=#{finishUserName}, deal_result=#{dealResult}, finish_time=#{finishTime},
	  business_type=#{businessType}
	WHERE id=#{id}
</update>

<update id="updateZx" parameterType="com.bt.itsevent.domain.dto.EventZxDTO">
	UPDATE `event`
	  SET brief_desc=#{briefDesc}, report_time=#{reportTime}, report_man=#{reportMan},
	      report_man_tel=#{reportManTel}, update_time=#{updateTime}, other_source=#{otherSource},
	      business_type=#{businessType}
	      <if test="source != null">, source=#{source}</if>
	      <if test="org1Id != null and org1Id !='' ">,org_id=#{org1Id}</if>
	      <if test="reportSourceKey != null">,report_Source_Key=#{reportSourceKey}</if>
	WHERE id=#{id}
</update>

<update id="updateEventZx" parameterType="com.bt.itsevent.domain.dto.EventZxDTO">
	UPDATE event_zx
	  SET feedback=#{feedback}, revisit=#{revisit}, satisfaction=#{satisfaction}
	  <if test="user1Id != null and user1Id !='' ">,user1_id=#{user1Id},user1_name=#{user1Name}</if>
	  <if test="org1Id != null and org1Id !='' ">,org1_id=#{org1Id}</if>
	  <if test="updateTime != null">,update_time=#{updateTime}</if>
	WHERE event_id=#{id}
</update>

<resultMap type="com.bt.itsevent.domain.vo.EventZxVO" id="EventZxMap">
	<id column="id" property="id"/>
	<result column="event_no" property="eventNo"/>
	<result column="source" property="source"/>
	<result column="other_source" property="otherSource"/>
	<result column="report_time" property="reportTime"/>
	<result column="brief_desc" property="briefDesc"/>
	<result column="create_user_id" property="createUserId"/>
	<result column="company_name" property="companyName"/>
	<result column="org_name" property="orgName"/>
	<result column="user_name" property="userName"/>
	<result column="user1_id" property="userId"/>
	<result column="company_id" property="companyId"/>
	<result column="org_id" property="orgId"/>
	<result column="deal_result" property="dealResult"/>
	<result column="finish_user_name" property="finishUserName"/>
	<result column="finish_time" property="finishTime"/>
	<result column="business_type_name" property="businessTypeName"/>
	<result column="report_source_key" property="reportSourceKey"/>
	<result column="report_man_tel" property="reportManTel"/>
</resultMap>
<select id="selectZx" parameterType="com.bt.itsevent.domain.dto.EventQueryDTO" resultMap="EventZxMap">
	SELECT e.id,e.event_no,e.source,e.other_source,e.report_time,e.brief_desc,e.create_user_id,e.deal_result,e.finish_user_name, e.finish_time,
	o.org_name AS company_name,o2.org_name AS org_name, zx.user1_id, u.user_name, u.company_id, u.org_id, t1.name AS business_type_name, e.report_source_key,e.report_man_tel
	FROM `event` AS e
		INNER JOIN event_zx AS zx ON e.id = zx.event_id
		LEFT JOIN `user` AS u ON zx.user1_id=u.user_id
		LEFT JOIN organization AS o ON zx.org1_id=o.org_id
		LEFT JOIN organization AS o2 ON u.org_id=o2.org_id
		LEFT JOIN ( SELECT `value`, `name` FROM dict_item WHERE type_id = 126 ) AS t1 ON t1.`value` = e.business_type
	WHERE e.del_status=0
	<choose>
		<when test="dealStatus != null and dealStatus == 100">
		AND e.deal_status = 100
		</when>
		<otherwise>
		AND 100 > e.deal_status
		</otherwise>
	</choose>
	<if test="startTime != null and startTime != '' ">
		AND e.report_time >= #{startTime}
		AND #{endTime}>= e.report_time
	</if>
	<if test="keyword != null and keyword != '' ">
		AND (e.event_no LIKE CONCAT('%',#{keyword},'%')
			OR e.brief_desc LIKE CONCAT('%',#{keyword},'%')
			OR e.report_man_tel LIKE CONCAT('%',#{keyword},'%')
			OR e.report_man LIKE CONCAT('%',#{keyword},'%')
			OR zx.user1_name LIKE CONCAT('%',#{keyword},'%')
			OR e.finish_user_name LIKE CONCAT('%',#{keyword},'%')
		)
	</if>
	<if test="organizations == null or organizations.size() == 0 "> AND (e.create_user_id=#{userId} OR zx.user1_id=#{userId}
		OR e.org_id IN (SELECT DISTINCT org_id FROM role_event re WHERE re.role_id IN 
		<foreach collection="roleIds" item="roleId" separator="," open="(" close=")">#{roleId}</foreach>
		)
		)
	</if>
	<if test="organizations != null and organizations.size()>0 "> AND e.org_id IN
	<foreach collection="organizations" item="orgId" open="(" separator="," close=")">#{orgId}</foreach>
	AND e.org_id IN (SELECT DISTINCT org_id FROM role_event re WHERE re.role_id IN 
		<foreach collection="roleIds" item="roleId" separator="," open="(" close=")">#{roleId}</foreach>
		)
	</if>
	ORDER BY e.report_time DESC
</select>

<resultMap type="com.bt.itsevent.domain.vo.EventZxDetailVO" id="EventZxDetailMap">
	<id column="id" property="id"/>
	<result column="event_no" property="eventNo"/>
	<result column="source" property="source"/>
	<result column="other_source" property="otherSource"/>
	<result column="report_time" property="reportTime"/>
	<result column="brief_desc" property="briefDesc"/>
	<result column="deal_result" property="dealResult"/>
	<result column="report_man" property="reportMan"/>
	<result column="report_man_tel" property="reportManTel"/>
	<result column="user1_id" property="user1Id"/>
	<result column="user_name" property="user1Name"/>
	<result column="org1_id" property="org1Id"/>
	<result column="feedback" property="feedback"/>
	<result column="revisit" property="revisit"/>
	<result column="satisfaction" property="satisfaction"/>
	<result column="finish_time" property="finishTime"/>
	<result column="finish_user_id" property="finishUserId"/>
	<result column="finish_user_name" property="finishUserName"/>
	<result column="org_name" property="company1Name"/>
	<result column="business_type" property="businessType"/>
	<result column="name" property="businessTypeName"/>
	<result column="report_source_key" property="reportSourceKey"/>
</resultMap>
<select id="selectZxDetail" parameterType="com.bt.itscore.domain.dto.IdStringDTO" resultMap="EventZxDetailMap">
	SELECT e.id,e.event_no, e.source, e.other_source, e.report_time, e.brief_desc, e.report_man, e.report_man_tel, e.deal_result, zx.user1_id,
	  u.user_name, zx.org1_id, zx.feedback, zx.revisit, zx.satisfaction, e.finish_time, e.finish_user_id, e.finish_user_name, o.org_name,
	  e.business_type,di.name , e.report_source_key
	FROM (`event` AS e, event_zx AS zx)
	LEFT JOIN `user` AS u ON zx.user1_id=u.user_id
	LEFT JOIN organization AS o ON zx.org1_id=o.org_id
	LEFT JOIN dict_item di ON di.value=e.business_type
	WHERE e.id=zx.event_id AND e.id=#{id}
</select>

<resultMap type="com.bt.itsevent.domain.vo.EventContructionVO" id="EventContructionMap">
	<result column="report_type" property="reportType" />
	<result column="id" property="id" />
	<result column="deal_status" property="dealStatus" />
	<result column="start_time" property="startTime" />
	<result column="end_time" property="endTime" />
	<result column="report_time" property="reportTime" />
	<result column="road_name" property="roadName" />
	<result column="direction_name" property="directionName" />
	<result column="start_mile_post" property="startMilePost" />
	<result column="end_mile_post" property="endMilePost" />
	<result column="brief_desc" property="briefDesc" />
</resultMap>
<select id="selectWithContruction" parameterType="java.util.Map" resultMap="EventContructionMap">
  SELECT e.*,r.road_name,di.direction_name FROM
	(SELECT 1 AS report_type,id,deal_status,0 AS start_time,0 AS end_time,report_time,road_no
	    ,direction_no,mile_post AS start_mile_post,mile_post AS end_mile_post,brief_desc 
	  FROM `event` WHERE app_record_man_id=#{userId}
	UNION ALL
	SELECT 2 AS report_type,id,-1 deal_status,start_time,end_time,create_time AS report_time,road_no
	    ,direction_no,start_mile_post,end_mile_post,brief_desc 
	  FROM r_construction WHERE report_user_id=#{userId}
	) e
  LEFT JOIN road r ON e.road_no=r.road_no
  LEFT JOIN direction di ON e.direction_no=di.direction_no
  ORDER BY report_time DESC
</select>

<update id="updateAppEmerRescue" parameterType="com.bt.itsevent.domain.dto.EmerRescueDTO">
	UPDATE `event` SET brief_desc=#{briefDesc},road_no=#{roadNo},direction_no=#{directionNo}
	  ,mile_post=#{milePost},death_man=#{deathMan},injure_man=#{injureMan},income_lose=#{incomeLose}
	  ,report_man=#{reportMan},report_man_tel=#{reportManTel},car_plate=#{carPlate},lng=#{lng},lat=#{lat},accident_pattern=#{accidentPattern},weather=#{weather},congestion_length=#{congestionLength}
	   <if test="accidentType != null">,accident_type=#{accidentType}</if>
	   <if test="otherTypeCause != null">,other_type_cause=#{otherTypeCause}</if>
	   <if test="dangeFlag != null">,dange_flag=#{dangeFlag}</if>
	   <if test="accidentCause != null">,accident_cause=#{accidentCause}</if>
	   <if test="otherCause != null">,other_cause=#{otherCause}</if>
	   <if test="roadLossCause != null">,road_loss_cause=#{roadLossCause}</if>
	WHERE id=#{id}
</update>

<delete id="deleteEventRoadLoss" parameterType="com.bt.itsevent.domain.dto.EmerRescueDTO">
	DELETE FROM event_road_loss WHERE event_id=#{id}
</delete>

<insert id="addEventRoadLoss" parameterType="com.bt.itsevent.domain.dto.EmerRescueDTO">
	    INSERT INTO event_road_loss (event_id,type_code,value) VALUES
	    <foreach collection="roadLoss" item="item" index="index" open="(" separator="),(" close=")">
			#{id},#{item.typeCode},#{item.value}
	    </foreach>	
</insert>

<resultMap id="eventRoadLossMap" type="com.bt.itsevent.domain.vo.EventRoadLossVO">
	<result column="event_id" property="eventId"/>
	<result column="type_code" property="typeCode"/>
	<result column="value" property="value"/>
</resultMap>

<select id="selectEventRoadLoss" parameterType="java.util.List" resultMap="eventRoadLossMap">
	select event_id, type_code, value from event_road_loss where event_id in
	<foreach item="item" index="index" collection="list"
			 open="(" separator="," close=")">
		#{item}
	</foreach>
</select>

<resultMap id="organRoadMap" type="com.bt.itsevent.domain.vo.OrganRoadVO">
	<result column="road_no" property="roadNo"/>
	<result column="org_id" property="orgId"/>
</resultMap>

<select id="selectOrganRoad" resultMap="organRoadMap">
	SELECT DISTINCT road_no,org_id FROM organization_road
</select>

<resultMap id="OrgRoadThreeLevelMap" type="com.bt.itsevent.domain.vo.OrgRoadThreeLevelVO">
	<id column="org_id" property="orgId"/>
	<collection property="children" ofType="com.bt.itsevent.domain.vo.ParentRoadVO">
		<id column="parent_road" property="parentRoad"/>
		<id column="parent_road" property="parentRoadName"/>
		<collection property="children" ofType="com.bt.itsevent.feign.RoadVO">
			<id column="road_no" property="roadNo"/>
			<result column="road_name" property="roadName"/>
			<result column="road_alias" property="roadAlias"/>
		</collection>
	</collection>
</resultMap>
<select id="selectEventOrgList" resultMap="OrgRoadThreeLevelMap" parameterType="java.util.Map">
	SELECT o.org_id,r.road_no,r.road_name,r.road_alias,r.parent_road FROM organization_road o, road r 
	WHERE o.road_no=r.road_no
	AND o.org_id IN (SELECT DISTINCT org_id FROM role_event re WHERE re.role_id IN 
		<foreach collection="roles" item="roleId" separator="," open="(" close=")">#{roleId}</foreach>
		)
</select>

<insert id="addEventDealStatus" parameterType="com.bt.itsevent.domain.dto.EventDealStatusDTO">
    INSERT INTO event_deal_status (event_id,plan_response,cms_publish,cancel_cms_publish,cancel_plan,create_time,init_report_time,full_close
    <if test="startFullClose != null">,start_full_close</if><if test="unblock != null">,unblock</if>
    ) VALUES
	(#{eventId},#{planResponse},#{cmsPublish},#{cancelCmsPublish},#{cancelPlan},#{createTime},#{initReportTime},#{fullClose}
    <if test="startFullClose != null">,#{startFullClose}</if><if test="unblock != null">,#{unblock}</if>
    )
</insert>

<update id="needUnblock" parameterType="com.bt.itscore.domain.dto.IdStringDTO">
    UPDATE event_deal_status SET unblock=0 WHERE event_id=#{id} AND unblock IS NULL
</update>

<update id="unblockFail" parameterType="com.bt.itscore.domain.dto.IdStringDTO">
    UPDATE event_deal_status SET unblock=0 WHERE event_id=#{id}
</update>

<update id="updateUnblock" parameterType="com.bt.itsevent.domain.dto.EventDealStatusDTO">
    UPDATE event_deal_status SET unblock=1,unblock_time=#{unblockTime},unblock_progress_id=#{unblockProgressId} WHERE event_id=#{eventId} AND unblock != 2
</update>

<update id="cleanUnblock" parameterType="com.bt.itsevent.domain.dto.EventDealStatusDTO">
    UPDATE event_deal_status SET unblock=2 WHERE event_id=#{eventId}
</update>

<update id="updateSceneUpload" parameterType="com.bt.itsevent.domain.dto.EventDealStatusDTO">
    UPDATE event_deal_status SET scene_upload=#{sceneUpload} WHERE event_id=#{eventId}
</update>

<update id="updateDealResponse" parameterType="com.bt.itsevent.domain.dto.EventDealStatusDTO">
    UPDATE event_deal_status SET deal_response=#{dealResponse}, deal_response_time=#{dealResponseTime}
    <if test="planResponse != null">
    ,plan_response=#{planResponse}
    </if>
      WHERE event_id=#{eventId} AND deal_response=0
</update>

<update id="updatePlanResponse" parameterType="com.bt.itsevent.domain.dto.EventDealStatusDTO">
    UPDATE event_deal_status SET plan_response=#{planResponse}, plan_response_time=#{planResponseTime}
    <if test="cancelPlan != null">
    ,cancel_plan=#{cancelPlan}
    </if>
      WHERE event_id=#{eventId} AND plan_response=0
</update>

<update id="updateCancelPlan" parameterType="com.bt.itsevent.domain.dto.EventDealStatusDTO">
    UPDATE event_deal_status SET cancel_plan=#{cancelPlan} WHERE event_id=#{eventId} AND cancel_plan=0
</update>

<update id="updateCancel" parameterType="com.bt.itsevent.domain.dto.EventDealStatusDTO">
    UPDATE event_deal_status SET cancel_plan=#{cancelPlan} WHERE event_id=#{eventId}
</update>

<resultMap id="EventDealStatusMap" type="com.bt.itsevent.domain.vo.EventDealStatusVO">
	<id column="id" property="id"/>
	<result column="event_id" property="eventId"/>
	<result column="deal_response" property="dealResponse"/>
	<result column="plan_response" property="planResponse"/>
	<result column="cms_publish" property="cmsPublish"/>
	<result column="scene_upload" property="sceneUpload"/>
	<result column="cancel_plan" property="cancelPlan"/>
	<result column="cancel_cms_publish" property="cancelCmsPublish"/>
	<result column="init_report_time" property="initReportTime"/>
	<result column="plan_response_time" property="planResponseTime"/>
	<result column="deal_response_time" property="dealResponseTime"/>
	<result column="full_close" property="fullClose"/>
	<result column="start_full_close" property="startFullClose"/>
	<result column="total_full_close" property="totalFullClose"/>
	<result column="unblock" property="unblock"/>
	<result column="unblock_time" property="unblockTime"/>
	<result column="unblock_progress_id" property="unblockProgressId"/>
</resultMap>
<select id="selectEventDealStatus" resultMap="EventDealStatusMap" parameterType="com.bt.itscore.domain.dto.IdStringDTO">
	SELECT * FROM event_deal_status WHERE event_id=#{id}
</select>

<select id="selectEmerplanAuditTime" parameterType="java.util.Map" resultType="com.bt.itsevent.domain.vo.EmerPlanVO">
	SELECT a.audit_time AS auditTime,ep.`name`,epll.response_time AS responseTime FROM emer_plan ep,emer_plan_level epl
	LEFT JOIN emer_plan_level_level epll ON epll.emer_plan_level_id=epl.id
	LEFT JOIN emer_plan_level_audit a ON a.emer_plan_level_id=epl.id
	WHERE epl.id=#{emerPlanLevelId} AND epl.emer_plan_id=ep.id
	 AND epll.plan_level=#{planLevel} LIMIT 1
</select>

<select id="selectEevnetFileAnalyseTime" parameterType="com.bt.itscore.domain.dto.IdStringDTO" resultType="long">
	SELECT (case when ISNULL(t2.analysis_time)=1 then 2000000000 when ISNULL(t1.finish_time)=1 then 2000000000 when ISNULL(t1.event_analyse)=0 then 2000000000 
	else t1.finish_time+IFNULL(t2.analysis_time,0)*86400-unix_timestamp(now()) end) countdown_time
	FROM event t1,emer_plan_level_analysis t2 where t1.emer_plan_level_id=t2.emer_plan_level_id and t1.id=#{id} limit 1
</select>

<update id="updateCmsPublish" parameterType="java.util.Map">
	UPDATE event_deal_status SET cms_publish=#{cmsPublish}, cancel_cms_publish=#{cancelCmsPublish} WHERE event_id=#{eventId}
</update>

<update id="updateCancelCmsPublish" parameterType="com.bt.itscore.domain.dto.IdStringDTO">
	UPDATE event_deal_status SET cancel_cms_publish=1 WHERE event_id=#{id}
</update>

<!-- 设置启动预案和撤销预案卡片无效 -->
<update id="updatePlanCardInvalid" parameterType="com.bt.itscore.domain.dto.IdStringDTO">
	UPDATE event_progress SET card_valid=2 WHERE event_id=#{id} AND (card_type=20 OR card_type=25)
</update>

<!-- 事件有新的启动预案卡片，重置执行状态 -->
<update id="resetEventDealStatus" parameterType="com.bt.itsevent.domain.dto.EventDealStatusDTO">
	UPDATE event_deal_status SET plan_response=#{planResponse},cancel_plan=#{cancelPlan},plan_response_time=#{planResponseTime},update_time=#{updateTime}
	 WHERE event_id=#{eventId}
</update>

<resultMap type="com.bt.itscore.domain.vo.EmerPlanLevelAuditVO" id="EmerPlanLevelAuditMap">
	<id column="emer_plan_level_id" property="emerPlanLevelId"/>
	<result column="audit_time" property="auditTime"/>
	<collection property="users" ofType="com.bt.itscore.domain.vo.BasicUserVO">
	  <result column="user_id" property="userId"/>
	  <result column="user_name" property="userName"/>
	  <result column="mobile" property="mobile"/>
	  <result column="company_id" property="companyId"/>
	</collection>
</resultMap>
<select id="selectAudit" parameterType="com.bt.itscore.domain.dto.IdStringDTO" resultMap="EmerPlanLevelAuditMap">
SELECT emla.*, 
u.user_name,u.mobile, u.company_id
FROM emer_plan_level_audit emla
LEFT JOIN `user` u ON emla.user_id=u.user_id
WHERE emla.emer_plan_level_id=#{id}
</select>

<resultMap type="com.bt.itsevent.domain.vo.EventAuditResponseTimeVO" id="EventAuditResponseTimeMap">
	<id column="event_id" property="eventId"/>
	<result column="audit_time" property="auditTime"/>
	<result column="response_time" property="responseTime"/>
</resultMap>
<select id="selectAuditResponseTimeList" parameterType="java.util.Map" resultMap="EventAuditResponseTimeMap">
SELECT * FROM (
	<foreach collection="list" separator=" UNION ALL " item="d">
	SELECT #{d.id} AS event_id,SUM(audit_time) audit_time,SUM(response_time) response_time FROM (
	SELECT 0 audit_time,0 response_time FROM DUAL
	UNION ALL
	SELECT DISTINCT audit_time,0 response_time FROM emer_plan_level_audit a 
	WHERE a.emer_plan_level_id=#{d.emerPlanLevelId}
	UNION ALL
	SELECT 0 audit_time,response_time FROM emer_plan_level_level 
	WHERE plan_level=#{d.level} AND emer_plan_level_id=#{d.emerPlanLevelId}) AS res
	</foreach>
) r

</select>

<resultMap type="com.bt.itsevent.domain.vo.WorkingTableCountVO" id="WorkingTableCountMap">
	<result column="audit_count" property="auditCount"/>
	<result column="deal_count" property="dealCount"/>
	<result column="analysis_count" property="analysisCount"/>
</resultMap>
<select id="countWorkingTable" parameterType="java.lang.String" resultMap="WorkingTableCountMap">
SELECT SUM(audit_count) AS audit_count,SUM(deal_count) AS deal_count,SUM(analysis_count) AS analysis_count FROM (
	SELECT COUNT(*) AS audit_count,0 deal_count,0 analysis_count FROM `event` e
		LEFT JOIN event_progress ep ON e.id=ep.event_id
		LEFT JOIN event_progress_user epu ON ep.id=epu.event_progress_id
		WHERE 100 > e.deal_status AND e.report_time >= (UNIX_TIMESTAMP()-604800) AND e.del_status=0 AND e.event_type=1 AND ep.card_valid=1
			AND epu.user_id=#{_parameter} AND (ep.card_type=20 OR ep.card_type=25) AND (ep.card_pass=0 OR ep.card_pass IS NULL)
			AND epu.del_status=0
	UNION ALL
	SELECT 0 audit_count,COUNT(*) AS deal_count,0 analysis_count FROM `event` e
		LEFT JOIN event_progress ep ON e.id=ep.event_id
		LEFT JOIN event_progress_user epu ON ep.id=epu.event_progress_id
		WHERE 100 > e.deal_status AND e.report_time >= (UNIX_TIMESTAMP()-604800) AND e.del_status=0 AND e.event_type=1 AND ep.card_valid=1
			AND epu.user_id=#{_parameter} AND ep.card_type=1 AND epu.del_status=0
	UNION ALL
	SELECT 0 audit_count,0 deal_count, COUNT(*) AS analysis_count FROM `event` e
		LEFT JOIN emer_plan_level_analysis epla ON epla.emer_plan_level_id=e.emer_plan_level_id
		WHERE e.deal_status=100 AND e.report_time >= (UNIX_TIMESTAMP()-604800) AND e.del_status=0 AND e.event_type=1 AND e.event_analyse IS NULL AND epla.user_id=#{_parameter}
) AS a
</select>

<select id="selectMyNoAuditEvent" parameterType="com.bt.itsevent.domain.dto.EventQueryDTO" resultMap="EventProgressMap">
	SELECT t2.*,e.source,e.id,e.event_no,e.`level`,e.event_three_type,e.event_four_type,e.deal_status,e.event_status,e.report_time,
	e.car_plate,e.brief_desc,e.record_man,e.record_man_tel,e.report_man,e.report_man_tel,e.road_no,e.direction_no,e.mile_post,
	e.lng,e.lat,e.emer_plan_level_id,e.distribute_time FROM (
	  SELECT t.id,SUM(t.start_plan) start_plan,SUM(t.cancel_plan) cancel_plan FROM (
		SELECT e.id,-1 start_plan,0 cancel_plan FROM `event` e
			LEFT JOIN event_progress ep ON e.id=ep.event_id
			LEFT JOIN event_progress_user epu ON ep.id=epu.event_progress_id
			WHERE e.report_time>#{startTime} AND 100 > e.deal_status AND e.del_status=0 AND e.event_type=1 AND ep.card_valid=1
				AND epu.user_id=#{userId} AND ep.card_type=20 AND (ep.card_pass=0 OR ep.card_pass IS NULL)
		UNION ALL
		SELECT e.id,0 start_plan,-1 cancel_plan FROM `event` e
			LEFT JOIN event_progress ep ON e.id=ep.event_id
			LEFT JOIN event_progress_user epu ON ep.id=epu.event_progress_id
			WHERE e.report_time>#{startTime} AND 100 > e.deal_status AND e.del_status=0 AND e.event_type=1 AND ep.card_valid=1
				AND epu.user_id=#{userId} AND ep.card_type=25 AND (ep.card_pass=0 OR ep.card_pass IS NULL)
		) t GROUP BY id
	) t2
	LEFT JOIN `event` e ON t2.id=e.id
	ORDER BY e.report_time DESC
</select>

<select id="selectMyAuditDealingEvent" parameterType="com.bt.itsevent.domain.dto.EventQueryDTO" resultMap="EventProgressMap">
	SELECT t2.*,e.source,e.id,e.event_no,e.`level`,e.event_three_type,e.event_four_type,e.deal_status,e.event_status,e.report_time,
	e.car_plate,e.brief_desc,e.record_man,e.record_man_tel,e.report_man,e.report_man_tel,e.road_no,e.direction_no,e.mile_post,
	e.lng,e.lat,e.emer_plan_level_id,e.distribute_time FROM (
	  SELECT t.id,SUM(t.start_plan) start_plan,SUM(t.cancel_plan) cancel_plan FROM (
		SELECT e.id,-1 start_plan,0 cancel_plan FROM `event` e
			LEFT JOIN event_progress ep ON e.id=ep.event_id
			LEFT JOIN event_progress_user epu ON ep.id=epu.event_progress_id
			WHERE e.report_time>#{startTime} AND 100 > e.deal_status AND e.del_status=0 AND e.event_type=1 AND ep.card_valid=1 AND (ep.card_pass=0 OR ep.card_pass IS NULL)
				AND epu.user_id=#{userId} AND ep.card_type=20
		UNION ALL
		SELECT e.id,ep.card_pass start_plan,0 cancel_plan FROM `event` e
			LEFT JOIN event_progress ep ON e.id=ep.event_id
			LEFT JOIN event_progress_user epu ON ep.id=epu.event_progress_id
			WHERE e.report_time>#{startTime} AND 100 > e.deal_status AND e.del_status=0 AND e.event_type=1 AND ep.card_valid=1 AND ep.card_pass > 0
				AND epu.user_id=#{userId} AND ep.card_type=20
		UNION ALL
		SELECT e.id,0 start_plan,-1 cancel_plan FROM `event` e
			LEFT JOIN event_progress ep ON e.id=ep.event_id
			LEFT JOIN event_progress_user epu ON ep.id=epu.event_progress_id
			WHERE e.report_time>#{startTime} AND 100 > e.deal_status AND e.del_status=0 AND e.event_type=1 AND ep.card_valid=1 AND (ep.card_pass=0 OR ep.card_pass IS NULL)
				AND epu.user_id=#{userId} AND ep.card_type=25
		UNION ALL
		SELECT e.id,0 start_plan,ep.card_pass cancel_plan FROM `event` e
			LEFT JOIN event_progress ep ON e.id=ep.event_id
			LEFT JOIN event_progress_user epu ON ep.id=epu.event_progress_id
			WHERE e.report_time>#{startTime} AND 100 > e.deal_status AND e.del_status=0 AND e.event_type=1 AND ep.card_valid=1 AND ep.card_pass > 0
				AND epu.user_id=#{userId} AND ep.card_type=25
		) t GROUP BY id
	) t2
	LEFT JOIN `event` e ON t2.id=e.id
	ORDER BY e.report_time DESC
</select>

<select id="selectMyNoDealEvent" parameterType="com.bt.itsevent.domain.dto.EventQueryDTO" resultMap="EventProgressMap">
	SELECT t2.*,e.source,e.id,e.event_no,e.`level`,e.event_three_type,e.event_four_type,e.deal_status,e.event_status,e.report_time,
	e.car_plate,e.brief_desc,e.record_man,e.record_man_tel,e.report_man,e.report_man_tel,e.road_no,e.direction_no,e.mile_post,
	e.lng,e.lat,e.emer_plan_level_id,e.distribute_time FROM (
	  SELECT t.id,SUM(t.new_event) new_event FROM (
		SELECT e.id,0 new_event FROM `event` e
			LEFT JOIN event_progress ep ON e.id=ep.event_id
			LEFT JOIN event_progress_user epu ON ep.id=epu.event_progress_id
			WHERE 100 > e.deal_status AND e.del_status=0 AND e.event_type=1 AND ep.card_valid=1
				AND e.report_time >= #{startTime}
				AND  #{endTime}>= e.report_time
				AND epu.user_id=#{userId} AND ep.card_type=1
		UNION ALL
		SELECT e.id,1 new_event FROM `event` e
			LEFT JOIN event_progress ep ON e.id=ep.event_id
			LEFT JOIN event_progress_user epu ON ep.id=epu.event_progress_id
			WHERE 100 > e.deal_status AND e.del_status=0 AND e.event_type=1
				AND e.report_time >= #{startTime}
				AND  #{endTime}>= e.report_time
				AND epu.user_id=#{userId} AND ep.card_type=1 AND (epu.card_pass=0 OR epu.card_pass IS NULL)
		UNION ALL
		SELECT e.id,5 new_event FROM `event` e
			LEFT JOIN event_progress ep ON e.id=ep.event_id
			LEFT JOIN event_progress_user epu ON ep.id=epu.event_progress_id
			WHERE 100 > e.deal_status AND e.del_status=0 AND e.event_type=1
				AND e.report_time >= #{startTime}
				AND  #{endTime}>= e.report_time
				AND epu.user_id=#{userId} AND ep.card_type=1 AND epu.card_pass=1
	  ) AS t GROUP BY id
	) t2
	LEFT JOIN `event` e ON t2.id=e.id
	ORDER BY e.report_time DESC
</select>

<select id="selectMyNoAnalysisEvent" parameterType="com.bt.itsevent.domain.dto.EventQueryDTO" resultMap="EventProgressMap">
	SELECT t2.*,e.source,e.id,e.event_no,e.`level`,e.event_three_type,e.event_four_type,e.deal_status,e.event_status,e.report_time,
	e.car_plate,e.brief_desc,e.record_man,e.record_man_tel,e.report_man,e.report_man_tel,e.road_no,e.direction_no,e.mile_post,
	e.lng,e.lat,e.emer_plan_level_id,e.distribute_time,e.finish_time FROM (
	  SELECT DISTINCT e.id,epla.analysis_time FROM `event` e
		LEFT JOIN emer_plan_level_analysis epla ON epla.emer_plan_level_id=e.emer_plan_level_id
		WHERE e.deal_status=100 AND e.del_status=0 AND e.event_type=1 AND e.event_analyse IS NULL AND epla.user_id=#{userId}
	) t2
	LEFT JOIN `event` e ON t2.id=e.id
	ORDER BY e.report_time DESC
</select>

<select id="selectAnalysisAuthById" parameterType="java.util.Map" resultType="int">
	SELECT COUNT(*) FROM `event` e
		LEFT JOIN emer_plan_level_analysis epla ON epla.emer_plan_level_id=e.emer_plan_level_id
		WHERE e.deal_status=100 AND e.del_status=0 AND e.event_type=1 
		 AND epla.user_id=#{userId} AND e.id=#{id}
</select>
<!-- AND e.event_analyse IS NULL -->

<select id="selectCarLane" parameterType="java.lang.Integer" resultType="int">
	SELECT IFNULL(car_lane,0) FROM road WHERE road_no=#{_parameter}
</select>

<update id="updateFullClose" parameterType="com.bt.itsevent.domain.dto.EventDealStatusDTO">
	UPDATE event_deal_status SET full_close = #{fullClose},start_full_close = #{startFullClose}
		<if test="totalFullClose != null">,total_full_close=#{totalFullClose}</if>
	WHERE id=#{id}
</update>

<insert id="addEventDealFullClose" parameterType="com.bt.itsevent.domain.dto.EventDealFullCloseDTO">
    INSERT INTO event_deal_full_close (event_id,start_close_time,create_time) VALUES
	(#{eventId},#{startCloseTime},#{createTime})
</insert>

<update id="updateEventDealFullClose" parameterType="com.bt.itsevent.domain.dto.EventDealFullCloseDTO">
    UPDATE event_deal_full_close SET end_close_time = #{endCloseTime},update_time = #{updateTime}
	WHERE event_id=#{eventId} AND end_close_time IS NULL
</update>

<resultMap type="com.bt.itsevent.domain.dto.EventDealFullCloseVO" id="EventDealFullCloseMap">
	<id column="id" property="id"/>
	<result column="event_id" property="eventId"/>
	<result column="start_close_time" property="startCloseTime"/>
	<result column="end_close_time" property="endCloseTime"/>
	<result column="create_time" property="createTime"/>
	<result column="update_time" property="updateTime"/>
</resultMap>
<select id="selectEventDealFullClose" parameterType="com.bt.itscore.domain.dto.IdStringDTO" resultMap="EventDealFullCloseMap">
	SELECT * FROM event_deal_full_close WHERE event_id=#{id}
</select>

<select id="selectAnalysisAuth" parameterType="java.util.Map" resultType="com.bt.itsevent.domain.vo.EventAnalysisAuthVO">
	SELECT t.eventId,SUM(t.analysisFlag) analysisFlag FROM (
	  SELECT e.id AS eventId,0 analysisFlag FROM `event` e
		WHERE e.deal_status=100 AND e.del_status=0 AND e.event_type=1 AND e.id IN
		 <foreach collection="list" separator="," item="d" open="(" close=")">
		 #{d.id}
		 </foreach>
	  UNION ALL
	  SELECT e.id AS eventId,1 analysisFlag FROM `event` e
		LEFT JOIN emer_plan_level_analysis epla ON epla.emer_plan_level_id=e.emer_plan_level_id
		WHERE e.deal_status=100 AND e.del_status=0 AND e.event_type=1 
		 AND epla.user_id=#{userId} AND e.id IN
		 <foreach collection="list" separator="," item="d" open="(" close=")">
		 #{d.id}
		 </foreach>
		 ) t GROUP BY eventId
</select>


<select id="nearestRelatedFacility" parameterType="java.util.Map" resultType="com.bt.itsevent.domain.vo.NearestFacilityTipVO">
SELECT FLOOR(ABS(mp_value-${mpValue})) AS distance,facility_no AS facilityNo,facility_name_short AS facilityNameShort FROM facility
WHERE road_no=#{roadNo} AND facility_type_no =#{facilityTypeNo} AND mp_value &lt;> ''
<if test="facilityTypeNo != null and facilityTypeNo == 7 and directionNo != null">AND direction_no =#{directionNo} </if>
ORDER BY distance ASC LIMIT 1
</select>

<select id="selectFacilityTunnel"  resultType="com.bt.itsevent.domain.dto.FacilityTunnelDTO">
	SELECT
		ft.facility_no AS facilityNo,
		ft.start_mile_post AS startMilePost,
		ft.end_mile_post AS endMilePost
	FROM
		facility_tunnel ft,
		facility f
	WHERE
		ft.facility_no = f.facility_no
	  	AND f.road_no = #{roadNo}
</select>

	<update id="updateEventEvlRecord" parameterType="com.bt.itsevent.domain.vo.EventEvlRecordVO">
		UPDATE `event_evl_record` SET state=#{state}, total_score=#{totalScore}, update_time=#{updateTime}
		WHERE event_id=#{eventId} AND id=#{id}
	</update>

	<update id="updateEventEvlItem" parameterType="java.util.List">
		UPDATE `event_evl_item`
		<trim prefix="set" suffixOverrides=",">
			<trim prefix="score=case" suffix="end,">
				<foreach collection="list" item="item" index="index">
					when id=#{item.id} then #{item.score}
				</foreach>
			</trim>
			<trim prefix="experience=case" suffix="end,">
				<foreach collection="list" item="item" index="index">
					when id=#{item.id} then #{item.experience}
				</foreach>
			</trim>
			<trim prefix="problem=case" suffix="end,">
				<foreach collection="list" item="item" index="index">
					when id=#{item.id} then #{item.problem}
				</foreach>
			</trim>
			<trim prefix="improvement=case" suffix="end,">
				<foreach collection="list" item="item" index="index">
					when id=#{item.id} then #{item.improvement}
				</foreach>
			</trim>
			<trim prefix="evl_org_name=case" suffix="end,">
				<foreach collection="list" item="item" index="index">
					when id=#{item.id} then #{item.evlOrgName}
				</foreach>
			</trim>
			<trim prefix="evl_user_name=case" suffix="end,">
				<foreach collection="list" item="item" index="index">
					when id=#{item.id} then #{item.evlUserName}
				</foreach>
			</trim>
			<trim prefix="remarks=case" suffix="end,">
				<foreach collection="list" item="item" index="index">
					when id=#{item.id} then #{item.remarks}
				</foreach>
			</trim>
			<trim prefix="update_time=case" suffix="end,">
				<foreach collection="list" item="item" index="index">
					when id=#{item.id} then #{item.updateTime}
				</foreach>
			</trim>
		</trim>
		WHERE
		<foreach collection="list" separator="or" item="item" index="index" >
			id = #{item.id}
		</foreach>
	</update>

	<select id="getEvlRecord" parameterType="com.bt.itsevent.domain.dto.EventEvlRecordDTO"
			resultType="com.bt.itsevent.domain.vo.EventEvlRecordVO">
		SELECT id, event_id AS eventId, state, total_score AS totalScore, create_time AS createTime, update_time AS updateTime
		FROM event_evl_record WHERE event_id=#{eventId}
	</select>

	<select id="getEvlItem" parameterType="com.bt.itsevent.domain.dto.EventEvlRecordDTO"
			resultType="com.bt.itsevent.domain.vo.EventEvlItemVO">
		SELECT i.id, i.percent, i.evl_content AS evlContent, i.project_name AS projectName, i.sort, i.score,
		       i.experience, i.problem, i.improvement, i.evl_org_name AS evlOrgName, i.evl_user_name AS evlUserName,
			   i.remarks, i.create_time AS createTime, i.update_time AS updateTime
		FROM event_evl_record r
		LEFT JOIN event_evl_record_item ri ON r.id=ri.evl_record_id
		LEFT JOIN event_evl_item i ON ri.evl_item_id=i.id
		WHERE r.event_id=#{eventId}
		ORDER BY i.sort
	</select>

	<select id="getTemplate" resultType="com.bt.itsevent.domain.vo.EventEvlItemVO">
		SELECT tp.percent, tc.evl_content AS evlContent, tp.project_name AS projectName, tc.sort
		FROM event_evl_template_project tp
		LEFT JOIN event_evl_template_content tc ON tp.id=tc.project_id
		ORDER BY tc.sort
	</select>

	<insert id="addEventEvlRecord" parameterType="com.bt.itsevent.domain.vo.EventEvlRecordVO" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO event_evl_record (event_id, state, total_score, create_time, update_time)
		VALUES (#{eventId}, #{state}, #{totalScore}, #{createTime}, #{updateTime})
	</insert>

	<insert id="addEventEvlItem" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO event_evl_item (percent, evl_content, project_name, sort, score, experience, problem, improvement,
		evl_org_name, evl_user_name, remarks, create_time, update_time) VALUES
		<foreach collection="list" item="item" index="index" separator=",">
			(#{item.percent}, #{item.evlContent}, #{item.projectName}, #{item.sort}, #{item.score}, #{item.experience},
			        #{item.problem}, #{item.improvement}, #{item.evlOrgName}, #{item.evlUserName}, #{item.remarks},
			        #{item.createTime}, #{item.updateTime})
		</foreach>
	</insert>

	<insert id="addEventEvlRecordItem" parameterType="com.bt.itsevent.domain.dto.EventEvlRecordItemDTO">
		INSERT INTO event_evl_record_item (evl_record_id, evl_item_id) VALUES
		<foreach collection="list" item="item" index="index" separator=",">
			(#{item.recordId}, #{item.itemId})
		</foreach>
	</insert>
	<select id="selectEventSource" parameterType="com.bt.itscore.domain.dto.IdStringDTO" resultType="Integer">
		SELECT source FROM `event` WHERE id =#{id}
	</select>


	<update id="updateEvent96333Order" parameterType="com.bt.itscore.domain.dto.Event96333NewOrderDTO">
	UPDATE event SET 
	<if test=" complaintType != null and complaintType != '' ">complaint_type=#{complaintType},</if>
	brief_desc=#{content}
	<if test=" carno != null and carno != '' ">,car_plate=#{carno} </if> WHERE id=#{id}
	</update>

	<update id="updateEvent96333ResultCode" parameterType="java.util.Map">
	UPDATE event_96333_sync SET deal_result_sync=1,create_time=#{createTime} WHERE event_no=#{eventNo}
	</update>

	<insert id="addEvent96333ResultCode" parameterType="java.util.Map">
	INSERT INTO event_96333_sync (id,event_no,deal_result_sync) VALUES (#{eventId},#{eventNo},0)
	</insert>

	<update id="updateEvent96333VisitCode" parameterType="java.util.Map">
	UPDATE event_96333_sync SET visit_result_sync=1,create_time=#{createTime} WHERE event_no=#{eventNo}
	</update>

	<select id="selectEvent96333ResultCode" parameterType="com.bt.itscore.domain.dto.IdStringDTO" resultType="java.lang.Integer">
	SELECT COUNT(*) AS num FROM event_96333_sync WHERE id=#{id}
	</select>

	<select id="lnglatToMilePost" parameterType="com.bt.itscore.domain.dto.LngLatDTO" resultType="com.bt.itscore.domain.vo.RoadPileNoVO">
	SELECT road_no AS roadNo,k_name AS milePost,lng,lat, road_name AS roadName,
		IFNULL(
		6371 * acos(
		cos(radians(#{lat}))
		* cos( radians( lat ) )
		* cos(radians( lng ) - radians(#{lng}))
		+
		sin( radians(#{lat}) )
		* sin( radians( lat ) )
		),0
		) AS distance
		FROM road_pile_no
		WHERE lng IS NOT NULL HAVING distance &lt; 1
		ORDER BY distance LIMIT 1
	</select>

	<insert id="addDDForGzh" parameterType="com.bt.itsevent.domain.dto.DDForGzhDTO">
	INSERT INTO `event` (id,event_no,brief_desc,event_type,event_two_type,
	event_three_type,report_time,level,source,event_status,
	deal_status,road_no,direction_no,mile_post,lng,
	lat,report_man,report_man_tel,car_plate,create_time,
	org_id,income_lose,del_status,source_id,report_source_key)
	VALUES 
	(#{id},#{eventNo},#{briefDesc},#{eventType},#{eventTwoType},
	#{eventThreeType},#{reportTime},#{level},#{source},#{eventStatus},
	#{dealStatus},#{roadNo},#{directionNo},#{milePost},#{lng},
	#{lat},#{reportMan},#{reportManTel},#{carPlate},#{createTime},
	#{orgId},#{incomeLose},#{delStatus},#{sourceId},#{reportSourceKey})
	</insert>

	<insert id="addEventUserGzh" parameterType="com.bt.itsevent.domain.dto.DDForGzhDTO">
	INSERT INTO `event_user_gzh` (openid,event_id) VALUES (#{openid},#{id})
	</insert>

	<resultMap type="com.bt.itsevent.domain.vo.DDForGzhVO" id="DDForGzhMap">
		<id column="id" property="id"/>
		<result column="report_time" property="reportTime"/>
		<result column="report_man" property="reportMan"/>
		<result column="report_man_tel" property="reportManTel"/>
		<result column="car_plate" property="carPlate"/>
		<result column="road_no" property="roadNo"/>
		<result column="direction_no" property="directionNo"/>
		<result column="mile_post" property="milePost"/>
		<result column="event_three_type" property="eventThreeType"/>
		<result column="deal_status" property="dealStatus"/>
		<result column="event_status" property="eventStatus"/>
		<result column="appraise_status" property="appraiseStatus"/>
	</resultMap>
	<select id="selectOwnDDForGzh" parameterType="com.bt.itscore.domain.dto.IdStringDTO" resultMap="DDForGzhMap">
	SELECT e.id,e.report_time,e.report_man,e.report_man_tel,e.car_plate,
		e.road_no,e.direction_no,e.mile_post,e.event_three_type,deal_status,
		e.event_status,e.appraise_status
		FROM `event` e, event_user_gzh g 
		WHERE e.id = g.event_id AND e.del_status = 0 AND g.openid = #{id}
		ORDER BY e.create_time DESC
	</select>

	<resultMap type="com.bt.itsevent.domain.vo.OngoingDDForGzhVO" id="OngoingDDForGzhMap">
		<id column="event_no" property="eventNo"/>
		<result column="report_time" property="reportTime"/>
		<result column="road_name" property="roadName"/>
		<result column="direction_name" property="directionName"/>
		<result column="mile_post" property="milePost"/>
		<result column="lng" property="lng"/>
		<result column="lat" property="lat"/>
		<collection property="occupiedLanes" ofType="com.bt.itsevent.domain.vo.OccupiedLaneVO">
			<id column="occupied_lane" property="occupiedLane"/>
		</collection>
	</resultMap>
	<select id="selectOngoingDDForGzh" resultMap="OngoingDDForGzhMap">
	SELECT e.event_no,e.report_time,r.road_name,d.direction_name,e.mile_post,e.lng,e.lat,l.occupied_lane 
	FROM `event` e
	LEFT JOIN road r ON e.road_no=r.road_no
	LEFT JOIN direction d ON e.direction_no=d.direction_no
	LEFT JOIN event_occupied_lane l ON e.id=l.event_id
	WHERE event_type=1 AND e.deal_status > 2 AND e.deal_status &lt; 100 AND e.del_status=0 AND LENGTH(e.lng) > 4
	ORDER BY e.report_time DESC
	</select>

	<select id="selectRoadRescuePhone" resultType="com.bt.itscore.domain.vo.RoadRescuePhoneVO" parameterType="java.util.Map">
	SELECT phone, remark FROM road_rescue_phone WHERE road_no=#{roadNo} AND start_mp_value &lt; #{mpValue} AND end_mp_value > #{mpValue}
	</select>

	<select id="selectOrgIdByRoles" resultType="java.lang.String" parameterType="java.util.Map">
	SELECT DISTINCT org_id FROM role_event WHERE role_id IN <foreach collection="roleIds" item="roleId" separator="," open="(" close=")">#{roleId}</foreach>
	</select>

	<select id="statAvgDealTimeGroupOrgId" parameterType="com.bt.itsevent.domain.dto.EventAnalysisDTO" resultType="com.bt.itsevent.domain.vo.EventStatDealTimeVO">
		SELECT 
			o.org_id AS orgId, 
			tmp.c AS total, 
			sum AS dealTime, 
			sum / tmp.c AS avgTime 
		FROM 
			organization AS o, 
			( 
				SELECT 
					org_id, 
					COUNT(*) AS c, 
<!-- 					SUM( e.final_report_time - e.order_create_time ) AS sum  -->
					SUM( e.final_report_time - e.distribute_time ) AS sum 
				FROM 
					`event` AS e LEFT JOIN event_dd AS ed ON e.id = ed.event_id 
				WHERE 
					e.event_type = 1 AND e.deal_status = 100 
<!--				  AND ( e.event_three_type = 79 OR e.event_four_type = 26 OR e.event_three_type = 11 )-->
				  AND ( ed.stat_deal_time IS NULL OR ed.stat_deal_time = 0 )
				  AND ( del_status = 0 OR del_status IS NULL ) 
				  AND org_id IS NOT NULL 
				  AND e.distribute_time IS NOT NULL 
				  AND e.final_report_time IS NOT NULL 
				  AND (e.finish_type IS NULL OR e.finish_type=0) 
				  <if test="ltTime != null"> AND #{ltTime} >= ( e.final_report_time - e.distribute_time ) </if>
				  <if test="reachFlag != null"> AND ed.reach_flag = 1 AND ed.reach_time IS NOT NULL </if>
				  <if test="reachTime != null"> AND ed.reach_time IS NOT NULL AND #{reachTime} >= (ed.reach_time-e.distribute_time)</if>
				  <if test="startTime != null"> AND e.report_time >= #{startTime} </if>
				  <if test="endTime != null"> AND e.report_time &lt;= #{endTime} </if>
				GROUP BY 
					org_id 
			) AS tmp 
		WHERE 
			o.org_id = tmp.org_id 
		UNION ALL 
		SELECT 
			'd9b2fe65-eb5d-4d53-952a-c490538c0e00' AS orgId, 
			tmp.c AS total, 
			sum AS dealTime, 
			sum / tmp.c AS avgTime 
		FROM 
			( 
				SELECT 
					COUNT(*) AS c, 
					SUM( e.final_report_time - e.distribute_time ) AS sum 
				FROM 
					`event` AS e LEFT JOIN event_dd AS ed ON e.id = ed.event_id 
				WHERE 
					e.event_type = 1 AND e.deal_status = 100 
<!--				  AND ( e.event_three_type = 79 OR e.event_four_type = 26 OR e.event_three_type = 11 )-->
				  AND ( ed.stat_deal_time IS NULL OR ed.stat_deal_time = 0 )
				  AND ( del_status = 0 OR del_status IS NULL ) 
				  AND org_id IS NOT NULL 
				  AND e.distribute_time IS NOT NULL 
				  AND e.final_report_time IS NOT NULL 
				  AND (e.finish_type IS NULL OR e.finish_type=0) 
				  <if test="ltTime != null"> AND #{ltTime} >= ( e.final_report_time - e.distribute_time ) </if>
				  <if test="reachFlag != null"> AND ed.reach_flag = 1 AND ed.reach_time IS NOT NULL </if>
				  <if test="reachTime != null"> AND ed.reach_time IS NOT NULL AND #{reachTime} >= (ed.reach_time-e.distribute_time)</if>
				  <if test="startTime != null"> AND e.report_time >= #{startTime} </if>
				  <if test="endTime != null"> AND e.report_time &lt;= #{endTime} </if>
				  AND org_id IN ('4a5c49a9-8152-4d38-9666-e536725c6670','681bbd49-1138-11ec-a11a-7c8ae1d066a4','c8d11e42-3cd2-48e0-9bae-b1d462c7ed26','60381fcb-1138-11ec-a11a-7c8ae1d066a4') 
			) AS tmp 
		ORDER BY 
			avgTime DESC 
	</select>

	<select id="confirmWithinXsecond" parameterType="com.bt.itsevent.domain.dto.EventAnalysisDTO" resultType="com.bt.itsevent.domain.vo.EventStatDealTimeVO">
		SELECT 
			o.org_id AS orgId, 
			tmp.c AS total, 
			sum AS dealTime, 
			sum / tmp.c AS avgTime 
		FROM 
			organization AS o, 
			( 
				SELECT 
					org_id, 
					COUNT(*) AS c, 
					SUM( e.final_report_time - e.distribute_time ) AS sum 
				FROM 
					`event` AS e LEFT JOIN event_deal_status AS ed ON e.id = ed.event_id 
				WHERE 
					e.event_type = 1 AND e.deal_status = 100
				  AND ( del_status = 0 OR del_status IS NULL ) 
				  AND org_id IS NOT NULL 
				  AND e.distribute_time IS NOT NULL 
				  AND e.final_report_time IS NOT NULL 
				  AND (e.finish_type IS NULL OR e.finish_type=0) 
				  <if test="reachFlag != null"> AND ed.deal_response=1 AND ed.deal_response_time IS NOT NULL </if>
				  <if test="reachTime != null"> AND #{reachTime} >= ( ed.deal_response_time - e.distribute_time ) </if>
				  <if test="startTime != null"> AND e.report_time >= #{startTime} </if>
				  <if test="endTime != null"> AND e.report_time &lt;= #{endTime} </if>
				GROUP BY 
					org_id 
			) AS tmp 
		WHERE 
			o.org_id = tmp.org_id 
		UNION ALL 
		SELECT 
			'd9b2fe65-eb5d-4d53-952a-c490538c0e00' AS orgId, 
			tmp.c AS total, 
			sum AS dealTime, 
			sum / tmp.c AS avgTime 
		FROM 
			( 
				SELECT 
					COUNT(*) AS c, 
					SUM( e.final_report_time - e.distribute_time ) AS sum 
				FROM 
					`event` AS e LEFT JOIN event_deal_status AS ed ON e.id = ed.event_id 
				WHERE 
					e.event_type = 1 AND e.deal_status = 100
				  AND ( del_status = 0 OR del_status IS NULL ) 
				  AND org_id IS NOT NULL 
				  AND e.distribute_time IS NOT NULL 
				  AND e.final_report_time IS NOT NULL 
				  AND (e.finish_type IS NULL OR e.finish_type=0) 
				  <if test="reachFlag != null"> AND ed.deal_response=1 AND ed.deal_response_time IS NOT NULL </if>
				  <if test="reachTime != null"> AND #{reachTime} >= ( ed.deal_response_time - e.distribute_time ) </if>
				  <if test="startTime != null"> AND e.report_time >= #{startTime} </if>
				  <if test="endTime != null"> AND e.report_time &lt;= #{endTime} </if>
				  AND org_id IN ('4a5c49a9-8152-4d38-9666-e536725c6670','681bbd49-1138-11ec-a11a-7c8ae1d066a4','c8d11e42-3cd2-48e0-9bae-b1d462c7ed26','60381fcb-1138-11ec-a11a-7c8ae1d066a4') 
			) AS tmp 
		ORDER BY 
			avgTime DESC 
	</select>

	<select id="unblockWithinXsecond" parameterType="com.bt.itsevent.domain.dto.EventAnalysisDTO" resultType="com.bt.itsevent.domain.vo.EventStatDealTimeVO">
		SELECT 
			o.org_id AS orgId, 
			tmp.c AS total, 
			sum AS dealTime, 
			sum / tmp.c AS avgTime 
		FROM 
			organization AS o, 
			( 
				SELECT 
					org_id, 
					COUNT(*) AS c, 
					SUM( e.final_report_time - e.distribute_time ) AS sum 
				FROM 
					`event` AS e LEFT JOIN event_deal_status AS ed ON e.id = ed.event_id 
				WHERE 
					e.event_type = 1 AND e.deal_status = 100
				  AND ( del_status = 0 OR del_status IS NULL ) 
				  AND org_id IS NOT NULL 
				  AND e.distribute_time IS NOT NULL 
				  AND e.final_report_time IS NOT NULL 
				  AND (e.finish_type IS NULL OR e.finish_type=0) 
				  <if test="reachFlag != null"> AND ed.unblock IS NOT NULL </if>
				  <if test="reachTime != null and reachFlag != null"> AND ed.unblock=2 AND #{reachTime} >= ( ed.unblock_time - e.distribute_time ) </if>
				  <if test="startTime != null"> AND e.report_time >= #{startTime} </if>
				  <if test="endTime != null"> AND e.report_time &lt;= #{endTime} </if>
				GROUP BY 
					org_id 
			) AS tmp 
		WHERE 
			o.org_id = tmp.org_id 
		UNION ALL 
		SELECT 
			'd9b2fe65-eb5d-4d53-952a-c490538c0e00' AS orgId, 
			tmp.c AS total, 
			sum AS dealTime, 
			sum / tmp.c AS avgTime 
		FROM 
			( 
				SELECT 
					COUNT(*) AS c, 
					SUM( e.final_report_time - e.distribute_time ) AS sum 
				FROM 
					`event` AS e LEFT JOIN event_deal_status AS ed ON e.id = ed.event_id 
				WHERE 
					e.event_type = 1 AND e.deal_status = 100
				  AND ( del_status = 0 OR del_status IS NULL ) 
				  AND org_id IS NOT NULL 
				  AND e.distribute_time IS NOT NULL 
				  AND e.final_report_time IS NOT NULL 
				  AND (e.finish_type IS NULL OR e.finish_type=0) 
				  <if test="reachFlag != null"> AND ed.unblock IS NOT NULL </if>
				  <if test="reachTime != null and reachFlag != null"> AND ed.unblock=2 AND #{reachTime} >= ( ed.unblock_time - e.distribute_time ) </if>
				  <if test="startTime != null"> AND e.report_time >= #{startTime} </if>
				  <if test="endTime != null"> AND e.report_time &lt;= #{endTime} </if>
				  AND org_id IN ('4a5c49a9-8152-4d38-9666-e536725c6670','681bbd49-1138-11ec-a11a-7c8ae1d066a4','c8d11e42-3cd2-48e0-9bae-b1d462c7ed26','60381fcb-1138-11ec-a11a-7c8ae1d066a4') 
			) AS tmp 
		ORDER BY 
			avgTime DESC 
	</select>

	<select id="selectChargePolicyForGzh" resultType="com.bt.itsevent.domain.vo.ChargePolicyVO">
	SELECT id,title,file_path AS filePath,fno,file_type AS fileType FROM gzh_charge_policy ORDER BY sno ASC
	</select>

	<select id="selectPublicInfoForGzh" resultType="com.bt.itsevent.domain.vo.PublicInfoVO">
	SELECT id,title,file_path AS filePath,publish_time AS publishTime FROM gzh_public_info ORDER BY publish_time DESC
	</select>


	<select id="checkStatDealEventType" parameterType="com.bt.itscore.domain.dto.IdStringDTO" resultType="java.lang.Integer">
	SELECT COUNT(*) FROM event e 
		WHERE ( e.event_three_type = 79 OR e.event_four_type = 26 OR e.event_three_type = 11 )
			AND e.id = #{id}
	</select>

	<update id="nostatDealReason" parameterType="com.bt.itsevent.domain.dto.NostatDealReasonDTO">
	UPDATE event_dd SET nostat_deal_reason=#{nostatDealReason}, stat_deal_time=1 WHERE event_id=#{id}
	</update>

	<select id="selectNostatDealReason" parameterType="com.bt.itscore.domain.dto.IdStringDTO" resultType="com.bt.itsevent.domain.vo.EventNostatDealReasonVO">
	SELECT ed.stat_deal_time AS statDealTime, ed.nostat_deal_reason AS nostatDealReason FROM event e ,event_dd ed 
		WHERE ( e.event_three_type = 79 OR e.event_four_type = 26 OR e.event_three_type = 11 )
			AND e.id=ed.event_id AND ed.stat_deal_time = 1 AND ed.event_id = #{id}
	</select>

	<update id="updatePushedAmap" parameterType="com.bt.itscore.domain.dto.IdStringDTO">
	UPDATE event_dd SET push_amap=1 WHERE event_id=#{id}
	</update>

	<resultMap id="OrgRoadListMap" type="com.bt.itsevent.domain.vo.OrgRoadListVO">
		<id column="org_id" property="orgId"/>
		<collection property="children" ofType="com.bt.itsevent.feign.RoadVO">
				<id column="road_no" property="roadNo"/>
				<result column="road_name" property="roadName"/>
				<result column="road_alias" property="roadAlias"/>
				<result column="parent_road" property="parentRoad"/>
				<collection property="directions" ofType="com.bt.itsevent.feign.DirectionVO">
					<id column="direction_no" property="directionNo"/>
					<result column="direction_name" property="directionName"/>
				</collection>
		</collection>
	</resultMap>
	<select id="orgRoadDirectionList" resultMap="OrgRoadListMap" parameterType="java.util.Map">
		SELECT DISTINCT o.org_id,r.road_no,r.road_name,r.road_alias,r.parent_road,d.direction_no,d.direction_name
		FROM organization_road o, road r, direction d
		WHERE o.road_no=r.road_no AND d.road_no = r.road_no
		AND 
		(o.org_id IN (SELECT DISTINCT org_id FROM role_event re WHERE re.role_id IN
			<foreach collection="roles" item="roleId" separator="," open="(" close=")">#{roleId}</foreach>
			) 
			<if test="roadNos != null and roadNos.size() > 0"> OR r.road_no IN <foreach collection="roadNos" item="roadNo" separator="," open="(" close=")">#{roadNo}</foreach> </if>
		)
	</select>
	<resultMap id="RoadMap" type="com.bt.itsevent.feign.RoadVO">
		<result column="road_no" property="roadNo"/>
	</resultMap>
	<select id="selectEventRoad" parameterType="java.util.List" resultMap="RoadMap">
	SELECT r.road_no FROM role_event_road r 
		WHERE r.role_id IN <foreach collection="list" item="roleId" open="(" close=")" separator=",">#{roleId}</foreach>
	</select>

	<select id="selectEmerUserByIdAndExcludeOrgName" parameterType="java.util.HashMap" resultMap="EventEmerUserMap">
		SELECT event_id,emer_group_id,eeu.user_id,IFNULL(u.user_name,eeu.user_id) AS
		user_name,IFNULL(u.mobile,eeu.mobile) AS mobile,IFNULL(eeu.force_remind,0) AS force_remind,IFNULL(eeu.sms_remind,0) AS sms_remind
		FROM event_emer_user eeu
		LEFT JOIN `user` u ON eeu.user_id=u.user_id
		<if test="orgName!=null &amp;&amp; orgName!=''">
			LEFT JOIN `organization` o ON o.org_id=u.org_id
		</if>
		WHERE eeu.event_id=#{eventId}
		<if test="orgName!=null &amp;&amp; orgName!=''">
			AND o.org_name NOT LIKE #{orgName}
		</if>
	</select>

	<select id="countEventEmerApproveStatus" parameterType="string" resultType="int">
		SELECT
		count(e.id)
		FROM
		( SELECT * FROM `event` WHERE id = #{eventId} ) AS e
		LEFT JOIN event_deal_status AS eds ON e.id = eds.event_id
		WHERE
		eds.plan_response = 1
	</select>

	<resultMap id="orgRoadFacilityMap" type="com.bt.itsevent.domain.vo.OrgRoadListVO">
		<id column="org_id" property="orgId"/>
		<collection property="children" ofType="com.bt.itsevent.feign.RoadVO">
				<id column="road_no" property="roadNo"/>
				<result column="road_name" property="roadName"/>
				<result column="road_alias" property="roadAlias"/>
				<result column="parent_road" property="parentRoad"/>
				<collection property="facilitys" ofType="com.bt.itsevent.feign.FacilityVO">
					<id column="facility_no" property="facilityNo"/>
					<result column="facility_name" property="facilityName"/>
					<result column="mile_post" property="milePost"/>
				</collection>
		</collection>
	</resultMap>
	<select id="orgRoadFacilityList" resultMap="orgRoadFacilityMap" parameterType="java.util.Map">
		SELECT o.org_id,r.road_no,r.road_name,r.road_alias,r.parent_road,d.facility_no,d.facility_name,d.mile_post
		FROM organization_road o, road r, facility d
		WHERE o.road_no=r.road_no AND d.road_no = r.road_no
		AND (d.facility_no IN (SELECT DISTINCT facility_no FROM role_facility re WHERE re.role_id IN
		<foreach collection="roles" item="roleId" separator="," open="(" close=")">#{roleId}</foreach>
		)
		<if test="companyId != null"> OR o.org_id=#{companyId}</if>
		)
		ORDER BY r.sort,d.sort
	</select>

	<update id="updateReachTime" parameterType="java.util.Map">
	UPDATE event_dd SET reach_time=#{reachTime} , reach_flag=1 WHERE event_id=#{id}
	</update>

	<update id="updateReachFlag" parameterType="java.util.Map">
	UPDATE event_dd SET reach_flag=1 WHERE event_id=#{id}
	</update>

	<select id="selectAvgDealTimeEventIds" parameterType="com.bt.itsevent.domain.dto.EventAnalysisDTO" resultType="com.bt.itsevent.domain.vo.EventOrgVO">
		SELECT 
			DISTINCT e.id, e.org_id AS orgId
		FROM 
			`event` AS e LEFT JOIN event_dd AS ed ON e.id = ed.event_id 
		WHERE 
		  e.event_type = 1 AND e.deal_status = 100
<!--		  ( AND e.event_three_type = 79 OR e.event_four_type = 26 OR e.event_three_type = 11 )-->
		  AND ( ed.stat_deal_time IS NULL OR ed.stat_deal_time = 0 )
		  AND ( del_status = 0 OR del_status IS NULL ) 
		  AND org_id IS NOT NULL 
		  AND e.distribute_time IS NOT NULL 
		  AND e.final_report_time IS NOT NULL 
		  AND (e.finish_type IS NULL OR e.finish_type=0) 
		  <if test="ltTime != null"> AND #{ltTime} >= ( e.final_report_time - e.distribute_time ) </if>
		  <if test="gtTime != null"> AND ( e.final_report_time - e.distribute_time ) > #{gtTime} </if>
		  <if test="startTime != null"> AND e.report_time >= #{startTime} </if>
		  <if test="endTime != null"> AND e.report_time &lt;= #{endTime} </if>
		  <if test="reachFlag != null"> AND ed.reach_flag = 1 AND ed.reach_time IS NOT NULL </if>
		  <if test="reachTime != null"> AND ed.reach_time IS NOT NULL AND #{reachTime} >= (ed.reach_time-e.distribute_time)</if>
		  <if test="gtReachTime != null"> AND ed.reach_time IS NOT NULL AND (ed.reach_time-e.distribute_time) > #{gtReachTime}</if>
		  <if test="roleIds != null"> AND e.org_id IN 
  		  (SELECT DISTINCT org_id FROM role_event re WHERE re.role_id IN <foreach collection="roleIds" item="item" open="(" separator="," close=")">#{item}</foreach>
  		   ) </if>
	</select>

	<select id="selectNoDealTimeEventIds" parameterType="com.bt.itsevent.domain.dto.EventAnalysisDTO" resultType="com.bt.itsevent.domain.vo.EventOrgVO">
		SELECT 
			DISTINCT e.id, e.org_id AS orgId
		FROM 
			`event` AS e LEFT JOIN event_dd AS ed ON e.id = ed.event_id 
		WHERE 
		  e.event_type = 1 AND e.deal_status = 100
		  AND ( ed.stat_deal_time IS NULL OR ed.stat_deal_time = 0 )
		  AND ( e.del_status = 0 OR del_status IS NULL ) 
		  AND e.org_id IS NOT NULL 
		  AND e.distribute_time IS NOT NULL 
		  AND e.final_report_time IS NOT NULL
		  AND (e.finish_type IS NULL OR e.finish_type=0) 
		  AND e.report_time >= #{startTime}
		  AND e.report_time &lt;= #{endTime}
		  AND #{timeSecond} &lt; ( e.final_report_time - e.distribute_time )
		  <if test="roleIds != null"> AND e.org_id IN 
  		  (SELECT DISTINCT org_id FROM role_event re WHERE re.role_id IN <foreach collection="roleIds" item="item" open="(" separator="," close=")">#{item}</foreach>
  		   ) </if>
	</select>

	<select id="selectConfirmEventIds" parameterType="com.bt.itsevent.domain.dto.EventAnalysisDTO" resultType="com.bt.itsevent.domain.vo.EventOrgVO">
		SELECT 
			DISTINCT e.id, e.org_id AS orgId
		FROM 
			`event` AS e LEFT JOIN event_deal_status AS ed ON e.id = ed.event_id 
		WHERE 
		  e.event_type = 1 AND e.deal_status = 100
		  AND ( del_status = 0 OR del_status IS NULL ) 
		  AND e.org_id IS NOT NULL 
		  AND e.distribute_time IS NOT NULL 
		  AND e.final_report_time IS NOT NULL 
		  AND (e.finish_type IS NULL OR e.finish_type=0) 
		  <if test="startTime != null"> AND e.report_time >= #{startTime} </if>
		  <if test="endTime != null"> AND e.report_time &lt;= #{endTime} </if>
		  <if test="reachFlag != null"> AND ed.deal_response=1 AND ed.deal_response_time IS NOT NULL </if>
  		  <if test="reachTime != null"> AND #{reachTime} >= ( ed.deal_response_time - e.distribute_time ) </if>
  		  <if test="roleIds != null"> AND e.org_id IN 
  		  (SELECT DISTINCT org_id FROM role_event re WHERE re.role_id IN <foreach collection="roleIds" item="item" open="(" separator="," close=")">#{item}</foreach>
  		   ) </if>
	</select>

<!--出发未达标分页使用-->
	<select id="selectNoConfirmEventIds" parameterType="com.bt.itsevent.domain.dto.EventAnalysisDTO" resultType="com.bt.itsevent.domain.vo.EventOrgVO">
		SELECT 
			DISTINCT e.id, e.org_id AS orgId
		FROM 
			`event` AS e LEFT JOIN event_deal_status AS ed ON e.id = ed.event_id 
		WHERE 
		  e.event_type = 1 AND e.deal_status = 100
		  AND ( del_status = 0 OR del_status IS NULL ) 
		  AND e.org_id IS NOT NULL 
		  AND e.distribute_time IS NOT NULL 
		  AND e.final_report_time IS NOT NULL 
		  AND (e.finish_type IS NULL OR e.finish_type=0) 
		  AND e.report_time >= #{startTime}
		  AND e.report_time &lt;= #{endTime}
		  AND ((ed.deal_response=1 AND ed.deal_response_time IS NOT NULL AND #{timeSecond} &lt; ( ed.deal_response_time - e.distribute_time )) 
		  OR ed.deal_response IS NULL OR ed.deal_response=0 OR ed.deal_response_time IS NULL)
  		  <if test="roleIds != null"> AND e.org_id IN 
  		  (SELECT DISTINCT org_id FROM role_event re WHERE re.role_id IN <foreach collection="roleIds" item="item" open="(" separator="," close=")">#{item}</foreach>
  		   ) </if>
	</select>
<!--到达未达标分页使用-->
	<select id="selectNoReachTimeEventIds" parameterType="com.bt.itsevent.domain.dto.EventAnalysisDTO" resultType="com.bt.itsevent.domain.vo.EventOrgVO">
		SELECT 
			DISTINCT e.id, e.org_id AS orgId
		FROM 
			`event` AS e LEFT JOIN event_dd AS ed ON e.id = ed.event_id 
		WHERE 
		  e.event_type = 1 AND e.deal_status = 100
		  AND ( ed.stat_deal_time IS NULL OR ed.stat_deal_time = 0 )
		  AND ( del_status = 0 OR del_status IS NULL ) 
		  AND org_id IS NOT NULL 
		  AND e.distribute_time IS NOT NULL 
		  AND e.final_report_time IS NOT NULL 
		  AND (e.finish_type IS NULL OR e.finish_type=0) 
		  AND e.report_time >= #{startTime}
		  AND e.report_time &lt;= #{endTime}
		  AND ((ed.reach_flag = 0 OR ed.reach_time IS NULL)
		  OR (ed.reach_time IS NOT NULL AND #{timeSecond} &lt; (ed.reach_time-e.distribute_time)))
		  <if test="roleIds != null"> AND e.org_id IN 
  		  (SELECT DISTINCT org_id FROM role_event re WHERE re.role_id IN <foreach collection="roleIds" item="item" open="(" separator="," close=")">#{item}</foreach>
  		   ) </if>
	</select>

	<select id="selectUnblockEventIds" parameterType="com.bt.itsevent.domain.dto.EventAnalysisDTO" resultType="com.bt.itsevent.domain.vo.EventOrgVO">
		SELECT 
			DISTINCT e.id, e.org_id AS orgId
		FROM 
			`event` AS e LEFT JOIN event_deal_status AS ed ON e.id = ed.event_id 
		WHERE 
		  e.event_type = 1 AND e.deal_status = 100
		  AND ( del_status = 0 OR del_status IS NULL ) 
		  AND e.org_id IS NOT NULL 
		  AND e.distribute_time IS NOT NULL 
		  AND e.final_report_time IS NOT NULL 
		  AND (e.finish_type IS NULL OR e.finish_type=0) 
		  <if test="startTime != null"> AND e.report_time >= #{startTime} </if>
		  <if test="endTime != null"> AND e.report_time &lt;= #{endTime} </if>
		  <if test="reachFlag != null"> AND ed.unblock IS NOT NULL </if>
  		  <if test="reachTime != null and reachFlag != null">  AND ed.unblock=2 AND #{reachTime} >= ( ed.unblock_time - e.distribute_time ) </if>
  		  <if test="roleIds != null"> AND e.org_id IN 
  		  (SELECT DISTINCT org_id FROM role_event re WHERE re.role_id IN <foreach collection="roleIds" item="item" open="(" separator="," close=")">#{item}</foreach>
  		   ) </if>
	</select>
<!--疏通未达标分页-->
	<select id="selectNoUnblockEventIds" parameterType="com.bt.itsevent.domain.dto.EventAnalysisDTO" resultType="com.bt.itsevent.domain.vo.EventOrgVO">
		SELECT 
			DISTINCT e.id, e.org_id AS orgId
		FROM 
			`event` AS e LEFT JOIN event_deal_status AS ed ON e.id = ed.event_id 
		WHERE 
		  e.event_type = 1 AND e.deal_status = 100
		  AND ( del_status = 0 OR del_status IS NULL ) 
		  AND e.org_id IS NOT NULL 
		  AND e.distribute_time IS NOT NULL 
		  AND e.final_report_time IS NOT NULL 
		  AND (e.finish_type IS NULL OR e.finish_type=0) 
		  AND e.report_time >= #{startTime}
		  AND e.report_time &lt;= #{endTime}
		  AND ed.unblock IS NOT NULL
  		  AND ((ed.unblock=2 AND #{reachTime} &lt; ( ed.unblock_time - e.distribute_time )) OR ed.unblock!=2)
  		  <if test="roleIds != null"> AND e.org_id IN 
  		  (SELECT DISTINCT org_id FROM role_event re WHERE re.role_id IN <foreach collection="roleIds" item="item" open="(" separator="," close=")">#{item}</foreach>
  		   ) </if>
	</select>

	<select id="selectListByIds" parameterType="java.util.List" resultMap="EmerRescueDetailMap">
		SELECT 
			e.id,e.event_no,e.report_time,e.report_source_key,e.event_type,e.event_two_type,e.event_three_type,e.event_four_type,
			e.road_no,e.mile_post,e.direction_no,e.brief_desc,e.deal_result,
			e.final_report_time,e.finish_time,e.order_create_time,e.record_man,e.distribute_time,
			r.road_name,d.direction_name,dd.reach_time,s.deal_response_time,s.unblock,s.unblock_time,
			e.`level`,o.org_name,CONCAT(IFNULL(o.org_name,''),IFNULL(r.road_name,''),IFNULL(d.direction_name,''),IFNULL(e.mile_post,'')) address,a.audit_status
		FROM 
			`event` e
			LEFT JOIN road r on e.road_no=r.road_no
			LEFT JOIN direction d on e.direction_no=d.direction_no
			LEFT JOIN event_dd dd on e.id=dd.event_id
			LEFT JOIN event_deal_status s on e.id=s.event_id
			LEFT JOIN organization o on e.org_id=o.org_id
			LEFT JOIN event_timeout_audit a on e.id=a.event_id
			
		WHERE 
			e.id IN
		<foreach collection="list" item="id" separator="," open="(" close=")">#{id}</foreach>
	</select>

	<select id="selectEventTimeoutAuditAttachs" parameterType="com.bt.itscore.domain.dto.IdStringDTO" resultMap="EventAttachMap">
		SELECT id,event_id,file_name,disk_file_name,file_size,content_type,digest,disk_directory,create_time FROM event_timeout_audit_attach WHERE event_id=#{id}
	</select>

	<select id="selectEventDistribute" parameterType="java.lang.String" resultType="int">
		SELECT IFNULL(SUM(re.distribute), 0) AS distribute FROM `user` u ,user_role ur ,role_event re WHERE u.user_id = ur.user_id AND ur.role_id = re.role_id AND u.user_id=#{userId}
	</select>

	<resultMap id="directionMap" type="com.bt.itsevent.feign.DirectionVO">
		<result column="direction_no" property="directionNo"/>
		<result column="direction_name" property="directionName"/>
	</resultMap>
	<select id="selectDirectionByRoad" parameterType="java.lang.Integer" resultMap="directionMap">
		SELECT direction_no, direction_name FROM direction WHERE road_no=#{roadNo}
	</select>
	<select id="selectRoadTypeListByEventId" parameterType="java.util.List" resultMap="EmerRescueDetailMap">
		SELECT e.id,f.facility_type_no,f.facility_name
		FROM `event` e  LEFT JOIN facility f ON e.facility_no=f.facility_no
		WHERE e.id IN
		<foreach collection="list" item="id" separator="," open="(" close=")">#{id}</foreach>
	</select>

	<insert id="addTsJy" parameterType="com.bt.itsevent.domain.dto.EventTsDTO">
	INSERT INTO `event` (id,source,report_source_key,brief_desc,report_time,
		event_type,event_two_type,event_three_type,event_four_type,complaint_type,
		report_man,report_man_tel,car_plate,complaint_target,org_id,business_type,
		record_man,record_man_id,record_man_tel,source_id,create_user_id,
		create_time,order_create_time,deal_status,event_no,complaint_supplement) VALUES (
		#{id},#{source},#{reportSourceKey},#{briefDesc},#{reportTime},
		#{eventType},#{eventTwoType},#{eventThreeType},#{eventFourType},#{complaintType},
		#{reportMan},#{reportManTel},#{carPlate},#{complaintTarget},#{orgId},#{businessType},
		#{recordMan},#{recordManId},#{recordManTel},#{sourceId},#{createUserId},
		#{createTime},#{createTime},2,#{eventNo},#{complaintSupplement}
		)
	</insert>

	<!-- 修改投诉举报意见的基本信息 -->
	<update id="updateTsJy" parameterType="com.bt.itsevent.domain.dto.EventTsDTO">
	UPDATE `event` SET org_id=#{orgId},
		source_id=#{sourceId},
		report_source_key=#{reportSourceKey},
		report_time=#{reportTime},
		event_type=#{eventType},
		event_two_type=#{eventTwoType},
		event_three_type=#{eventThreeType},
		complaint_type=#{complaintType},
		report_man=#{reportMan},
		report_man_tel=#{reportManTel},
		car_plate=#{carPlate},
		complaint_target=#{complaintTarget},
		brief_desc=#{briefDesc},
		event_no=#{eventNo},
		business_type=#{businessType},
		complaint_supplement=#{complaintSupplement}
	WHERE id=#{id}
	</update>

	<resultMap type="com.bt.itsevent.domain.dto.ProcessSceneExportDTO" id="ProcessSceneExportMap">
		<id column="id" property="sceneId"/>
		<result column="file_name" property="fileName"/>
		<result column="disk_file_name" property="diskFileName"/>
		<result column="name" property="sceneName"/>
	</resultMap>
	<select id="selectProcessScene" parameterType="com.bt.itscore.domain.dto.IdStringDTO" resultMap="ProcessSceneExportMap">
		SELECT
			p.id,
			a.file_name,
			a.disk_file_name,
			d.`name`
		FROM
			event_progress_scene p,
			progress_attach a,
			dict_item d
		WHERE
			p.event_id = #{id}
			AND p.id = a.progress_scene_id
			AND d.type_id = 97
			AND d.`value` = p.step
	</select>

	<resultMap type="com.bt.itsevent.domain.vo.OrgListVO" id="OrgListMap">
	<result column="org_id" property="orgId"/>
	<result column="org_name" property="orgName"/>
	</resultMap>
	<select id="orgByUser" parameterType="java.util.Map" resultMap="OrgListMap">
		SELECT DISTINCT
		r.org_id,
		o.org_name
		FROM
		role_event r, organization o
		WHERE
		r.org_id = o.org_id
		AND r.role_id IN <foreach collection="roleIds" item="roleId" separator="," open="(" close=")">#{roleId}</foreach>
	</select>

	<update id="updateInitialReportBrief" parameterType="com.bt.itsevent.domain.dto.EmerRescueDTO">
         update event set brief_desc=#{briefDesc} where id=#{id}
	</update>

	<update id="updateAttachs" parameterType="com.bt.itsevent.domain.dto.EmerRescueDTO">
		<if test="attachs != null and attachs.size() > 0 ">
			UPDATE event_attach SET event_id=#{id} WHERE id IN
			<foreach collection="attachs" item="a" open="(" close=")" separator=",">#{a}</foreach>
		</if>
	</update>

	<update id="update96333OrderDealOrg" parameterType="com.bt.itscore.domain.dto.Event96333NewOrderDTO">
		UPDATE event SET org_id=#{sourceDept}, source_id=#{sourceId}, deal_org_id=#{sourceDept}, deal_org_name=#{sourceArea} WHERE event_no=#{recordId}
	</update>

	<update id="updateEventZxDealOrg" parameterType="com.bt.itscore.domain.dto.Event96333NewOrderDTO">
		UPDATE event_zx SET org1_id=#{sourceDept} WHERE event_id=#{id}
	</update>

	<select id="query96333OrderList" parameterType="java.util.Map" resultType="java.lang.String">
	SELECT event_no FROM `event` WHERE del_status=0 AND event_no IN 
		<foreach collection="eventNos" item="eventNo" separator="," open="(" close=")">#{eventNo}</foreach>
	</select>

	<select id="selectLocation" parameterType="com.bt.itscore.domain.dto.UserLocationDTO" resultType="com.bt.itsevent.domain.vo.EventLocationVO">
	SELECT e.id AS eventId,eeu.user_id AS userId,e.lng,e.lat,IFNULL(
		6371 * acos(
		cos(radians(#{lat}))
		* cos( radians( e.lat ) )
		* cos(radians( e.lng ) - radians(#{lng}))
		+
		sin( radians(#{lat}) )
		* sin( radians( e.lat ) )
		),0
		) AS distance
		FROM  `event` e 
	LEFT JOIN event_emer_user eeu ON e.id=eeu.event_id WHERE <![CDATA[ e.deal_status<100 ]]> AND e.lng IS NOT NULL AND eeu.user_id=#{userId}
	</select>

	<select id="personLocationByEventId" parameterType="com.bt.itscore.domain.dto.IdStringDTO" resultType="com.bt.itscore.domain.vo.UserLocationVO">
	SELECT t.userId,t.userName,t.mobile,t.locationTime,t.lat,t.lng,o1.org_name AS deptName,o2.org_name AS companyName FROM (
		SELECT u.user_id AS userId,u.user_name AS userName,u.mobile,u.location_time AS locationTime,u.lat,u.lng,u.org_id,u.company_id
		FROM event_emer_user eeu 
		LEFT JOIN `user` u ON eeu.user_id=u.user_id WHERE eeu.event_id=#{id} AND u.lat IS NOT NULL
		)t
		LEFT JOIN organization o1 ON t.org_id=o1.org_id
		LEFT JOIN organization o2 ON t.company_id=o2.org_id
	</select>

	<select id="selectTodayEmerSeqno" parameterType="java.util.HashMap" resultType="int">
	SELECT COUNT(*) FROM event WHERE report_time &lt; #{reportTime} AND report_time >= #{todayStartTime} AND event_type=1 AND del_status=0
	</select>

	<insert id="submitInfoReview" parameterType="com.bt.itsevent.domain.dto.EventInfoReviewDTO">
	INSERT INTO event_info_review (id,event_id,event_no,info_type,progress_desc,create_user_id,create_user_name,create_time,review_status,
	event_desc,report_time,report_source,location) VALUES (
		#{reviewId},#{id},#{eventNo},#{infoType},#{progressDesc},#{createUserId},
		#{createUserName},#{createTime},#{reviewStatus},
		#{eventDesc},#{reportTime},#{reportSource},#{location});
	INSERT INTO event_info_review_user (review_id,user_id,user_name) VALUES
	<foreach collection="infoReviewUsers" item="u" open="" close="" separator=",">(#{reviewId},#{u.userId},#{u.userName})</foreach>
	</insert>

	<resultMap type="com.bt.itsevent.domain.vo.EventInfoReviewVO" id="EventInfoReviewMap">
		<id column="id" property="id" />
		<result column="event_id" property="eventId" />
		<result column="event_no" property="eventNo" />
		<result column="progress_desc" property="progressDesc" />
		<result column="create_user_id" property="createUserId" />
		<result column="create_user_name" property="createUserName" />
		<result column="review_user_id" property="reviewUserId" />
		<result column="review_user_name" property="reviewUserName" />
		<result column="info_type" property="infoType" />
		<result column="review_status" property="reviewStatus" />
		<result column="create_time" property="createTime" />
		<result column="review_time" property="reviewTime" />
		<result column="review_remark" property="reviewRemark" />
		<result column="event_desc" property="eventDesc" />
		<result column="report_time" property="reportTime" />
		<result column="report_source" property="reportSource" />
		<result column="location" property="location" />
		<collection property="infoReviewUsers" ofType="com.bt.itsevent.domain.dto.ProgressUserDTO">
			<id column="uid" property="eventProgressId" />
			<result column="user_id" property="userId" />
			<result column="user_name" property="userName" />
		</collection>
	</resultMap>
	<select id="selectEventInfoReview" parameterType="com.bt.itsevent.domain.dto.EventInfoReviewDTO" resultMap="EventInfoReviewMap">
	SELECT DISTINCT r.* FROM event_info_review r, event_info_review_user u
		WHERE r.id=u.review_id
		<if test="loginUserId != null and loginUserId != ''">
		AND (r.create_user_id=#{loginUserId} OR u.user_id=#{loginUserId})
		</if>
		<if test="eventNo != null and eventNo != ''"> AND r.event_no LIKE CONCAT('%',#{eventNo},'%')</if>
		<if test="reviewStatus != null"> AND r.review_status=#{reviewStatus}</if>
		<if test="infoType != null"> AND r.info_type=#{infoType}</if>
		<if test="startTime != null and startTime != ''"> AND r.create_time>=#{startTime}</if>
		<if test="endTime != null and endTime != ''"> AND r.create_time&lt;=#{endTime}</if>
		<if test="eventIds != null and eventIds.size() > 0 "> 
		AND r.event_id IN <foreach collection="eventIds" item="eventId" open="(" close=")" separator=",">#{eventId}</foreach>
		</if>
		ORDER BY r.create_time DESC
	</select>
	<select id="selectEventInfoReviewUser" parameterType="java.util.Map" resultMap="EventInfoReviewMap">
	SELECT r.id,r.event_id,u.id AS uid,u.user_id,u.user_name FROM event_info_review r, event_info_review_user u
		WHERE r.id=u.review_id
		AND u.review_id IN 
		<foreach collection="reviewIds" item="reviewId" open="(" close=")" separator=",">#{reviewId}</foreach>
	</select>
	
	<insert id="addEventReportDraft" parameterType="com.bt.itsevent.domain.dto.EventReportDraftDTO">
	INSERT INTO event_report_draft (review_id,progress_desc,report_time,report_source,location,event_desc,create_time) VALUES (
		#{reviewId},#{progressDesc},#{reportTime},#{reportSource},#{location},
		#{eventDesc},#{createTime}
		);
		<if test="atUser != null and atUser.size() > 0">
	INSERT INTO event_report_draft_user (review_id,mobile,user_id,user_name) VALUES
	<foreach collection="atUser" item="u" open="" close="" separator=",">(#{reviewId},#{u.mobile},#{u.userId},#{u.userName})</foreach>
	</if>
	</insert>

	<update id="updateEventInfoReview" parameterType="com.bt.itsevent.domain.dto.EventInfoReviewDTO">
	UPDATE event_info_review SET review_status=#{reviewStatus},review_remark=#{reviewRemark},review_user_id=#{createUserId},
	review_user_name=#{createUserName},review_time=#{createTime} WHERE id=#{reviewId}
	</update>

	<select id="selectEventInfoReviewByReviewId" parameterType="com.bt.itsevent.domain.dto.EventInfoReviewDTO" resultMap="EventInfoReviewMap">
	SELECT * FROM event_info_review WHERE id=#{reviewId}
	</select>

	<resultMap type="com.bt.itsevent.domain.dto.EventConfirmDTO" id="EventReportDraftMap">
		<id column="event_id" property="id" />
		<result column="progress_desc" property="progressDesc" />
		<result column="report_time" property="reportTime" />
		<result column="report_source" property="reportSource" />
		<result column="location" property="location" />
		<result column="event_desc" property="eventDesc" />
		<collection property="atUsers" ofType="com.bt.itsevent.domain.dto.ProgressUserDTO">
			<id column="uid" property="eventProgressId" />
			<result column="user_id" property="userId" />
			<result column="user_name" property="userName" />
			<result column="mobile" property="mobile" />
		</collection>
	</resultMap>
	<select id="selectEventReportDraftByReviewId" parameterType="com.bt.itsevent.domain.dto.EventInfoReviewDTO" resultMap="EventReportDraftMap">
	SELECT e.event_id,d.*,u.id AS uid,u.user_id,u.user_name,u.mobile FROM event_report_draft d, event_report_draft_user u,event_info_review e
	 WHERE d.review_id=#{reviewId} AND d.review_id=u.review_id AND d.review_id=e.id
	</select>

	<update id="updateEventSetPending" parameterType="com.bt.itscore.domain.dto.IdStringDTO">
	UPDATE `event` SET deal_status=2,del_status=0 WHERE id=#{id}
	</update>

	<update id="updateEventInfoInvalid" parameterType="com.bt.itsevent.domain.dto.EventInfoReviewDTO">
	UPDATE event_info_review SET review_status=#{reviewStatus},review_remark=#{reviewRemark} WHERE event_id=#{eventId} AND review_status!=1 
	<if test="infoType != null"> AND info_type=#{infoType}</if>
	</update>

	<select id="statLoginUserEventInfoReview" parameterType="java.util.Map" resultType="int">
	SELECT COUNT(DISTINCT r.id) FROM event_info_review r, event_info_review_user u
		WHERE r.id=u.review_id AND review_status=0
		<if test="userId != null and userId != ''">
		AND (r.create_user_id=#{userId} OR u.user_id=#{userId})
		</if>
	</select>

	<update id="updateEventInfoReviewDesc" parameterType="com.bt.itsevent.domain.dto.EventInfoReviewDTO">
	UPDATE event_info_review SET progress_desc=#{progressDesc}, review_remark='', review_status=0, event_desc=#{eventDesc},
	report_time=#{reportTime},report_source=#{reportSource},location=#{location}
	 WHERE id=#{reviewId}
	</update>

	<select id="statEventInfoReviewByEventId" parameterType="com.bt.itsevent.domain.dto.EventInfoReviewDTO" resultType="int">
	SELECT COUNT(r.id) FROM event_info_review r WHERE r.create_user_id=#{createUserId} AND review_status=#{reviewStatus} AND r.event_id=#{eventId}
	</select>

	<update id="updateEventReportDraft" parameterType="com.bt.itsevent.domain.dto.EventInfoReviewDTO">
	UPDATE event_report_draft SET progress_desc=#{progressDesc}, event_desc=#{eventDesc},
	report_time=#{reportTime},report_source=#{reportSource},location=#{location}
	 WHERE review_id=#{reviewId}
	</update>

	<select id="selectTipList" resultType="com.bt.itsevent.domain.vo.UavTipVO">
	SELECT * FROM uav_tip
	</select>
</mapper>

