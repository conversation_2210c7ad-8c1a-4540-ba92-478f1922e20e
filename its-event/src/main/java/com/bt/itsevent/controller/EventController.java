package com.bt.itsevent.controller;

import com.bt.itscore.auth.CheckAuthz;
import com.bt.itscore.auth.Login;
import com.bt.itscore.domain.dto.*;
import com.bt.itscore.domain.vo.*;
import com.bt.itscore.exception.ArgumentException;
import com.bt.itscore.exception.AuthzException;
import com.bt.itscore.utils.OssUtils;
import com.bt.itscore.utils.TimeUtils;
import com.bt.itscore.utils.ValidUtils;
import com.bt.itsevent.domain.dto.*;
import com.bt.itsevent.domain.vo.*;
import com.bt.itsevent.feign.*;
import com.bt.itsevent.service.*;
import com.bt.itsevent.utils.AssignJobs;
import com.bt.itsevent.utils.RobotCallTmpResultContext;
import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;

/**
* <AUTHOR>
* @date 2021年5月13日 下午2:58:35
* @Description 事件处理类
 */
@RestController
@RequestMapping("event")
public class EventController {
	private final static Logger LOGGER = LoggerFactory.getLogger(EventController.class);
	@Autowired
	private EventService eventService;
	@Autowired
	private Event96333Service event96333Service;
	@Autowired
	private ItsVideoFeignClient itsVideoFeignClient;
	@Autowired
	private FeignClient feignClient;
	@Autowired
	private EventDraftService eventDraftService;
	@Autowired
	private EventStatisticsService eventStatisticsService;
	@SuppressWarnings("rawtypes")
	@Autowired
	private RedisTemplate redisTemplate;

	@Autowired
	private RobotCallHandlerService robotCallHandlerService;

	public static final String SYS_GLOBAL_FRONT_VERSION = "sys.global.front.version";

	/**
	 * .新增事件，有附件的需要批量更新event_attach表中的event_no字段
	 */
	@Deprecated
	@Login
	@CheckAuthz(hasPermissions = "sys:event:add")
	@PostMapping("add")
	public ResponseVO add(@Valid @RequestBody EventDTO eventDTO, BindingResult result) {
        ValidUtils.error(result);
		boolean ret = eventService.add(eventDTO);
		return new ResponseVO(ret);
	}

	/**
	 * .分页查询与我关联的事件列表
	 */
	@Login
	@PostMapping("pageMyEvent")
	public PageVO pageMyEvent(@Valid PageDTO pageDTO, @RequestBody EventQueryDTO dto, BindingResult result, HttpServletRequest request) {
		ValidUtils.error(result);
	    dto.setUserId((String)request.getAttribute("userId"));
		String role = (String)request.getAttribute("role");
		dto.setRoleIds(Arrays.asList(role.split(";")));
		return new PageVO(eventService.pageMyEvent(pageDTO, dto));
	}

	@Login
	@PostMapping("detail")
	public EventVO detail(@RequestBody IdStringDTO dto) {
		if(StringUtils.isBlank(dto.getId())) {
			throw new ArgumentException("请求body参数校验不通过:事件ID不能为空");
		}
		event96333Service.getRequestConfig();
		return eventService.detail(dto);
	}

	@Login
	@CheckAuthz(hasPermissions = "sys:event:delete")
	@PostMapping("delete")
	public ResponseVO delete(@Valid @RequestBody IdStringDTO idStringDTO, BindingResult result) {
		ValidUtils.error(result);
		boolean ret = eventService.delete(idStringDTO);
		return new ResponseVO(ret);
	}

	/**
	 * 用户事件状态更改（card_pass已出发15，已到达20，已离开30）。同时生成新的进展
	 * @param dto
	 * @param result
	 * @param request
	 * @return
	 */
	@Login
	@PostMapping("updateEventStatus")
	public ResponseVO updateEventStatus(@Valid @RequestBody EventClockStatuDTO dto, BindingResult result,HttpServletRequest request) {
		ValidUtils.error(result);
		dto.setUserId((String)request.getAttribute("userId"));
		return eventService.clockEventStatus(dto);
	}

	/**
	 * .统计未完成事件 -按照类型统计
	 */
	@Login
	@PostMapping("statNoDeal")
	public List<EventStatVO> statNoDeal(HttpServletRequest request) {
		String role = (String)request.getAttribute("role");
		if(role == null || role.length() < 1) {
			LOGGER.info("没有角色权限");
			throw new AuthzException("没有角色权限");
		}
		LOGGER.info("role:{}", role);
		EventQueryDTO dto = new EventQueryDTO();
//		dto.setRoleIds(Arrays.asList(role.split(";")));
		return eventService.statNoDeal(dto);
	}

// ========= 新的需求接口 ===============
	/** 
    * @api {POST} /event/addEmerRescue 救援事件新增第一步/event/addEmerRescue
    * @apiDescription 救援事件新增第一步；创建人：邓云钢，修改人：龙思颖
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiBody {number} source=0 事件来源，0-本地，1-96333，2-告警消息
    * @apiBody {String} briefDesc=事件介绍 事件介绍
    * @apiBody {number} roadNo=171 路段编号
    * @apiBody {String} directionNo=cb46cb40-b169-40f6-a3dd-153106fc842e 方向编号
    * @apiBody {String} [milePost=K50+100] 桩号
    * @apiBody {number} reportTime=1652431791 上报时间戳（10位数字）
    * @apiBody {number} [deathMan=0] 死亡人数,无-0，不详-1，有-2'
    * @apiBody {number} [missMan=0] 失踪人数,无-0，不详-1，有-2'
    * @apiBody {number} [injureMan=0] 受伤人数,无-0，不详-1，有-2'
    * @apiBody {String} [reportMan] 求助人
    * @apiBody {String} [reportManTel] 求助人电话
    * @apiBody {String} [carPlate] 求助人车牌号
    * @apiBody {number[]} [occupiedLanes=【1,2】] 占用车道
    * @apiBody {number} eventType=1 事件一级类型
    * @apiBody {number} eventTwoType=6 事件二级类型
    * @apiBody {number} eventThreeType=11 事件三级类型
    * @apiBody {number} [eventFourType] 事件四级类型
    * @apiBody {number[]} [attachs=【1,2】] 附件id列表
    * @apiBody {String} emerPlanId 预案id
    * @apiBody {String} emerPlanLevelId 预案等级id
    * @apiBody {number} level=1 预案等级值，普通-10，I级-1，II级-2，III级-3，IV级-4
    * @apiBody {String} [weather] 天气（数据字典天气代码图标类型字典项值：CLEAR_DAY）
    * @apiBody {String} [facilityNo] 路况类型（设施）
    * @apiBody {String} [address] 所属区域
    * @apiBody {String} [accidentType] 事故类型值（数据字典事故类型值）
    * @apiBody {String} [otherTypeCause] 应急事故类型其他原因
    * @apiBody {number} [dangeFlag=0] 涉及危化品车 （0-未涉及,1-涉及；默认 0)
    * @apiBody {String} [accidentCause] 事故原因值（数据字典事故原因值）
    * @apiBody {String} [otherCause] 应急事故其他原因
    * @apiBody {String} [roadLossCause] 其他路损原因
    * @apiBody {String[]} [roadLoss] 路损内容列表
    * @apiBody {String} [typeCode] 路损列表-类型编号
    * @apiBody {String} [value] 路损列表-数值
    * @apiBody {number} [congestionLength] 拥堵长度
    * @apiSuccess (Success 200) {number=0,1,401} code 返回状态码，1成功，0失败，401未登录或token失效
    * @apiSuccess (Success 200) {String} message 提示信息，SUCCESS：成功，FAIL：失败
    * @apiSuccess (Success 200) {String} id 事件id
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "code": 1
    *       "message": "SUCCESS",
    *       "id": "8fe0076e-7e6d-4ebd-8f71-7dcef1bc8375"
    *     }
    * @apiSuccessExample {json} Success-Response:
	*     HTTP/1.1 200 OK code=401用户未登录状态
	*     {
	*       "code: 401
	*     }
	* @apiSampleRequest /event/addEmerRescue
    */
	@Login
//	@CheckAuthz(hasPermissions = "sys:event:addEmerRescue")
	@PostMapping("addEmerRescue")
	public ResponseVO addEmerRescue(@Valid @RequestBody EmerRescueDTO emerRescueDTO, BindingResult result, HttpServletRequest request) {
        ValidUtils.error(result);
        if (StringUtils.isBlank(emerRescueDTO.getEmerPlanId())) {
            throw new ArgumentException("预案id不能为空");
        }
        if (StringUtils.isBlank(emerRescueDTO.getEmerPlanLevelId())) {
            throw new ArgumentException("预案等级id不能为空");
        }
        if(emerRescueDTO.getLevel() == null) {
            throw new ArgumentException("响应等级不能为空");
        }
        if(emerRescueDTO.getEventThreeType() == null) {
            throw new ArgumentException("事件三级分类不能为空");
        }
        String userId = (String)request.getAttribute("userId");
        emerRescueDTO.setRecordManId(userId);
		emerRescueDTO.setDelStatus(1);
		emerRescueDTO.setDealStatus(3);
        emerRescueDTO.setId(UUID.randomUUID().toString());
		int ret = eventService.addEmerRescue(emerRescueDTO);
		if(ret > 0) {
			return new ResponseVO(ret, emerRescueDTO.getId());
		}
		return new ResponseVO(ret);
	}

    /** 
    * @api {POST} /event/updateEmerRescueStepOne 救援事件更新第一步/event/updateEmerRescueStepOne
    * @apiDescription 救援事件更新第一步；创建人：邓云钢，修改人：宁艺强
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiBody {String} id=事件id 事件id
    * @apiBody {number} source=0 事件来源，0-本地，1-96333，2-告警消息
    * @apiBody {String} briefDesc=事件介绍 事件介绍
    * @apiBody {number} roadNo=171 路段编号
    * @apiBody {String} directionNo=cb46cb40-b169-40f6-a3dd-153106fc842e 方向编号
    * @apiBody {String} [milePost=K50+100] 桩号
    * @apiBody {number} reportTime=1652431791 上报时间戳（10位数字）
    * @apiBody {number} [deathMan=0] 死亡人数,无-0，不详-1，有-2'
    * @apiBody {number} [missMan=0] 失踪人数,无-0，不详-1，有-2'
    * @apiBody {number} [injureMan=0] 受伤人数,无-0，不详-1，有-2'
    * @apiBody {String} [reportMan] 求助人
    * @apiBody {String} [reportManTel] 求助人电话
    * @apiBody {String} [carPlate] 求助人车牌号
    * @apiBody {number[]} [occupiedLanes=【1,2】] 占用车道
    * @apiBody {number} eventType=1 事件一级类型
    * @apiBody {number} eventTwoType=6 事件二级类型
    * @apiBody {number} eventThreeType=11 事件三级类型
    * @apiBody {number} [eventFourType] 事件四级类型
    * @apiBody {number[]} [attachs=【1,2】] 附件id列表
    * @apiBody {String} emerPlanId 预案id
    * @apiBody {String} emerPlanLevelId 预案等级id
    * @apiBody {number} level=1 预案等级值，普通-10，I级-1，II级-2，III级-3，IV级-4
    * @apiBody {String} [weather] 天气（数据字典天气代码图标类型字典项值：CLEAR_DAY）
    * @apiBody {String} [facilityNo] 路况类型（设施）
    * @apiBody {String} [address] 所属区域
    * @apiBody {String} [accidentType] 事故类型值（数据字典事故类型值）
    * @apiBody {String} [otherTypeCause] 应急事故类型其他原因
    * @apiBody {number} [dangeFlag=0] 涉及危化品车 （0-未涉及,1-涉及；默认 0)
    * @apiBody {String} [accidentCause] 事故原因值（数据字典事故原因值）
    * @apiBody {String} [otherCause] 应急事故其他原因
    * @apiBody {String} [roadLossCause] 其他路损原因
    * @apiBody {String} [reportSource] 接报来源（其他选项的文本）
    * @apiBody {number} [reportSourceKey] 接报来源key
    * @apiBody {String[]} [roadLoss] 路损内容列表
    * @apiBody {String} [typeCode] 路损列表-类型编号
    * @apiBody {String} [value] 路损列表-数值
    * @apiBody {number} [congestionLength] 拥堵长度
    * @apiSuccess (Success 200) {number=0,1,401} code 返回状态码，1成功，0失败，401未登录或token失效
    * @apiSuccess (Success 200) {String} message 提示信息，SUCCESS：成功，FAIL：失败
    * @apiSuccess (Success 200) {String} id 事件id
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "code": 1
    *       "message": "SUCCESS",
    *       "id": "8fe0076e-7e6d-4ebd-8f71-7dcef1bc8375"
    *     }
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK code=401用户未登录状态
    *     {
    *       "code: 401
    *     }
    * @apiSampleRequest /event/updateEmerRescueStepOne
    */
    @Login
    @PostMapping("updateEmerRescueStepOne")
    public Object updateEmerRescueStepOne(@RequestBody EmerRescueDTO emerRescueDTO, BindingResult result, HttpServletRequest request) {
        ValidUtils.error(result);
        if (StringUtils.isBlank(emerRescueDTO.getEmerPlanId())) {
            throw new ArgumentException("预案id不能为空");
        }
        if (StringUtils.isBlank(emerRescueDTO.getEmerPlanLevelId())) {
            throw new ArgumentException("预案等级id不能为空");
        }
        if(emerRescueDTO.getLevel() == null) {
            throw new ArgumentException("响应等级不能为空");
        }
        if(emerRescueDTO.getEventThreeType() == null) {
            throw new ArgumentException("事件三级分类不能为空");
        }
        String userId = (String)request.getAttribute("userId");
        emerRescueDTO.setRecordManId(userId);
        int ret = eventService.updateEmerRescueStepOne(emerRescueDTO);
        return new ResponseVO(ret > 0);
    }

	/**
	 * 删除应急救援归档事件
	 */
	@Login
	@CheckAuthz(hasPermissions = "sys:event:deleteEmerRescue")
	@PostMapping("deleteEmerRescue")
	public ResponseVO deleteEmerRescue(@Valid @RequestBody IdStringDTO idStringDTO, BindingResult result) {
		ValidUtils.error(result);
		if(eventService.selectEventSource(idStringDTO) == 1){
			throw new ArgumentException("信息来源是96333的数据，不允许删除");
		}
		boolean ret = eventService.delete(idStringDTO);
		return new ResponseVO(ret);
	}

    /** 
    * @api {POST} /event/addAppEmerRescue APP新增应急救援事件/event/addAppEmerRescue
    * @apiDescription APP新增应急救援事件；创建人：邓云钢，修改人：宁艺强
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiBody {number} source=0 事件来源，0-本地，1-96333，2-告警消息
    * @apiBody {String} briefDesc=事件介绍 事件介绍
    * @apiBody {number} roadNo=171 路段编号
    * @apiBody {String} directionNo=cb46cb40-b169-40f6-a3dd-153106fc842e 方向编号
    * @apiBody {String} [milePost=K50+100] 桩号（事件地点）
    * @apiBody {number} reportTime=1652431791 上报时间戳（10位数字）
    * @apiBody {number} [deathMan=0] 死亡人数,无-0，不详-1，有-2'
    * @apiBody {number} [missMan=0] 失踪人数,无-0，不详-1，有-2'
    * @apiBody {number} [injureMan=0] 受伤人数,无-0，不详-1，有-2'
    * @apiBody {String} [reportMan] 求助人
    * @apiBody {String} [reportManTel] 求助人电话
    * @apiBody {String} [carPlate] 求助人车牌号
    * @apiBody {number[]} [occupiedLanes=【1,2】] 占用车道
    * @apiBody {number[]} [attachs=【1,2】] 附件id列表
    * @apiBody {String} [weather] 天气（数据字典天气代码图标类型字典项值：CLEAR_DAY）
    * @apiBody {String} [accidentType] 事故类型值（数据字典事故类型值）
    * @apiBody {String} [otherTypeCause] 应急事故类型其他原因
    * @apiBody {number} [dangeFlag=0] 涉及危化品车 （0-未涉及,1-涉及；默认 0)
    * @apiBody {String} [accidentCause] 事故原因值（数据字典事故原因值）
    * @apiBody {String} [otherCause] 应急事故其他原因
    * @apiBody {String} [roadLossCause] 其他路损原因
    * @apiBody {String[]} [roadLoss] 路损内容列表
    * @apiBody {String} [typeCode] 路损列表-类型编号
    * @apiBody {String} [value] 路损列表-数值
    * @apiSuccess (Success 200) {number=0,1,401} code 返回状态码，1成功，0失败，401未登录或token失效
    * @apiSuccess (Success 200) {String} message 提示信息，SUCCESS：成功，FAIL：失败
    * @apiSuccess (Success 200) {String} id 事件id
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "code": 1
    *       "message": "SUCCESS",
    *       "id": "8fe0076e-7e6d-4ebd-8f71-7dcef1bc8375"
    *     }
    * @apiSampleRequest /event/addAppEmerRescue
    */
	@Login
	//	@CheckAuthz(hasPermissions = "sys:event:addAppEmerRescue")
	@PostMapping("addAppEmerRescue")
	public ResponseVO addAppEmerRescue(@Valid @RequestBody EmerRescueDTO emerRescueDTO, BindingResult result, HttpServletRequest request) {
		ValidUtils.error(result);
		String userId = (String)request.getAttribute("userId");
		emerRescueDTO.setRecordManId(userId);
		emerRescueDTO.setSource(3);
		return eventService.addAppEmerRescue(emerRescueDTO);
	}


    /** 
    * @api {POST} /event/updateAppEmerRescue APP修改应急救援事件/event/updateAppEmerRescue
    * @apiDescription APP修改应急救援事件；创建人：邓云钢，修改人：
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiBody {String} id=事件id 事件id
    * @apiBody {number} source=0 事件来源，0-本地，1-96333，2-告警消息
    * @apiBody {String} briefDesc=事件介绍 事件介绍
    * @apiBody {number} roadNo=171 路段编号
    * @apiBody {String} directionNo=cb46cb40-b169-40f6-a3dd-153106fc842e 方向编号
    * @apiBody {String} [milePost=K50+100] 桩号（事件地点）
    * @apiBody {number} reportTime=1652431791 上报时间戳（10位数字）
    * @apiBody {number} [deathMan=0] 死亡人数,无-0，不详-1，有-2'
    * @apiBody {number} [missMan=0] 失踪人数,无-0，不详-1，有-2'
    * @apiBody {number} [injureMan=0] 受伤人数,无-0，不详-1，有-2'
    * @apiBody {String} [reportMan] 求助人
    * @apiBody {String} [reportManTel] 求助人电话
    * @apiBody {String} [carPlate] 求助人车牌号
    * @apiBody {number[]} [occupiedLanes=【1,2】] 占用车道
    * @apiBody {number[]} [attachs=【1,2】] 附件id列表
    * @apiBody {String} [weather] 天气（数据字典天气代码图标类型字典项值：CLEAR_DAY）
    * @apiBody {String} [accidentType] 事故类型值（数据字典事故类型值）
    * @apiBody {String} [otherTypeCause] 应急事故类型其他原因
    * @apiBody {number} [dangeFlag=0] 涉及危化品车 （0-未涉及,1-涉及；默认 0)
    * @apiBody {String} [accidentCause] 事故原因值（数据字典事故原因值）
    * @apiBody {String} [otherCause] 应急事故其他原因
    * @apiBody {String} [roadLossCause] 其他路损原因
    * @apiBody {String[]} [roadLoss] 路损内容列表
    * @apiBody {String} [typeCode] 路损列表-类型编号
    * @apiBody {String} [value] 路损列表-数值
    * @apiSuccess (Success 200) {number=0,1,401} code 返回状态码，1成功，0失败，401未登录或token失效
    * @apiSuccess (Success 200) {String} message 提示信息，SUCCESS：成功，FAIL：失败
    * @apiSuccess (Success 200) {String} id 事件id
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "code": 1
    *       "message": "SUCCESS",
    *       "id": "8fe0076e-7e6d-4ebd-8f71-7dcef1bc8375"
    *     }
    * @apiSampleRequest /event/updateAppEmerRescue
    */
	@Login
	//	@CheckAuthz(hasPermissions = "sys:event:updateAppEmerRescue")
	@PostMapping("updateAppEmerRescue")
	public ResponseVO updateAppEmerRescue(@Valid @RequestBody EmerRescueDTO emerRescueDTO, BindingResult result, HttpServletRequest request) {
		ValidUtils.error(result);
		if (StringUtils.isBlank(emerRescueDTO.getId())) {
            throw new ArgumentException("事件id不能为空");
        }
		emerRescueDTO.setRecordManId((String)request.getAttribute("userId"));
		emerRescueDTO.setSource(3);
		return eventService.updateAppEmerRescue(emerRescueDTO);
	}

	/**
	 * .分页查询应急救援事件列表
	 */
	@Login
	@PostMapping("pageEmerRescue")
	public PageVO pageEmerRescue(@Valid PageDTO pageDTO, @RequestBody EventQueryDTO dto, BindingResult result, HttpServletRequest request) {
	    ValidUtils.error(result);
	    String role = (String)request.getAttribute("role");
	    if(role == null || role.length() < 1) {
	        LOGGER.info("没有角色权限");
	        throw new AuthzException("没有角色权限");
	    }
	    LOGGER.info("role:{}", role);
	    String userId = (String)request.getAttribute("userId");
	    dto.setUserId(userId);
	    dto.setRoleIds(Arrays.asList(role.split(";")));
	    return new PageVO(eventService.pageEmerRescue(pageDTO, dto));
	}

    /** 
    * @api {POST} /event/selectSeqNo 查询某个救援事件的排序号/event/selectSeqNo
    * @apiDescription 查询某个救援事件的排序号；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiBody {String} id=事件id 事件id
    * @apiBody {number} dealStatus=0 处理状态（0未完成，1接报，2待处置，3处置中，100已完成）
    * @apiSuccess (Success 200) {number} seqNo 排序号
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "seqNo": 16
    *     }
    * @apiSampleRequest /event/selectSeqNo
    */
	@Login
	@PostMapping("selectSeqNo")
	public Object selectSeqNo(@RequestBody EventQueryDTO dto, HttpServletRequest request) {
		String role = (String)request.getAttribute("role");
		if(role == null || role.length() < 1) {
			LOGGER.info("没有角色权限");
			throw new AuthzException("没有角色权限");
		}
		LOGGER.info("role:{}", role);
		String userId = (String)request.getAttribute("userId");
		dto.setUserId(userId);
		dto.setRoleIds(Arrays.asList(role.split(";")));
		return eventService.selectSeqNo(dto);
	}

	/**
	 * .app查询应急救援处置中事件列表
	 */
	@Login
	@PostMapping("selectAppEmerRescue")
	public List<EmerResuceVO> selectAppEmerRescue( @RequestBody EventQueryDTO dto, HttpServletRequest request) {
		String role = (String)request.getAttribute("role");
		if(role == null || role.length() < 1) {
			LOGGER.info("没有角色权限");
			throw new AuthzException("没有角色权限");
		}
		LOGGER.info("role:{}", role);
		String userId = (String)request.getAttribute("userId");
		dto.setUserId(userId);
		dto.setRoleIds(Arrays.asList(role.split(";")));
		return eventService.selectAppEmerRescue(dto);
	}


	/**
	 * .查询应急救援事件类型tree结构（包括一二三四级）
	 */
	@Login
	@PostMapping("treeEmerRescueType")
	public List<EventTypeVO> treeEmerRescueType() {
		return eventService.treeRescueType();
	}

	/**
	 * .查询所有事件类型tree结构（包括一二三四级）
	 */
	@Login
	@PostMapping("treeEventType")
	public List<EventTypeVO> treeEventType(@RequestBody EventTypeDTO dto) {
		return eventService.treeEventType(dto);
	}

	/**
	 * 查询事件业务类型tree结构
	 */
	@Login
	@PostMapping("treeBusinessType")
	public List<BusinessTypeVO> treeBusinessType(@RequestBody BusinessTypeDTO dto) {
		return eventService.treeBusinessType(dto);
	}

	/**
	 * 通过事件id，查询应急救援详情
	 * @param dto com.bt.itscore.domain.dto.IdStringDTO
	 * @return EventDetailVO
	 */
	@Login
	@PostMapping("selectEmerRescueByEventId")
	public EventDetailVO selectEmerRescueByEventId(@RequestBody IdStringDTO dto) {
		if(StringUtils.isBlank(dto.getId())) {
			throw new ArgumentException("请求body参数校验不通过:事件ID不能为空");
		}
		return eventService.selectEmerRescueByEventId(dto);
	}

	/**
	 * 通过事件id，查询是否新事件，待出单
	 * @EventQueryDTO dto
	 * @return EventOrderStatusVO
	 */
	@Login
	@PostMapping("selectEOrderStatusByEventId")
	public EventOrderStatusVO selectEOrderStatusByEventId(@RequestBody EventQueryDTO dto, HttpServletRequest request) {
		if(StringUtils.isBlank(dto.getId())) {
			throw new ArgumentException("请求body参数校验不通过:事件ID不能为空");
		}
		dto.setUserId((String)request.getAttribute("userId"));
		return eventService.selectEventOrder(dto);
	}

	/**
	 * 通过事件id查询事件详情，app调用，加返回能否排障出单字段
	 * @param dto
	 * @param request
	 * @return
	 */
	@Login
	@PostMapping("selectEventDetailById")
	public EventDetailVO selectEventDetailById(@RequestBody EventQueryDTO dto,HttpServletRequest request) {
		if(StringUtils.isBlank(dto.getId())) {
			throw new ArgumentException("请求body参数校验不通过:事件ID不能为空");
		}
		dto.setUserId((String)request.getAttribute("userId"));
		return eventService.selectEventDetailById(dto);
	}

	/**
	 * 通过事件id，查询事件相关的附件（事件附件和进展附件）
	 * @param dto com.bt.itscore.domain.dto.IdStringDTO
	 * @return List&lt;EventAttachVO>
	 */
	@Login
	@PostMapping("allAttach")
	public List<EventAttachVO> allAttach(@RequestBody IdStringDTO dto) {
		if(StringUtils.isBlank(dto.getId())) {
			throw new ArgumentException("请求body参数校验不通过:事件ID不能为空");
		}
		return eventService.allAttach(dto);
	}

	/**
	 * 事件相关的实时视频（根据路段编号、桩号数值[米]、距离范围[米]查询）
	 */
	@Login
	@CheckAuthz(hasPermissions = "sys:event:monitorVideo")
	@PostMapping("monitorVideo")
	public List<VideoVO> monitorVideo(@Valid @RequestBody VideoPostionDTO dto, BindingResult result) {
        ValidUtils.error(result);
        List<VideoVO> selectByPosition = itsVideoFeignClient.selectByPosition(dto);
		return selectByPosition;
	}

    /** 
    * @api {POST} /event/initialReport 救援事件新增第三步（事件初报）/event/initialReport
    * @apiDescription 救援事件新增第三步（事件初报），短信发送，强提醒，修改delStatus为0；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiBody {String} id=事件id 事件介绍
    * @apiBody {number} roadNo=171 路段编号
    * @apiBody {String} progressDesc=初报描述 初报描述
    * @apiBody {Object[]} atUsers=【{"userId":"用户id","userName":"用户名","mobile":"***********"}】 被@的用户列表
    * @apiBody {String} atUsers.userId=用户id 用户id
    * @apiBody {String} atUsers.userName=用户名 用户名
    * @apiBody {String} atUsers.mobile=*********** 用户手机号
    * @apiBody {Object[]} notifyUsers=【{"userId":"用户id","userName":"用户名","mobile":"***********"}】 强提醒用户列表
    * @apiBody {String} notifyUsers.userId=用户id 用户id
    * @apiBody {String} notifyUsers.userName=用户名 用户名
    * @apiBody {String} notifyUsers.mobile=*********** 用户手机号
    * @apiSuccess (Success 200) {number=0,1,401} code 返回状态码，1成功，0失败，401未登录或token失效
    * @apiSuccess (Success 200) {String} message 提示信息，SUCCESS：成功，FAIL：失败
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "code": 1
    *       "message": "SUCCESS"
    *     }
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK code=401用户未登录状态
    *     {
    *       "code: 401
    *     }
    * @apiSampleRequest /event/initialReport
    */
	@Login
	@PostMapping("initialReport")
	public ResponseVO initialReport(@Valid @RequestBody EventConfirmDTO dto, BindingResult result, HttpServletRequest request) {
        ValidUtils.error(result);
        dto.setCreateUserId((String)request.getAttribute("userId"));
		boolean add = eventService.initialReport(dto);
		if (add && !CollectionUtils.isEmpty(dto.getNotifyUsers())) {
			// 异步拉起机器人外呼通知结果状态更新
			try {
				List<String> jobIds = RobotCallTmpResultContext.obtainJobIds();
				robotCallHandlerService.handleRobotCallResponse(AssignJobs.INSTANCE_ID, jobIds, null, dto.getId());
			} finally {
				RobotCallTmpResultContext.remove();
			}
		}
		return new ResponseVO(add);
	}

	/**
	 * @description 提交初报信息审核
	 */
	@Login
	@PostMapping("submitInitialReportReview")
	public ResponseVO submitInitialReportReview(@RequestBody EventInfoReviewDTO dto, HttpServletRequest request) {
		dto.setCreateUserId((String)request.getAttribute("userId"));
		boolean add = eventService.submitInitialReportReview(dto);
		return new ResponseVO(add);
	}

	/**
	 * @description 审核事件信息（初报、续报、终报）
	 */
	@Login
	@PostMapping("reviewEventInfo")
	public ResponseVO reviewEventInfo(@RequestBody EventInfoReviewDTO dto, HttpServletRequest request) {
		if (StringUtils.isBlank(dto.getReviewId())) {
			throw new ArgumentException("审核记录ID不能为空");
		}
		if (dto.getReviewStatus() == null) {
			throw new ArgumentException("审核记录状态不能为空");
		}
		dto.setCreateUserId((String)request.getAttribute("userId"));
		boolean add = eventService.reviewEventInfo(dto);
		return new ResponseVO(add);
	}
	
	/**
	 * @description 提交事件续报审核
	 */
	@Login
	@PostMapping("submitResubmitInfoReview")
	public ResponseVO submitResubmitInfoReview(@Valid @RequestBody EventConfirmDTO dto, BindingResult result, HttpServletRequest request) {
		ValidUtils.error(result);
		dto.setCreateUserId((String)request.getAttribute("userId"));
		return new ResponseVO(eventService.submitInfoReview(dto, 5));
	}

	/**
	 * 事件续报-待审批状态
	 */
	@Login
	@PostMapping("resubmit")
	public ResponseVO resubmit(@Valid @RequestBody EventConfirmDTO dto, BindingResult result, HttpServletRequest request) {
		ValidUtils.error(result);
		dto.setCreateUserId((String)request.getAttribute("userId"));
		return new ResponseVO(eventService.resubmit(dto));
	}
	
	/**
	 * 事件终报-待审批状态
	 */
	@Login
	@PostMapping("submitFinalReportInfoReview")
	public ResponseVO submitFinalReportInfoReview(@Valid @RequestBody EventConfirmDTO dto, BindingResult result, HttpServletRequest request) {
		ValidUtils.error(result);
		dto.setCreateUserId((String)request.getAttribute("userId"));
		return new ResponseVO(eventService.submitInfoReview(dto, 10));
	}

	/**
	 * 事件终报-待审批状态
	 */
	@Login
	@PostMapping("finalReport")
	public ResponseVO finalReport(@Valid @RequestBody EventConfirmDTO dto, BindingResult result, HttpServletRequest request) {
		ValidUtils.error(result);
		dto.setCreateUserId((String)request.getAttribute("userId"));
		return new ResponseVO(eventService.finalReport(dto));
	}

    /** 
    * @api {POST} /event/updateEmerRescue 修改应急救援详情/event/updateEmerRescue
    * @apiDescription 修改应急救援详情(填报人不修改)；创建人：邓云钢，修改人：宁艺强
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiBody {String} id=事件id 事件id
    * @apiBody {number} source=0 事件来源，0-本地，1-96333，2-告警消息
    * @apiBody {String} briefDesc=事件介绍 事件介绍
    * @apiBody {number} roadNo=171 路段编号
    * @apiBody {String} directionNo=cb46cb40-b169-40f6-a3dd-153106fc842e 方向编号
    * @apiBody {String} [milePost=K50+100] 桩号
    * @apiBody {number} reportTime=1652431791 上报时间戳（10位数字）
    * @apiBody {number} [deathMan=0] 死亡人数
    * @apiBody {number} [missMan=0] 失踪人数
    * @apiBody {number} [injureMan=0] 受伤人数
    * @apiBody {String} [reportMan] 求助人
    * @apiBody {String} [reportManTel] 求助人电话
    * @apiBody {String} [carPlate] 求助人车牌号
    * @apiBody {number[]} [occupiedLanes=【1,2】] 占用车道
    * @apiBody {number[]} [attachs=【1,2】] 附件id列表
    * @apiBody {String} [weather] 天气（数据字典天气代码图标类型字典项值：CLEAR_DAY）
    * @apiBody {String} [facilityNo] 路况类型（设施）
    * @apiBody {String} [address] 所属区域
    * @apiBody {String} [accidentType] 事故类型值（数据字典事故类型值）
    * @apiBody {String} [otherTypeCause] 应急事故类型其他原因
    * @apiBody {number} [dangeFlag=0] 涉及危化品车 （0-未涉及,1-涉及；默认 0)
    * @apiBody {String} [accidentCause] 事故原因值（数据字典事故原因值）
    * @apiBody {String} [otherCause] 应急事故其他原因
    * @apiBody {String} [roadLossCause] 其他路损原因
    * @apiBody {String[]} [roadLoss] 路损内容列表
    * @apiBody {String} [reportSource] 接报来源（其他选项的文本）
    * @apiBody {number} [reportSourceKey] 接报来源key
    * @apiBody {String} [typeCode] 路损列表-类型编号
    * @apiBody {String} [value] 路损列表-数值
    * @apiSuccess (Success 200) {number=0,1,401} code 返回状态码，1成功，0失败，401未登录或token失效
    * @apiSuccess (Success 200) {String} message 提示信息，SUCCESS：成功，FAIL：失败
    * @apiSuccess (Success 200) {String} id 1，需要变更预案，0不需要
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "code": 1
    *       "message": "SUCCESS",
    *       "id": "1"
    *     }
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK code=401用户未登录状态
    *     {
    *       "code: 401
    *     }
    * @apiSampleRequest /event/updateEmerRescue
    */
	@Login
	@PostMapping("updateEmerRescue")
	public Object updateEmerRescue(@RequestBody EmerRescueDTO dto, HttpServletRequest request) {
		String userId = (String)request.getAttribute("userId");
        dto.setRecordManId(userId);
		return eventService.updateEmerRescue(dto);
	}

	/**
	 * 完结事件
	 */
	@Login
	@PostMapping("finishEmerRescue")
	public ResponseVO finishEmerRescue(@Valid @RequestBody EventFinishDTO dto, BindingResult result, HttpServletRequest request) {
		ValidUtils.error(result);
		dto.setFinishUserId((String)request.getAttribute("userId"));
		dto.setFinishAccount((String)request.getAttribute("account"));
		return new ResponseVO(eventService.finishEmerRescue(dto));
	}

    /** 
    * @api {POST} /event/emerUser 救援事件新增第二步（关联应急人员）/event/emerUser
    * @apiDescription 救援事件新增第二步（关联应急人员）；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiBody {String} id=事件id 事件id
    * @apiBody {String[]} emerRoles=【"应急工作组id"】 应急工作组选中的列表
    * @apiBody {Object[]} emerUsers=【{"emerRoleId":"应急工作组id","emerGroupId":"应急小组id","forceRemind":1,
    * "users":【{"userId":"用户id","userName":"用户名","mobile":"***********","post":1}】}】 应急人员
    * @apiBody {String} [emerUsers.emerRoleId=应急工作组id] 应急工作组id
    * @apiBody {String} [emerUsers.emerGroupId=应急小组id] 应急小组id
    * @apiBody {number} emerUsers.forceRemind=1 是否强提醒，1强提醒，0不提醒
    * @apiBody {Object[]} emerUsers.users=【{"userId":"用户id","userName":"用户名","mobile":"***********"}】 用户列表
    * @apiBody {String} emerUsers.users.userId=用户id 用户id
    * @apiBody {String} emerUsers.users.userName=用户名 用户名
    * @apiBody {String} emerUsers.users.mobile=*********** 用户手机号
    * @apiBody {number} [emerUsers.users.post] 1-组长，2-副组长
    * @apiSuccess (Success 200) {number=0,1,401} code 返回状态码，1成功，0失败，401未登录或token失效
    * @apiSuccess (Success 200) {String} message 提示信息，SUCCESS：成功，FAIL：失败
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "code": 1
    *       "message": "SUCCESS"
    *     }
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK code=401用户未登录状态
    *     {
    *       "code: 401
    *     }
    * @apiSampleRequest /event/emerUser
    */
	@Login
	@PostMapping("emerUser")
	public ResponseVO emerUser(@Valid @RequestBody EventEmerUserDTO dto, BindingResult result) {
        ValidUtils.error(result);
		return new ResponseVO(eventService.emerUser(dto) > 0);
	}

	/**
	 * 申请排障
	 */
	@Login
	@PostMapping("applyRemoveObstacles")
	public ResponseVO applyRemoveObstacles(@Valid @RequestBody RemoveObstaclesDTO dto, BindingResult result, HttpServletRequest request) {
        ValidUtils.error(result);
        dto.setCreateUserId((String)request.getAttribute("userId"));
		return new ResponseVO(eventService.applyRemoveObstacles(dto));
	}

	/**
	 * 根据事件id查询排障组
	 */
	@Login
	@PostMapping("selectRmObstaclesById")
	public RemoveObstaclesUserVO selectRmObstaclesById(@Valid @RequestBody IdStringDTO dto, BindingResult result) {
		ValidUtils.error(result);
		return eventService.selectRmObstaclesById(dto);
	}
	
	/**
	 * 根据事件id查询当前登录用户是否有权限操作
	 */
	@Login
	@PostMapping("operateAuthorityById")
	public ResponseVO operateAuthorityById(@Valid @RequestBody IdStringDTO dto, BindingResult result, HttpServletRequest request) {
		ValidUtils.error(result);
		return new ResponseVO(eventService.operateAuthorityById(dto, request.getAttribute("userId")) > 0);
	}

	/**
	 * 根据事件id查询当前登录用户是否有权限查看
	 */
	@Login
	@PostMapping("viewAuthorityById")
	public ResponseVO viewAuthorityById(@Valid @RequestBody EventAuthorityDTO dto, BindingResult result, HttpServletRequest request) {
        ValidUtils.error(result);
        String role = (String)request.getAttribute("role");
		if(role == null || role.length() < 1) {
			LOGGER.info("没有角色权限");
			throw new AuthzException("没有角色权限");
		}
		LOGGER.info("role:{}", role);
		String userId = (String)request.getAttribute("userId");
		dto.setUserId(userId);
		dto.setRoleIds(Arrays.asList(role.split(";")));
		return new ResponseVO(eventService.viewAuthorityById(dto) > 0);
	}

	@Login
	@RequestMapping("ossUpload")
	public void ossUpload(AttachDTO dto, HttpServletRequest request, HttpServletResponse response) {
		Map<String, String> map = eventService.ossUpload(dto);
		try {
			OssUtils.response(request, response, new Gson().toJson(map));
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	@RequestMapping("ossCallback")
	public void ossCallback(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		OssUtils.response(request, response, new Gson().toJson(eventService.ossCallback(request)));
	}

	@Login
	@RequestMapping("ossDelete")
	public ResponseVO ossDelete(@RequestBody AttachDTO dto) {
		boolean ret = eventService.ossDelete(dto);
		return new ResponseVO(ret);
	}

	@Login
	@RequestMapping("ossUrl")
	public List<AttachDTO> ossUrl(@RequestBody List<AttachDTO> dtos) {
		return eventService.ossUrl(dtos);
	}

    /** 
    * @api {POST} /event/selectEmerUserById 查询事件应急人员/event/selectEmerUserById
    * @apiDescription 根据事件id查询事件应急人员；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiBody {String} id=事件id 事件id
    * @apiSuccess (Success 200) {String} emerRoleId 应急工作组id
    * @apiSuccess (Success 200) {String} emerRoleName 应急工作组名称
    * @apiSuccess (Success 200) {Object[]} emerGroups 应急小组列表
    * @apiSuccess (Success 200) {String} emerGroups.emerGroupId 应急小组id
    * @apiSuccess (Success 200) {String} emerGroups.emerGroupName 应急小组名称
    * @apiSuccess (Success 200) {number} emerGroups.forceRemind 是否强提醒，1强提醒，0不提醒
    * @apiSuccess (Success 200) {Object[]} emerGroups.emerUsers 用户列表
    * @apiSuccess (Success 200) {String} emerGroups.emerUsers.userId 用户id
    * @apiSuccess (Success 200) {String} emerGroups.emerUsers.userName 用户名
    * @apiSuccess (Success 200) {String} emerGroups.emerUsers.mobile 用户手机号
    * @apiSuccess (Success 200) {number} emerGroups.emerUsers.post 1-组长，2-副组长
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "emerRoleId": "应急工作组id",
    *       "emerRoleName": "应急工作组名称",
    *       "emerGroups": [
    *         "emerGroupId": "应急小组组id",
    *         "emerRoleName": "应急小组组名称",
    *         "forceRemind": 1,
    *         "emerUsers": [
    *           {"userId":"用户id","userName":"用户名","mobile":"***********","post": 1},
    *           {"userId":"用户id","userName":"用户名","mobile":"18000000002","post": null}
    *         ]
    *       ]
    *     }
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK code=401用户未登录状态
    *     {
    *       "code: 401
    *     }
    * @apiSampleRequest /event/selectEmerUserById
    */
	@Login
	@PostMapping("selectEmerUserById")
	public List<GroupEventEmerUserVO> selectEmerUserById(@Valid @RequestBody IdStringDTO dto, BindingResult result) {
		ValidUtils.error(result);
		return eventService.selectGroupEmerUserById(dto);
	}

	/**
	 * 变更事件的应急预案
	 */
	@Deprecated
	@Login
	@PostMapping("changeEmerPlan")
	public ResponseVO changeEmerPlan(@Valid @RequestBody EventEmerUserDTO dto, BindingResult result, HttpServletRequest request) {
        ValidUtils.error(result);
		return new ResponseVO(eventService.changeEmerUser(dto, (String)request.getAttribute("userId")));
	}

	/** 
    * @api {POST} /event/changeEmerUser 变更事件关联的应急人员/event/changeEmerUser
    * @apiDescription 变更事件关联的应急人员，变更的人员在进展中留痕，新增的用户会发送应用消息和短信；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiBody {String} id=事件id 事件id
    * @apiBody {String[]} emerRoles=【"应急工作组id"】 应急工作组选中的列表
    * @apiBody {Object[]} emerUsers=【{"emerRoleId":"应急工作组id","emerGroupId":"应急小组id","forceRemind":1,
    * "users":【{"userId":"用户id","userName":"用户名","mobile":"***********","post":1}】}】 应急人员
    * @apiBody {String} [emerUsers.emerRoleId=应急工作组id] 应急工作组id
    * @apiBody {String} [emerUsers.emerGroupId=应急小组id] 应急小组id
    * @apiBody {number} emerUsers.forceRemind=1 是否强提醒，1强提醒，0不提醒
    * @apiBody {Object[]} emerUsers.users=【{"userId":"用户id","userName":"用户名","mobile":"***********"}】 用户列表
    * @apiBody {String} emerUsers.users.userId=用户id 用户id
    * @apiBody {String} emerUsers.users.userName=用户名 用户名
    * @apiBody {String} emerUsers.users.mobile=*********** 用户手机号
    * @apiBody {number} [emerUsers.users.post] 1-组长，2-副组长
    * @apiSuccess (Success 200) {number=0,1,401} code 返回状态码，1成功，0失败，401未登录或token失效
    * @apiSuccess (Success 200) {String} message 提示信息，SUCCESS：成功，FAIL：失败
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "code": 1
    *       "message": "SUCCESS"
    *     }
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK code=401用户未登录状态
    *     {
    *       "code: 401
    *     }
    * @apiSampleRequest /event/changeEmerUser
    */
    @Login
    @PostMapping("changeEmerUser")
    public ResponseVO changeEmerUser(@Valid @RequestBody EventEmerUserDTO dto, BindingResult result, HttpServletRequest request) {
        ValidUtils.error(result);
		int isSuccess = eventService.changeEmerUser(dto, (String) request.getAttribute("userId"));
		if (isSuccess > 0) {
			try {
				List<String> jobIds = RobotCallTmpResultContext.obtainJobIds();
				robotCallHandlerService.handleRobotCallResponse(AssignJobs.INSTANCE_ID, jobIds, null, dto.getId());
			} finally {
				RobotCallTmpResultContext.remove();
			}
		}
		return new ResponseVO(isSuccess);
    }

    /**
     * @description 邀请人员参与事件处置 （只新增人员）
     */
    @Login
    @PostMapping("addEmerUser")
    public ResponseVO addEmerUser(@Valid @RequestBody EventEmerUserDTO dto, BindingResult result, HttpServletRequest request) {
    	ValidUtils.error(result);
    	int isSuccess = eventService.addEmerUserAndSms(dto, (String) request.getAttribute("userId"));
    	return new ResponseVO(isSuccess);
    }

	/**
	 * 统计个人相关的事件信息
	 */
	@Login
	@PostMapping("countPersonal")
	public EventCountVO countPersonal(@RequestBody EventCountDTO dto,  HttpServletRequest request) {
		String userId = (String)request.getAttribute("userId");
		dto.setUserId(userId);
		return eventService.countEvent(dto);
	}

	/**
	 * 统计个人所在运营公司相关的事件信息（全部，交通事故，车辆救援，信息咨询，其他）
	 */
	@Login
	@PostMapping("countEventByOrg")
	public EventCountByOrgVO countEventByOrg(@RequestBody EventCountDTO dto) {
		if(StringUtils.isBlank(dto.getOrgId())){
			throw new ArgumentException("请求body参数校验不通过:所属运营公司id不能为空");
		}
		return eventService.countEventByOrg(dto);
	}

	/**
	 * 统计个人所在运营公司事件类型信息
	 */
	@Login
	@PostMapping("countEventTypeByOrg")
	public Map<String,Object> countEventTypeByOrg(@RequestBody EventCountDTO dto) {
		if(StringUtils.isBlank(dto.getOrgId())){
			throw new ArgumentException("请求body参数校验不通过:所属运营公司id不能为空");
		}
		return eventService.countEventTypeByOrg(dto);
	}

	/**
	 * 统计个人所在运营公司各路段事件统计
	 */
	@Login
	@PostMapping("countEventRoadByOrg")
	public Map<String,Object> countEventRoadByOrg(@RequestBody EventCountDTO dto,HttpServletRequest request) {
		if(StringUtils.isBlank(dto.getOrgId())){
			throw new ArgumentException("请求body参数校验不通过:所属运营公司id不能为空");
		}
		String role = (String)request.getAttribute("role");
		dto.setOrgIds(feignClient.getSubOrg(dto.getOrgId()));
        List<RoadVO> vos = feignClient.selectOwnByRole(role);
		if(vos == null || vos.size() == 0){
			throw new ArgumentException("没有权限路段数据。");
		}
		dto.setRoadVOS(vos);
		return eventService.countEventRoadByOrg(dto);
	}
	/**
	 * 添加96333救援事件
	 */
	@Deprecated
	@Login
	@PostMapping("add96333Rescue")
	public Object add96333Rescue(@RequestBody Event96333DTO dto) {
		return new ResponseVO(eventService.add96333Rescue(dto));
	}

	/**
	 * @描述 对接千方96333系统，新工单
	 */
	@Login
	@PostMapping("addEvent96333Order")
	public ResponseVO addEvent96333Order(@RequestBody Event96333NewOrderDTO dto) {
		return new ResponseVO(eventService.addEvent96333Order(dto));
	}

	/**
	 * 更新96333救援事件
	 */
	@Login
	@PostMapping("update96333Rescue")
	public Object update96333Rescue(@Valid @RequestBody EmerRescueDTO dto, BindingResult result, HttpServletRequest request) {
		String userId = (String)request.getAttribute("userId");
        dto.setRecordManId(userId);
		return eventService.updateEmerRescue(dto);
	}

	/**
	 * .查询应急救援事件类型tree结构（包括一二三四级）
	 */
	@Login
	@PostMapping("tsJyType")
	public List<EventTypeVO> tsJyType() {
		return eventService.tsJyType();
	}

	/**
	 * .分页查询投诉举报意见建议
	 */
	@Login
	@PostMapping("pageTsJy")
	public Object pageTsJy(@Valid PageDTO pageDTO, @RequestBody EventQueryDTO dto, BindingResult result, HttpServletRequest request) {
		ValidUtils.error(result);
		String role = (String)request.getAttribute("role");
		if(role == null || role.length() < 1) {
			LOGGER.info("没有角色权限");
			throw new AuthzException("没有角色权限");
		}
		LOGGER.info("role:{}", role);
		String userId = (String)request.getAttribute("userId");
		dto.setUserId(userId);
		dto.setRoleIds(Arrays.asList(role.split(";")));
		return new PageVO(eventService.pageTsJy(pageDTO, dto));
	}

	/**
	 * .通过事件id，查询投诉举报和意见建议详情
	 * @param dto com.bt.itscore.domain.dto.IdStringDTO
	 * @return TsJyDetailVO
	 */
	@Login
	@PostMapping("selectTsJyByEventId")
	public TsJyDetailVO selectTsJyByEventId(@RequestBody IdStringDTO dto) {
		if(StringUtils.isBlank(dto.getId())) {
			throw new ArgumentException("请求body参数校验不通过:事件ID不能为空");
		}
		return eventService.selectTsJyByEventId(dto);
	}

	/**
	 * .处置投诉举报和意见建议的第一步保存
	 * @param dto com.bt.itsevent.domain.dto.EventTsDTO
	 * @return TsJyDetailVO
	 */
	@Login
	@PostMapping("dealTsStepOne")
	public ResponseVO dealTsStepOne(@RequestBody EventTsDTO dto, HttpServletRequest request) {
		if(StringUtils.isBlank(dto.getId())) {
			throw new ArgumentException("请求body参数校验不通过:事件ID不能为空");
		}
		if(StringUtils.isBlank(dto.getUser2Id())) {
			throw new ArgumentException("请求body参数校验不通过:请选择【移交责任人】");
		}
		String userId = (String)request.getAttribute("userId");
		dto.setUser1Id(userId);
		String role = (String)request.getAttribute("role");
		dto.setRoleIds(Arrays.asList(role.split(";")));
		return eventService.dealTsStepOne(dto);
	}

	/**
	 * .处置投诉举报和意见建议的第二步保存
	 * @param dto com.bt.itsevent.domain.dto.EventTsDTO
	 * @return TsJyDetailVO
	 */
	@Login
	@PostMapping("dealTsStepTwo")
	public ResponseVO dealTsStepTwo(@RequestBody EventTsDTO dto, HttpServletRequest request) {
		if(StringUtils.isBlank(dto.getId())) {
			throw new ArgumentException("请求body参数校验不通过:事件ID不能为空");
		}
		if(StringUtils.isBlank(dto.getEtId())) {
			throw new ArgumentException("请求body参数校验不通过:投诉意见处置ID不能为空");
		}
		if(StringUtils.isBlank(dto.getBuildResult())) {
			throw new ArgumentException("请求body参数校验不通过:信息调查情况不能为空");
		}
		String userId = (String)request.getAttribute("userId");
		dto.setUser3Id(userId);
		String role = (String)request.getAttribute("role");
		dto.setRoleIds(Arrays.asList(role.split(";")));
		return eventService.dealTsStepTwo(dto);
	}

	/**
	 * .处置投诉举报和意见建议的第三步保存
	 * @param dto com.bt.itsevent.domain.dto.EventTsDTO
	 * @return TsJyDetailVO
	 */
	@Login
	@PostMapping("dealTsStepThree")
	public ResponseVO dealTsStepThree(@RequestBody EventTsDTO dto, HttpServletRequest request) {
		if(StringUtils.isBlank(dto.getId())) {
			throw new ArgumentException("请求body参数校验不通过:事件ID不能为空");
		}
		if(StringUtils.isBlank(dto.getEtId())) {
			throw new ArgumentException("请求body参数校验不通过:投诉意见处置ID不能为空");
		}
//		if(StringUtils.isBlank(dto.getKfFinishAdvice())) {
//			throw new ArgumentException("请求body参数校验不通过:客服中心办结意见不能为空");
//		}
		String userId = (String)request.getAttribute("userId");
		dto.setUser4Id(userId);
		String role = (String)request.getAttribute("role");
		dto.setRoleIds(Arrays.asList(role.split(";")));
		return eventService.dealTsStepThree(dto);
	}

	/**
	 * .完成投诉举报和意见建议（提交到96333）
	 * @param dto com.bt.itsevent.domain.dto.EventTsDTO
	 * @return TsJyDetailVO
	 */
	@Login
	@PostMapping("finishTsJy")
	public ResponseVO finishTsJy(@RequestBody EventTsDTO dto, HttpServletRequest request) {
		if(StringUtils.isBlank(dto.getId())) {
			throw new ArgumentException("请求body参数校验不通过:事件ID不能为空");
		}
//		if(StringUtils.isBlank(dto.getDealResult())) {
//			throw new ArgumentException("请求body参数校验不通过:处理结果不能超过1000字！");
//		}
		if(StringUtils.isBlank(dto.getBuildResult())) {
			throw new ArgumentException("请求body参数校验不通过:信息调查情况（处理结果）不能为空！");
		}
		dto.setDealResult(StringUtils.trim(dto.getBuildResult()) + StringUtils.trim(dto.getBuildDealAdvice()));
		if(StringUtils.length(dto.getDealResult()) > 1000) {
			throw new ArgumentException("请求body参数校验不通过:提交到96333的处理结果不能超过1000字");
		}
		String userId = (String)request.getAttribute("userId");
		dto.setUser4Id(userId);
		dto.setFinishAccount((String)request.getAttribute("account"));
		return eventService.finishTsJy(dto);
	}

    /** 
    * @api {POST} /event/updateTsJyRevisit 信息归档后的修改投诉意见回访/event/updateTsJyRevisit
    * @apiDescription 信息归档后的修改投诉意见回访；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiBody {String} id 事件id
    * @apiBody {number} revisit 用户回访（1已回访,0稍后回访）
    * @apiBody {number} satisfaction 满意度（5满意,10一般,15不满意）
    * @apiSuccess (Success 200) {number=0,1,401} code 返回状态码，1成功，0失败，401未登录或token失效
    * @apiSuccess (Success 200) {String} message 提示信息，SUCCESS：成功，FAIL：失败
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "code": 1,
    *       "message": "SUCCESS"
    *     }
    * @apiSampleRequest /event/updateTsJyRevisit
    */
	@Login
	@PostMapping("updateTsJyRevisit")
	public ResponseVO updateTsJyRevisit(@RequestBody EventTsDTO dto) {
		if(StringUtils.isBlank(dto.getId())) {
			throw new ArgumentException("请求body参数校验不通过:事件ID不能为空");
		}
		if(dto.getRevisit() == null) {
			throw new ArgumentException("请求body参数校验不通过:请选择回访状态");
		}
		return eventService.updateTsJyRevisit(dto);
	}

    /** 
    * @api {POST} /event/addDraft APP新增事件草稿/event/addDraft
    * @apiDescription APP新增事件草稿；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiBody {number} source=0 事件来源，0-本地，1-96333，2-告警消息
    * @apiBody {String} briefDesc=事件介绍 事件介绍
    * @apiBody {number} roadNo=171 路段编号
    * @apiBody {String} directionNo=cb46cb40-b169-40f6-a3dd-153106fc842e 方向编号
    * @apiBody {String} [milePost=K50+100] 桩号（事件地点）
    * @apiBody {number} reportTime=1652431791 上报时间戳（10位数字）
    * @apiBody {number} [deathMan=0] 死亡人数,无-0，不详-1，有-2'
    * @apiBody {number} [missMan=0] 失踪人数,无-0，不详-1，有-2'
    * @apiBody {number} [injureMan=0] 受伤人数,无-0，不详-1，有-2'
    * @apiBody {number} [incomeLose=0] 路产损失
    * @apiBody {String} [reportMan] 求助人
    * @apiBody {String} [reportManTel] 求助人电话
    * @apiBody {String} [carPlate] 求助人车牌号
    * @apiBody {number[]} [occupiedLanes=【1,2】] 占用车道
    * @apiBody {number[]} [attachs=【1,2】] 附件id列表
    * @apiBody {String} [reportSource] 接报来源（其他选项的文本）
    * @apiBody {number} [reportSourceKey] 接报来源key
    * @apiSuccess (Success 200) {number=0,1,401} code 返回状态码，1成功，0失败，401未登录或token失效
    * @apiSuccess (Success 200) {String} message 提示信息，SUCCESS：成功，FAIL：失败
    * @apiSuccess (Success 200) {String} id 事件草稿id
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "code": 1
    *       "message": "SUCCESS",
    *       "id": "8fe0076e-7e6d-4ebd-8f71-7dcef1bc8375"
    *     }
    * @apiSampleRequest /event/addDraft
    */
	@Login
	@PostMapping("addDraft")
	public ResponseVO addDraft(@RequestBody EventDraftDTO dto, HttpServletRequest request) {
		String userId = (String)request.getAttribute("userId");
		dto.setUserId(userId);
		return new ResponseVO(eventDraftService.add(dto));
	}

	/**
	 * 查询个人草稿事件
	 * @param
	 * @param request
	 * @return
	 */
	@Login
	@PostMapping("selectDraft")
	public Object selectDraft(HttpServletRequest request) {
		String userId = (String)request.getAttribute("userId");
		return eventDraftService.selectByUserId(userId);
	}

	/**
	 * 清除个人草稿事件
	 * @param
	 * @param request
	 * @return
	 */
	@Login
	@PostMapping("clearDraft")
	public Object clearDraft(HttpServletRequest request) {
		String userId = (String)request.getAttribute("userId");
		return new ResponseVO(eventDraftService.delete(userId));
	}

	/**
	 * @描述 今日应急救援事件的平均处置时长，（结束事件finish_time-分发时间distribute_time）
	 */
	@Login
	@PostMapping("todayAvgDealTime")
	public Object todayAvgDealTime() {
		return eventService.todayAvgDealTime();
	}

	/**
	 * @描述 各运营管理中心的今日救援、事故、抛洒物和行人事件数量
	 * 该接口已弃用，请使用（/its-event/event/selectEventHeat/selectEventHeat)
	 */
	@Login
	@PostMapping("countEventTodayType")
	@Deprecated
	public Object countEventTodayType() {
		return eventService.countEventTodayType();
	}

	/**
	 *  统计沿海各分中心今日待处置、事件总数
	 * @return
	 */
	@Login
	@PostMapping("countYanHaiEvent")
	public Object countYanHaiEvent() {
		return eventService.countYanHaiEvent();
	}


	/**
	 *  各运营中心事件分布类型top5
	 * @return
	 */
	@Login
	@PostMapping("eventTypeTop5VO")
	public Object eventTypeTop5VO() {
		List<EventTypeTop5MapVO> vo = eventService.countTypeTop5();
		return vo;
	}

	/**
	 *  事件分布类型top5 事件详情
	 * @return
	 */
	@Login
	@PostMapping("selectEventHeat")
	public Object selectEventHeat() {
		return eventService.selectEventHeat();
	}

	/**
	*  统计今日事件总数+上月同比
	 *  上月同比计算方式（（当天-上个月当天）/上个月当天*100%）
	* @return
	*/
	@Login
	@PostMapping("countEventTotal")
	public Map<String,Object> countEventTotal() {
		Map<String,Object> todayMap = new HashMap<>();
        todayMap.put("compareDay", TimeUtils.formatDate(new Date(),TimeUtils.DATE));
		Map<String,Object> lastMothDayMap = new HashMap<>();
		lastMothDayMap.put("compareDay", TimeUtils.getLastMoth());
		return eventService.countEventTotal(todayMap,lastMothDayMap);
	}

	/**
	 *  查看今日投诉总数+上月同比
	 *  上月同比计算方式（（当天-上个月当天）/上个月当天*100%）
	 * @return
	 */
	@Login
	@PostMapping("countComplaintTotal")
	public Map<String,Object> countComplaintTotal() {
		Map<String,Object> todayMap = new HashMap<>();
		todayMap.put("compareDay", TimeUtils.formatDate(new Date(),TimeUtils.DATE));
		todayMap.put("complaintType", 1); //投诉事件传参
		Map<String,Object> lastMothDayMap = new HashMap<>();
		lastMothDayMap.put("compareDay", TimeUtils.getLastMoth());
		lastMothDayMap.put("complaintType", 1); //投诉事件传参
		return eventService.countEventTotal(todayMap,lastMothDayMap);
	}

    /** 
    * @api {POST} /event/nearestFacilityById 查询事件最近的设施（收费站或服务区）/event/nearestFacilityById
    * @apiDescription 查询事件最近的设施（收费站或服务区）；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiBody {String} id=事件id 事件id
    * @apiSuccess (Success 200) {String} tip 提示信息，距离XXX收费站或服务区X公里
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "tip": "距离XXX收费站或服务区X公里"
    *     }
    * @apiSampleRequest /event/nearestFacilityById
    */
	@Login
    @PostMapping("nearestFacilityById")
	public Object nearestFacilityById(@Valid @RequestBody IdStringDTO dto, BindingResult result) {
	    ValidUtils.error(result);
	    return eventService.nearestFacilityById(dto);
	}

	/** 
    * @api {POST} /event/testCreate96333 模拟96333事件创建/event/testCreate96333
    * @apiDescription 模拟96333事件创建（5个运营中心）；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiSuccess (Success 200) {number} code 1成功，0失败
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "code": 1
    *     }
    * @apiSampleRequest /event/testCreate96333
    */
    @Login
    @CheckAuthz(hasPermissions = "sys:event:testCreate96333")
    @PostMapping("testCreate96333")
    public Object testCreate96333(Event96333DTO dto) {
        long time = System.currentTimeMillis()/1000;
        for (int i = 1; i <= 5; i++) {
            dto.setId(UUID.randomUUID().toString());
            dto.setSource(1);
            dto.setSourceId(i);
            dto.setEventStatus(1);
            dto.setDealStatus(2);
            dto.setBusiId(time + i + "");
            dto.setEventNo("DD" + TimeUtils.getTimeString("yyyyMMddHHmmss", (time + i) * 1000));// 工单号
            dto.setReportMan("张先生");//报警人
            dto.setReportManTel("15994369149");//报警人电话
            dto.setOccurTime(time); // 发生时间
            dto.setReportTime(time); // 上报时间
            dto.setCreateTime(time);
            dto.setBriefDesc("【测试模拟96333】车辆发生碰撞");//事件描述
            dto.setCarType(1);
            dto.setCarPlate("桂A179AS");
            dto.setGgjDealType("转分中心");
            dto.setEventType(1);
            dto.setEventTwoType(6);
            dto.setEventThreeType(11);
            if(i == 1) {
                dto.setOrgId("d9b2fe65-eb5d-4d53-952a-c490538c0e00");
                dto.setDealOrgId("d9b2fe65-eb5d-4d53-952a-c490538c0e00");
                dto.setDealOrgName("沿海高速公路分公司本部");
                dto.setEventAddress("钦州收费站1公里");
                dto.setGgjDealDept("沿海高速分中心");
                dto.setRoadNo(null);
                dto.setDirectionNo(null);
                dto.setMilePost(null);
                dto.setEventAddress(null);
            } else if(i == 2) {
                dto.setOrgId("fe46e99b-5d48-4140-a38b-9b4463f50c3c");
                dto.setDealOrgId("fe46e99b-5d48-4140-a38b-9b4463f50c3c");
                dto.setDealOrgName("罗城高速公路运营管理中心");
                dto.setGgjDealDept("罗城高速公路运营管理中心");
                dto.setRoadNo(131);
                dto.setDirectionNo("03dad27e-4bb7-4551-a43c-50a4cb12b47a");
                dto.setMilePost("K128+000");
                dto.setEventAddress("罗城收费站1公里");
            } else if(i == 3) {
                dto.setOrgId("d9bffff7-4f6c-4768-94e8-2491ed592453");
                dto.setDealOrgId("d9bffff7-4f6c-4768-94e8-2491ed592453");
                dto.setDealOrgName("大化高速公路运营管理中心");
                dto.setGgjDealDept("大化高速公路运营管理中心");
                dto.setRoadNo(162);
                dto.setDirectionNo("4b3dc5ae-4b10-45b9-87f4-b1254665f068");
                dto.setMilePost("K498+000");
                dto.setEventAddress(null);
            } else if(i == 4) {
                dto.setOrgId("f761c9f0-26aa-455c-b9eb-aac201d1e3a3");
                dto.setDealOrgId("f761c9f0-26aa-455c-b9eb-aac201d1e3a3");
                dto.setDealOrgName("昭平运营管理中心");
                dto.setGgjDealDept("昭平运营管理中心");
                dto.setRoadNo(null);
                dto.setDirectionNo(null);
                dto.setMilePost(null);
                dto.setEventAddress(null);
            } else if(i == 5) {
                dto.setOrgId("706019ac-9e22-4837-8950-3768e3793c47");
                dto.setDealOrgId("706019ac-9e22-4837-8950-3768e3793c47");
                dto.setDealOrgName("灵山运营管理中心");
                dto.setGgjDealDept("灵山运营管理中心");
                dto.setRoadNo(null);
                dto.setDirectionNo(null);
                dto.setMilePost(null);
                dto.setEventAddress(null);
            }
            eventService.add96333Rescue(dto);
            try {
				Thread.sleep(50);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
        }
        for (int i = 1; i <= 5; i++) {
            dto.setId(UUID.randomUUID().toString());
            dto.setSource(1);
            dto.setSourceId(i);
            dto.setEventStatus(1);
            dto.setDealStatus(2);
            time += 5;
            dto.setBusiId(time + i + "");
            dto.setEventNo("TS" + TimeUtils.getTimeString("yyyyMMddHHmmss", (time + i) * 1000));// 工单号
            dto.setReportMan("张先生");//报警人
            dto.setReportManTel("15994369149");//报警人电话
            dto.setOccurTime(time); // 发生时间
            dto.setReportTime(time); // 上报时间
            dto.setCreateTime(time);
            dto.setBriefDesc("【测试模拟96333】投诉事件");//事件描述
            dto.setCarType(1);
            dto.setCarPlate("桂A179AS");
            dto.setGgjDealType("转分中心");
            dto.setEventType(2);
            dto.setEventTwoType(13);
            dto.setEventThreeType(15);
            dto.setComplaintType(1);
            dto.setComplaintTarget("投诉对象");
            if(i == 1) {
                dto.setOrgId("d9b2fe65-eb5d-4d53-952a-c490538c0e00");
                dto.setDealOrgId("d9b2fe65-eb5d-4d53-952a-c490538c0e00");
                dto.setDealOrgName("沿海高速公路分公司本部");
                dto.setGgjDealDept("沿海高速分中心");
                dto.setEventAddress("钦州收费站1公里");
            } else if(i == 2) {
                dto.setOrgId("fe46e99b-5d48-4140-a38b-9b4463f50c3c");
                dto.setDealOrgId("fe46e99b-5d48-4140-a38b-9b4463f50c3c");
                dto.setDealOrgName("罗城高速公路运营管理中心");
                dto.setGgjDealDept("罗城高速公路运营管理中心");
                dto.setRoadNo(131);
                dto.setDirectionNo("03dad27e-4bb7-4551-a43c-50a4cb12b47a");
                dto.setMilePost("K128+000");
                dto.setEventAddress("罗城收费站1公里");
            } else if(i == 3) {
                dto.setOrgId("d9bffff7-4f6c-4768-94e8-2491ed592453");
                dto.setDealOrgId("d9bffff7-4f6c-4768-94e8-2491ed592453");
                dto.setDealOrgName("大化高速公路运营管理中心");
                dto.setGgjDealDept("大化高速公路运营管理中心");
            } else if(i == 4) {
                dto.setOrgId("f761c9f0-26aa-455c-b9eb-aac201d1e3a3");
                dto.setDealOrgId("f761c9f0-26aa-455c-b9eb-aac201d1e3a3");
                dto.setDealOrgName("昭平运营管理中心");
                dto.setGgjDealDept("昭平运营管理中心");
            } else if(i == 5) {
                dto.setOrgId("706019ac-9e22-4837-8950-3768e3793c47");
                dto.setDealOrgId("706019ac-9e22-4837-8950-3768e3793c47");
                dto.setDealOrgName("灵山运营管理中心");
                dto.setGgjDealDept("灵山运营管理中心");
            }
            eventService.add96333Rescue(dto);
            try {
				Thread.sleep(50);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
        }
        for (int i = 1; i <= 5; i++) {
            dto.setId(UUID.randomUUID().toString());
            dto.setSource(1);
            dto.setSourceId(i);
            dto.setEventStatus(1);
            dto.setDealStatus(2);
            time += 5;
            dto.setBusiId(time + i + "");
            dto.setEventNo("JY" + TimeUtils.getTimeString("yyyyMMddHHmmss", (time + i) * 1000));// 工单号
            dto.setReportMan("张先生");//报警人
            dto.setReportManTel("15994369149");//报警人电话
            dto.setOccurTime(time); // 发生时间
            dto.setReportTime(time); // 上报时间
            dto.setCreateTime(time);
            dto.setBriefDesc("【测试模拟96333】建议事件");//事件描述
            dto.setCarType(1);
            dto.setCarPlate("桂A179AS");
            dto.setGgjDealType("转分中心");
            dto.setEventType(3);
            dto.setEventTwoType(21);
            dto.setEventThreeType(22);
            dto.setComplaintType(1);
            dto.setComplaintTarget("建议对象");
            if(i == 1) {
                dto.setOrgId("d9b2fe65-eb5d-4d53-952a-c490538c0e00");
                dto.setDealOrgId("d9b2fe65-eb5d-4d53-952a-c490538c0e00");
                dto.setDealOrgName("沿海高速公路分公司本部");
                dto.setGgjDealDept("沿海高速分中心");
                dto.setEventAddress("钦州收费站1公里");
            } else if(i == 2) {
                dto.setOrgId("fe46e99b-5d48-4140-a38b-9b4463f50c3c");
                dto.setDealOrgId("fe46e99b-5d48-4140-a38b-9b4463f50c3c");
                dto.setDealOrgName("罗城高速公路运营管理中心");
                dto.setGgjDealDept("罗城高速公路运营管理中心");
                dto.setRoadNo(131);
                dto.setDirectionNo("03dad27e-4bb7-4551-a43c-50a4cb12b47a");
                dto.setMilePost("K128+000");
                dto.setEventAddress("罗城收费站1公里");
            } else if(i == 3) {
                dto.setOrgId("d9bffff7-4f6c-4768-94e8-2491ed592453");
                dto.setDealOrgId("d9bffff7-4f6c-4768-94e8-2491ed592453");
                dto.setDealOrgName("大化高速公路运营管理中心");
                dto.setGgjDealDept("大化高速公路运营管理中心");
            } else if(i == 4) {
                dto.setOrgId("f761c9f0-26aa-455c-b9eb-aac201d1e3a3");
                dto.setDealOrgId("f761c9f0-26aa-455c-b9eb-aac201d1e3a3");
                dto.setDealOrgName("昭平运营管理中心");
                dto.setGgjDealDept("昭平运营管理中心");
            } else if(i == 5) {
                dto.setOrgId("706019ac-9e22-4837-8950-3768e3793c47");
                dto.setDealOrgId("706019ac-9e22-4837-8950-3768e3793c47");
                dto.setDealOrgName("灵山运营管理中心");
                dto.setGgjDealDept("灵山运营管理中心");
            }
            eventService.add96333Rescue(dto);
            try {
				Thread.sleep(50);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
        }
        return new ResponseVO(1);
    }

    // 信息咨询
    /**
    * @api {POST} /event/addZx 新增本地信息咨询事件/event/addZx
    * @apiDescription 新增本地信息咨询事件；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiBody {String} briefDesc 咨询内容
    * @apiBody {number} reportTime 接报时间
    * @apiBody {number} source=0 信息来源，0本地，1:96333,2告警信息
    * @apiBody {number} reportSourceKey=0 接报来源，0本地，1:96333
    * @apiBody {String} org1Id 责任单位
    * @apiBody {String} user1Id 责任人id
    * @apiBody {String} reportMan 咨询人
    * @apiBody {String} reportManTel 咨询人电话
    * @apiSuccess (Success 200) {number} code 1成功，0失败
    * @apiSuccess (Success 200) {String} id 咨询事件id
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "code": 1,
    *       "id": "95dc93a3-f341-4c0c-9087-25a0820f13e5"
    *     }
    * @apiSampleRequest /event/addZx
    */
    @Login
    @PostMapping("addZx")
    public ResponseVO addZx(@RequestBody EventZxDTO eventZxDTO,  BindingResult result, HttpServletRequest request) {
    	ValidUtils.error(result);
    	String userId = (String)request.getAttribute("userId");
    	eventZxDTO.setId(UUID.randomUUID().toString());
    	eventZxDTO.setRecordManId(userId);
    	eventZxDTO.setCreateUserId(userId);
    	eventZxDTO.setSource(0);
        return new ResponseVO(eventService.addZx(eventZxDTO), eventZxDTO.getId());
    }

    /**
    * @api {POST} /event/updateZx 修改信息咨询事件/event/updateZx
    * @apiDescription 修改信息咨询事件；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiBody {String} briefDesc 咨询内容
    * @apiBody {number} reportTime 接报时间
    * @apiBody {String} org1Id 责任单位
    * @apiBody {String} user1Id 责任人id
    * @apiBody {String} reportMan 咨询人
    * @apiBody {String} reportManTel 咨询人电话
    * @apiSuccess (Success 200) {number} code 1成功，0失败
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "code": 1
    *     }
    * @apiSampleRequest /event/updateZx
    */
    @Login
	@CheckAuthz(hasPermissions = "sys:event:updateZx")
    @PostMapping("updateZx")
    public ResponseVO updateZx(@RequestBody EventZxDTO eventZxDTO, BindingResult result) {
    	ValidUtils.error(result);
    	if(StringUtils.isBlank(eventZxDTO.getId())) {
    		throw new ArgumentException("事件id不能为空");
    	}
    	return new ResponseVO(eventService.updateZx(eventZxDTO));
    }


	/**
	 * 删除信息咨询归档事件
	 */
	@Login
	@CheckAuthz(hasPermissions = "sys:event:deleteZx")
	@PostMapping("deleteZx")
	public ResponseVO deleteZx(@Valid @RequestBody IdStringDTO idStringDTO, BindingResult result) {
		ValidUtils.error(result);
		if(eventService.selectEventSource(idStringDTO) == 1){
			throw new ArgumentException("信息来源是96333的数据，不允许删除");
		}
		boolean ret = eventService.delete(idStringDTO);
		return new ResponseVO(ret);
	}

    /**
    * @api {POST} /event/finishZx 完结信息咨询事件/event/finishZx
    * @apiDescription 完结信息咨询事件；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiBody {String} id 事件id
    * @apiBody {String} org1Id 责任单位
    * @apiBody {String} user1Id 责任人id
    * @apiBody {String} dealResult 办理结果
    * @apiBody {String} [feedback] 反馈意见
    * @apiBody {number} [revisit] 用户回访（1已回访,0未回访）
    * @apiBody {number} [satisfaction] 满意度（5满意,10一般,15不满意）
    * @apiSuccess (Success 200) {number} code 1成功，0失败
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "code": 1
    *     }
    * @apiSampleRequest /event/finishZx
    */
    @Login
    @PostMapping("finishZx")
    public ResponseVO finishZx(@RequestBody EventZxDTO eventZxDTO, HttpServletRequest request) {
    	if(StringUtils.isBlank(eventZxDTO.getDealResult())) {
    		throw new ArgumentException("办理结果不能为空");
    	}
    	if(StringUtils.isBlank(eventZxDTO.getId())) {
    		throw new ArgumentException("事件id不能为空");
    	}
    	String userId = (String)request.getAttribute("userId");
    	eventZxDTO.setFinishAccount((String)request.getAttribute("account"));
    	eventZxDTO.setFinishUserId(userId);
        return new ResponseVO(eventService.finishZx(eventZxDTO));
    }

    /**
    * @api {POST} /event/pageZx 信息咨询事件分页查询/event/pageZx
    * @apiDescription 信息咨询事件分页查询；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiParam {number} page 页码，必须大于0
    * @apiParam {number} limit 每页的数目，必须大于0
    * @apiBody {number} startTime 范围开始时间
    * @apiBody {number} endTime 范围结束时间 
    * @apiBody {String} keyword 工关键字，支持单号或咨询内容或咨询人电话、咨询人和责任人模糊检索 
    * @apiSuccess (Success 200) {number} total 总记录数
    * @apiSuccess (Success 200) {Object[]} items 事件对象数组
    * @apiSuccess (Success 200) {String} items.id 事件id
    * @apiSuccess (Success 200) {String} items.eventNo 事件编号
    * @apiSuccess (Success 200) {number} items.source 信息来源，0本地，1-96333,2告警信息
    * @apiSuccess (Success 200) {number} items.reportSource 接报来源文本
    * @apiSuccess (Success 200) {number} items.reportTime 接报时间，格式为10位数字
    * @apiSuccess (Success 200) {String} items.briefDesc 咨询内容
    * @apiSuccess (Success 200) {String} items.companyName 责任人公司
    * @apiSuccess (Success 200) {String} items.orgName 责任人部门
    * @apiSuccess (Success 200) {String} items.userName 责任人
    * @apiSuccess (Success 200) {String} items.userId 责任人
    * @apiSuccess (Success 200) {String} items.companyId 责任人公司id
    * @apiSuccess (Success 200) {String} items.orgId 责任人部门id
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "total": 510,
    *       "items": [
    *         {}
    *       ]
    *     }
    * @apiSampleRequest /event/pageZx
    */
    @Login
    @PostMapping("pageZx")
    public PageVO pageZx(@Valid PageDTO pageDTO, @RequestBody EventQueryDTO eventQueryDTO, BindingResult result, HttpServletRequest request) {
    	ValidUtils.error(result);
    	String role = (String)request.getAttribute("role");
    	if(role == null || role.length() < 1) {
    		LOGGER.info("没有角色权限");
    		throw new AuthzException("没有角色权限");
    	}
    	LOGGER.info("role:{}", role);
    	String userId = (String)request.getAttribute("userId");
    	eventQueryDTO.setDealStatus(0);
    	eventQueryDTO.setUserId(userId);
    	eventQueryDTO.setRoleIds(Arrays.asList(role.split(";")));
    	return new PageVO(eventService.pageZx(pageDTO, eventQueryDTO));
    }

    /**
    * @api {POST} /event/selectZxDetail 单个信息咨询事件详情/event/selectZxDetail
    * @apiDescription 单个信息咨询事件详情；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiBody {String} id 事件id 
    * @apiSuccess (Success 200) {String} id 事件id
    * @apiSuccess (Success 200) {String} eventNo 事件编号
    * @apiSuccess (Success 200) {number} source 信息来源，0本地，1-96333,2告警信息
    * @apiSuccess (Success 200) {number} reportTime 接报时间，格式为10位数字
    * @apiSuccess (Success 200) {String} briefDesc 咨询内容
    * @apiSuccess (Success 200) {String} reportMan 咨询人
    * @apiSuccess (Success 200) {String} reportManTel 咨询人电话
    * @apiSuccess (Success 200) {String} user1Id 责任人id
    * @apiSuccess (Success 200) {String} user1Name 责任人
    * @apiSuccess (Success 200) {String} org1Id 责任人单位id
    * @apiSuccess (Success 200) {String} dealResult 办理结果，处理结果
    * @apiSuccess (Success 200) {String} feedback 反馈意见
    * @apiSuccess (Success 200) {number} revisit 1已回访，0未回访
    * @apiSuccess (Success 200) {number} satisfaction 满意度（5满意,10一般,15不满意）
    * @apiSuccess (Success 200) {number} finishTime 处理时间，格式为10位数字
    * @apiSuccess (Success 200) {String} finishUserId 处理人id
    * @apiSuccess (Success 200) {String} finishUserName 处理人名称
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "id": "见 Success 中的字段"
    *     }
    * @apiSampleRequest /event/selectZxDetail
    */
    @Login
    @PostMapping("selectZxDetail")
    public EventZxDetailVO selectZxDetail(@Valid @RequestBody IdStringDTO dto, BindingResult result) {
    	ValidUtils.error(result);
	    return eventService.selectZxDetail(dto);
    }

    /**
    * @api {POST} /event/selectWithContruction 查询当前登录人的事件和施工上报记录/event/selectWithContruction
    * @apiDescription 查询当前登录人的事件和施工上报记录；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiSuccess (Success 200) {number} reportType 上报类型（1-事件，2-施工）
    * @apiSuccess (Success 200) {String} id 事件id或施工id
    * @apiSuccess (Success 200) {number} dealStatus 状态：事件（1接报，2待处置，3处置中，100已完成），施工（0未开始、1进行中、2已完成）
    * @apiSuccess (Success 200) {number} reportTime 上报时间，格式为10位数字
    * @apiSuccess (Success 200) {String} roadName 路段名称
    * @apiSuccess (Success 200) {String} directionName 方向名称
    * @apiSuccess (Success 200) {String} startMilePost 桩号（开始桩号）
    * @apiSuccess (Success 200) {String} endMilePost 结束桩号
    * @apiSuccess (Success 200) {String} briefDesc 事件描述，施工描述
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     [{
    *       "reportType": 1,
    *       "id": "dasfd-gds4-fgdag-34fss",
    *       "dealStatus": 2,
    *       "reportTime": 1659488397,
    *       "roadName": "S22贵合高速(融合路)",
    *       "directionName": "河池往桂林(下行)",
    *       "startMilePost": "K100+200",
    *       "endMilePost": "K100+200",
    *       "briefDesc": "小车抛锚",
    *     }]
    * @apiSampleRequest /event/selectWithContruction
    */
    @Login
    @PostMapping("selectWithContruction")
    public List<EventContructionVO> selectWithContruction(HttpServletRequest request) {
    	String userId = (String)request.getAttribute("userId");
	    return eventService.selectWithContruction(userId);
    }

    /**
     * @描述 从redis获取前端版本
     */
	@GetMapping("selectYkVueVersion")
	public Object selectYkVueVersion() {
		Object ykVueVersion = redisTemplate.opsForValue().get(SYS_GLOBAL_FRONT_VERSION);
		return ykVueVersion;
	}

    /**
    * @api {POST} /event/abnormalFinish 待处置直接完结，暂时先处理本地来源/event/abnormalFinish
    * @apiDescription 待处置直接完结，暂时先处理本地来源；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiBody {String} id 事件id
    * @apiBody {String} dealResult 完结原因，处理结果
    * @apiSuccess (Success 200) {number} code 1成功，0失败
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "code": 1
    *     }
    * @apiSampleRequest /event/abnormalFinish
    */
	@Login
	@PostMapping("abnormalFinish")
	public ResponseVO abnormalFinish(@Valid @RequestBody EventFinishDTO dto, BindingResult result, HttpServletRequest request) {
		ValidUtils.error(result);
		dto.setFinishUserId((String)request.getAttribute("userId"));
		dto.setFinishType(1);// 异常结束
		return new ResponseVO(eventService.abnormalFinish(dto));
	}

    /**
    * @api {POST} /event/eventStatistics 待处置直接完结，暂时先处理本地来源/event/eventStatistics
    * @apiDescription 待处置直接完结，暂时先处理本地来源；创建人：龙舒展，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiSuccess (Success 200) {number} pendingCount “待处置”的救援工单数量总和
    * @apiSuccess (Success 200) {number} processingCount 投诉建议的正在处置事件的总和(即未办结的投诉建议事件总和）
    * @apiSuccess (Success 200) {number} zxPendingCount 咨询的正在处置事件的总和(即未办结的咨询事件总和）
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "pendingCount": 1,
    *       "processingCount": 10,
    *       "zxPendingCount": 2
    *     }
    * @apiSampleRequest /event/eventStatistics
    */
	@Login
	@PostMapping("eventStatistics")
	public EventStatisticsVO eventStatistics(HttpServletRequest request) {
		String role = (String)request.getAttribute("role");
		LOGGER.info("role:{}", role);
		if(StringUtils.isBlank(role)) {
			return new EventStatisticsVO(0, 0);
		}
		String[] roles = role.split(";");
		if(eventStatisticsService.userRole(roles) == 0) {
			LOGGER.info("角色菜单权限没有事件处置");
			return new EventStatisticsVO(0, 0);
		}
		String userId = (String)request.getAttribute("userId");
		return eventStatisticsService.statistics(userId, roles);
	}

	/**
	 * @描述 应急救援归档的5级组织机构+路段的树形列表
	 */
	@Login
	@PostMapping("orgRoadByUser")
	public List<OrganizationVO> orgRoadByUser(HttpServletRequest request) {
		OrganizationDTO dto = new OrganizationDTO();
		dto.setOrgType(1);
		dto.setUse(1);
		List<OrganizationVO> organizationTree = feignClient.organizationTree(dto);
		String userId = (String)request.getAttribute("userId");
		String role = (String)request.getAttribute("role");
		LOGGER.info("role:{}", role);
		if(StringUtils.isBlank(role)) {
			return new ArrayList<OrganizationVO>();
		}
		String[] roles = role.split(";");
		return eventService.orgRoadByUser(userId, organizationTree, roles);
	}

    /**
    * @api {POST} /event/exportTsJyWord 导出投诉举报和意见建议详情event/exportTsJyWord
    * @apiDescription 导出投诉举报和意见建议详情；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiBody {String} id 事件id
    * @apiSampleRequest /event/exportTsJyWord
    */
	@Login
	@PostMapping("exportTsJyWord")
	public void exportTsJyWord(@RequestBody IdStringDTO dto, HttpServletResponse response) {
		if(StringUtils.isBlank(dto.getId())) {
			throw new ArgumentException("请求body参数校验不通过:事件ID不能为空");
		}
		eventService.exportTsJyWord(dto, response);
	}

    /**
    * @api {POST} /event/exportZxWord 导出信息咨询详情event/exportZxWord
    * @apiDescription 导出信息咨询详情；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiBody {String} id 事件id
    * @apiSampleRequest /event/exportZxWord
    */
	@Login
	@PostMapping("exportZxWord")
	public void exportZxWord(@RequestBody IdStringDTO dto, HttpServletResponse response) {
		if(StringUtils.isBlank(dto.getId())) {
			throw new ArgumentException("请求body参数校验不通过:事件ID不能为空");
		}
		eventService.exportZxWord(dto, response);
	}

	/**
	* @api {POST} /event/selectEventDealStatus 事件的预案执行相关状态查询/event/selectEventDealStatus
    * @apiDescription 事件的预案执行相关状态查询；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiBody {String} id 事件id
    * @apiSuccess (Success 200) {number} id 主键
    * @apiSuccess (Success 200) {String} eventId 事件id
    * @apiSuccess (Success 200) {number} dealResponse 初报确认(处置)响应状态，1已响应，0未响应
    * @apiSuccess (Success 200) {number} planResponse 启动预案响应状态，2已响应-不同意，1已响应-同意，0未响应,null没有该状态
    * @apiSuccess (Success 200) {number} cmsPublish 情报板发布状态，1已发布，0未发布，null没有该状态
    * @apiSuccess (Success 200) {number} sceneUpload 现场处置图片上传，1已上传，0未上传，null没有该状态
    * @apiSuccess (Success 200) {number} cancelPlan 申请解除预案状态，2-申请中，1已解除，0未解除，null没有该状态
    * @apiSuccess (Success 200) {number} cancelCmsPublish 撤销事件相关的情报板，1已撤销，0未撤销，null没有该状态
    * @apiSuccess (Success 200) {number} initReportTime 初报时间
    * @apiSuccess (Success 200) {number} planResponseTime 启动预案同意时间
    * @apiSuccess (Success 200) {number} dealResponseTime 事件确认时间（初报确认时间）
    * @apiSuccess (Success 200) {number} responseTime 事件确认响应时长（单位分）
    * @apiSuccess (Success 200) {number} planAuditTime 事件确认响应时长（单位分）
    * @apiSuccess (Success 200) {String} planName 预案名称
    * @apiSuccess (Success 200) {String} planLevelName 预案等级名称
    * @apiSuccess (Success 200) {number} initReportSms 初报短信发送状态，1全部成功，0有失败短信
    * @apiSuccess (Success 200) {number} countdownTime 归档评价分析倒计时(null-不用做分析评价或者已经评价；值大于0-未评价但未超时；值小于0-已超时未评价)
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *          "id": 55,
    *          "eventId": "f334861e-e798-4917-ae55-70d93e4ee518",
    *          "dealResponse": 1,
    *          "planResponse": 1,
    *          "cmsPublish": null,
    *          "sceneUpload": 0,
    *          "cancelPlan": 0,
    *          "cancelCmsPublish": null,
    *          "createTime": null,
    *          "initReportTime": 1669775665,
    *          "planResponseTime": 1669775913,
    *          "dealResponseTime": 1669775913,
    *          "responseTime": 1,
    *          "planAuditTime": 2,
    *          "planName": "南宁分公司测试用预案",
    *          "planLevelName": "II级",
    *          "initReportSms": 0,
    *          "countdownTime": null
    *     }
    *     //无数据返回
    *     {}
    * @apiSampleRequest /event/selectEventDealStatus
    */
	@Login
	@PostMapping("selectEventDealStatus")
	public Object selectEventDealStatus(@RequestBody IdStringDTO dto) {
		if(StringUtils.isBlank(dto.getId())) {
			throw new ArgumentException("请求body参数校验不通过:事件ID不能为空");
		}
		return NullObjectWapperVO.toObject(eventService.selectEventDealStatus(dto));
	}
	
	/**
	* @api {POST} /event/cancelPlan 解除预案申请/event/cancelPlan
    * @apiDescription 解除预案申请；创建人：龙思颖，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiBody {String} id 事件id
    * @apiSuccess (Success 200) {number} code 1成功，0失败
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "code": 1
    *     }
    * @apiSampleRequest /event/cancelPlan
    */
	@Login
	@PostMapping("cancelPlan")
	public ResponseVO cancelPlan(@Valid @RequestBody IdStringDTO dto, BindingResult result, HttpServletRequest request) {
		ValidUtils.error(result);
		String userId = (String)request.getAttribute("userId");
		return new ResponseVO(eventService.cancelPlan(dto, userId));
	}
	
	/**
	* @api {POST} /event/updateCmsPublish 事件的预案执行情报板发布状态更新/event/updateCmsPublish
    * @apiDescription 事件的预案执行情报板发布状态更新；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiBody {String} id 事件id
    * @apiSuccess (Success 200) {number} code 1成功，0失败
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "code": 1
    *     }
    * @apiSampleRequest /event/updateCmsPublish
    */
	@Login
	@PostMapping("updateCmsPublish")
	public ResponseVO updateCmsPublish(@Valid @RequestBody IdStringDTO dto, BindingResult result, HttpServletRequest request) {
		ValidUtils.error(result);
		String userId = (String)request.getAttribute("userId");
		return new ResponseVO(eventService.updateCmsPublish(dto, userId));
	}
	
	/**
	* @api {POST} /event/updateCancelCmsPublish 事件的预案执行情报板撤销状态更新/event/updateCancelCmsPublish
    * @apiDescription 事件的预案执行情报板撤销状态更新；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiBody {String} id 事件id
    * @apiSuccess (Success 200) {number} code 1成功，0失败
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "code": 1
    *     }
    * @apiSampleRequest /event/updateCancelCmsPublish
    */
	@Login
	@PostMapping("updateCancelCmsPublish")
	public ResponseVO updateCancelCmsPublish(@Valid @RequestBody IdStringDTO dto, BindingResult result, HttpServletRequest request) {
		ValidUtils.error(result);
		String userId = (String)request.getAttribute("userId");
		return new ResponseVO(eventService.updateCancelCmsPublish(dto, userId));
	}

	/**
	* @api {POST} /event/countWorkingTable 小程序-工作台与我相关的统计/event/countWorkingTable
    * @apiDescription 小程序-工作台与我相关的统计；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiSuccess (Success 200) {number} auditCount 预案待审核数量
    * @apiSuccess (Success 200) {number} dealCount 参与待处置的事件数量
    * @apiSuccess (Success 200) {number} analysisCount 待分析总结的数量
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "auditCount": 1,
    *       "dealCount": 5,
    *       "analysisCount": 2
    *     }
    * @apiSampleRequest /event/countWorkingTable
    */
	@Login
	@PostMapping("countWorkingTable")
	public WorkingTableCountVO countWorkingTable(HttpServletRequest request) {
		String userId = (String)request.getAttribute("userId");
		return eventService.countWorkingTable(userId);
	}
	
	/**
	 * @api {POST} /event/pageMyNoAuditEvent 分页查询与我关联的待审核的事件/event/pageMyNoAuditEvent
	 * @apiDescription 分页查询与我关联的待审核的事件；创建人：邓云钢，修改人：无
	 * @apiGroup  事件管理EventController
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam {number} page 页码
	 * @apiParam {number} limit 每页的数量
	 * @apiSuccess (Success 200) {String} id 事件id
	 * @apiSuccess (Success 200) {String} briefDesc 事件描述
	 * @apiSuccess (Success 200) {number} roadNo 路段编号
	 * @apiSuccess (Success 200) {number} level 预案等级，10普通，1-I级，2-II级，3-III级，4-IV级
	 * @apiSuccess (Success 200) {number} reportTime 事件上报时间戳，秒
	 * @apiSuccess (Success 200) {number} initReportTime 事件初报时间戳，秒
	 * @apiSuccess (Success 200) {number} startPlan 启动预案标识，-1待审核，0没审核权限，1已同意审核，2不同意审核
	 * @apiSuccess (Success 200) {number} cancelPlan 取消预案标识，-1待审核，0没审核权限，1已同意审核，2不同意审核
	 * @apiSuccess (Success 200) {Object} auditResponseTime 审核响应时长对象
	 * @apiSuccess (Success 200) {number} auditResponseTime.auditTime 审核响应最大时长，分钟
	 * @apiSuccessExample {json} Success-Response:
	 *   {
	 *       "total": 1,
	 *       "items": [
	 *           {
	 *               "source": 0,
	 *               "id": "b3ae83ec-9bb8-4acb-839d-27f810c21d0f",
	 *               "eventThreeType": 79,
	 *               "dealStatus": 3,
	 *               "eventStatus": 2,
	 *               "reportTime": 1669712160,
	 *               "briefDesc": "123123123",
	 *               "roadNo": 131,
	 *               "level": 10,
	 *               "directionNo": "03dad27e-4bb7-4551-a43c-50a4cb12b47a",
	 *               "milePost": "",
	 *               "startPlan": -1,
	 *               "cancelPlan": 0,
	 *               "emerPlanLevelId": "54967d26-b03e-44d0-a52a-5ce23e5a3a9d",
	 *               "auditResponseTime": {
	 *                   "eventId": "b3ae83ec-9bb8-4acb-839d-27f810c21d0f",
	 *                   "auditTime": 0,
	 *                   "responseTime": 2
	 *               },
	 *               "initReportTime": 1669712202
	 *           }
	 *       ]
	 *   }
	 * @apiSampleRequest /event/pageMyNoAuditEvent
	 */
	@Login
	@PostMapping("pageMyNoAuditEvent")
	public PageVO pageMyNoAuditEvent(@Valid PageDTO pageDTO, @RequestBody EventQueryDTO dto, BindingResult result, HttpServletRequest request) {
	    ValidUtils.error(result);
	    dto.setUserId((String)request.getAttribute("userId"));
	    return new PageVO(eventService.pageMyNoAuditEvent(pageDTO, dto));
	}

	/**
	* @api {POST} /event/pageMyAuditDealingEvent 分页查询与我关联的待审核和已审核的处置中事件/event/pageMyAuditDealingEvent
    * @apiDescription 分页查询与我关联的待审核和已审核的处置中事件；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiParam {number} page 页码
    * @apiParam {number} limit 每页的数量
    * @apiSuccess (Success 200) {String} id 事件id
    * @apiSuccess (Success 200) {String} briefDesc 事件描述
    * @apiSuccess (Success 200) {number} roadNo 路段编号
    * @apiSuccess (Success 200) {number} level 预案等级，10普通，1-I级，2-II级，3-III级，4-IV级
    * @apiSuccess (Success 200) {number} reportTime 事件上报时间戳，秒
    * @apiSuccess (Success 200) {number} initReportTime 事件初报时间戳，秒
    * @apiSuccess (Success 200) {number} startPlan 启动预案标识，-1待审核，0没审核权限，1已同意审核，2不同意审核
    * @apiSuccess (Success 200) {number} cancelPlan 取消预案标识，-1待审核，0没审核权限，1已同意审核，2不同意审核
    * @apiSuccess (Success 200) {Object} auditResponseTime 审核响应时长对象
    * @apiSuccess (Success 200) {number} auditResponseTime.auditTime 审核响应最大时长，分钟
    * @apiSuccessExample {json} Success-Response:
    *   {
    *       "total": 1,
    *       "items": [
    *           {
    *               "source": 0,
    *               "id": "b3ae83ec-9bb8-4acb-839d-27f810c21d0f",
    *               "eventThreeType": 79,
    *               "dealStatus": 3,
    *               "eventStatus": 2,
    *               "reportTime": 1669712160,
    *               "briefDesc": "123123123",
    *               "roadNo": 131,
    *               "level": 10,
    *               "directionNo": "03dad27e-4bb7-4551-a43c-50a4cb12b47a",
    *               "milePost": "",
    *               "startPlan": -1,
    *               "cancelPlan": 0,
    *               "emerPlanLevelId": "54967d26-b03e-44d0-a52a-5ce23e5a3a9d",
    *               "auditResponseTime": {
    *                   "eventId": "b3ae83ec-9bb8-4acb-839d-27f810c21d0f",
    *                   "auditTime": 0,
    *                   "responseTime": 2
    *               },
    *               "initReportTime": 1669712202
    *           }
    *       ]
    *   }
    * @apiSampleRequest /event/pageMyAuditDealingEvent
    */
	@Login
	@PostMapping("pageMyAuditDealingEvent")
	public PageVO pageMyAuditDealingEvent(@Valid PageDTO pageDTO, @RequestBody EventQueryDTO dto, BindingResult result, HttpServletRequest request) {
		ValidUtils.error(result);
		dto.setUserId((String)request.getAttribute("userId"));
		return new PageVO(eventService.pageMyAuditDealingEvent(pageDTO, dto));
	}

	/**
	* @api {POST} /event/pageMyNoDealEvent 分页查询与我关联的参与处置的的未完成的事件/event/pageMyNoDealEvent
    * @apiDescription 分页查询与我关联的参与处置的的未完成的事件；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiParam {number} page 页码
    * @apiParam {number} limit 每页的数量
    * @apiSuccess (Success 200) {String} id 事件id
    * @apiSuccess (Success 200) {String} briefDesc 事件描述
    * @apiSuccess (Success 200) {number} dealStatus 处置状态，2待处置，3处置中
    * @apiSuccess (Success 200) {number} roadNo 路段编号
    * @apiSuccess (Success 200) {number} level 预案等级，10普通，1-I级，2-II级，3-III级，4-IV级
    * @apiSuccess (Success 200) {number} reportTime 事件上报时间戳，秒
    * @apiSuccess (Success 200) {number} initReportTime 事件初报时间戳，秒
    * @apiSuccess (Success 200) {number} newEvent 1待确认，5已确认 （与个人确认相关的）
    * @apiSuccess (Success 200) {number} startPlan 启动预案标识，-1待审核，0没审核权限，1已同意审核，2不同意审核
    * @apiSuccess (Success 200) {number} cancelPlan 取消预案标识，-1待审核，0没审核权限，1已同意审核，2不同意审核
    * @apiSuccess (Success 200) {Object} auditResponseTime 审核响应时长对象
    * @apiSuccess (Success 200) {number} auditResponseTime.auditTime 审核响应最大时长，分钟
    * @apiSuccess (Success 200) {number} auditResponseTime.responseTime 处置响应最大时长，分钟
    * @apiSuccessExample {json} Success-Response:
    *   {
    *       "total": 1,
    *       "items": [
    *           {
    *               "source": 0,
    *               "id": "b3ae83ec-9bb8-4acb-839d-27f810c21d0f",
    *               "eventThreeType": 79,
    *               "dealStatus": 3,
    *               "eventStatus": 2,
    *               "reportTime": 1669712160,
    *               "briefDesc": "123123123",
    *               "roadNo": 131,
    *               "level": 10,
    *               "directionNo": "03dad27e-4bb7-4551-a43c-50a4cb12b47a",
    *               "milePost": "",
    *               "newEvent": 1,
    *               "startPlan": -1,
    *               "cancelPlan": 0,
    *               "emerPlanLevelId": "54967d26-b03e-44d0-a52a-5ce23e5a3a9d",
    *               "auditResponseTime": {
    *                   "eventId": "b3ae83ec-9bb8-4acb-839d-27f810c21d0f",
    *                   "auditTime": 0,
    *                   "responseTime": 2
    *               },
    *               "initReportTime": 1669712202
    *           }
    *       ]
    *   }
    * @apiSampleRequest /event/pageMyNoDealEvent
    */
	@Login
	@PostMapping("pageMyNoDealEvent")
	public PageVO pageMyNoDealEvent(@Valid PageDTO pageDTO, @RequestBody EventQueryDTO dto, BindingResult result, HttpServletRequest request) {
		ValidUtils.error(result);
		dto.setUserId((String)request.getAttribute("userId"));
		return new PageVO(eventService.pageMyNoDealEvent(pageDTO, dto));
	}

	/**
	* @api {POST} /event/pageMyNoAnalysisEvent 分页查询与我关联的待总结的事件/event/pageMyNoAnalysisEvent
    * @apiDescription 分页查询与我关联的待总结的事件；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiParam {number} page 页码
    * @apiParam {number} limit 每页的数量
    * @apiSuccess (Success 200) {String} id 事件id
    * @apiSuccess (Success 200) {String} briefDesc 事件描述
    * @apiSuccess (Success 200) {number} roadNo 路段编号
    * @apiSuccess (Success 200) {number} level 预案等级，10普通，1-I级，2-II级，3-III级，4-IV级
    * @apiSuccess (Success 200) {number} reportTime 事件上报时间戳，秒
    * @apiSuccess (Success 200) {number} initReportTime 事件初报时间戳，秒
    * @apiSuccess (Success 200) {number} analysisTime 分析总结最大时长，秒
    * @apiSuccess (Success 200) {number} finishTime 事件完结的事件，秒
    * @apiSuccessExample {json} Success-Response:
    *   {
    *       "total": 1,
    *       "items": [
    *           {
    *               "source": 0,
    *               "id": "b3ae83ec-9bb8-4acb-839d-27f810c21d0f",
    *               "eventThreeType": 79,
    *               "dealStatus": 100,
    *               "eventStatus": 8,
    *               "reportTime": 1669712160,
    *               "briefDesc": "123123123",
    *               "roadNo": 131,
    *               "level": 10,
    *               "directionNo": "03dad27e-4bb7-4551-a43c-50a4cb12b47a",
    *               "milePost": "",
    *               "newEvent": 1,
    *               "startPlan": -1,
    *               "cancelPlan": 0,
    *               "emerPlanLevelId": "54967d26-b03e-44d0-a52a-5ce23e5a3a9d",
    *               "analysisTime": 86400,
    *               "finishTime": 1661928917
    *               "initReportTime": 1661733165
    *           }
    *       ]
    *   }
    * @apiSampleRequest /event/pageMyNoAnalysisEvent
    */
	@Login
	@PostMapping("pageMyNoAnalysisEvent")
	public PageVO pageMyNoAnalysisEvent(@Valid PageDTO pageDTO, @RequestBody EventQueryDTO dto, BindingResult result, HttpServletRequest request) {
		ValidUtils.error(result);
		dto.setUserId((String)request.getAttribute("userId"));
		return new PageVO(eventService.pageMyNoAnalysisEvent(pageDTO, dto));
	}

	/**
	* @api {POST} /event/selectAnalysisAuthById 查询登录用户是否有该事件的总结权限/event/selectAnalysisAuthById
    * @apiDescription 查询登录用户是否有该事件的总结权限；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiBody {String} id 事件id
    * @apiSuccess (Success 200) {number} code 1有权限，0无权限
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "code": 1
    *     }
    * @apiSampleRequest /event/selectAnalysisAuthById
    */
	@Login
	@PostMapping("selectAnalysisAuthById")
	public ResponseVO selectAnalysisAuthById(@RequestBody IdStringDTO dto, BindingResult result, HttpServletRequest request) {
		ValidUtils.error(result);
		String userId = (String)request.getAttribute("userId");
		return eventService.selectAnalysisAuthById(dto, userId);
	}

	/**
	 * @api {post}/event/getAddress 获取所属区域 /event/getAddress
	 * @apiGroup 事件管理EventController
	 * @apiHeader {String} Authorization token
	 * @apiBody   {Number} roadNo=100  路段
	 * @apiBody   {String} directionNo=6378a480-0924-46b6-be59-ce7f6ed2b823  方向
	 * @apiBody   {String} milePost=K100+000  桩号
	 * @apiSuccess (Success 200) {String} status=1 返回值为 0 或 1，0 表示请求失败；1 表示请求成功
	 * @apiSuccess (Success 200) {Object} geocodes 地理编码信息列表
	 * @apiSuccess (Success 200) {Object} geocodes.addressComponent 用户姓名
	 * @apiSuccess (Success 200) {String} geocodes.addressComponent.city 地址所在的城市名
	 * @apiSuccess (Success 200) {String} geocodes.addressComponent.province 地址所在的省份名
	 * @apiSuccess (Success 200) {String} geocodes.addressComponent.district 地址所在的区
	 * @apiSuccessExample {json} Success-Response:
	 * {
	 *       "status": "1",
	 *       "geocodes": {
	 *       	"addressComponent":{
	 *       		"city":"地址所在的城市名",
	 *       		"province":"地址所在的省份名",
	 *       		"district":"地址所在的区"
	 *       	}
	 *       }
	 * }
	 * @apiSampleRequest /event/getAddress
	 */
	@Login
	@PostMapping("getAddress")
	public Object getAddress(@RequestBody RoadPileNoDTO dto) {
		return NullObjectWapperVO.toObject(eventService.getAddress(dto));
	}

	/**
	* @api {POST} /event/selectEventDealFullClose 通过事件id查询全幅封闭历史记录/event/selectEventDealFullClose
    * @apiDescription 通过事件id查询全幅封闭历史记录；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiBody {String} id 事件id
    * @apiSuccess (Success 200) {String} eventId 事件id
    * @apiSuccess (Success 200) {number} startCloseTime 开始全幅封闭时间戳，秒
    * @apiSuccess (Success 200) {number} endCloseTime 解除全幅封闭时间戳，秒
    * @apiSuccess (Success 200) {String} createTime 记录生成时间
    * @apiSuccess (Success 200) {String} updateTime 记录更新时间
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "eventId": "24bc1646-ce41-4afd-b8c1-75f72db00c21",
    *       "startCloseTime": 1671071972,
    *       "endCloseTime": null,
    *       "createTime": "2022-12-15 10:39:30",
    *       "updateTime": null
    *     }
    * @apiSampleRequest /event/selectEventDealFullClose
    */
	@Login
	@PostMapping("selectEventDealFullClose")
	public List<EventDealFullCloseVO> selectEventDealFullClose(@RequestBody IdStringDTO dto, BindingResult result) {
		ValidUtils.error(result);
		return eventService.selectEventDealFullClose(dto);
	}

    /** 
    * @api {POST} /event/nearestRelatedFacility 查询事件最近的涉隧-桥设施/event/nearestRelatedFacility
    * @apiDescription 查询事件最近的涉隧-桥设施；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiBody {number} facilityTypeNo 设施类型编号
    * @apiBody {number} roadNo 路段编号
    * @apiBody {String} milePost 桩号
    * @apiSuccess (Success 200) {String} facilityNo 设施编号，最近的设施编号
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "facilityNo": "设施编号"
    *     }
    * @apiSampleRequest /event/nearestRelatedFacility
    */
	@Login
    @PostMapping("nearestRelatedFacility")
	public Object nearestRelatedFacility(@Valid @RequestBody RelatedFacilityDTO dto, BindingResult result) {
	    ValidUtils.error(result);
	    return eventService.nearestRelatedFacility(dto);
	}

	/**
	 * @api {POST} /event/saveEventEvaluation 保存/暂存突发事件应急处置评价表/event/saveEventEvaluation
	 * @apiDescription 保存/暂存突发事件应急处置评价表；创建人：覃士蘅，修改人：无
	 * @apiGroup  事件管理EventController
	 * @apiHeader {String} Authorization 用户登录后的token.
	 */
	@Login
	@PostMapping("/saveEventEvaluation")
	public Object saveEventEvaluation(HttpServletRequest request,  @RequestBody EventEvaluationVO evaVO) {
		String userId = (String)request.getAttribute("userId");
		return eventService.saveEventEvaluation(evaVO, userId);
	}

	/**
	 * @api {POST} /event/getEventEvaluation 获取突发事件应急处置评价表/event/getEventEvaluation
	 * @apiDescription 获取突发事件应急处置评价表；创建人：覃士蘅，修改人：无
	 * @apiGroup  事件管理EventController
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiBody {String} eventId 事件id
	 * @apiSuccessExample {json} Success-Response:
	 *     HTTP/1.1 200 OK
	 *     {
	 *     "id": 5,
	 *     "title": "广西沿海分公司突发事件应急处置表",
	 *     "state": 3,
	 *     "totalScore": 86.0,
	 *     "eventId": "00025351-e139-4c4f-a167-3c45d1451a99",
	 *     "evl": [
	 *         {
	 *             "projectName": "1.应急预警",
	 *             "percent": 0.1,
	 *             "project": [
	 *                 {
	 *                     "id": 213,
	 *                     "content": "1.1 能够结合一体化应急管理平台系统数据变化状况或有关部门提供的预警信息进行预警；",
	 *                     "sort": 1,
	 *                     "score": 0,
	 *                     "experience": "111111111111",
	 *                     "problem": "问题",
	 *                     "improvement": "啊啊啊",
	 *                     "remarks": "备注测试",
	 *                     "evlOrg": "客服中心",
	 *                     "evlUser": "覃士蘅",
	 *                     "evlTime": "2023-02-06 18:01:11"
	 *                 }
	 *             ]
	 *         }
	 *     ]
	 *     }
	 */
	@Login
	@PostMapping("/getEventEvaluation")
	public EventEvaluationVO getEventEvaluation(HttpServletRequest request,  @RequestBody EventEvlRecordDTO evaDTO) {
		String userId = (String)request.getAttribute("userId");
		evaDTO.setUserId(userId);
		return eventService.getEventEvaluationData(evaDTO);
	}
	
	/**
	 * @描述 96333工单提交处理结果成功码同步
	 */
	@Login
	@PostMapping("updateEvent96333ResultCode")
	public ResponseVO updateEvent96333ResultCode(@RequestBody Event96333OrderSuccessCodeDTO dto) {
		return eventService.updateEvent96333ResultCode(dto);
	}

	/**
	 * @描述 96333工单提交回访结果成功码同步
	 */
	@Login
    @PostMapping("updateEvent96333VisitCode")
    public ResponseVO updateEvent96333VisitCode(@RequestBody Event96333OrderSuccessCodeDTO dto) {
        return eventService.updateEvent96333VisitCode(dto);
    }
	
	/**
	 * @描述 96333工单被撤销
	 */
	@Login
	@PostMapping("cancel96333Order")
	public ResponseVO cancel96333Order(@RequestBody Event96333NewOrderDTO dto) {
		return eventService.cancel96333Order(dto);
	}

	/**
	 * @描述 96333工单责任部门更新
	 */
	@Login
    @PostMapping("update96333OrderDealOrg")
	public ResponseVO update96333OrderDealOrg(@RequestBody Event96333NewOrderDTO dto) {
		return eventService.update96333OrderDealOrg(dto);
	}

	/**
	 * @api {POST} /event/lnglatToMilePost 经纬度转路段和桩号/event/lnglatToMilePost
	 * @apiDescription 经纬度转路段和桩号；创建人：邓云钢，修改人：无
	 * @apiGroup 事件管理EventController
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiBody {String} lng=108.414026 经度
	 * @apiBody {String} lat=22.677031 纬度
	 * @apiSuccess (Success 200) {number=0,1} code 返回状态码，1有数据，0无数据，401未登录或token失效
	 * @apiSuccess (Success 200) {Object} data 结果数据
	 * @apiSuccess (Success 200) {String} data.roadNo 路段编号
	 * @apiSuccess (Success 200) {String} data.milePost 桩号
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 
	 * { "code": 1,
	 *   "data": {
	 *   	"roadNo": 179, "milePost": "K2006+100"
	 *   }
	 * }
	 * @apiSampleRequest /event/lnglatToMilePost
	 */
	@Login
    @PostMapping("lnglatToMilePost")
	public Object lnglatToMilePost(@RequestBody LngLatDTO dto) {
		RoadPileNoVO lnglatToMilePost = eventService.lnglatToMilePost(dto);
		return new ResultVO<RoadPileNoVO>(lnglatToMilePost);
	}

	@Login
	@PostMapping("addDDForGzh")
	public Object addDDForGzh(@RequestBody DDForGzhDTO dto, HttpServletRequest request) {
		String openid = (String)request.getHeader("openid");
		dto.setOpenid(openid);
		boolean ret = eventService.addDDForGzh(dto);
		return new ResponseVO(ret);
	}

	/**
	 * 新增一键救援工单（桂享高速）
	 * @param ddIntegralDTO
	 * @return
	 */
	@Login
	@PostMapping("addDDIntegral")
	public String addDDIntegral(@RequestBody DDIntegralDTO ddIntegralDTO) {
		return eventService.addDDIntegral(ddIntegralDTO);
	}

	/**
	 * 修改一键救援拖车进度状态（桂享高速）
	 * @param ddRescuerInfoDTO
	 * @return
	 */
	@Login
	@PostMapping("updateDDIntegral")
	public Integer updateDDIntegral(@RequestBody DDRescuerInfoDTO ddRescuerInfoDTO) {
		return eventService.updateDDIntegral(ddRescuerInfoDTO);
	}

	/**
	 * 一键救援拖车进展消息新增（桂享高速）
	 * @param ddRescuerInfoDTO
	 * @return
	 */
	@Login
	@PostMapping("addDDProgress")
	public Integer addDDProgress(@RequestBody DDRescuerInfoDTO ddRescuerInfoDTO) {
		return eventService.addDDProgress(ddRescuerInfoDTO);
	}

	/**
	 * 一键救援拖车进展图片新增（桂享高速）
	 * @param ddRescuerInfoDTO
	 * @return
	 */
	@Login
	@PostMapping("addDDProgressImg")
	public Integer addDDProgressImg(@RequestBody DDRescuerInfoDTO ddRescuerInfoDTO) {
		return eventService.addDDProgressImg(ddRescuerInfoDTO);
	}

	/**
	 * 一键救援拖车取消订单（桂享高速）
	 * @param ddRescuerInfoDTO
	 * @return
	 */
	@Login
	@PostMapping("cancelDDIntegral")
	public Integer cancelDDIntegral(@RequestBody DDRescuerInfoDTO ddRescuerInfoDTO) {
		return eventService.cancelDDIntegral(ddRescuerInfoDTO);
	}

	/**
	 * @描述 查询公众号个人提交的救援记录
	 */
	@Login
    @PostMapping("selectOwnDDForGzh")
	public Object selectOwnDDForGzh(@RequestBody IdStringDTO dto, HttpServletRequest request) {
		String openid = (String)request.getHeader("openid");
		return eventService.selectOwnDDForGzh(dto, openid);
	}

	/**
	 * @描述 查询公众号上处置中的救援记录（带经纬度）
	 */
	@Login
    @PostMapping("selectOngoingDDForGzh")
	public Object selectOngoingDDForGzh() {
		return new ResultVO<>(eventService.selectOngoingDDForGzh());
	}

	/**
	 * @描述 根据事件id获取事件的基本信息
	 */
	@Login
    @PostMapping("selectBaseInfoByEventId")
	public Object selectBaseInfoByEventId(@RequestBody IdStringDTO dto,BindingResult result) {
		ValidUtils.error(result);
		return eventService.selectBaseInfoByEventId(dto);
	}
	
	/**
	 * @描述 统计救援事件的平均处置时间，处置时间=final_report_time-distribute_time
	 */
	@Login
	@PostMapping("statAvgDealTimeGroupOrgId")
	public Object statAvgDealTimeGroupOrgId(@RequestBody EventAnalysisDTO eventAnalysisDTO, HttpServletRequest request) {
		String role = (String) request.getAttribute("role");
		if (role == null || role.length() < 1) {
			LOGGER.info("没有角色权限");
			throw new AuthzException("没有角色权限");
		}
		return eventService.statAvgDealTimeGroupOrgId(role, eventAnalysisDTO);
	}
	
	/**
	 * @描述 查询收费政策
	 */
	@Login
	@PostMapping("selectChargePolicyForGzh")
	public Object selectChargePolicyForGzh() {
		return new ResultVO<>(eventService.selectChargePolicyForGzh());
	}
	
	/**
	 * @描述 查询公告信息
	 */
	@Login
	@PostMapping("selectPublicInfoForGzh")
	public Object selectPublicInfoForGzh() {
	    return new ResultVO<>(eventService.selectPublicInfoForGzh());
	}

    /**
     * @描述 不参与分公司统计平均处置时长的原因
     */
	@Deprecated
    @Login
    @CheckAuthz(hasPermissions = "sys:event:nostatDealReason")
    @PostMapping("nostatDealReason")
    public Object nostatDealReason(@Valid @RequestBody NostatDealReasonDTO dto, BindingResult result, HttpServletRequest request) {
        ValidUtils.error(result);
        dto.setUserId((String)request.getAttribute("userId"));
        return new ResponseVO(eventService.nostatDealReason(dto));
    }
    
    /**
     * @描述 查询不参与分公司统计平均处置时长的原因，statDealTime=1审核通过，2待审核，3审核不通过
     */
    @Login
    @PostMapping("selectNostatDealReason")
    public Object selectNostatDealReason(@RequestBody IdStringDTO dto, BindingResult result) {
        ValidUtils.error(result);
        return eventService.selectNostatDealReason(dto);
    }

    /**
     * @描述 发布事件到高德，测试使用
     */
    @Login
    @PostMapping("publishToAmap")
    public Object publishToAmap(@RequestBody AmapEventPublishDTO dto, BindingResult result) {
        ValidUtils.error(result);
        return eventService.publishToAmap(dto);
    }

	/**
	 * @描述 新增事件-路段和方向的3级组织机构+路段的树形列表
	 */
	@Login
	@PostMapping("orgRoadDirection")
	public List<OrganizationVO> orgRoadDirection(HttpServletRequest request) {
		OrganizationDTO dto = new OrganizationDTO();
		dto.setOrgType(1);
		dto.setUse(1);
//		List<OrganizationVO> organizationTree = feignClient.organizationTree(dto);
//		List<OrganizationVO> organizationTree = feignClient.loginUserOrgTree(dto);// 使用登录用户的组织机构权限树
		List<OrganizationVO> organizationTree = feignClient.allTree(dto);// 使用所有的组织机构权限树
		String userId = (String)request.getAttribute("userId");
		String role = (String)request.getAttribute("role");
		LOGGER.info("role:{}", role);
		if(StringUtils.isBlank(role)) {
			return new ArrayList<OrganizationVO>();
		}
		String[] roles = role.split(";");
		return eventService.orgRoadDirection(userId, organizationTree, roles);
	}
	
	/**
	 * @描述 设备离线状态统计的5级组织机构+路段+路段设施的树形列表
	 */
	@Login
	@PostMapping("orgRoadFacilityByUser")
	public List<OrganizationVO> orgRoadFacilityByUser(HttpServletRequest request) {
		OrganizationDTO dto = new OrganizationDTO();
		dto.setOrgType(1);
		dto.setUse(1);
		List<OrganizationVO> organizationTree = feignClient.organizationTree(dto);
		String userId = (String)request.getAttribute("userId");
		String role = (String)request.getAttribute("role");
		LOGGER.info("role:{}", role);
		if(StringUtils.isBlank(role)) {
			return new ArrayList<OrganizationVO>();
		}
		String[] roles = role.split(";");
		return eventService.orgRoadFacilityByUser(userId, organizationTree, roles);
	}
	
	/**
	 * @描述 统计（车辆救援、交通事故、轻微交通事故）事件的平均处置时间在2小时内的数目和总的数目， 2小时内处置率
	 */
	@Login
	@PostMapping("dealWithin2Hour")
	public Object dealWithin2Hour(@RequestBody EventAnalysisDTO eventAnalysisDTO, HttpServletRequest request) {
	    String role = (String) request.getAttribute("role");
	    if (role == null || role.length() < 1) {
	        LOGGER.info("没有角色权限");
	        throw new AuthzException("没有角色权限");
	    }
	    return eventService.dealWithin2Hour(role, eventAnalysisDTO);
	}

	/**
	 * @描述 统计事件的平均处置时间在timeSecond秒内的数目和总的数目， timeSecond秒内处置率
	 */
	@Login
	@PostMapping("dealWithinXsecond")
	public Object dealWithinXsecond(@RequestBody EventAnalysisDTO eventAnalysisDTO, HttpServletRequest request) {
	    String role = (String) request.getAttribute("role");
	    if (role == null || role.length() < 1) {
	        LOGGER.info("没有角色权限");
	        throw new AuthzException("没有角色权限");
	    }
	    return eventService.dealWithinXsecond(role, eventAnalysisDTO);
	}

	/**
	 * @描述 出发响应率，在timeSecond秒内的数目和总的数目， timeSecond秒内出发响应率
	 */
	@Login
	@PostMapping("confirmWithinXsecond")
	public Object confirmWithinXsecond(@RequestBody EventAnalysisDTO eventAnalysisDTO, HttpServletRequest request) {
	    String role = (String) request.getAttribute("role");
	    if (role == null || role.length() < 1) {
	        LOGGER.info("没有角色权限");
	        throw new AuthzException("没有角色权限");
	    }
	    return eventService.confirmWithinXsecond(role, eventAnalysisDTO);
	}

	/**
	 * @描述 统计（车辆救援、交通事故、轻微交通事故）事件的前往现场、1小时内到达的数目、和总的数目， 1小时内到达率
	 */
	@Login
	@PostMapping("reachWithin1Hour")
	public Object reachWithin1Hour(@RequestBody EventAnalysisDTO eventAnalysisDTO, HttpServletRequest request) {
	    String role = (String) request.getAttribute("role");
	    if (role == null || role.length() < 1) {
	        LOGGER.info("没有角色权限");
	        throw new AuthzException("没有角色权限");
	    }
	    return eventService.reachWithin1Hour(role, eventAnalysisDTO);
	}

	/**
	 * @描述 统计应急救援事件的前往现场、timeSecond秒内到达的数目、总的数目， timeSecond秒内到达率
	 */
	@Login
	@PostMapping("reachWithinXsecond")
	public Object reachWithinXsecond(@RequestBody EventAnalysisDTO eventAnalysisDTO, HttpServletRequest request) {
	    String role = (String) request.getAttribute("role");
	    if (role == null || role.length() < 1) {
	        LOGGER.info("没有角色权限");
	        throw new AuthzException("没有角色权限");
	    }
	    return eventService.reachWithinXsecond(role, eventAnalysisDTO);
	}

	/**
	 * @描述 统计应急救援事件timeSecond秒内疏通的数目、需要疏通的数目， timeSecond秒内疏通率
	 */
	@Login
	@PostMapping("unblockWithinXsecond")
	public Object unblockWithinXsecond(@RequestBody EventAnalysisDTO eventAnalysisDTO, HttpServletRequest request) {
	    String role = (String) request.getAttribute("role");
	    if (role == null || role.length() < 1) {
	        LOGGER.info("没有角色权限");
	        throw new AuthzException("没有角色权限");
	    }
	    return eventService.unblockWithinXsecond(role, eventAnalysisDTO);
	}

    /**
     * @描述 事件匹配摄像机锁定
     */
    @Login
    @PostMapping("eventMatchLock")
    public Object eventMatchLock(@RequestBody VideoPostionDTO dto) {
        if (StringUtils.isBlank(dto.getEventId())) {
            throw new ArgumentException("事件id不能为空");
        }
        if (StringUtils.isBlank(dto.getDeviceId())) {
            throw new ArgumentException("摄像机id不能为空");
        }
        return new ResponseVO(eventService.eventMatchLock(dto));
    }

 // 本地新增-投诉举报意见建议
    /**
    * @api {POST} /event/addTsJy 新增本地(投诉举报意见建议)事件/event/addTsJy
    * @apiDescription 新增本地(投诉举报意见建议)事件；创建人：邓云钢，修改人：无
    * @apiGroup  事件管理EventController
    * @apiHeader {String} Authorization 用户登录后的token.
    * @apiBody {String} briefDesc 投诉内容
    * @apiBody {number} reportTime 接报时间
    * @apiBody {number} reportSourceKey=0 接报来源，0本地，1:96333
    * @apiBody {String} orgId 责任单位
    * @apiBody {String} reportMan 来电人
    * @apiBody {String} reportManTel 来电人电话
	* @apiBody {String} carPlate 车牌号
    * @apiBody {number} eventType 事件一级类型
    * @apiBody {number} eventTwoType 事件二级类型
    * @apiBody {number} eventThreeType 事件三级类型
    * @apiBody {number} complaintType 0无理，1有理
    * @apiBody {number} complaintTarget 投诉对象
    * @apiBody {number} businessType 业务类型
    * @apiSuccess (Success 200) {number} code 1成功，0失败
    * @apiSuccess (Success 200) {String} id 事件id
    * @apiSuccessExample {json} Success-Response:
    *     HTTP/1.1 200 OK
    *     {
    *       "code": 1,
    *       "id": "95dc93a3-f341-4c0c-9087-25a0820f13e5"
    *     }
    * @apiSampleRequest /event/addTsJy
    */
    @Login
    @PostMapping("addTsJy")
    public ResponseVO addTsJy(@Valid @RequestBody EventTsDTO eventTsDTO,  BindingResult result, HttpServletRequest request) {
    	ValidUtils.error(result);
    	String userId = (String)request.getAttribute("userId");
    	eventTsDTO.setId(UUID.randomUUID().toString());
    	eventTsDTO.setRecordManId(userId);
    	eventTsDTO.setCreateUserId(userId);
    	eventTsDTO.setSource(0);
        return new ResponseVO(eventService.addTsJy(eventTsDTO), eventTsDTO.getId());
    }

    @Login
    @PostMapping("updateTsJy")
    public ResponseVO updateTsJy(@Valid @RequestBody EventTsDTO eventTsDTO,  BindingResult result, HttpServletRequest request) {
    	ValidUtils.error(result);
    	if(StringUtils.isBlank(eventTsDTO.getId())) {
    		throw new ArgumentException("事件id不能为空");
    	}
        return new ResponseVO(eventService.updateTsJy(eventTsDTO));
    }
	/**
	 * 	投诉建议导出（处置中，非归档）
	 */
	@Login
	@PostMapping("exportTsJy")
	public void exportTsJy(@RequestBody EventQueryDTO dto,HttpServletRequest request, HttpServletResponse response) {
		String role = (String)request.getAttribute("role");
		if(role == null || role.length() < 1) {
			LOGGER.info("没有角色权限");
			throw new AuthzException("没有角色权限");
		}
		LOGGER.info("role:{}", role);
		String userId = (String)request.getAttribute("userId");
		dto.setUserId(userId);
		dto.setRoleIds(Arrays.asList(role.split(";")));
		eventService.exportTsJy(dto, response);
	}
	/**
	 * 	信息咨询导出（处置中，非归档）
	 */
	@Login
	@PostMapping("exportZx")
	public void exportZx(@RequestBody EventQueryDTO eventQueryDTO, HttpServletRequest request,HttpServletResponse response) {
		String role = (String)request.getAttribute("role");
		if(role == null || role.length() < 1) {
			LOGGER.info("没有角色权限");
			throw new AuthzException("没有角色权限");
		}
		LOGGER.info("role:{}", role);
		String userId = (String)request.getAttribute("userId");
		eventQueryDTO.setDealStatus(0);
		eventQueryDTO.setUserId(userId);
		eventQueryDTO.setRoleIds(Arrays.asList(role.split(";")));
		eventService.exportZx(eventQueryDTO, response);
	}

	/**
	 * 小程序数据分析-新增组织机构菜单
	 * @param request
	 * @return
	 */
	@Login
	@PostMapping("orgByUser")
	public List<OrgListVO> orgByUser(HttpServletRequest request){
		String role = (String)request.getAttribute("role");
		if(role == null || role.length() < 1) {
			LOGGER.info("没有角色权限");
			throw new AuthzException("没有角色权限");
		}
		Map<String, Object> map = new HashMap<>();
		map.put("roleIds", Arrays.asList(role.split(";")));
		return eventService.orgByUser(map);
	}

	@Login
	@PostMapping("query96333OrderList")
	public List<String> query96333OrderList(@RequestBody List<String> eventNos) {
		return eventService.query96333OrderList(eventNos);
	}

	/**
	 * @描述 自动打卡
	 */
	@Login
	@PostMapping("/location")
	public ResponseVO location(@RequestBody UserLocationDTO dto) {
		return new ResponseVO(eventService.location(dto));
	}

	/**
	 * @描述 应急救援圈根据事件ID查询处置人员位置
	 */
	@Login
	@PostMapping("/personLocationByEventId")
	public List<UserLocationVO> personLocationByEventId(@RequestBody IdStringDTO dto) {
		return eventService.personLocationByEventId(dto);
	}

	/**
	 * @description 分页查询事件信息审核
	 */
	@Login
	@PostMapping("pageEventInfoReview")
	public Object pageEventInfoReview(@Valid PageDTO pageDTO, @RequestBody EventInfoReviewDTO dto, BindingResult result, HttpServletRequest request) {
		ValidUtils.error(result);
		String userId = (String)request.getAttribute("userId");
		dto.setLoginUserId(userId);
		return new PageVO(eventService.pageEventInfoReview(pageDTO, dto));
	}

	/**
	 * @description 统计登录用户的事件信息待审核数目
	 */
	@Login
	@PostMapping("statLoginUserEventInfoReview")
	public Object statLoginUserEventInfoReview(@RequestBody EventInfoReviewDTO dto, HttpServletRequest request) {
		String userId = (String)request.getAttribute("userId");
		int num = eventService.statLoginUserEventInfoReview(userId);
		return new ResultVO<>(1, num);
	}
	
	/**
	 * @description 修改事件信息审核
	 */
	@Login
	@PostMapping("updateEventInfoReview")
	public Object updateEventInfoReview(@RequestBody EventInfoReviewDTO dto, HttpServletRequest request) {
		String userId = (String)request.getAttribute("userId");
		dto.setCreateUserId(userId);
		boolean ret = eventService.updateEventInfoReview(dto);
		return new ResponseVO(ret);
	}

	/**
	 * @description 统计登录用户的某个事件信息待审核和审核不通过的数目
	 */
	@Login
	@PostMapping("statEventInfoReviewByEventId")
	public Object statEventInfoReviewByEventId(@RequestBody EventInfoReviewDTO dto, HttpServletRequest request) {
		String userId = (String)request.getAttribute("userId");
		dto.setCreateUserId(userId);
		return eventService.statEventInfoReviewByEventId(dto);
	}

	@Login
    @PostMapping("confirmUnblock")
	public ResponseVO confirmUnblock(HttpServletRequest request, @Valid @RequestBody EventDealStatusDTO dto, BindingResult result) {
		String userId = (String)request.getAttribute("userId");
		boolean ret = eventService.confirmUnblock(dto, userId);
		return new ResponseVO(ret);
	}
	
}