package com.bt.itsevent.service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.apache.commons.lang.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.nacos.api.utils.StringUtils;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.bt.itscore.config.OssConfig;
import com.bt.itscore.domain.dto.AttachDTO;
import com.bt.itscore.domain.dto.IdStringDTO;
import com.bt.itscore.domain.vo.ResponseVO;
import com.bt.itscore.utils.GsonUtils;
import com.bt.itscore.utils.HttpClientUtils;
import com.bt.itscore.utils.OssUtils;
import com.bt.itsevent.constants.ShoutControlEnum;
import com.bt.itsevent.domain.dto.ProgressDTO;
import com.bt.itsevent.domain.dto.UavFlyDTO;
import com.bt.itsevent.domain.dto.UavFlyResponseDTO;
import com.bt.itsevent.domain.dto.UavShoutDTO;
import com.bt.itsevent.domain.dto.UavShoutPayloadDTO;
import com.bt.itsevent.domain.vo.EventDetailVO;
import com.bt.itsevent.domain.vo.UavTipVO;
import com.bt.itsevent.mapper.EventMapper;
import com.bt.itsevent.mapper.ProgressMapper;
/**
 * <AUTHOR>
 * @date 2025年3月8日 下午1:39:44
 * @Description 无人机业务逻辑实现类
 */
@Service("uavService")
public class UavService {
	@Autowired
	RestTemplate restTemplate;
	@Autowired
	RestTemplate sslRestTemplate;
	@Autowired
	ProgressService progressService;
	@Value("${uav.host:http://***********:6789}")
	private String uavHost;
    @Autowired
    OssConfig ossConfig;
    @Autowired
    ProgressMapper progressMapper;
    @Autowired
    EventMapper eventMapper;

	@Autowired
	private StringRedisTemplate stringRedisTemplate;
	private Object lock = new Object();

	public boolean getContrlAuth(IdStringDTO id) {
		// TODO 请求无人机系统的授权接口
		return false;
	}

	public Object fly(UavFlyDTO dto, String userId) {
		String eventId = dto.getEventId();
		EventDetailVO eventDetail = eventMapper.selectEmerRescueByEventId(new IdStringDTO(eventId));
		String directionName = eventDetail.getDirectionName();
		String wayline_keyword = "上行";
		if (directionName != null && directionName.contains("下行")) {
			wayline_keyword = "下行";
		}
		String url = uavHost + "/thirdparty/api/v1/flightTask";
		// 添加进展，无人机调度成功，新增进展：无人机{无人机名称}调度成功，正飞向目标点。
		Map<String, String> body = new HashMap<>();
		body.put("longitude", dto.getLongitude());
		body.put("latitude", dto.getLatitude());
		body.put("coordinate_system", "WGS84");
		body.put("flight_type", "0");
		body.put("dock_sn", dto.getSn());
		body.put("wayline_keyword", wayline_keyword);
		Map<String, String> headParam = new HashMap<>();
		headParam.put("X-Auth-Token", stringRedisTemplate.opsForValue().get("its-job:uav_access_token"));
		String post = HttpClientUtils.postWithBody(8000, url, headParam, body);
		if (StringUtils.isBlank(post)) {
			return new ResponseVO("请求服务异常", 0);
		}
		ResponseVO jsonToBean = GsonUtils.jsonToBean(post, ResponseVO.class);
		if (jsonToBean.getCode() == 0) {
			UavFlyResponseDTO res = GsonUtils.jsonToBean(post, UavFlyResponseDTO.class);
			// 新增eventId和job_id关联
			stringRedisTemplate.opsForValue().set("its-event:uav_job_id_" + eventId, res.getData().getJob_id());
			stringRedisTemplate.opsForValue().set("its-event:uav_event_id_" + res.getData().getJob_id(), eventId);
			stringRedisTemplate.opsForValue().set("its-event:uav_sn_event_id_" + dto.getSn(), eventId);
			stringRedisTemplate.opsForValue().set("its-event:uav_sn_user_id_" + dto.getSn(), userId);
			addProgress(eventId, "无人机调度成功，无人机飞向目标点。", userId);
			res.setCode(1);
			res.setMessage("无人机调度成功，无人机飞向目标点。");
			return res;
		} else {
			return jsonToBean;
		}
	}

	public ResponseVO pause(UavFlyDTO dto, String userId) {
		String url = uavHost + "/thirdparty/api/v1/flightTask/operate";
		Map<String, String> body = new HashMap<>();
		String eventId = dto.getEventId();
		String job_id = stringRedisTemplate.opsForValue().get("its-event:uav_job_id_" + eventId);
		body.put("job_id", job_id);
		body.put("operation_type", "0");
		Map<String, String> headParam = new HashMap<>();
		headParam.put("X-Auth-Token", stringRedisTemplate.opsForValue().get("its-job:uav_access_token"));
		String post = HttpClientUtils.postWithBody(8000, url, headParam, body);
		if (StringUtils.isBlank(post)) {
			return new ResponseVO("请求服务异常", 0);
		}
		ResponseVO jsonToBean = GsonUtils.jsonToBean(post, ResponseVO.class);
		if (jsonToBean.getCode() == 0) {
			return new ResponseVO("无人机暂停成功", 1);
		} else {
			return jsonToBean;
		}
	}

	public ResponseVO resume(UavFlyDTO dto, String userId) {
		String url = uavHost + "/thirdparty/api/v1/flightTask/operate";
		Map<String, String> body = new HashMap<>();
		String eventId = dto.getEventId();
		String job_id = stringRedisTemplate.opsForValue().get("its-event:uav_job_id_" + eventId);
		body.put("job_id", job_id);
		body.put("operation_type", "2");
		Map<String, String> headParam = new HashMap<>();
		headParam.put("X-Auth-Token", stringRedisTemplate.opsForValue().get("its-job:uav_access_token"));
		String post = HttpClientUtils.postWithBody(8000, url, headParam, body);
		if (StringUtils.isBlank(post)) {
			return new ResponseVO("请求服务异常", 0);
		}
		ResponseVO jsonToBean = GsonUtils.jsonToBean(post, ResponseVO.class);
		if (jsonToBean.getCode() == 0) {
			return new ResponseVO("无人机恢复航线至目标点", 1);
		} else {
			return jsonToBean;
		}
	}

	public ResponseVO back(UavFlyDTO dto, String userId) {
		String url = uavHost + "/thirdparty/api/v1/flightTask/operate";
		Map<String, String> body = new HashMap<>();
		String eventId = dto.getEventId();
		String job_id = stringRedisTemplate.opsForValue().get("its-event:uav_job_id_" + eventId);
		body.put("job_id", job_id);
		body.put("operation_type", "1");
		Map<String, String> headParam = new HashMap<>();
		headParam.put("X-Auth-Token", stringRedisTemplate.opsForValue().get("its-job:uav_access_token"));
		String post = HttpClientUtils.postWithBody(8000, url, headParam, body);
		if (StringUtils.isBlank(post)) {
			return new ResponseVO("请求服务异常", 0);
		}
		ResponseVO jsonToBean = GsonUtils.jsonToBean(post, ResponseVO.class);
		if (jsonToBean.getCode() == 0) {
			addProgress(dto.getEventId(), "无人机返航", userId);
			return new ResponseVO("无人机返航", 1);
		} else {
			return jsonToBean;
		}
	}

	public boolean screenshot(IdStringDTO id) {
		// TODO Auto-generated method stub
		// TODO 在无人机视频播放窗口点击【截图】按钮，新增进展：无人机取证+图片。
		return false;
	}

	public Object shout(UavShoutDTO dto, String userId) {
		String name = UUID.randomUUID().toString();
		Integer play_mode = dto.getPlay_mode();
		if (play_mode == null) {
			play_mode = 0;
		}
		String sn = dto.getSn();
		
		String url = uavHost + "/thirdparty/api/v1/"+sn+"/speaker/commands";
		UavShoutPayloadDTO mode = new UavShoutPayloadDTO();
		mode.setSn(sn);
		mode.setCmd("speaker_play_mode_set");
		UavShoutPayloadDTO.Data d = new UavShoutPayloadDTO.Data();
		d.setPlay_mode(play_mode);
		d.setPsdk_index(dto.getPsdk_index());
		mode.setData(d);
		Map<String, String> headParam = new HashMap<>();
		headParam.put("X-Auth-Token", stringRedisTemplate.opsForValue().get("its-job:uav_access_token"));
		String post = HttpClientUtils.post(8000, url, headParam, GsonUtils.beanToJson(mode));
		if (StringUtils.isBlank(post)) {
			return new ResponseVO("请求服务异常", 0);
		}
		ResponseVO jsonToBean = GsonUtils.jsonToBean(post, ResponseVO.class);
		if (jsonToBean.getCode() != 0) {
			return jsonToBean;
		}

        url = uavHost + "/thirdparty/api/v1/audio_file/speak/"+dto.getSn()+"?name="+name+"&md5="+dto.getMd5();
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.add("X-Auth-Token", stringRedisTemplate.opsForValue().get("its-job:uav_access_token"));
        // 构建请求体
        MultipartFile file = dto.getFile();
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        Resource resource = file.getResource();
        body.add("file", resource);
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
		try {
//		        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
//	        ResponseEntity<byte[]> responseEntity = restTemplate.exchange(url, HttpMethod.POST, requestEntity, byte[].class);
	        ResponseEntity<byte[]> responseEntity = sslRestTemplate.exchange(url, HttpMethod.POST, requestEntity, byte[].class);
	        String ret = new String(responseEntity.getBody(), StandardCharsets.UTF_8);
	        jsonToBean = GsonUtils.jsonToBean(ret, ResponseVO.class);
			if (jsonToBean.getCode() == 0) {
				// 新增进展：无人机喊话：{喊话内容}（可能是音频可能是文本）
		        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");// 把文件按照日期进行分类
		        Date date = new Date(); 
		        String datePath = format.format(date); // 用户上传文件时指定的前缀，按月存储。
		        String filePath = "" + datePath + "/" + file.getName()+UUID.randomUUID().toString();
				try {
					AttachDTO attachDTO = OssUtils.remoteFileUpload(ossConfig, filePath, file.getInputStream(), 10000);
					if(attachDTO != null) {
			            attachDTO.setFileName(file.getName()+".pcm");
			        }
					attachDTO.setContentType(file.getContentType());
					progressMapper.addAttach(attachDTO);
					Integer id = attachDTO.getId();
					List<Integer> attachs = new ArrayList<>();
					attachs.add(id);
					addProgress(dto.getEventId(), "无人机语音喊话", attachs, userId);
				} catch (IOException e1) {
					e1.printStackTrace();
				}
				return new ResponseVO("无人机语音喊话成功", 1);
			} else {
				return jsonToBean;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return new ResponseVO("请求喊话失败", 0);
	}

	public Object shoutTxt(UavFlyDTO dto, String userId) {
		Integer play_mode = dto.getPlay_mode();
		if (play_mode == null) {
			play_mode = 0;
		}
		String sn = dto.getSn();
		
		String url = uavHost + "/thirdparty/api/v1/"+sn+"/speaker/commands";
		UavShoutPayloadDTO mode = new UavShoutPayloadDTO();
		mode.setSn(sn);
		mode.setCmd("speaker_play_mode_set");
		UavShoutPayloadDTO.Data d = new UavShoutPayloadDTO.Data();
		d.setPlay_mode(play_mode);
		d.setPsdk_index(dto.getPsdk_index());
		mode.setData(d);
		Map<String, String> headParam = new HashMap<>();
		headParam.put("X-Auth-Token", stringRedisTemplate.opsForValue().get("its-job:uav_access_token"));
		String post = HttpClientUtils.post(8000, url, headParam, GsonUtils.beanToJson(mode));
		if (StringUtils.isBlank(post)) {
			return new ResponseVO("请求服务异常", 0);
		}
		ResponseVO jsonToBean = GsonUtils.jsonToBean(post, ResponseVO.class);
		if (jsonToBean.getCode() != 0) {
			return jsonToBean;
		}

		url = uavHost + "/thirdparty/api/v1/audio_file/tts/" + sn;
		url = url + "?name=" + UUID.randomUUID().toString() + "&content=" + dto.getContent();
		post = HttpClientUtils.postWithParam(8000, url, headParam, null);
		if (StringUtils.isBlank(post)) {
			return new ResponseVO("请求服务异常", 0);
		}
		jsonToBean = GsonUtils.jsonToBean(post, ResponseVO.class);
		if (jsonToBean.getCode() == 0) {
			// 新增进展：无人机喊话：{喊话内容}（可能是音频可能是文本）
	        addProgress(dto.getEventId(), "无人机文本喊话：" + dto.getContent(), userId);
			return new ResponseVO("无人机文本喊话成功", 1);
		} else {
			return jsonToBean;
		}

	}
	
	public Object shoutStop(UavFlyDTO dto, String userId) {
		String sn = dto.getSn();
		String url = uavHost + "/thirdparty/api/v1/"+sn+"/speaker/commands";
		Map<String, String> headParam = new HashMap<>();
		headParam.put("X-Auth-Token", stringRedisTemplate.opsForValue().get("its-job:uav_access_token"));
		UavShoutPayloadDTO payload = new UavShoutPayloadDTO();
		payload.setSn(sn);
		payload.setCmd("speaker_play_stop");
		UavShoutPayloadDTO.Data data = new UavShoutPayloadDTO.Data();
		data.setPsdk_index(dto.getPsdk_index());
		payload.setData(data);
		String post = HttpClientUtils.post(8000, url, headParam, GsonUtils.beanToJson(payload));
		if (StringUtils.isBlank(post)) {
			return new ResponseVO("请求服务异常", 0);
		}
		ResponseVO jsonToBean = GsonUtils.jsonToBean(post, ResponseVO.class);
		if (jsonToBean.getCode() == 0) {
//			addProgress(dto.getEventId(), "无人机停止喊话", userId);
			return new ResponseVO("无人机停止喊话", 1);
		} else {
			return jsonToBean;
		}
		
	}

	public Object setWidgetValue(UavFlyDTO dto, String userId) {
		String url = uavHost + "/thirdparty/api/v1/"+dto.getSn()+"/psdk/widget-value/set";
		Map<String, String> body = new HashMap<>();
		body.put("index", dto.getIndex());
		body.put("value", dto.getValue());
		body.put("psdk_index", dto.getPsdk_index());
		Map<String, String> headParam = new HashMap<>();
		headParam.put("X-Auth-Token", stringRedisTemplate.opsForValue().get("its-job:uav_access_token"));
		String post = HttpClientUtils.postWithBody(8000, url, headParam, body);
		if (StringUtils.isBlank(post)) {
			return new ResponseVO("请求服务异常", 0);
		}
		ResponseVO jsonToBean = GsonUtils.jsonToBean(post, ResponseVO.class);
		if (jsonToBean.getCode() == 0) {
			String eventId = dto.getEventId();
			if (StringUtils.isBlank(eventId)) {
				return new ResponseVO("设置参数成功", 1);
			}
			if (NumberUtils.toInt(dto.getTurn_on()) == 1) {
				addProgress(eventId, "无人机开启" + ShoutControlEnum.getDescByIndex(NumberUtils.toInt(dto.getButton())), userId);
				return new ResponseVO("无人机开启" + ShoutControlEnum.getDescByIndex(NumberUtils.toInt(dto.getButton())), 1);
			} else {
				return new ResponseVO("无人机关闭" + ShoutControlEnum.getDescByIndex(NumberUtils.toInt(dto.getButton())), 1);
			}
		} else {
			return jsonToBean;
		}
	}
	
	public Object getWorkStatus(UavFlyDTO dto, String userId) {
		String url = uavHost + "/thirdparty/api/v1/flightTask/latestJob/"+dto.getSn();
		Map<String, String> headParam = new HashMap<>();
		headParam.put("X-Auth-Token", stringRedisTemplate.opsForValue().get("its-job:uav_access_token"));
		String get = HttpClientUtils.get(url, headParam, 8000);
		if (StringUtils.isBlank(get)) {
			return new ResponseVO("请求服务异常", 0);
		}
		ResponseVO jsonToBean = GsonUtils.jsonToBean(get, ResponseVO.class);
		if (jsonToBean.getCode() == 0) {
			UavFlyResponseDTO ret = GsonUtils.jsonToBean(get, UavFlyResponseDTO.class);
			String drone_flight_to_target_status = ret.getData().getDrone_flight_to_target_status();
			String eventId = stringRedisTemplate.opsForValue().get("its-event:uav_sn_event_id_" + dto.getSn());
			synchronized (lock) {
				String reach = stringRedisTemplate.opsForValue().get("its-event:uav_sn_reach_" + eventId);
				if ("2".equals(drone_flight_to_target_status) && !"reach".equals(reach)) {
					userId = stringRedisTemplate.opsForValue().get("its-event:uav_sn_user_id_" + dto.getSn());
					boolean success = addProgress(eventId, "无人机已到达现场", userId);
					if (success) {
						stringRedisTemplate.opsForValue().set("its-event:uav_sn_reach_" + eventId, "reach");
					}
				}
			}
			ret.setCode(1);
			return ret;
		} else {
			return jsonToBean;
		}
	}

	private boolean addProgress(String eventId, String content, String userId) {
		return addProgress(eventId, content, null, userId);
	}

	/**
	 * @Desc 新增事件进展，并且支持附件
	 */
	private boolean addProgress(String eventId, String content, List<Integer> attachs, String userId) {
		ProgressDTO addProgressDTO = new ProgressDTO();// 人员变化留痕进展
		long serverTime = System.currentTimeMillis() / 1000;
		addProgressDTO.setCreateTime(serverTime);
		addProgressDTO.setOccurTime(serverTime);
		addProgressDTO.setProgressDesc(content);
		addProgressDTO.setEventId(eventId);
		addProgressDTO.setCreateUserId(userId);
		if (!CollectionUtils.isEmpty(attachs)) {
			addProgressDTO.setAttachs(attachs);
		}
		return progressService.add(addProgressDTO);
	}

	public List<UavTipVO> selectTipList() {
		return eventMapper.selectTipList();
	}

}
