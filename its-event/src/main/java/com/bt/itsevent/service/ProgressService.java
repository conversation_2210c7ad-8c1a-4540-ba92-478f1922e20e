package com.bt.itsevent.service;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.bt.itscore.config.OssConfig;
import com.bt.itscore.domain.dto.AttachDTO;
import com.bt.itscore.domain.dto.EventProgressMessageDTO;
import com.bt.itscore.domain.dto.IdIntegerDTO;
import com.bt.itscore.domain.dto.IdStringDTO;
import com.bt.itscore.domain.dto.SmsTemplateDTO;
import com.bt.itscore.domain.vo.ResponseVO;
import com.bt.itscore.domain.vo.UserSimpleVO;
import com.bt.itscore.enums.EmerPlanLevelEnum;
import com.bt.itscore.exception.ArgumentException;
import com.bt.itscore.utils.AliyunSmsUtils;
import com.bt.itscore.utils.EncodeUtils;
import com.bt.itscore.utils.OssUtils;
import com.bt.itscore.utils.TimeUtils;
import com.bt.itsevent.domain.dto.ApproveDTO;
import com.bt.itsevent.domain.dto.ComfirmPlanDTO;
import com.bt.itsevent.domain.dto.EventCountDTO;
import com.bt.itsevent.domain.dto.EventDealStatusDTO;
import com.bt.itsevent.domain.dto.EventProcessSmsMessageDTO;
import com.bt.itsevent.domain.dto.EventProgressDraftDTO;
import com.bt.itsevent.domain.dto.EventProgressSceneDTO;
import com.bt.itsevent.domain.dto.EventWorkOrderDTO;
import com.bt.itsevent.domain.dto.ProgressAttachDTO;
import com.bt.itsevent.domain.dto.ProgressDTO;
import com.bt.itsevent.domain.dto.ProgressSceneDTO;
import com.bt.itsevent.domain.dto.ProgressUserDTO;
import com.bt.itsevent.domain.dto.RemoveObstaclesDTO;
import com.bt.itsevent.domain.dto.RobotMobileDTO;
import com.bt.itsevent.domain.dto.UserDTO;
import com.bt.itsevent.domain.vo.EmerPlanVO;
import com.bt.itsevent.domain.vo.EmerResuceVO;
import com.bt.itsevent.domain.vo.EventAttachVO;
import com.bt.itsevent.domain.vo.EventDetailVO;
import com.bt.itsevent.domain.vo.EventEmerUserVO;
import com.bt.itsevent.domain.vo.EventProgressDraftVO;
import com.bt.itsevent.domain.vo.GroupProgress2VO;
import com.bt.itsevent.domain.vo.GroupProgressVO;
import com.bt.itsevent.domain.vo.ProgressAttachVO;
import com.bt.itsevent.domain.vo.ProgressRemindMeVO;
import com.bt.itsevent.domain.vo.ProgressSceneVO;
import com.bt.itsevent.domain.vo.ProgressUserVO;
import com.bt.itsevent.domain.vo.ProgressVO;
import com.bt.itsevent.feign.FeignClient;
import com.bt.itsevent.feign.ItsWebSocketFeignClient;
import com.bt.itsevent.mapper.EventMapper;
import com.bt.itsevent.mapper.ProgressMapper;
import com.bt.itsevent.mapper.ProgressSmsMessageMapper;
import com.bt.itsevent.utils.AssignJobs;
import com.bt.itsevent.utils.AudioConversionUtils;
import com.bt.itsevent.utils.GenerateWeiXinUrlUtils;
import com.bt.itsevent.utils.SpeechSoundsUtils;
@RefreshScope
@Service("progressService")
public class ProgressService {
	private final static Logger LOGGER = LoggerFactory.getLogger(ProgressService.class);
	
	@Autowired
	private ProgressMapper progressMapper;

	@Autowired
	private EventMapper eventMapper;
	
	@Autowired
	private OssConfig ossConfig;
	
	@Autowired
	private EventWorkOrderService eventWorkOrderService;

	@Autowired
	private EventService eventService;

    @Autowired
    private ItsWebSocketFeignClient itsWebSocketFeignClient;

    @Autowired
    FeignClient feignClient;

	@Autowired
	private ProgressSmsMessageMapper progressSmsMessageMapper;
	@Autowired
	StringRedisTemplate stringRedisTemplate;

	@Value("${event.progress.attach.path}")
	private String progressAttachPath;
//	@Value("${aliyun-oss.access-key.id}")
//	private String accessKeyId;
//	@Value("${aliyun-oss.access-key.secret}")
//	private String accessKeySecret;
//	@Value("${aliyun-oss.endpoint}")
//	private String endpoint;
//	@Value("${aliyun-oss.bucket-name}")
//	private String bucketName;
//	@Value("${aliyun-oss.callback-url}")
//	private String callbackUrl;
	@Value("${event.initial.report}")
	private String eventInitialReport;
	@Value("${event.resubmit.approved}")
	private String eventResubmitApproved;
	@Value("${event.resubmit.disagree}")
	private String eventResubmitDisagree;
	@Value("${event.finalreport.approved}")
	private String eventFinalreportApproved;
	@Value("${event.finalreport.disagree}")
	private String eventFinalreportDisagree;
	@Value("${ffmpeg.path}")
	private String ffmpegPath;

	@Transactional
	public boolean add(ProgressDTO progressDTO) {
	    if(StringUtils.isBlank(progressDTO.getProgressDesc()) && CollectionUtils.isEmpty(progressDTO.getAttachs())) {
	        throw new ArgumentException("进展描述和附件至少一个不能为空！");
	    }
		return addProgressAndUpdateAttach(progressDTO, 1);
	}

	@Transactional
	public boolean addUnblock(ProgressDTO progressDTO) {
	    if(CollectionUtils.isEmpty(progressDTO.getAttachs())) {
	        throw new ArgumentException("附件不能为空！");
	    }
	    String progressDesc = progressDTO.getProgressDesc();
		if (StringUtils.isBlank(progressDesc)) {
			progressDTO.setProgressDesc("阻断已疏通。");
		} else {
			progressDTO.setProgressDesc("阻断已疏通。备注：" + progressDesc);
		}
		int progressId = addProgressAndAttach(progressDTO, 1);
		EventDealStatusDTO eventDealStatusDTO = new EventDealStatusDTO();
		eventDealStatusDTO.setEventId(progressDTO.getEventId());
		eventDealStatusDTO.setUnblockTime(System.currentTimeMillis()/1000);
		eventDealStatusDTO.setUnblockProgressId(progressId);
		eventMapper.updateUnblock(eventDealStatusDTO);
		return progressId > 0;
	}
	private int addProgressAndAttach(ProgressDTO dto, int theme) {
		dto.setTheme(theme);
		Integer id = addProgress(dto);
		LOGGER.info("progressId:{}", id);
		if(id != null && id > 0) {
			if(dto.getAttachs().size() > 0){
				progressMapper.updateProgressAttach(dto);// 更新进展附件 
			}
			if(theme == 1) {
				// 更新主题theme字段
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("id", id);
				progressMapper.updateProgressTheme(map);
			}
			return id;
		}
		return 0;
	}

	@Transactional
	public boolean reply(ProgressDTO progressDTO) {
		return addProgressAndUpdateAttach(progressDTO, 0);
	}

	private boolean addProgressAndUpdateAttach(ProgressDTO dto, int theme) {
		dto.setTheme(theme);
		Integer id = addProgress(dto);
		LOGGER.info("progressId:{}", id);
		if(id != null && id > 0) {
			if(dto.getAttachs().size() > 0){
				progressMapper.updateProgressAttach(dto);// 更新进展附件 
			}
			//添加 （进展-用户）关联信息
			if(dto.getAtUsers().size() > 0) {
				progressMapper.batchAddProgressUser(dto);
			} else {
				if(theme == 1) {
					// 更新主题theme字段
					Map<String, Object> map = new HashMap<String, Object>();
					map.put("id", id);
					return progressMapper.updateProgressTheme(map) > 0;
				}
			}
			return true;
		}
		return false;
	}

	private Integer addProgress(ProgressDTO dto) {
		long time = System.currentTimeMillis()/1000;
		if(dto.getOccurTime() == null) {
			dto.setOccurTime(time);
		}
		dto.setCreateTime(time);
		if(dto.getTheme() == null) {
			dto.setTheme(0);
		}
		if(dto.getPid() == null) {
			dto.setPid(0);
		}
		
//		dto.setPid(dto.getTheme() == 0 ? dto.getPid() : 0);
		boolean success = progressMapper.add(dto) > 0;
		if(success) {
			// websocket推送消息
			LOGGER.info("---进展推送消息：" + dto.getProgressDesc());
			// 查询初报的人员
			IdStringDTO eventIdDTO = new IdStringDTO(dto.getEventId());
			List<EventEmerUserVO> emerUsers = eventMapper.selectEmerUserById(eventIdDTO);
			EventDetailVO eventVO = eventMapper.selectEmerRescueByEventId(eventIdDTO);
			ProgressVO initReportVO = progressMapper.selectInitReport(eventIdDTO);
			List<String> userIds = new ArrayList<String>();
			for (EventEmerUserVO tmp : emerUsers) {
				userIds.add(tmp.getUserId());
			}
			if(eventVO != null && eventVO.getRecordManId() != null) {
				userIds.add(eventVO.getRecordManId());
			}
			if(initReportVO != null) {
				String initReportUserId = initReportVO.getCreateUserId();
				if(initReportUserId != null && !userIds.contains(initReportUserId)) {
					userIds.add(initReportUserId);
				}
			}
			List<ProgressUserDTO> atUsers = dto.getAtUsers();
			for (ProgressUserDTO tmp : atUsers) {
				// 进展参数带过来的用户
				String atUserId = tmp.getUserId();
				if(atUserId != null && !userIds.contains(atUserId)) {
					userIds.add(atUserId);
				}
			}
			// 去重
			Set<String> set = new HashSet<>();
			set.addAll(userIds);
			userIds.clear();
			userIds.addAll(set);
			
			new Thread(new Runnable() {
				@Override
				public void run() {
					try {
						Thread.sleep(1000L);//TODO: 后期使用事务事件监听优化
					} catch (InterruptedException e) {
						LOGGER.error("推送进展消息休眠中断异常");
					}
					EventProgressMessageDTO eventProgressMessageDTO = new EventProgressMessageDTO();
					eventProgressMessageDTO.setWebsocketType("addEventProgress");
					eventProgressMessageDTO.setEventId(dto.getEventId());
					eventProgressMessageDTO.setId(dto.getId());
					eventProgressMessageDTO.setUserIds(userIds);
					itsWebSocketFeignClient.pushEventProgress(eventProgressMessageDTO);
				}
			}).start();
			return dto.getId();
		}
		return 0;
	}
    @Transactional
	public ProgressDTO uploadSoundAttach(MultipartFile file) {
		ProgressDTO progressDTO = new ProgressDTO();
		Calendar calendar = new GregorianCalendar();
		String diskDirectory = calendar.get(Calendar.YEAR) + "_" + (calendar.get(Calendar.MONTH)+1);
		String basePath = progressAttachPath+File.separator+diskDirectory+"/";
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
		Date date = new Date();
		String dir = format.format(date) + "/"; // 用户上传文件时指定的前缀，按月存储。
		if(file == null){
			return null;
		}
		AttachDTO dto = new AttachDTO();
		InputStream in = null;
		try {
			in = file.getInputStream();
			String digest = EncodeUtils.getDigest(in, "SHA-256");
			String fileName = file.getOriginalFilename();
			String diskFileName = System.currentTimeMillis() + "_" + UUID.randomUUID().toString().replaceAll("-","");
			long fileSize = file.getSize();
			String contentType = file.getContentType();
			if("video/mp4".equals(contentType) || "video/mov".equals(contentType) || fileName.contains(".amr") || "audio/mp3".equals(contentType)){
				diskFileName = diskFileName + "." + contentType.substring(contentType.length()-3);
			}
			// 将音频文件amr保存到本地硬盘上
			FileUtils.writeByteArrayToFile(new File(basePath + diskFileName), file.getBytes());
			//音频格式转换
			String wavName = diskFileName.substring(0,diskFileName.lastIndexOf("."))+".wav";
			System.out.println("保存wav文件："+basePath+wavName);
			String pcmName = diskFileName.substring(0,diskFileName.lastIndexOf("."))+".pcm";
			System.out.println("保存pcm文件："+basePath+pcmName);
			AudioConversionUtils.changeAmrToPcm(ffmpegPath,basePath + diskFileName,basePath + pcmName);
			AudioConversionUtils.changeAmrToWaV(ffmpegPath,basePath + diskFileName,basePath + wavName);
			//解析
			File file1 = new File (basePath + pcmName);
			if(!file1.exists()){
				System.out.println("pcm文件不存在"+basePath + pcmName);
			}
			File file2 = new File (basePath + wavName);
			if(!file2.exists()){
				System.out.println("wav文件不存在"+basePath + wavName);
			}
			try {
				String result = new SpeechSoundsUtils().voice2words(basePath + pcmName);
				progressDTO.setProgressDesc(result) ;
//				System.out.println("path::::"+System.getProperty("java.library.path"));
				System.out.println("解析结果：："+result);
			} catch (Exception e) {
				e.printStackTrace();
			}
			//oss上传
            OssUtils.localFileUpload(ossConfig,new File(basePath + wavName),dir+wavName);
			//附件存进表
			dto.setFileName(fileName);
			dto.setDiskFileName(dir+wavName);
			dto.setDigest(digest);
			dto.setFileSize(fileSize);
			dto.setDiskDirectory(diskDirectory);
			dto.setContentType(contentType);
			dto.setCreateTime(System.currentTimeMillis()/1000);
			// 保存附件表记录
			progressMapper.addAttach(dto);
			List<Integer> attachs = new ArrayList<>();
			attachs.add(dto.getId());
			progressDTO.setAttachs(attachs);
		} catch (Exception e) {
			LOGGER.info(e.getMessage());
			return progressDTO;
		} finally {
			try {
				if(in != null) {
					in.close();
				}
			} catch (Exception e) {
				LOGGER.info(e.getMessage());
			}
		}

		return progressDTO;
	}

	public List<ProgressAttachVO> uploadAttach(List<MultipartFile> files) {
		LOGGER.info("progressAttachPath:{}", progressAttachPath);
		List<ProgressAttachVO> list = new ArrayList<ProgressAttachVO>();
		if(files == null){
			return list;
		}
		AttachDTO dto = new AttachDTO();
    	for(MultipartFile file : files) {
    		InputStream in = null;
			try {
				in = file.getInputStream();

	    		String digest = EncodeUtils.getDigest(in, "SHA-256");
		        Map<String, Object> map = new HashMap<>();
		        String fileName = file.getOriginalFilename();
		        String diskFileName = System.currentTimeMillis() + "_" + UUID.randomUUID().toString().replaceAll("-","");
		        long fileSize = file.getSize();
		        String contentType = file.getContentType();
		        if("video/mp4".equals(contentType) || "video/mov".equals(contentType) || fileName.contains(".amr") || "audio/mp3".equals(contentType)){
		        	diskFileName = diskFileName + "." + contentType.substring(contentType.length()-3);
		        }
		        Calendar calendar = new GregorianCalendar();
		        String diskDirectory = calendar.get(Calendar.YEAR) + "_" + (calendar.get(Calendar.MONTH)+1);
		        // 数据库查询 是否存在该 SHA-256 散列值
		        map.put("digest", digest);
		        ProgressAttachVO vo = progressMapper.selectByDigest(map);
		        if(vo != null) { // 存在
		        	LOGGER.info("file exits");
		        	diskFileName = vo.getDiskFileName();
    		        fileSize = vo.getFileSize();
    		        contentType = vo.getContentType();
    		        diskDirectory = vo.getDiskDirectory();
		        } else {
		        	System.out.println("no exits");
		        	vo = new ProgressAttachVO();
		        	// 保存文件到服务器硬盘上.
		        	FileUtils.writeByteArrayToFile(new File(progressAttachPath+File.separator+diskDirectory+File.separator+diskFileName), file.getBytes());
		        }
		        dto.setFileName(fileName);
		        dto.setDiskFileName(diskFileName);
		        dto.setDigest(digest);
		        dto.setFileSize(fileSize);
		        dto.setDiskDirectory(diskDirectory);
		        dto.setContentType(contentType);
		        dto.setCreateTime(System.currentTimeMillis()/1000);
		        // 保存附件表记录
		        progressMapper.addAttach(dto);
		        vo.setId(dto.getId());
    		    list.add(vo);
		    } catch (Exception e) {
		    	LOGGER.info(e.getMessage());
		        return list;
		    } finally {
		        try {
		        	if(in != null) {
		        		in.close();
		        	}
		        } catch (Exception e) {
		        	LOGGER.info(e.getMessage());
		        }
		    }
		}
		return list;
	}

	public List<GroupProgressVO> selectListWithGroup(ProgressDTO progressDTO) {
		List<GroupProgressVO> list = new ArrayList<GroupProgressVO>();
		List<GroupProgressVO> groupProgress = progressMapper.selectListWithGroup(progressDTO);
		for (int i = 0; i < groupProgress.size(); i++) {
			GroupProgressVO vo = groupProgress.get(i);
//			if(vo.getPid() == null || vo.getPid() == 0) { // 主题
				list.add(vo);
//			}
		}
		for(GroupProgressVO vo : list) {
			List<GroupProgress2VO> list1 = new ArrayList<GroupProgress2VO>();
			vo.setList(list1);
			for (int i = 0; i < groupProgress.size(); i++) {
				GroupProgressVO tmp = groupProgress.get(i);
				GroupProgress2VO tmp2 = new GroupProgress2VO();
				BeanUtils.copyProperties(tmp, tmp2);
				if(vo.getTheme() != null && tmp.getTheme() != null && tmp.getPid() != null && tmp.getPid().intValue() == vo.getId().intValue() && vo.getId().intValue() != tmp.getId().intValue()) { // 主题
					list1.add(tmp2);
				}
			}
		}
		int size = list.size();
		// 进展条数大于0，在事件初报后面增加一条事件附件的进展返回
		if(size > 0) {
		    // 查询事件附件
		    List<EventAttachVO> eventAttachs = eventMapper.selectEventAttachs(new IdStringDTO(list.get(0).getEventId()));
		    if(!CollectionUtils.isEmpty(eventAttachs)) {
		        List<ProgressAttachVO> attachs = new ArrayList<>();
	            for (EventAttachVO tmp : eventAttachs) {
	                ProgressAttachVO attach = new ProgressAttachVO();
	                attach.setId(tmp.getId());
	                attach.setFileName(tmp.getFileName());
	                attach.setDiskFileName(tmp.getDiskFileName());
	                attach.setFileSize(tmp.getFileSize());
	                attach.setDiskDirectory(tmp.getDiskDirectory());
	                attach.setContentType(tmp.getContentType());
	                attachs.add(attach);
	            }
	            // 查找初报进展的下标
	            for(int i = size-1; i >= 0; i--) {
	            	GroupProgressVO progressVO = list.get(i);
	            	Integer cardType = progressVO.getCardType();
	            	if(cardType != null && cardType.intValue() == 1) {
	    	            GroupProgressVO vo = new GroupProgressVO();
	    	            vo.setCardType(0);
	    	            vo.setCompanyName(progressVO.getCompanyName());
	    	            vo.setCreateTime(progressVO.getCreateTime());
	    	            vo.setOccurTime(progressVO.getOccurTime());
	    	            vo.setCreateUserId(progressVO.getCreateUserId());
	    	            vo.setCreateUserName(progressVO.getCreateUserName());
						vo.setCreateUserPhone(progressVO.getCreateUserPhone());
	    	            vo.setEventId(progressVO.getEventId());
	    	            vo.setOrgName(progressVO.getOrgName());
	    	            vo.setTheme(1);
	    	            vo.setLng(progressVO.getLng());
						vo.setLat(progressVO.getLat());
	    	            vo.setAtUsers(new ArrayList<>());
	    	            vo.setAttachs(attachs);
	    	            list.add(i, vo);
	            		break;
	            	}
	            	
	            }

		    }

		}
		return list;
	}
	
	final static String PROGRESS_URL = "its-event/progress/ossCallback"; 
	public Map<String, String> ossUpload(AttachDTO dto) {
		Map<String, String> map = OssUtils.getSignature(ossConfig, dto, PROGRESS_URL);
		return map;
	}

	public AttachDTO ossCallback(HttpServletRequest request) throws ServletException, IOException {
		AttachDTO dto = OssUtils.callback(request.getInputStream(), NumberUtils.toInt(request.getHeader("content-length")));
        // 保存附件表记录
        progressMapper.addAttach(dto);
        return dto;
	}

	public boolean ossDelete(AttachDTO dto) {
		//删除附件记录
		boolean ret = progressMapper.deleteAttach(dto) >0;
		List<String> keys = new ArrayList<String>();
		keys.add(dto.getDiskFileName());
		OssUtils.batchDelete(ossConfig, keys);
		return ret;
	}
	public List<AttachDTO> ossUrl(List<AttachDTO> dtos) {
		return OssUtils.getUrl(ossConfig, dtos);
	}

//	@Transactional
	public int confirm(IdIntegerDTO dto, String userId) {
		ProgressVO vo = progressMapper.selectById(dto);
		String eventId = vo.getEventId();
		Integer progressId = dto.getId();
		initReportConfirm(eventId, progressId, userId);
		return 1;
	}

	public int confirmByEventId(IdStringDTO dto, String userId) {
		ProgressVO initReport = progressMapper.selectInitReport(dto);
		String eventId = dto.getId();
		Integer progressId = initReport.getId();
		initReportConfirm(eventId, progressId, userId);
		return 1;
	}

	public void initReportConfirm(String eventId, Integer progressId, String userId) {
		Map<String, Object> map = new HashMap<>();
		EmerResuceVO eventVO = progressMapper.selectEventById(new IdIntegerDTO(progressId));
		if(eventVO.getEventStatus() == null || eventVO.getEventStatus() < 4) {
			// 更新时事件状态event_status=4
			map.put("eventStatus", 4);
			map.put("id", eventId);
			eventMapper.updateEventStatus(map);
		}
		
		Long serverTime = System.currentTimeMillis()/1000;
		ProgressDTO progressDTO = new ProgressDTO();
		progressDTO.setCreateUserId(userId);
		progressDTO.setOccurTime(serverTime);
		progressDTO.setEventId(eventId);
		progressDTO.setProgressDesc(eventInitialReport);
		progressDTO.setPid(0);
		progressDTO.setCardPass(5);
		progressDTO.setTheme(0);
		addProgress(progressDTO);

//		progressMapper.add(progressDTO);
		// 进展@用户的记录需要设置cas_pass=1
		ProgressUserDTO progressUserDTO = new ProgressUserDTO();
		progressUserDTO.setUserId(userId);
		progressUserDTO.setEventProgressId(progressId);
		progressUserDTO.setCardPass(1);
		progressMapper.updateProgressUserCardPass(progressUserDTO);
		map.put("id", progressId);
		map.put("updateTime", serverTime);
		progressMapper.confirm(map);
		// 更新event_deal_status的deal_response和deal_response_time
		EventDealStatusDTO eventDealStatusDTO = new EventDealStatusDTO();
		eventDealStatusDTO.setEventId(eventVO.getId());
		eventDealStatusDTO.setDealResponse(1);
		eventDealStatusDTO.setDealResponseTime(serverTime);
		eventMapper.updateDealResponse(eventDealStatusDTO);
	}

	/** 续报或终报审批（同意或不同意） */
	public int approve(ApproveDTO dto, String userId) {
		// 查询事件ID
		IdIntegerDTO idIntegerDTO = new IdIntegerDTO(dto.getId());
		ProgressVO vo = progressMapper.selectById(idIntegerDTO);
		
		Map<String, Object> map = new HashMap<>();
		map.put("id", dto.getId());
		map.put("cardPass", dto.getCardPass());
		ProgressDTO progressDTO = new ProgressDTO();
		progressDTO.setCreateUserId(userId);
		progressDTO.setOccurTime(System.currentTimeMillis()/1000);
		progressDTO.setEventId(vo.getEventId());
		Integer cardType = vo.getCardType();
		if(cardType != null && cardType == 5) {
			if(dto.getCardPass() == 1) {
				progressDTO.setProgressDesc(eventResubmitApproved);
			} else {
				progressDTO.setProgressDesc(eventResubmitDisagree);
			}
		} else if(cardType != null && cardType == 10) {
			if(dto.getCardPass() == 1) {
				progressDTO.setProgressDesc(eventFinalreportApproved);
			} else {
				progressDTO.setProgressDesc(eventFinalreportDisagree);
			}
		}
		addProgress(progressDTO);
//		progressMapper.add(progressDTO);
		// 进展@用户的记录需要设置cas_pass=1
		ProgressUserDTO progressUserDTO = new ProgressUserDTO();
		progressUserDTO.setUserId(userId);
		progressUserDTO.setEventProgressId(dto.getId());
		progressUserDTO.setCardPass(dto.getCardPass());
		progressMapper.updateProgressUserCardPass(progressUserDTO);
		return progressMapper.approve(map);
	}

	/** 申请排障（同意或不同意） */
	public int approveObstacles(ApproveDTO dto, String userId) {
		// 查询事件ID
		IdIntegerDTO idIntegerDTO = new IdIntegerDTO(dto.getId());
		EmerResuceVO vo = progressMapper.selectEventById(idIntegerDTO);

		Map<String, Object> map = new HashMap<>();
		map.put("id", dto.getId());
		map.put("cardPass", dto.getCardPass());
		ProgressDTO progressDTO = new ProgressDTO();
		progressDTO.setCreateUserId(userId);
		progressDTO.setOccurTime(System.currentTimeMillis()/1000);
		progressDTO.setEventId(vo.getId());
		if(dto.getCardPass() != null && dto.getCardPass() == 1) {
			dto.setProgressDesc("已同意排障申请。");
		} else {
			dto.setProgressDesc("不同意排障申请。");
		}
		progressDTO.setProgressDesc(dto.getProgressDesc());
		if(dto.getCardPass() != null && dto.getCardPass() == 1) {
			// 更新事件中的car_order为0，排障派单情况
			RemoveObstaclesDTO obstaclesDTO = new RemoveObstaclesDTO();
			obstaclesDTO.setCarOrder(0);
			obstaclesDTO.setId(dto.getEventId());
			eventMapper.updateCarOrder(obstaclesDTO);
			// 生成排障工单
			EventWorkOrderDTO eventWorkOrderDTO = new EventWorkOrderDTO();
			eventWorkOrderDTO.setEventId(vo.getId());
			eventWorkOrderDTO.setAgencyName(vo.getOrgName());// 责任单位名称
			eventWorkOrderDTO.setClient(vo.getReportMan());// 报障人
			eventWorkOrderDTO.setClientPlate(vo.getCarPlate());// 报障人车牌
			eventWorkOrderDTO.setTelephone(vo.getReportManTel());//报障人电话
			eventWorkOrderService.add(eventWorkOrderDTO);
		}
		
		addProgress(progressDTO);
//		progressMapper.add(progressDTO);
		// 进展@用户的记录需要设置cas_pass=1
		ProgressUserDTO progressUserDTO = new ProgressUserDTO();
		progressUserDTO.setUserId(userId);
		progressUserDTO.setEventProgressId(dto.getId());
		progressUserDTO.setCardPass(dto.getCardPass());
		progressMapper.updateProgressUserCardPass(progressUserDTO);
		return progressMapper.approve(map);
	}

	@Transactional
	public int cancelObstacles(ApproveDTO dto, String userId) {
		// 查询事件ID
		IdIntegerDTO idIntegerDTO = new IdIntegerDTO(dto.getId());
		ProgressVO vo = progressMapper.selectById(idIntegerDTO);
		ProgressDTO progressDTO = new ProgressDTO();
		progressDTO.setCreateUserId(userId);
		progressDTO.setOccurTime(System.currentTimeMillis()/1000);
		progressDTO.setEventId(vo.getEventId());
		progressDTO.setProgressDesc("撤销了申请排障。");
		progressDTO.setTheme(0);
		addProgress(progressDTO);
//		progressMapper.add(progressDTO);
		return progressMapper.delete(idIntegerDTO);
	}

	public ProgressVO selectInitReport(IdStringDTO dto) {
		return progressMapper.selectInitReport(dto);
	}

	public  int deleteProgressUser(List<ProgressUserDTO> progressUsers) {
		return progressMapper.deleteProgressUser(progressUsers);
	}

	public List<ProgressUserVO> selectConfirmIRUser(IdIntegerDTO dto) {
		return progressMapper.selectConfirmIRUser(dto);
	}

	public int deletePlanUser(ProgressDTO dto) {
		return progressMapper.deletePlanUser(dto);
	}

	public int addAtInitReportUser(ProgressDTO dto) {
		return progressMapper.batchAddProgressUser(dto);
	}

    public List<ProgressRemindMeVO> selectProgressRemindMe(EventCountDTO dto){
		return progressMapper.selectProgressRemindMe(dto);
	}

	public boolean updateReadStatus (ProgressUserDTO dto){
       return progressMapper.updateReadStatus(dto) > 0;
	}
	public GroupProgressVO selectDetailById(IdIntegerDTO dto) {
		return progressMapper.selectDetailById(dto);
	}

	public ProgressVO selectById(IdIntegerDTO dto){
		return  progressMapper.selectById(dto);
	}

	public ResponseVO addByCall(ProgressDTO dto) {
		int progressId = addProgress(dto);
		if(progressId > 0) {
			return new ResponseVO(1, String.valueOf(progressId));
		}
		return new ResponseVO(0, String.valueOf(progressId));
	}

	@Transactional
	public boolean addScene(EventProgressSceneDTO dto) {
		List<ProgressSceneDTO> scenes = dto.getScenes();
		// 先批量删除，后新增现场处置列表
		progressMapper.deleteScene(dto);
		if(CollectionUtils.isEmpty(scenes)) {
//			throw new ArgumentException("现场处置列表不能为空！");
			return true;
		}
		long createTime = System.currentTimeMillis() / 1000;
		for (ProgressSceneDTO scene : scenes) {
			scene.setId(UUID.randomUUID().toString());
			scene.setCreateTime(createTime);
		}
		int rows = progressMapper.addScene(dto);
		// 批量修改附件progress_scene_id
		for (int i = scenes.size()-1; i >= 0; i--) {
			if(CollectionUtils.isEmpty(scenes.get(i).getAttachs())) {
				scenes.remove(i);
			}
		}
		String createUserId = dto.getCreateUserId();
		if(scenes.size() > 0) {
		    // 循环附件列表
		    for (ProgressSceneDTO scene : scenes) {
		        List<ProgressAttachDTO> attachs = scene.getAttachs();
                for (ProgressAttachDTO attach : attachs) {
                    if(StringUtils.isBlank(attach.getUserId())) {
                        attach.setUserId(createUserId);
                    }
                }
            }
			progressMapper.batchUpdateSceneAttach(dto);
		}

		String eventId = dto.getId();
		boolean ret = rows > 0;
		if(ret) {
			IdStringDTO eventIdDTO = new IdStringDTO(eventId);
			EventDetailVO eventVO = eventMapper.selectEmerRescueByEventId(eventIdDTO);
			Integer eventStatus = eventVO.getEventStatus();
			if(eventStatus == null || eventStatus < 6) {
				ProgressDTO progressDTO = new ProgressDTO();
				progressDTO.setEventId(eventId);
		        progressDTO.setCreateUserId(createUserId);
		        progressDTO.setProgressDesc("已到达");
		        progressDTO.setCardPass(20);
		        progressDTO.setTheme(1);
		        progressDTO.setOccurTime(createTime);
		        add(progressDTO);
				// 修改事件状态为6（到达）
				Map<String, Object> map = new HashMap<>();
				map.put("id", eventId);
				map.put("eventStatus", 6);
				eventMapper.updateEventStatus(map);
			} else {
				List<EventEmerUserVO> emerUsers = eventMapper.selectEmerUserById(eventIdDTO);
				ProgressVO initReportVO = progressMapper.selectInitReport(eventIdDTO);
				List<String> userIds = new ArrayList<String>();
				for (EventEmerUserVO tmp : emerUsers) {
					userIds.add(tmp.getUserId());
				}
				if(eventVO != null && eventVO.getRecordManId() != null) {
					userIds.add(eventVO.getRecordManId());
				}
				if(initReportVO != null) {
					String initReportUserId = initReportVO.getCreateUserId();
					if(initReportUserId != null && !userIds.contains(initReportUserId)) {
						userIds.add(initReportUserId);
					}
				}
				new Thread(new Runnable() {
					@Override
					public void run() {
						Set<String> set = new HashSet<>(userIds);
						EventProgressMessageDTO eventProgressMessageDTO = new EventProgressMessageDTO();
						eventProgressMessageDTO.setWebsocketType("addEventProgress");
						eventProgressMessageDTO.setEventId(eventId);
						eventProgressMessageDTO.setUserIds(new ArrayList<>(set));
						itsWebSocketFeignClient.pushEventProgress(eventProgressMessageDTO);
					}
				}).start();
			}

			// 更新event_deal_status的现场处置上传图片标识
			// 新增-事件执行预案相关状态
			EventDealStatusDTO eventDealStatusDTO = new EventDealStatusDTO();
			eventDealStatusDTO.setEventId(eventVO.getId());
			eventDealStatusDTO.setSceneUpload(1);
			eventMapper.updateSceneUpload(eventDealStatusDTO);
		}

		return ret;
	}

	public List<ProgressSceneVO> selectScene(IdStringDTO dto) {
		List<ProgressSceneVO> scenes = progressMapper.selectScene(dto);
		List<ProgressSceneVO> ret = new ArrayList<>();
		if (CollectionUtils.isEmpty(scenes)) {
			for (int i = 0; i < 5; i++) {
				ProgressSceneVO vo = new ProgressSceneVO();
				vo.setStep((i + 1) * 10);
				vo.setAttachs(new ArrayList<>());
				ret.add(vo);
			}
		} else {
			// 补全
			int k = 0;
			for (int i = 0; i < 5; i++) {
				for (int j = k; j < scenes.size();) {
					ProgressSceneVO tmp = scenes.get(j);
					if (tmp.getStep().intValue() == (i + 1) * 10) {
						k = j + 1;
						ret.add(tmp);
					} else {
						ProgressSceneVO vo = new ProgressSceneVO();
						vo.setStep((i + 1) * 10);
						vo.setAttachs(new ArrayList<>());
						ret.add(vo);
						k = j;
					}
					break;
				}
			}
		}

		return ret;
	}

	public List<ProgressUserVO> selectSmsSendById(IdIntegerDTO dto) {
		List<ProgressUserVO> progressUserVOList = progressMapper.selectSmsSendById(dto);
		if (!CollectionUtils.isEmpty(progressUserVOList)) {
			progressUserVOList.forEach(item -> item.setForceRemind(item.getReferenceId() != null ? 1 : 0));
		}
		return progressUserVOList;
	}

	@Transactional
	public boolean resendInitReportSmsById(IdIntegerDTO dto, String userId) {
		List<ProgressUserVO> list = progressMapper.selectSmsSendFailById(dto);
		if(CollectionUtils.isEmpty(list)) {
			return false;
		}
		IdStringDTO eventIdDTO = new IdStringDTO(list.get(0).getEventId());
		EventDetailVO eventDetailVO = eventMapper.selectEmerRescueByEventId(eventIdDTO);
		 String time = TimeUtils.getTimeString(TimeUtils.DATE_TIME_2, eventDetailVO.getReportTime() * 1000);
		if (StringUtils.isNotBlank(eventDetailVO.getReportTimeStr())) {
			time = eventDetailVO.getReportTimeStr();
		}
		String source = eventService.jointSource(eventDetailVO);
		 String address = eventService.jointAddress(eventDetailVO);
         String content = eventService.jointInitReport(eventDetailVO);
         // UserSimpleVO loginUser = feignClient.selectByUserId(userId);
         // content += "——报送人：" + loginUser.getUserName();
		Map<String, String> urlMap = getUrlMap(list);
		for (ProgressUserVO vo : list) {
			String mobile = vo.getMobile();
			SmsTemplateDTO smsTemplateDTO = new SmsTemplateDTO();
			smsTemplateDTO.setTemplateId(stringRedisTemplate.opsForValue().get("its-event:SmsTemplateCode-INIT_REPORT_4PART"));
            smsTemplateDTO.setMobile(mobile);
			String urlMsg = GenerateWeiXinUrlUtils.getUrlAndAssembleContent(urlMap, mobile);
			// 组合报送内容
			smsTemplateDTO.setContent(smsTemplateDTO.createInitReportContent(time, address, content, source, urlMsg));
            boolean sendStatus = AliyunSmsUtils.sendAnSms(smsTemplateDTO);
            String bizId = smsTemplateDTO.getBizId();
        	// 更新进展用户的短信发送状态为失败(0)
        	ProgressUserDTO eventPorgressUserDTO = new ProgressUserDTO();
        	eventPorgressUserDTO.setEventProgressId(dto.getId());
        	eventPorgressUserDTO.setMobile(mobile);
        	eventPorgressUserDTO.setSmsSendStatus(sendStatus ? 2 : 0);
        	eventPorgressUserDTO.setSmsFailReason(sendStatus ? null : smsTemplateDTO.getFailReason());
        	eventPorgressUserDTO.setBizId(bizId);
        	eventPorgressUserDTO.setSendTime(TimeUtils.getTimeString());
        	progressMapper.updateSmsSendStatus(eventPorgressUserDTO);
        }
		ProgressDTO progressDTO = new ProgressDTO();
        Long serverTime = System.currentTimeMillis() / 1000;
        progressDTO.setCreateTime(serverTime);
        progressDTO.setOccurTime(serverTime);
        progressDTO.setProgressDesc("【事件初报】短信已重发，请关注短信送达状态的更新。");
        progressDTO.setEventId(eventIdDTO.getId());
        progressDTO.setCreateUserId(userId);
        return add(progressDTO);
	}

	@Transactional
	public boolean resendResubmitSmsById(IdIntegerDTO dto, int sno, String userId) {
		List<ProgressUserVO> list = progressMapper.selectSmsSendFailById(dto);
		if(CollectionUtils.isEmpty(list)) {
			return false;
		}
		// 查询进展描述
		ProgressVO progressVO = progressMapper.selectById(dto);
		EventProcessSmsMessageDTO smsMessageDTO = progressSmsMessageMapper.selectByEventProcessId(dto.getId().longValue());
		SmsTemplateDTO smsTemplateDTO = new SmsTemplateDTO();/*
        String content = progressVO.getProgressDesc().replace("【事件续报】", "");
        content = content.replaceAll("。。", "。").replaceAll("\\.。", "。");
        char charAt = content.charAt(content.length()-1);
        if("。".equals("" + charAt) || ".".equals("" + charAt) || "，".equals("" + charAt) || ",".equals("" + charAt)) {
            content = content.substring(0, content.length()-1);
        }
        UserSimpleVO loginUser = feignClient.selectByUserId(userId);
        content += "——报送人：" + loginUser.getUserName();*/
        smsTemplateDTO.setTemplateId(stringRedisTemplate.opsForValue().get("its-event:SmsTemplateCode-RESUBMIT"));
		for (ProgressUserVO vo : list) {
			String mobile = vo.getMobile();
			smsTemplateDTO.setMobile(mobile);
			String content = smsTemplateDTO.createResubmitReportContent(smsMessageDTO.getTimes() == 0 ? "" : smsMessageDTO.getTimes() + "",
					smsMessageDTO.getReportTime(), smsMessageDTO.getLocation(), smsMessageDTO.getWeatherDesc(), smsMessageDTO.getEventDesc(),
					smsMessageDTO.getReportSource(), smsMessageDTO.getUrlSuffix());
			smsTemplateDTO.setContent(content);
			boolean sendStatus = AliyunSmsUtils.sendAnSms(smsTemplateDTO);
            String bizId = smsTemplateDTO.getBizId();
            // 更新进展用户的短信发送状态为失败(0)
        	ProgressUserDTO eventPorgressUserDTO = new ProgressUserDTO();
        	eventPorgressUserDTO.setEventProgressId(dto.getId());
        	eventPorgressUserDTO.setMobile(mobile);
        	eventPorgressUserDTO.setSmsSendStatus(sendStatus ? 2 : 0);
        	eventPorgressUserDTO.setSmsFailReason(sendStatus ? null : smsTemplateDTO.getFailReason());
        	eventPorgressUserDTO.setBizId(bizId);
        	eventPorgressUserDTO.setSendTime(TimeUtils.getTimeString());
        	progressMapper.updateSmsSendStatus(eventPorgressUserDTO);
        }
        ProgressDTO progressDTO = new ProgressDTO();
        Long serverTime = System.currentTimeMillis() / 1000;
        progressDTO.setCreateTime(serverTime);
        progressDTO.setOccurTime(serverTime);
        progressDTO.setProgressDesc("【事件续报"+sno+"】短信已重发，请关注短信送达状态的更新。");
        progressDTO.setEventId(progressVO.getEventId());
        progressDTO.setCreateUserId(userId);
        return add(progressDTO);
	}

	@Transactional
	public boolean resendFinalReportSmsById(IdIntegerDTO dto, int sno, String userId) {
		List<ProgressUserVO> list = progressMapper.selectSmsSendFailById(dto);
		if(CollectionUtils.isEmpty(list)) {
			return false;
		}
		// 查询进展描述
		ProgressVO progressVO = progressMapper.selectById(dto);
		EventProcessSmsMessageDTO smsMessageDTO = progressSmsMessageMapper.selectByEventProcessId(dto.getId().longValue());
        SmsTemplateDTO smsTemplateDTO = new SmsTemplateDTO();
        /*String content = progressVO.getProgressDesc().replace("【事件终报】", "");
        content = content.replaceAll("。。", "。").replaceAll("\\.。", "。");
        char charAt = content.charAt(content.length()-1);
        if("。".equals("" + charAt) || ".".equals("" + charAt) || "，".equals("" + charAt) || ",".equals("" + charAt)) {
            content = content.substring(0, content.length()-1);
        }
        UserSimpleVO loginUser = feignClient.selectByUserId(userId);
        content += "——报送人：" + loginUser.getUserName();*/
        smsTemplateDTO.setTemplateId(stringRedisTemplate.opsForValue().get("its-event:SmsTemplateCode-FINAL_REPORT"));
		for (ProgressUserVO vo : list) {
			String mobile = vo.getMobile();
			smsTemplateDTO.setMobile(mobile);
			String content = smsTemplateDTO.createResubmitReportContent(smsMessageDTO.getTimes() == 0 ? "" : smsMessageDTO.getTimes() + "",
					smsMessageDTO.getReportTime(), smsMessageDTO.getLocation(), smsMessageDTO.getWeatherDesc(), smsMessageDTO.getEventDesc(),
					smsMessageDTO.getReportSource(), smsMessageDTO.getUrlSuffix());
			smsTemplateDTO.setContent(content);
            boolean sendStatus = AliyunSmsUtils.sendAnSms(smsTemplateDTO);
            String bizId = smsTemplateDTO.getBizId();
            // 更新进展用户的短信发送状态为失败(0)
        	ProgressUserDTO eventPorgressUserDTO = new ProgressUserDTO();
        	eventPorgressUserDTO.setEventProgressId(dto.getId());
        	eventPorgressUserDTO.setMobile(mobile);
        	eventPorgressUserDTO.setSmsSendStatus(sendStatus ? 2 : 0);
        	eventPorgressUserDTO.setSmsFailReason(sendStatus ? null : smsTemplateDTO.getFailReason());
        	eventPorgressUserDTO.setBizId(bizId);
        	eventPorgressUserDTO.setSendTime(TimeUtils.getTimeString());
        	progressMapper.updateSmsSendStatus(eventPorgressUserDTO);
        }
        ProgressDTO progressDTO = new ProgressDTO();
        Long serverTime = System.currentTimeMillis() / 1000;
        progressDTO.setCreateTime(serverTime);
        progressDTO.setOccurTime(serverTime);
        progressDTO.setProgressDesc("【事件终报"+sno+"】短信已重发，请关注短信送达状态的更新。");
        progressDTO.setEventId(progressVO.getEventId());
        progressDTO.setCreateUserId(userId);
        return add(progressDTO);
	}

	private Map<String, String> getUrlMap(List<ProgressUserVO> list) {
		List<String> mobileList = new ArrayList<>();
		list.forEach(item-> mobileList.add(item.getMobile()));
		Map<String, String> urlMap = eventService.getWeiXinUrlMap(mobileList.toArray(new String[]{}));
		return urlMap;
	}

	/** 启动预案同意或不同意，传参进展id **/
	public int confirmPlan(ComfirmPlanDTO dto, String userId) {
		return startPlanConfirm(dto, userId);
	}

	/** 启动预案同意或不同意，传参事件id **/
	public int confirmPlanByEventId(ComfirmPlanDTO dto, String userId) {
		return startPlanConfirm(dto, userId);
	}

	/** 启动预案同意或不同意 **/
	public int startPlanConfirm(ComfirmPlanDTO dto, String userId) {
		Integer progressId = dto.getId();
		String eventId = dto.getEventId();;
		IdStringDTO eventIdDTO = new IdStringDTO(eventId);
		if(progressId != null && progressId > 0) {
			ProgressVO vo = progressMapper.selectById(new IdIntegerDTO(progressId));
			eventId = vo.getEventId();
			eventIdDTO = new IdStringDTO(eventId);
		} else {
			ProgressVO progressVO = progressMapper.selectStartPlanCard(eventIdDTO);
			progressId = progressVO.getId();
		}
		Integer pass = dto.getPass();
		IdIntegerDTO progressIdDTO = new IdIntegerDTO(progressId);
		// 查询事件ID
		Map<String, Object> map = new HashMap<>();

		EmerResuceVO eventVO = progressMapper.selectEventById(progressIdDTO);
		if(eventVO.getEventStatus() == null || eventVO.getEventStatus() < 4) {
			// 更新时事件状态event_status=4
			map.put("eventStatus", 4);
			map.put("id", eventId);
			eventMapper.updateEventStatus(map);
		}

		Long serverTime = System.currentTimeMillis()/1000;
		// 查询初报进展，初报确认（event_progress设置card_pass=1）
		ProgressVO initReportVO = progressMapper.selectInitReport(eventIdDTO);
		map.put("id", initReportVO.getId());
		map.put("updateTime", serverTime);
		progressMapper.confirm(map);

		// 初报进展@用户的记录需要设置cas_pass=1
		ProgressUserDTO initProgressUserDTO = new ProgressUserDTO();
		initProgressUserDTO.setUserId(userId);
		initProgressUserDTO.setEventProgressId(initReportVO.getId());
		initProgressUserDTO.setCardPass(1);
		progressMapper.updateProgressUserCardPass(initProgressUserDTO);

		// 启动预案进展@用户的记录需要设置cas_pass=1
		ProgressUserDTO progressUserDTO = new ProgressUserDTO();
		progressUserDTO.setUserId(userId);
		progressUserDTO.setEventProgressId(progressId);
		progressUserDTO.setCardPass(pass);
		progressMapper.updateProgressUserCardPass(progressUserDTO);
		map.put("id", progressId);
		map.put("pass", pass);
		map.put("updateTime", serverTime);
		progressMapper.confirm(map);

		EmerPlanVO emerPlanVO = eventMapper.selectEmerPlan(new IdStringDTO(eventVO.getEmerPlanId()));
		String planName = emerPlanVO.getName();
		String planLevel = EmerPlanLevelEnum.getCname(eventVO.getLevel());
		String reason = dto.getReason();
		UserSimpleVO loginUserVO = feignClient.selectByUserId(userId);
		String desc = "";
		// 拼接同意或不同意进展内容，并新增进展
		if(pass == 1) {
			desc = "经" + loginUserVO.getUserName() + "审核，同意启动《"+planName+"》，预案等级" + planLevel + "。";
		} else if(pass == 2) {
			desc = "经" + loginUserVO.getUserName() + "审核，不同意启动《"+planName+"》，预案等级" + planLevel + "。审核不通过理由：" + reason;
		}
		ProgressDTO progressDTO = new ProgressDTO();
		progressDTO.setCreateUserId(userId);
		progressDTO.setOccurTime(serverTime);
		progressDTO.setEventId(eventId);
		progressDTO.setProgressDesc(desc);
		progressDTO.setCreateUserId(userId);
		progressDTO.setPid(0);
		progressDTO.setCardPass(0);
		progressDTO.setTheme(0);
		addProgress(progressDTO);

		EventDealStatusDTO eventDealStatusDTO = new EventDealStatusDTO();
		if(pass == 1) {
			// 更新event_deal_status的启动预案响应状态，1已响应
			// 新增-事件执行预案相关状态
			eventDealStatusDTO.setEventId(eventVO.getId());
			eventDealStatusDTO.setPlanResponse(1);
			eventDealStatusDTO.setCancelPlan(0);
			eventDealStatusDTO.setPlanResponseTime(serverTime);
			eventMapper.updatePlanResponse(eventDealStatusDTO);
		}
		if(pass == 2) {
			eventDealStatusDTO.setEventId(eventVO.getId());
			eventDealStatusDTO.setPlanResponse(2);
			eventDealStatusDTO.setPlanResponseTime(serverTime);
			eventMapper.updatePlanResponse(eventDealStatusDTO);
		}
		eventDealStatusDTO.setDealResponse(1);
		eventDealStatusDTO.setDealResponseTime(serverTime);
		eventMapper.updateDealResponse(eventDealStatusDTO);
		return 1;
	}
	
	/** 撤销预案同意或不同意，传参进展id **/
	public int cancelPlan(ComfirmPlanDTO dto, String userId) {
		return cancelPlanConfirm(dto, userId);
	}
	
	/** 撤销预案同意或不同意，传参事件id **/
	public int cancelPlanByEventId(ComfirmPlanDTO dto, String userId) {
		return cancelPlanConfirm(dto, userId);
	}

	/** 撤销预案同意或不同意 **/
	public int cancelPlanConfirm(ComfirmPlanDTO dto, String userId) {
		Integer progressId = dto.getId();
		String eventId = dto.getEventId();;
		IdStringDTO eventIdDTO = new IdStringDTO(eventId);
		if(progressId != null && progressId > 0) {
			ProgressVO vo = progressMapper.selectById(new IdIntegerDTO(progressId));
			eventId = vo.getEventId();
			eventIdDTO = new IdStringDTO(eventId);
		} else {
			ProgressVO progressVO = progressMapper.selectCancelPlanCard(eventIdDTO);
			progressId = progressVO.getId();
		}
		Integer pass = dto.getPass();
		IdIntegerDTO progressIdDTO = new IdIntegerDTO(progressId);
		// 查询事件ID
		Map<String, Object> map = new HashMap<>();
		EmerResuceVO eventVO = progressMapper.selectEventById(progressIdDTO);
		Long cretaeTime = System.currentTimeMillis()/1000;

		// 启动预案进展@用户的记录需要设置cas_pass=1
		ProgressUserDTO progressUserDTO = new ProgressUserDTO();
		progressUserDTO.setUserId(userId);
		progressUserDTO.setEventProgressId(progressId);
		progressUserDTO.setCardPass(pass);
		progressMapper.updateProgressUserCardPass(progressUserDTO);
		map.put("id", progressId);
		map.put("pass", pass);
		map.put("updateTime", cretaeTime);
		progressMapper.confirm(map);

		EmerPlanVO emerPlanVO = eventMapper.selectEmerPlan(new IdStringDTO(eventVO.getEmerPlanId()));
		String planName = emerPlanVO.getName();
		String planLevel = EmerPlanLevelEnum.getCname(eventVO.getLevel());
		String reason = dto.getReason();
		UserSimpleVO loginUserVO = feignClient.selectByUserId(userId);
		String desc = "";
		// 拼接同意或不同意进展内容，并新增进展
		if(pass == 1) {
			desc = "经" + loginUserVO.getUserName() + "审核，同意解除《"+planName+"》，预案等级" + planLevel + "。";
		} else if(pass == 2) {
			desc = "经" + loginUserVO.getUserName() + "审核，不同意解除《"+planName+"》，预案等级" + planLevel + "。审核不通过理由：" + reason;
		}
		ProgressDTO progressDTO = new ProgressDTO();
		progressDTO.setCreateUserId(userId);
		progressDTO.setOccurTime(cretaeTime);
		progressDTO.setEventId(eventId);
		progressDTO.setProgressDesc(desc);
		progressDTO.setCreateUserId(userId);
		progressDTO.setPid(0);
		progressDTO.setCardPass(25);
		progressDTO.setTheme(0);
		addProgress(progressDTO);

		// 更新event_deal_status的撤销预案响应状态，1已响应
		// 新增-事件执行预案相关状态
		EventDealStatusDTO eventDealStatusDTO = new EventDealStatusDTO();
		if(pass == 1) {
			eventDealStatusDTO.setEventId(eventVO.getId());
			eventDealStatusDTO.setCancelPlan(1);
		} else {
			eventDealStatusDTO.setEventId(eventVO.getId());
			eventDealStatusDTO.setCancelPlan(0);
		}
		eventMapper.updateCancel(eventDealStatusDTO);
		return 1;
	}

	/**
	 * 初报进展重拨失败的电话
	 *
	 * @param referenceIdList 唯一标识集合
	 * @param userId          用户id
	 * @param oldList         用于后续更新准备
	 * @return true-成功，false-失败
	 */
	@Transactional
	public boolean resendInitReportMobileByReferenceId(List<String> referenceIdList, String userId, List<ProgressUserVO> oldList) {
		if (CollectionUtils.isEmpty(referenceIdList)) {
			LOGGER.error("The resend init report mobile By reference id params is empty.");
			return false;
		}
		List<ProgressUserVO> progressUserDTOList = progressMapper.selectFailedCallByReferenceId(referenceIdList);
		oldList.addAll(progressUserDTOList);
		if (CollectionUtils.isEmpty(progressUserDTOList)) {
			LOGGER.error("The robot call data is missing,the reference id list is {}", referenceIdList);
			return false;
		}
		// 报送内容封装
		String eventId = progressUserDTOList.get(0).getEventId();
		IdStringDTO eventIdDTO = new IdStringDTO(eventId);
		EventDetailVO eventDetailVO = eventMapper.selectEmerRescueByEventId(eventIdDTO);
		Integer eventProgressId = progressUserDTOList.get(0).getEventProgressId();
		ProgressVO progressVO = progressMapper.selectById(new IdIntegerDTO(eventProgressId));
		if (progressVO == null) {
			return false;
		}
		String content = progressVO.getProgressDesc();
		UserSimpleVO loginUser = feignClient.selectByUserId(userId);
		content += "——报送人：" + loginUser.getUserName();

		// 电话号码封装
		List<UserDTO> robotMobileUserList = new ArrayList<>();
		for (ProgressUserVO progressUserVO : progressUserDTOList) {
			UserDTO userDTO = new UserDTO();
			userDTO.setMobile(progressUserVO.getMobile());
			userDTO.setUserName(progressUserVO.getUserName());
			robotMobileUserList.add(userDTO);
		}
		// 外呼对象信息封装
		RobotMobileDTO robotMobileDTO = new RobotMobileDTO();
		robotMobileDTO.setId(eventId);
		robotMobileDTO.setBriefDesc(content);
		robotMobileDTO.setSourceId(eventDetailVO.getSourceId());
		robotMobileDTO.setUsers(robotMobileUserList);

		// 外呼机器人播报发起请求
		AssignJobs.robotMobile(robotMobileDTO,true);

		// 重置呼叫状态为空
		for (ProgressUserVO progressUserVO : progressUserDTOList) {
			progressUserVO.setRobotCallStatus(null);
			progressMapper.updateByIdAndUserAndMobile(progressUserVO);
		}
		// 事件进展记录封装
		ProgressDTO progressDTO = new ProgressDTO();
		Long serverTime = System.currentTimeMillis() / 1000;
		progressDTO.setCreateTime(serverTime);
		progressDTO.setOccurTime(serverTime);
		progressDTO.setProgressDesc("【事件初报】电话已重拨，请关注电话送达状态的更新。");
		progressDTO.setEventId(eventId);
		progressDTO.setCreateUserId(userId);
		return add(progressDTO);
	}

	public boolean addDraft(EventProgressDraftDTO dto, String userId) {
		// 用户为空
		if(StringUtils.isBlank(userId)) {
			LOGGER.warn("进展草稿创建者ID为空");
			return true;
		}
		
		// 入库，每个事件和用户只保留最新的一条草稿进展
		dto.setUserId(userId);
		progressMapper.addDraft(dto);
		return true;
	}

	public EventProgressDraftVO getDraft(EventProgressDraftDTO dto, String userId) {
		dto.setUserId(userId);
		EventProgressDraftVO vo = progressMapper.getDraft(dto);
		
		//草稿为空时不需要返回
		if(vo != null && StringUtils.isNoneBlank(vo.getDraftDesc())) {
			return vo;
		}
		return null;
	}

    public int checkSceneAttach(IdStringDTO dto) {
        return progressMapper.checkSceneAttach(dto);
    }

	public int withdraw(IdIntegerDTO dto, String userId) {
		ProgressVO vo = progressMapper.selectById(dto);
		String createUserId = vo.getCreateUserId();
		if (!userId.equals(createUserId)) {
			throw new ArgumentException("您不能撤回其他人发的消息.");
		}
		Long createTime = vo.getCreateTime();
		if (createTime != null) {
			if ((System.currentTimeMillis() / 1000 - createTime.longValue()) >= 120) {
				throw new ArgumentException("消息超过2分钟，您不能撤回这条消息.");
			}
		}
		return progressMapper.updateWithdraw(dto);
	}
	public ProgressVO selectWithAttachById(IdIntegerDTO dto) {
		ProgressVO vo = progressMapper.selectById(dto);
		if (vo != null) {
			List<ProgressAttachVO> attachs = progressMapper.selectAttachsById(dto);
			vo.setAttachs(attachs);
		}
		return vo;
	}
}