package com.bt.itsevent.service;

	import java.io.File;
import java.io.FileInputStream;
	import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import javax.activation.MimetypesFileTypeMap;

	public class TestPcm {

	    public static void main(String[] args) {
	    	String filePath = "D:/Users/<USER>/call02.pcm";
	        Path path = Paths.get(filePath);

	        try {
	            String contentType = Files.probeContentType(path);
	            System.out.println("Content-Type: " + contentType);
	        } catch (IOException e) {
	            e.printStackTrace();
	        }
	        File file = new File(filePath);

	        MimetypesFileTypeMap fileTypeMap = new MimetypesFileTypeMap();
	        String contentType = fileTypeMap.getContentType(file);
	        System.out.println("Content-Type: " + contentType);
	        
	    }

	    public static short[] readPCMFile(String filePath, int bitDepth) throws IOException {
	        FileInputStream fis = new FileInputStream(filePath);
	        int bytesPerSample = bitDepth / 8;
	        byte[] buffer = new byte[1024 * bytesPerSample];
	        int bytesRead;
	        int totalSamples = 0;
	        short[] pcmData = new short[(int) (fis.getChannel().size() / bytesPerSample)];

	        while ((bytesRead = fis.read(buffer)) != -1) {
	            for (int i = 0; i < bytesRead; i += bytesPerSample) {
	                // 将字节数据转换为short（假设是小端序）
	                short sample = (short) ((buffer[i] & 0xFF) | (buffer[i + 1] << 8));
	                pcmData[totalSamples++] = sample;
	            }
	        }

	        fis.close();
	        return pcmData;
	    }
}
