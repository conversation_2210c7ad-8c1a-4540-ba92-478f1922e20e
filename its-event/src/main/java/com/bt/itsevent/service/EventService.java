package com.bt.itsevent.service;

import java.io.File;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;

import com.bt.itscore.config.OssConfig;
import com.bt.itscore.constants.SystemConstants;
import com.bt.itscore.domain.dto.AttachDTO;
import com.bt.itscore.domain.dto.CommandDTO;
import com.bt.itscore.domain.dto.DDIntegralDTO;
import com.bt.itscore.domain.dto.DDRescuerInfoDTO;
import com.bt.itscore.domain.dto.DictItemDTO;
import com.bt.itscore.domain.dto.Event96333DTO;
import com.bt.itscore.domain.dto.Event96333NewOrderDTO;
import com.bt.itscore.domain.dto.Event96333OrderSuccessCodeDTO;
import com.bt.itscore.domain.dto.Event96333SubmitDTO;
import com.bt.itscore.domain.dto.EventMessageDTO;
import com.bt.itscore.domain.dto.EventProgressMessageDTO;
import com.bt.itscore.domain.dto.IdIntegerDTO;
import com.bt.itscore.domain.dto.IdStringDTO;
import com.bt.itscore.domain.dto.LngLatDTO;
import com.bt.itscore.domain.dto.MessageDTO;
import com.bt.itscore.domain.dto.PageDTO;
import com.bt.itscore.domain.dto.RoadDirectionDTO;
import com.bt.itscore.domain.dto.RoadPileNoDTO;
import com.bt.itscore.domain.dto.SmsTemplateDTO;
import com.bt.itscore.domain.dto.UserLocationDTO;
import com.bt.itscore.domain.dto.WechatEventDTO;
import com.bt.itscore.domain.dto.WechatMsgDTO;
import com.bt.itscore.domain.vo.BasicUserVO;
import com.bt.itscore.domain.vo.CountVO;
import com.bt.itscore.domain.vo.DictItemVO;
import com.bt.itscore.domain.vo.EmerPlanLevelAuditVO;
import com.bt.itscore.domain.vo.OrganizationRoadVO;
import com.bt.itscore.domain.vo.ResponseVO;
import com.bt.itscore.domain.vo.ResultVO;
import com.bt.itscore.domain.vo.RoadDirectionVO;
import com.bt.itscore.domain.vo.RoadPileNoVO;
import com.bt.itscore.domain.vo.RoadRescuePhoneVO;
import com.bt.itscore.domain.vo.UserLocationVO;
import com.bt.itscore.domain.vo.UserSimpleVO;
import com.bt.itscore.enums.EmerPlanLevelEnum;
import com.bt.itscore.enums.EventCarTypeEnum;
import com.bt.itscore.enums.EventReportSourceEnum;
import com.bt.itscore.enums.EventSourceEnum;
import com.bt.itscore.enums.OccupiedLaneEnum;
import com.bt.itscore.enums.OrgSourceIdEnum;
import com.bt.itscore.enums.OrganShortNameEnum;
import com.bt.itscore.enums.ServiceTypeEnum;
import com.bt.itscore.enums.SourceIdEnum;
import com.bt.itscore.enums.YanHaiOrgEnum;
import com.bt.itscore.exception.ArgumentException;
import com.bt.itscore.utils.AliyunSmsUtils;
import com.bt.itscore.utils.Base64Utils;
import com.bt.itscore.utils.DictSortUtils;
import com.bt.itscore.utils.ExcelUtils;
import com.bt.itscore.utils.ExportUtils;
import com.bt.itscore.utils.GsonUtils;
import com.bt.itscore.utils.HmacSHA256Utils;
import com.bt.itscore.utils.HttpClientUtils;
import com.bt.itscore.utils.OssUtils;
import com.bt.itscore.utils.ServiceUtils;
import com.bt.itscore.utils.TimeUtils;
import com.bt.itsevent.constants.EventConstant;
import com.bt.itsevent.constants.EventEvaStateConstants;
import com.bt.itsevent.constants.EventFacilityEnums;
import com.bt.itsevent.constants.EventTypeEnum;
import com.bt.itsevent.domain.dto.AmapEventPublishDTO;
import com.bt.itsevent.domain.dto.BusinessTypeDTO;
import com.bt.itsevent.domain.dto.DDForGzhDTO;
import com.bt.itsevent.domain.dto.DDRescuerDTO;
import com.bt.itsevent.domain.dto.EmerRescueDTO;
import com.bt.itsevent.domain.dto.EmerUserDTO;
import com.bt.itsevent.domain.dto.EventAnalysisDTO;
import com.bt.itsevent.domain.dto.EventAuthorityDTO;
import com.bt.itsevent.domain.dto.EventCarDTO;
import com.bt.itsevent.domain.dto.EventClockStatuDTO;
import com.bt.itsevent.domain.dto.EventCmsDTO;
import com.bt.itsevent.domain.dto.EventConfirmDTO;
import com.bt.itsevent.domain.dto.EventCountDTO;
import com.bt.itsevent.domain.dto.EventDDDTO;
import com.bt.itsevent.domain.dto.EventDTO;
import com.bt.itsevent.domain.dto.EventDealFullCloseDTO;
import com.bt.itsevent.domain.dto.EventDealFullCloseVO;
import com.bt.itsevent.domain.dto.EventDealStatusDTO;
import com.bt.itsevent.domain.dto.EventEmerUserDTO;
import com.bt.itsevent.domain.dto.EventEvlRecordDTO;
import com.bt.itsevent.domain.dto.EventEvlRecordItemDTO;
import com.bt.itsevent.domain.dto.EventFinishDTO;
import com.bt.itsevent.domain.dto.EventInfoReviewDTO;
import com.bt.itsevent.domain.dto.EventInfoReviewStatVO;
import com.bt.itsevent.domain.dto.EventLogDTO;
import com.bt.itsevent.domain.dto.EventProcessSmsMessageDTO;
import com.bt.itsevent.domain.dto.EventQueryDTO;
import com.bt.itsevent.domain.dto.EventReportDraftDTO;
import com.bt.itsevent.domain.dto.EventTsDTO;
import com.bt.itsevent.domain.dto.EventTypeDTO;
import com.bt.itsevent.domain.dto.EventUserWeiXinUrlDTO;
import com.bt.itsevent.domain.dto.EventZxDTO;
import com.bt.itsevent.domain.dto.FacilityTunnelDTO;
import com.bt.itsevent.domain.dto.NostatDealReasonDTO;
import com.bt.itsevent.domain.dto.ProcessSceneExportDTO;
import com.bt.itsevent.domain.dto.ProgressDTO;
import com.bt.itsevent.domain.dto.ProgressUserDTO;
import com.bt.itsevent.domain.dto.RelatedFacilityDTO;
import com.bt.itsevent.domain.dto.RemoveObstaclesDTO;
import com.bt.itsevent.domain.dto.RobotMobileDTO;
import com.bt.itsevent.domain.dto.UserDTO;
import com.bt.itsevent.domain.dto.VideoPostionDTO;
import com.bt.itsevent.domain.vo.BusinessTypeVO;
import com.bt.itsevent.domain.vo.ChargePolicyVO;
import com.bt.itsevent.domain.vo.DDForGzhVO;
import com.bt.itsevent.domain.vo.EmerPlanVO;
import com.bt.itsevent.domain.vo.EmerProgressVO;
import com.bt.itsevent.domain.vo.EmerResuceVO;
import com.bt.itsevent.domain.vo.EmerRoleVO;
import com.bt.itsevent.domain.vo.EventAnalysisAuthVO;
import com.bt.itsevent.domain.vo.EventAttachVO;
import com.bt.itsevent.domain.vo.EventAuditResponseTimeVO;
import com.bt.itsevent.domain.vo.EventContructionVO;
import com.bt.itsevent.domain.vo.EventCountByOrgVO;
import com.bt.itsevent.domain.vo.EventCountVO;
import com.bt.itsevent.domain.vo.EventDDVO;
import com.bt.itsevent.domain.vo.EventDealStatusVO;
import com.bt.itsevent.domain.vo.EventDetailVO;
import com.bt.itsevent.domain.vo.EventEmerUserVO;
import com.bt.itsevent.domain.vo.EventEvaluationContentVO;
import com.bt.itsevent.domain.vo.EventEvaluationItemVO;
import com.bt.itsevent.domain.vo.EventEvaluationVO;
import com.bt.itsevent.domain.vo.EventEvlItemVO;
import com.bt.itsevent.domain.vo.EventEvlRecordVO;
import com.bt.itsevent.domain.vo.EventHeatVO;
import com.bt.itsevent.domain.vo.EventInfoReviewVO;
import com.bt.itsevent.domain.vo.EventLocationVO;
import com.bt.itsevent.domain.vo.EventNostatDealReasonVO;
import com.bt.itsevent.domain.vo.EventOrderStatusVO;
import com.bt.itsevent.domain.vo.EventRoadLossVO;
import com.bt.itsevent.domain.vo.EventStatDealTimeVO;
import com.bt.itsevent.domain.vo.EventStatVO;
import com.bt.itsevent.domain.vo.EventTimeoutAuditVO;
import com.bt.itsevent.domain.vo.EventTodayTypeVO;
import com.bt.itsevent.domain.vo.EventTypeDetailVO;
import com.bt.itsevent.domain.vo.EventTypeRescueVO;
import com.bt.itsevent.domain.vo.EventTypeTop5MapVO;
import com.bt.itsevent.domain.vo.EventTypeTop5VO;
import com.bt.itsevent.domain.vo.EventTypeVO;
import com.bt.itsevent.domain.vo.EventVO;
import com.bt.itsevent.domain.vo.EventZxDetailVO;
import com.bt.itsevent.domain.vo.EventZxVO;
import com.bt.itsevent.domain.vo.GaodeAddrVO;
import com.bt.itsevent.domain.vo.GroupEventEmerUserVO;
import com.bt.itsevent.domain.vo.GroupProgressVO;
import com.bt.itsevent.domain.vo.NearestFacilityTipVO;
import com.bt.itsevent.domain.vo.OccupiedLaneVO;
import com.bt.itsevent.domain.vo.OngoingDDForGzhVO;
import com.bt.itsevent.domain.vo.OrgListVO;
import com.bt.itsevent.domain.vo.OrgRoadListVO;
import com.bt.itsevent.domain.vo.OrgRoadThreeLevelVO;
import com.bt.itsevent.domain.vo.OrganRoadVO;
import com.bt.itsevent.domain.vo.ProcessSceneExportVO;
import com.bt.itsevent.domain.vo.ProgressAttachVO;
import com.bt.itsevent.domain.vo.ProgressExportVO;
import com.bt.itsevent.domain.vo.ProgressMyUserVO;
import com.bt.itsevent.domain.vo.ProgressSceneVO;
import com.bt.itsevent.domain.vo.ProgressUserVO;
import com.bt.itsevent.domain.vo.ProgressVO;
import com.bt.itsevent.domain.vo.PublicInfoVO;
import com.bt.itsevent.domain.vo.RemoveObstaclesUserVO;
import com.bt.itsevent.domain.vo.TsJyDetailVO;
import com.bt.itsevent.domain.vo.TsJyVO;
import com.bt.itsevent.domain.vo.WorkingTableCountVO;
import com.bt.itsevent.feign.DirectionVO;
import com.bt.itsevent.feign.EmerDutyTodayDTO;
import com.bt.itsevent.feign.EmerGroupMatchVO;
import com.bt.itsevent.feign.EmerPersonMatchDTO;
import com.bt.itsevent.feign.EmerPersonMatchVO;
import com.bt.itsevent.feign.EmerPlanMatchDTO;
import com.bt.itsevent.feign.EmerPlanMatchLevelVO;
import com.bt.itsevent.feign.EmerPlanMatchPlanVO;
import com.bt.itsevent.feign.EmerPlanMatchVO;
import com.bt.itsevent.feign.EmerUserVO;
import com.bt.itsevent.feign.FeignClient;
import com.bt.itsevent.feign.ItsEmerFeignClient;
import com.bt.itsevent.feign.ItsMsFeignClient;
import com.bt.itsevent.feign.ItsWebSocketFeignClient;
import com.bt.itsevent.feign.OrganizationVO;
import com.bt.itsevent.feign.RoadMilePostDTO;
import com.bt.itsevent.feign.RoadVO;
import com.bt.itsevent.feign.TodayEmerDutyVO;
import com.bt.itsevent.feign.User;
import com.bt.itsevent.mapper.EventFileMapper;
import com.bt.itsevent.mapper.EventLogMapper;
import com.bt.itsevent.mapper.EventMapper;
import com.bt.itsevent.mapper.EventTypeMapper;
import com.bt.itsevent.mapper.ProgressMapper;
import com.bt.itsevent.mapper.ProgressSmsMessageMapper;
import com.bt.itsevent.mq.rabbitmq.WechatProductor;
import com.bt.itsevent.utils.AssignJobs;
import com.bt.itsevent.utils.GaodeMapUtils;
import com.bt.itsevent.utils.GenerateWeiXinUrlUtils;
import com.bt.itsevent.utils.RobotCallTmpResultContext;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;

import io.prometheus.client.Counter;


@RefreshScope
@Service("eventService")
public class EventService {
    private final static Logger LOGGER = LoggerFactory.getLogger(EventService.class);

    @Value("${event.attach.path}")
    private String eventAttachPath;

    @Value("${event.rpc96333.finish}")
    private String eventRpc96333Finish;

    @Value("${event.96333.mode:new}")
    private String event96333Mode;

    @Value("${event.96333.xfzmobile}")
    private String event96333XfzMobile;
    
    @Value("${event.96333.yhmobile}")
    private String event96333YhMobile;

    @Value("${file.export.path}")
    private String fileExportPath;

    @Value("${file.export.templates}")
    private String templatePath;

    @Value("${amap.web.seq:8b1f9a853bf746e8c0b6d11e11c5a9c3}")
    private String amapWebSeq;

    @Value("${amap.web.clientSecret:f6961ed2893af87907f0363757ff9388}")
    private String amapWebClientSecret;

    @Value("${amap.web.sourceId:FSvFmZpR}")
    private String amapWebClientSourceId;

    @Value("${integral.rescuer.domain}")
    private String rescuerDomain;

    @Value("${integral.rescuer.username}")
    private String jfptUserName; // 北软桂享高速鉴权用户名

    @Value("${integral.rescuer.password}")
    private String jfptPassword; // 北软桂享高速鉴权密码
    @Value("${file.export.max}")
    private int fileExportMaxCount;
    
    @Value("${location.mile}")
    private int locationMile;//自动到达打卡范围

    @Value("${yk.domain:https://yktest.itsgx.cn/s}")
    private String ykDomain;
    
    @Value("${auto.release.cms:false}")
    private boolean autoReleaseCms;

    @Autowired
    EventMapper eventMapper;

    @Autowired
    EventTypeMapper eventTypeMapper;

    @Autowired
    private ProgressMapper progressMapper;

    @Autowired
    FeignClient feignClient;

    @Autowired
    ItsWebSocketFeignClient itsWebSocketFeignClient;

    @Autowired
    ItsMsFeignClient itsMsFeignClient;

    @Autowired
    ItsEmerFeignClient itsEmerFeignClient;

    @Autowired
    ProgressService progressService;

    @Autowired
    OssConfig ossConfig;

    @Autowired
    EventValidService eventValidService;

    @Autowired
    private EventUserWeiXinUrlService eventUserWeiXinUrlService;

    @Autowired
    private WechatProductor wechatProductor;

    @Autowired
    private EventFileMapper eventFileMapper;

    @Autowired
    private ProgressSmsMessageMapper progressSmsMessageMapper;

    @Autowired
	StringRedisTemplate stringRedisTemplate;
    
    @Autowired
    private EventLogMapper eventLogMapper;
	@Autowired
	private RobotCallHandlerService robotCallHandlerService;
	long day7 = 604800;


    private static Counter pvCounter;
    //    private final String TEMPLATE_ID = "967017";//容联通讯的沿海模板
    private static final String DATE = "yyyy年MM月dd日";
    private static final String DATETIME = "yyyy年MM月dd日 HH:mm:ss";

    static {
        pvCounter = Counter.build().name("amap_push_event").help("统计计数视图").labelNames("event").register();
    }

    @Deprecated
    @Transactional
    public boolean add(EventDTO eventDTO) {
        eventDTO.setUuid(UUID.randomUUID().toString());
        eventDTO.setDealStatus(0);
        eventDTO.setRevisitStatus(0);
        eventDTO.setEventNo("" + System.currentTimeMillis() / 1000);
        // 有路段编号和桩号，生成经纬度
        pileNo2Lnglat(eventDTO);
        if (eventDTO.getEventType() == 1 || eventDTO.getEventType() == 2) {// 1事故 和 2车辆救援
            eventMapper.add(eventDTO);
        } else if (eventDTO.getEventType() == 3) {// 投诉举报
            eventMapper.addTs(eventDTO);
        } else if (eventDTO.getEventType() == 4) {// 意见建议
            eventMapper.addTs(eventDTO);
            // eventMapper.addJy(eventDTO);
        } else if (eventDTO.getEventType() == 5) {// 信息咨询

        }

        if (eventDTO.getId() > 0) {
            String yyMMdd = TimeUtils.getTimeString(TimeUtils.DATE_3);
            String count = ("" + (eventDTO.getId() % 10000 + 10000)).replaceFirst("1", "");
            String eventNo = yyMMdd + count;
            eventDTO.setEventNo(eventNo);
            eventMapper.updateEventNo(eventDTO);
            if (eventDTO.getAttachs() != null && eventDTO.getAttachs().size() > 0) {
                eventMapper.batchUpdateAttach(eventDTO);
            }
            if (eventDTO.getOrgIds() != null && eventDTO.getOrgIds().size() > 0) {
                eventMapper.batchAddEventOrg(eventDTO);
            }
            return true;
        }

        return false;
    }

    public EventOrderStatusVO selectEventOrder(EventQueryDTO dto) {
        EventOrderStatusVO eventOrderStatusVO = new EventOrderStatusVO();
        List<ProgressMyUserVO> vos = eventMapper.selectEventOrder(dto);
        for (ProgressMyUserVO vo : vos) {
            if (vo.getCardType() == 1) {
                eventOrderStatusVO.setNewEvent(1);
                eventOrderStatusVO.setNewProId(vo.getProId());
                continue;
            } else if (vo.getCardType() == 15) {
                eventOrderStatusVO.setPzCarPass(1);
                eventOrderStatusVO.setPzProId(vo.getProId());
                continue;
            }
        }
        return eventOrderStatusVO;
    }

    public PageInfo<EmerProgressVO> pageMyEvent(PageDTO pageDTO, EventQueryDTO dto) {
    	if (dto.getStartTime() == null) {
			dto.setStartTime(System.currentTimeMillis() / 1000 - 604800);// 7天内的进展
			dto.setEndTime(System.currentTimeMillis() / 1000 + 3600);
		}

        PageHelper.startPage(pageDTO.getPage(), pageDTO.getLimit());
        List<EmerProgressVO> list = eventMapper.selectMyEvent(dto);
        PageInfo<EmerProgressVO> pageInfo = new PageInfo<>(list);
        if (!CollectionUtils.isEmpty(list)) {
            // 根据所属路段，增加路段中心简称
            List<OrganRoadVO> organRoadList = eventMapper.selectOrganRoad();
            for (EmerProgressVO vo : list) {
                vo.setOrgName(this.getRoadOrganShortName(organRoadList, vo.getRoadNo(), vo.getSourceId()));
            }
            Map<String, Object> map = new HashMap<>();
            map.put("list", list);
            List<EventAuditResponseTimeVO> res = eventMapper.selectAuditResponseTimeList(map);
            for (EventAuditResponseTimeVO vo : res) {
                map.put(vo.getEventId(), vo);
            }
            for (EmerProgressVO vo : list) {
                vo.setAuditResponseTime((EventAuditResponseTimeVO) map.get(vo.getId()));
            }
        }
        return pageInfo;
    }

    /**
     * <pre>
     * 已出发 或 已到达 或 已离开
     * 1. eventStatus=5，已出发;eventStatus=6，已到达;eventStatus=7，已离开
     * 2. eventStatus重复或倒退，返回失败；否则新增进展留痕，并且更新事件状态字段（event_status）</pre>
     */
    @Transactional
    public ResponseVO clockEventStatus(EventClockStatuDTO dto) {
        Integer eventStatus = dto.getEventStatus();
        String eventId = dto.getId();
        String loginUserId = dto.getUserId();

        StringBuffer strBuff = new StringBuffer("");
        ProgressDTO progressDTO = new ProgressDTO();
        long serverTime = System.currentTimeMillis() / 1000;
        int reachFlag = 0;
        long reachTime = 0;
        if (eventStatus == 5) {
            strBuff.append("已出发");
            progressDTO.setCardPass(15);
            reachFlag = 1;
        } else if (eventStatus == 6) {
            strBuff.append("已到达");
            progressDTO.setCardPass(20);
            reachFlag = 1;
            reachTime = serverTime;
        } else if (eventStatus == 7) {
            strBuff.append("已离开");
            progressDTO.setCardPass(30);
            reachFlag = 1;
        }

        String progressDesc = dto.getProgressDesc();
        if (StringUtils.isBlank(progressDesc)) {
            progressDesc = strBuff.toString();
        }

        String lng = dto.getLng();
        String lat = dto.getLat();
        if (StringUtils.isNotBlank(lng) && StringUtils.isNotBlank(lat)) {
            LngLatDTO lngLatDTO = new LngLatDTO();
            lngLatDTO.setLng(lng);
            lngLatDTO.setLat(lat);
            RoadPileNoVO lnglatToMilePost = lnglatToMilePost(lngLatDTO);
            if (lnglatToMilePost != null) {
                Integer roadNo = lnglatToMilePost.getRoadNo();
//				查询路段基本信息
                RoadVO roadVO = feignClient.selectRoadByRoadNo(roadNo);
                if (roadVO != null) {// 已到达，地点：XXX-路段名称-桩号：K1+120附近
                    progressDesc = progressDesc + "-" + roadVO.getRoadName() + "-桩号：" + lnglatToMilePost.getMilePost()
                            + "附近";
                }
            }

        }

        progressDTO.setProgressDesc(progressDesc);
        progressDTO.setEventId(eventId);
        progressDTO.setOccurTime(serverTime);
        progressDTO.setLng(lng);
        progressDTO.setLat(dto.getLat());
        progressDTO.setTheme(1);
        progressDTO.setCreateUserId(loginUserId);

        // 验证个人进展是否重复或者倒退
        Map<String, Object> map = new HashMap<>();
        map.put("userId", loginUserId);
        map.put("eventId", eventId);
        List<ProgressVO> progressVOS = progressMapper.selectByEIdUId(map);
        if (!CollectionUtils.isEmpty(progressVOS)) {
            for (ProgressVO pro : progressVOS) {
                Integer cardPass = pro.getCardPass();
                Integer newCardPass = progressDTO.getCardPass();
                if (cardPass != null && newCardPass != null && cardPass >= newCardPass) {
                    return new ResponseVO(0, "同一个事件不允许多次重复或者倒退！");
                }
            }
        }

        boolean progressStatus = progressService.add(progressDTO);// 新增进展留痕
        map.put("eventStatus", eventStatus);
        map.put("id", eventId);
        eventMapper.updateEventStatus(map);// 更新事件状态
        if (reachFlag == 1) {// 需要检查eventDD中reachFlag
            IdStringDTO eventIdDTO = new IdStringDTO(eventId);
            EventDDVO eventDDVO = eventMapper.selectEventDD(eventIdDTO);
            Long dbReachTime = eventDDVO.getReachTime();
            if (reachTime > 0) {// 已到达打卡
                if (dbReachTime == null) {
                    // 更新event_dd的reach_time,reach_flag
                    map.put("reachTime", reachTime);
                    eventMapper.updateReachTime(map);
                }
            } else {
                Integer dbReachFlag = eventDDVO.getReachFlag();
                if (dbReachFlag == null || dbReachFlag == 0) {
                    // 更新event_dd的reach_flag
                    eventMapper.updateReachFlag(map);
                }
            }

        }
        return new ResponseVO(progressStatus);
    }

    public EventVO detail(IdStringDTO dto) {
        return eventMapper.detail(dto);
    }

    public List<EventStatVO> statNoDeal(EventQueryDTO dto) {
        return eventMapper.statNoDeal(dto);
    }

    public boolean delete(IdStringDTO idStringDTO) {
        return eventMapper.delete(idStringDTO) > 0;
    }

    private void pileNo2Lnglat(EventDTO eventDTO) {
        // 有路段编号和桩号，生成经纬度
        if (eventDTO.getRoadNo() != null && eventDTO.getMilePost() != null) {
            // 经纬度查询
            RoadPileNoDTO roadPileNoDTO = new RoadPileNoDTO(eventDTO.getRoadNo(), eventDTO.getDirectionNo(),
                    eventDTO.getMilePost());
            RoadPileNoVO roadPileNoVO = feignClient.pileno2Lnglat(roadPileNoDTO);
            if (roadPileNoVO != null) {
                eventDTO.setLng(roadPileNoVO.getLng());
                eventDTO.setLat(roadPileNoVO.getLat());
            }
        }
    }

    private void pileNo2Lnglat(EmerRescueDTO eventDTO) {
        // 有路段编号和桩号，生成经纬度
        if (eventDTO.getRoadNo() != null && eventDTO.getMilePost() != null) {
            // 经纬度查询
            RoadPileNoDTO roadPileNoDTO = new RoadPileNoDTO(eventDTO.getRoadNo(), eventDTO.getDirectionNo(),
                    eventDTO.getMilePost());
            RoadPileNoVO roadPileNoVO = feignClient.pileno2Lnglat(roadPileNoDTO);
            if (roadPileNoVO != null) {
                eventDTO.setLng(roadPileNoVO.getLng());
                eventDTO.setLat(roadPileNoVO.getLat());
            }
        }
    }

    public List<EventAttachVO> allAttach(IdStringDTO dto) {
        return eventMapper.allAttach(dto);
    }

    @Transactional
    public int addEmerRescue(EmerRescueDTO dto) {
        validReportSource(dto);
        String milePost = dto.getMilePost();
        if (dto.getRoadNo() != null && StringUtils.isNotBlank(milePost)) {
            Map<String, Object> map = new HashMap<>();
            map.put("roadNo", dto.getRoadNo());
            map.put("mile", NumberUtils.toInt(milePost.replace("K", "").replace("+", ""), -1));
            int rows = eventMapper.validRoadMilePost(map);
            if (rows == 0) {
                throw new ArgumentException("桩号不在范围");
            }
        }

        Long createTime = System.currentTimeMillis();
        dto.setCreateTime(createTime / 1000);
        if (dto.getOccupiedLanes() != null && dto.getOccupiedLanes().size() > 0) {
            eventMapper.addOccupiedLane(dto);
        }
        // 批量修改附件
        if (dto.getAttachs() != null && dto.getAttachs().size() > 0) {
            eventMapper.batchUpdateAttachs(dto);
        }

        // 批量新增告警来源的附件
        if (dto.getAlarmAttachs() != null && dto.getAlarmAttachs().size() > 0) {
            eventMapper.batchAddAlarmAttachs(dto);
        }
        // 批量新增路损
        if (dto.getRoadLoss() != null && dto.getRoadLoss().size() > 0) {
            eventMapper.addEventRoadLoss(dto);
        }
        String yyMMddHHmmssSSS = TimeUtils.getTimeString(TimeUtils.DATETIME, createTime);
        String eventNo = "DD-" + yyMMddHHmmssSSS;
        dto.setEventNo(eventNo);
        if (dto.getOrgId() != null) {
            eventMapper.addEventOrg(dto);
        }
        // 查询当前填报用户的信息
        UserSimpleVO recordManVO = feignClient.selectByUserId(dto.getRecordManId());
        dto.setRecordMan(recordManVO.getUserName());
        dto.setRecordManTel(recordManVO.getMobile());
        dto.setEventStatus(1);
        if (StringUtils.isNotBlank(dto.getMilePost())) {
            pileNo2Lnglat(dto);
        } else {
            dto.setLng(null);
            dto.setLat(null);
        }

        // 根据路段roadNo匹配责任公司
        if (dto.getRoadNo() != null) {
            OrganizationRoadVO organizationRoadVO = feignClient.selectByRoadNo(new IdIntegerDTO(dto.getRoadNo()));
            if (organizationRoadVO != null) {
                dto.setOrgId(organizationRoadVO.getOrgId());
                dto.setSourceId(organizationRoadVO.getSourceId());
            }
        }
        String reportMan = dto.getReportMan();
        if (StringUtils.isNotBlank(reportMan)) {
            Integer accidentCarNum = dto.getAccidentCarNum();
            if (accidentCarNum == null) {
                dto.setAccidentCarNum(1);
            }
        }

        EventDDDTO eventDDDto = new EventDDDTO();
        eventDDDto.setEventId(dto.getId());
        if (StringUtils.isNotBlank(reportMan)) {
            // 有求助人，事故车数默认置为1
            eventDDDto.setAccidentCarNum(1);
        }
        eventMapper.addEventDD(eventDDDto); // 所有事件都默认新建一条记录，初报需要使用到

        List<EventCarDTO> carDTOList = dto.getCarOwners();
        if (carDTOList != null && carDTOList.size() > 0) {
            // 此事件有多个车主信息
            for (EventCarDTO carDTO : carDTOList) {
                carDTO.setEventId(dto.getId());
            }
            // 写入event_car表
            eventMapper.addCarOwners(dto);
        }
        return eventMapper.addEmerRescue(dto);
    }

    @Transactional
    public int updateEmerRescueStepOne(EmerRescueDTO dto) {
        String id = dto.getId();
        if (StringUtils.isBlank(id)) {
            throw new ArgumentException("事件id不能为空");
        }
        validReportSource(dto);

        long serverTime = System.currentTimeMillis() / 1000;
        String milePost = dto.getMilePost();
        if (dto.getRoadNo() != null && StringUtils.isNotBlank(milePost)) {
            Map<String, Object> map = new HashMap<>();
            map.put("roadNo", dto.getRoadNo());
            map.put("mile", NumberUtils.toInt(milePost.replace("K", "").replace("+", ""), -1));
            int rows = eventMapper.validRoadMilePost(map);
            if (rows == 0) {
                throw new ArgumentException("桩号不在范围");
            }
        }

        UserSimpleVO loginUser = feignClient.selectByUserId(dto.getRecordManId());
        IdStringDTO eventIdDTO = new IdStringDTO(id);
        EventDetailVO eventDetailVO = eventMapper.selectEmerRescueByEventId(eventIdDTO);
        Integer dealStatus = eventDetailVO.getDealStatus();
        Integer delStatus = eventDetailVO.getDelStatus();
        String changeTip = "";
        boolean startPlanCard = false;
        if (delStatus != null && delStatus == 1) {
            // 删除状态下的事件不做预案变更
        } else if (dealStatus != null && dealStatus > 2 && dealStatus < 100) {
            String emerPlanId = eventDetailVO.getEmerPlanId();
            String newEmerPlanId = dto.getEmerPlanId();
            Integer level = eventDetailVO.getLevel();
            Integer newLevel = dto.getLevel();
            Integer eventThreeType = eventDetailVO.getEventThreeType();
            Integer eventFourType = eventDetailVO.getEventFourType();

            Integer newEventThreeType = dto.getEventThreeType();
            Integer newEventFourType = dto.getEventFourType();
            // 变更提示内容
            boolean changeEmerPlan = false;
            // 当前登录用户的信息
        	String operator = loginUser.getOrgName() + "-" + loginUser.getUserName();
        	EventLogDTO eventLog = new EventLogDTO();
        	eventLog.setInfoType("处置信息");
        	String beforeChange = "";
        	String afterChange = "";
        	boolean eventTypeChangeFlag = false;
            if (!emerPlanId.equals(newEmerPlanId)) {
            	EmerPlanVO oldEemerPlanVO = eventMapper.selectEmerPlan(new IdStringDTO(emerPlanId));
            	beforeChange += "预案：" + oldEemerPlanVO.getName() + "、、";
                EmerPlanVO emerPlanVO = eventMapper.selectEmerPlan(new IdStringDTO(newEmerPlanId));
                changeTip += "预案变更为" + emerPlanVO.getName() + "；";
                afterChange += "预案：" + emerPlanVO.getName() + "、、";
                changeEmerPlan = true;
                startPlanCard = true;
            }
            if (level != dto.getLevel()) {
            	beforeChange += "预案等级：" + EmerPlanLevelEnum.getCname(level) + "、、";
            	afterChange += "预案等级：" + EmerPlanLevelEnum.getCname(newLevel) + "、、";
                changeTip += "预案等级变更为" + EmerPlanLevelEnum.getCname(newLevel) + "；";
                startPlanCard = true;
            } else {
                if (changeEmerPlan) {
                    changeTip += "预案等级变更为" + EmerPlanLevelEnum.getCname(newLevel) + "；";
                }
            }
            if (newEventThreeType.equals(eventThreeType)) {
                if (newEventFourType != null && !newEventFourType.equals(eventFourType)) {
                    eventTypeChangeFlag = true;
                }
            } else {
                eventTypeChangeFlag = true;
            }
            if (eventTypeChangeFlag) {
                EventTypeDTO eventTypeDTO = new EventTypeDTO();
                eventTypeDTO.setEventThreeType(newEventThreeType);
                eventTypeDTO.setEventFourType(newEventFourType);
                EventTypeDetailVO eventTypeDetailVO = eventTypeMapper.selectAnDetail(eventTypeDTO);
                if (eventTypeDetailVO != null) {
                	String eventFourTypeName = eventDetailVO.getEventFourTypeName();
                	String newEventFourTypeName = eventTypeDetailVO.getEventFourTypeName();
                	
                	if (StringUtils.isNotBlank(eventFourTypeName)) {
                		beforeChange += "事件类型：" + eventFourTypeName + "、、";
                	} else {
                		beforeChange += "事件类型：" + eventDetailVO.getEventThreeTypeName() + "、、";
                	}
                	if (StringUtils.isNotBlank(newEventFourTypeName)) {
                		afterChange += "事件类型：" + newEventFourTypeName + "、、";
                        changeTip += "事件类型变更为" + newEventFourTypeName + "；";
                    } else {
                        changeTip += "事件类型变更为" + eventTypeDetailVO.getEventThreeTypeName() + "；";
                        afterChange += "事件类型：" + eventTypeDetailVO.getEventThreeTypeName() + "、、";
                    }
                }
            }
            eventLog.setCreateTime(TimeUtils.getTimeString(TimeUtils.FULL_TIME, serverTime * 1000));
        	eventLog.setOperator(operator);
            eventLog.setEventId(id);
            eventLog.setBeforeChange(beforeChange);
            eventLog.setAfterChange(afterChange);
            if (StringUtils.isNotBlank(afterChange)) {
            	eventLogMapper.add(eventLog);
            }
        }

        Long updateTime = System.currentTimeMillis();
        dto.setUpdateTime(updateTime / 1000);
        // 先删除后新增，占用车道
        eventMapper.deleteOccupiedLane(dto);
        if (dto.getOccupiedLanes() != null && dto.getOccupiedLanes().size() > 0) {
            eventMapper.addOccupiedLane(dto);
        }
        // 先删除路损，在新增
        if (dto.getRoadLoss() != null && dto.getRoadLoss().size() > 0) {
            eventMapper.deleteEventRoadLoss(dto);
            eventMapper.addEventRoadLoss(dto);
        }
        // 批量修改附件
        eventMapper.batchUpdateAttachs(dto);

        // 查询当前填报用户的信息
        dto.setRecordMan(loginUser.getUserName());
        dto.setRecordManTel(loginUser.getMobile());
        dto.setEventStatus(1);
        if (StringUtils.isNotBlank(dto.getMilePost())) {
            pileNo2Lnglat(dto);
        } else {
            dto.setLng(null);
            dto.setLat(null);
        }

        // 根据路段roadNo匹配责任公司
        if (dto.getSource() != null && dto.getSource() != 1 && dto.getRoadNo() != null) {
            OrganizationRoadVO organizationRoadVO = feignClient.selectByRoadNo(new IdIntegerDTO(dto.getRoadNo()));
            if (organizationRoadVO != null) {
                // 待处置的事件都不需要在第一步设置责任公司
//                dto.setOrgId(organizationRoadVO.getOrgId());
                dto.setSourceId(organizationRoadVO.getSourceId());
            }
        }
        String reportMan = dto.getReportMan();
        // 查询救援扩展表，有数据update，无数据insert
        EventDDVO eventDDVO = eventMapper.selectEventDD(eventIdDTO);
        EventDDDTO eventDDDto = new EventDDDTO();
        eventDDDto.setEventId(id);
        eventDDDto.setUpdateTime(new Date());
        if (eventDDVO == null) {
            if (StringUtils.isNotBlank(reportMan)) {
                eventDDDto.setAccidentCarNum(1);
            }
            eventMapper.addEventDD(eventDDDto);
        } else {
            BeanUtils.copyProperties(eventDDVO, eventDDDto);
            if (eventDDVO.getAccidentCarNum() == null) {
                if (StringUtils.isNotBlank(reportMan)) {
                    Integer accidentCarNum = dto.getAccidentCarNum();
                    if (accidentCarNum == null) {
                        eventDDDto.setAccidentCarNum(1);
                    }
                }
            }
            eventMapper.updateEventDD(eventDDDto);
        }

        // 更新事件关联车主信息表
        eventMapper.clearCarOwners(eventIdDTO); // 先删除原有的event_car信息
        if (dto.getCarOwners() != null && dto.getCarOwners().size() > 0) {
            eventMapper.addCarOwners(dto); // 填入最新的信息
        }

        int rows = eventMapper.updateEmerRescueStepOne(dto);
        if (rows > 0) {
            if (StringUtils.isNotBlank(changeTip)) {
                // 新增留痕进展
                ProgressDTO progressDTO = new ProgressDTO();
                progressDTO.setCreateTime(serverTime);
                progressDTO.setOccurTime(serverTime);
                progressDTO.setProgressDesc(changeTip);
                progressDTO.setEventId(id);
                progressDTO.setCreateUserId(dto.getRecordManId());
                progressDTO.setCardType(0);
                progressService.add(progressDTO);
            }
        }

        // 前面的启动预案、撤销预案卡片都设置为无效卡片
        if (startPlanCard) {
            eventMapper.updatePlanCardInvalid(eventIdDTO);
            String emerPlanId = dto.getEmerPlanId();
            String emerPlanLevelId = dto.getEmerPlanLevelId();
            EmerPlanLevelAuditVO emerplanLevelAuditVO = eventMapper.selectAudit(new IdStringDTO(emerPlanLevelId));
            // 新的启动预案卡片
            if (emerplanLevelAuditVO != null) {
                List<BasicUserVO> emerPlanAuditUsers = emerplanLevelAuditVO.getUsers();
                if (!CollectionUtils.isEmpty(emerPlanAuditUsers)) {
                    List<ProgressUserDTO> auditProgressUsers = new ArrayList<>();
                    String orgId = eventDetailVO.getOrgId();
                    for (BasicUserVO tmpVO : emerPlanAuditUsers) {
                        ProgressUserDTO emerPlanAuditUser = new ProgressUserDTO();
                        emerPlanAuditUser.setUserId(tmpVO.getUserId());
                        emerPlanAuditUser.setUserName(tmpVO.getUserName());
                        emerPlanAuditUser.setMobile(tmpVO.getMobile());
                        if (orgId.equals(tmpVO.getCompanyId())) {
                            auditProgressUsers.add(emerPlanAuditUser);
                        }
                    }

                    // 生成“预案启动”卡片
                    if (StringUtils.isNotBlank(emerPlanId)) {
                        EmerPlanVO emerPlanVO = eventMapper.selectEmerPlan(new IdStringDTO(emerPlanId));
                        String progressDesc = "该事件采用《" + emerPlanVO.getName() + "》，响应等级"
                                + EmerPlanLevelEnum.getCname(dto.getLevel()) + "，等待领导审核启动。";
                        // 新增启动预案卡片
                        ProgressDTO startPlanProgressDTO = new ProgressDTO();
                        startPlanProgressDTO.setCreateTime(serverTime + 1);
                        startPlanProgressDTO.setOccurTime(serverTime + 1);
                        startPlanProgressDTO.setProgressDesc(progressDesc);
                        startPlanProgressDTO.setAtUsers(auditProgressUsers);
                        startPlanProgressDTO.setEventId(id);
                        startPlanProgressDTO.setCreateUserId(dto.getRecordManId());
                        startPlanProgressDTO.setCardType(20);
                        progressService.add(startPlanProgressDTO);

                        // 执行预案相关状态
                        EventDealStatusDTO eventDealStatusDTO = new EventDealStatusDTO();
                        eventDealStatusDTO.setEventId(eventDetailVO.getId());
                        eventDealStatusDTO.setPlanResponse(0);
                        eventDealStatusDTO.setCancelPlan(null);
                        eventDealStatusDTO.setPlanResponseTime(0L);
                        eventDealStatusDTO.setUpdateTime(TimeUtils.getTimeString(TimeUtils.FULL_TIME));
                        int row = eventMapper.resetEventDealStatus(eventDealStatusDTO);
                        if (row == 0) {
                            // 查询初报的发生时间
                            ProgressVO initReport = progressService.selectInitReport(eventIdDTO);
                            eventDealStatusDTO.setInitReportTime(initReport.getOccurTime());
                            eventMapper.addEventDealStatus(eventDealStatusDTO);
                        }
                    }
                }
            }
        }

        return rows;
    }

    @Transactional
    public ResponseVO addAppEmerRescue(EmerRescueDTO emerRescueDTO) {
    	String eventId = UUID.randomUUID().toString();
    	String loginUserId = emerRescueDTO.getRecordManId();
        emerRescueDTO.setId(eventId);
        emerRescueDTO.setEventType(1);
        emerRescueDTO.setDelStatus(0);
        emerRescueDTO.setDealStatus(2); // 事件待处置
        emerRescueDTO.setAppRecordManId(loginUserId);
//        UserSimpleVO recordManVO = feignClient.selectByUserId(loginUserId);
        // 拼接事件简述
//        emerRescueDTO.setBriefDesc(joinAppEmerRescueBrief(emerRescueDTO, recordManVO));

        boolean matchSuccess = false;
        Integer eventFourType = emerRescueDTO.getEventFourType();
        Integer eventThreeType = emerRescueDTO.getEventThreeType();
        Integer eventTwoType = emerRescueDTO.getEventTwoType();
        EmerPlanMatchDTO emerPlanMatchDTO = new EmerPlanMatchDTO();
        emerPlanMatchDTO.setRoadNo(emerRescueDTO.getRoadNo());
        emerPlanMatchDTO.setEventTwoType(eventTwoType);
        emerPlanMatchDTO.setEventThreeType(eventThreeType);
        emerPlanMatchDTO.setEventFourType(eventFourType);
    	List<EmerPlanMatchVO> emerPlanMatchs = itsEmerFeignClient.emerPlanMatch(emerPlanMatchDTO);
    	int matchLevel = 10;
    	String emerPlanId = null;
    	String emerPlanLevelId = null;
        if (eventFourType != null && eventFourType.intValue() == 26) {//1-5-25-26交通事故
        	// 匹配预案，专项、综合
        	int level = 0;
        	Integer deathMan = emerRescueDTO.getDeathMan();
        	if (deathMan == null) {
        		deathMan = 0;
        	}
        	Integer injureMan = emerRescueDTO.getInjureMan();
        	if (injureMan == null) {
        		injureMan = 0;
        	}
        	if ((deathMan.intValue() >= 3) || (injureMan >= 11) || (deathMan >= 1 && injureMan >= 8) || (deathMan > 1 && injureMan >= 5)){
        		level = 5;
        	} else if ((deathMan.intValue() > 0) || (injureMan > 3 && injureMan <= 10)) {
        		level = 6;
        	} else if ((injureMan > 0 && injureMan <= 3)) {
        		level = 7;
        	} else {
        		level = 8;
        	}

        	if (!CollectionUtils.isEmpty(emerPlanMatchs)) {
        		for (EmerPlanMatchVO emerPlanMatch : emerPlanMatchs) {
					if (emerPlanMatch.getType() == 2) {
						List<EmerPlanMatchPlanVO> emerPlans = emerPlanMatch.getEmerPlans();
						for (EmerPlanMatchPlanVO emerPlan : emerPlans) {
							emerPlanId = emerPlan.getId();
							List<EmerPlanMatchLevelVO> levels = emerPlan.getLevels();
							for (EmerPlanMatchLevelVO levelVO : levels) {
								Integer planLevel = levelVO.getPlanLevel();
								if (planLevel != null && planLevel.intValue() == level) {
									emerPlanLevelId = levelVO.getEmerPlanLevelId();
									matchSuccess = true;
									break;
								}
							}
						}
					}
				}
        		if (!matchSuccess) {
        			for (EmerPlanMatchVO emerPlanMatch : emerPlanMatchs) {
    					if (emerPlanMatch.getType() == 1) {
    						List<EmerPlanMatchPlanVO> emerPlans = emerPlanMatch.getEmerPlans();
    						for (EmerPlanMatchPlanVO emerPlan : emerPlans) {
    							emerPlanId = emerPlan.getId();
    							List<EmerPlanMatchLevelVO> levels = emerPlan.getLevels();
    							for (EmerPlanMatchLevelVO levelVO : levels) {
    								Integer planLevel = levelVO.getPlanLevel();
    								if (planLevel != null && planLevel.intValue() == level) {
    									emerPlanLevelId = levelVO.getEmerPlanLevelId();
    									matchSuccess = true;
    									break;
    								}
    							}
    						}
    					}
    				}
        		}
        		
        	}
        	matchLevel = level;
        } else if (eventTwoType != null && eventTwoType.intValue() == 5) {//1-5突发事件
        	for (EmerPlanMatchVO emerPlanMatch : emerPlanMatchs) {
				if (emerPlanMatch.getType() == 2) {
					List<EmerPlanMatchPlanVO> emerPlans = emerPlanMatch.getEmerPlans();
					for (EmerPlanMatchPlanVO emerPlan : emerPlans) {
						emerPlanId = emerPlan.getId();
						List<EmerPlanMatchLevelVO> levels = emerPlan.getLevels();
						emerPlanLevelId = levels.get(0).getEmerPlanLevelId();
						matchLevel = levels.get(0).getPlanLevel();
						matchSuccess = true;
						break;
					}
				}
			}
        	if (!matchSuccess) {
        		for (EmerPlanMatchVO emerPlanMatch : emerPlanMatchs) {
        			if (emerPlanMatch.getType() == 1) {
        				List<EmerPlanMatchPlanVO> emerPlans = emerPlanMatch.getEmerPlans();
        				for (EmerPlanMatchPlanVO emerPlan : emerPlans) {
        					emerPlanId = emerPlan.getId();
        					List<EmerPlanMatchLevelVO> levels = emerPlan.getLevels();
        					emerPlanLevelId = levels.get(0).getEmerPlanLevelId();
        					matchLevel = levels.get(0).getPlanLevel();
        					matchSuccess = true;
        					break;
        				}
        			}
        		}
        	}
        } else if (eventTwoType != null && eventTwoType.intValue() == 6) {//1-6轻微事件
        	// 优先现场，然后通用
        	for (EmerPlanMatchVO emerPlanMatch : emerPlanMatchs) {
				if (emerPlanMatch.getType() == 3) {
					List<EmerPlanMatchPlanVO> emerPlans = emerPlanMatch.getEmerPlans();
					for (EmerPlanMatchPlanVO emerPlan : emerPlans) {
						List<EmerPlanMatchLevelVO> levels = emerPlan.getLevels();
						for (EmerPlanMatchLevelVO levelVO : levels) {
							Integer planLevel = levelVO.getPlanLevel();
							if (planLevel != null && planLevel.intValue() == 10) {
								emerPlanLevelId = levels.get(0).getEmerPlanLevelId();
								matchSuccess = true;
								emerPlanId = emerPlan.getId();
								break;
							}
						}
					}
				}
			}
        	if (!matchSuccess) {
        		for (EmerPlanMatchVO emerPlanMatch : emerPlanMatchs) {
            		if (emerPlanMatch.getType() == 10) {
            			List<EmerPlanMatchPlanVO> emerPlans = emerPlanMatch.getEmerPlans();
            			for (EmerPlanMatchPlanVO emerPlan : emerPlans) {
            				List<EmerPlanMatchLevelVO> levels = emerPlan.getLevels();
            				for (EmerPlanMatchLevelVO levelVO : levels) {
            					Integer planLevel = levelVO.getPlanLevel();
            					if (planLevel != null && planLevel.intValue() == 10) {
            						emerPlanLevelId = levels.get(0).getEmerPlanLevelId();
            						matchSuccess = true;
            						emerPlanId = emerPlan.getId();
            						break;
            					}
            				}
            			}
            		}
            	}
        	}
        	matchLevel = 10;
        }
        if (matchSuccess) {
        	emerRescueDTO.setLevel(matchLevel);
        	emerRescueDTO.setEmerPlanLevelId(emerPlanLevelId);
        	emerRescueDTO.setEmerPlanId(emerPlanId);
        }
        int addEvent = addEmerRescue(emerRescueDTO);
        if (matchSuccess) {
        	EmerPersonMatchDTO emerPersonMatchDTO = new EmerPersonMatchDTO();
        	emerPersonMatchDTO.setEventId(eventId);
        	emerPersonMatchDTO.setEmerPlanLevelId(emerPlanLevelId);
        	emerPersonMatchDTO.setLevel(matchLevel);
        	emerPersonMatchDTO.setRoadNo(emerRescueDTO.getRoadNo());
        	String milePost = emerRescueDTO.getMilePost();
        	if (StringUtils.isNoneBlank(milePost)) {
        		int mile = NumberUtils.toInt(milePost.replace("K", "").replace("+", ""), -1);
        		if (mile > -1) {
        			emerPersonMatchDTO.setMile(mile);
        		}
        	}
        	List<EmerPersonMatchVO> emerPersonMatchs = itsEmerFeignClient.emerPersonMatch(emerPersonMatchDTO);
        	if (!CollectionUtils.isEmpty(emerPersonMatchs)) {
				EventEmerUserDTO eventEmerUserDTO = new EventEmerUserDTO();
				List<String> emerRoles = new ArrayList<>();
				List<EmerUserDTO> emerUsers = new ArrayList<>();
				List<ProgressUserDTO> atUsers = new ArrayList<>();
				List<ProgressUserDTO> notifyUsers = new ArrayList<>();
				List<ProgressUserDTO> smsUsers = new ArrayList<>();
				emerPersonMatchToData(emerPersonMatchs, emerRoles, emerUsers, atUsers, notifyUsers, smsUsers);

				// 过滤 emerUsers 
        		eventEmerUserDTO.setEmerRoles(emerRoles);
        		eventEmerUserDTO.setEmerUsers(emerUsers);
        		eventEmerUserDTO.setEmerPlanId(emerPlanId);
        		eventEmerUserDTO.setId(eventId);
        		int ret = 0;
        		if (!CollectionUtils.isEmpty(emerUsers)) {
        			for (int i = emerUsers.size() - 1; i >= 0; i--) {
        				EmerUserDTO emerUserDTO = emerUsers.get(i);
                        if (emerUserDTO.getUsers() == null || emerUserDTO.getUsers().size() == 0) {
                        	emerUsers.remove(i);
                        }
                    }
        			if (!CollectionUtils.isEmpty(emerUsers)) {
        				eventEmerUserDTO.setEmerUsers(emerUsers);
        				// 查询今日值班人员 its-emer/emerDuty/selectTodayByRoadNo
        				// {"id":"0f87ffb8-e260-4a2c-a4b6-2b801031bde9","dutyTime":"2024-09-25"}
        				EmerDutyTodayDTO emerDutyTodayDTO = new EmerDutyTodayDTO();
        				emerDutyTodayDTO.setRoadNo(emerRescueDTO.getRoadNo());
        				emerDutyTodayDTO.setDutyTime(TimeUtils.getTimeString(TimeUtils.DATE));
        				List<TodayEmerDutyVO> todayEmerDutys = itsEmerFeignClient.emerDutySelectTodayByRoadNo(emerDutyTodayDTO);
        				Set<String> emerDutyUserIds = new HashSet<>();
        				for (TodayEmerDutyVO todayEmerDutyVO : todayEmerDutys) {
        					List<EmerUserVO> users = todayEmerDutyVO.getUsers();
        					for (EmerUserVO emerDutyUser : users) {
        						String emerDutyUserId = emerDutyUser.getUserId();
        						if (StringUtils.isNotBlank(emerDutyUserId)) {
        							emerDutyUserIds.add(emerDutyUserId);
        						}
							}
						}
        				if (emerDutyUserIds.size() > 0) {
        					for (EmerUserDTO emerUserDTO : emerUsers) {
        						List<UserDTO> users = emerUserDTO.getUsers();
        						if (users.size() > 1) {
        							// 是否存在一人为今日值班表的
        							boolean existEmerDutyUser = false;
        							List<UserDTO> todayDutyUsers = new ArrayList<>();
        							for (UserDTO user : users) {
        								if (emerDutyUserIds.contains(user.getUserId())) {
        									existEmerDutyUser = true;
        									todayDutyUsers.add(user);
        								}
									}
        							if (existEmerDutyUser) {
        								users.clear();
        								users.addAll(todayDutyUsers);
        							}
        						}
        						emerUserDTO.setUsers(users);
        					}
        				}
        				emerUserToData(emerRoles, emerUsers, atUsers, notifyUsers, smsUsers);
        				ret = addEmerUser(eventEmerUserDTO);
        			}
        		}
        		if (ret > 0) {
        			// 发送初报，短信，打电话提醒
	    			EventConfirmDTO eventConfirmDTO = new EventConfirmDTO();
	    			eventConfirmDTO.setCreateUserId(loginUserId);
	    			eventConfirmDTO.setRoadNo(emerRescueDTO.getRoadNo());
	    			eventConfirmDTO.setId(eventId);
	    			Integer reportSourceKey = emerRescueDTO.getReportSourceKey();
	    	    	String reportSource = emerRescueDTO.getReportSource();
	    	    	if (reportSourceKey != null && reportSourceKey == 99) {
	    	    		eventConfirmDTO.setReportSource(reportSource);
	    	    	} else {
		        		eventConfirmDTO.setReportSource(this.getDictItemName(123, "事件管理-接报来源", String.valueOf(reportSourceKey)));
	    	    	}
	    			eventConfirmDTO.setReportTime(TimeUtils.getTimeString(TimeUtils.DATE_TIME_2, emerRescueDTO.getReportTime()*1000));
	    			String briefDesc = emerRescueDTO.getBriefDesc();
	    			// TODO 拼接eventDesc
	    			EventQueryDTO tempDTO = new EventQueryDTO();
	    			tempDTO.setId(eventId);
	    			EventDetailVO detailVO = eventMapper.selectEventDetailById(tempDTO);
	    			eventConfirmDTO.setEventDesc(jointInitReport(detailVO));
	    	        String location = this.jointAddress(detailVO);// 需要拼接
	    	        eventConfirmDTO.setLocation(location);
	    			eventConfirmDTO.setProgressDesc("【事件初报】" + eventConfirmDTO.getReportTime() + "，" + "接" + eventConfirmDTO.getReportSource() + "报，地点" + location + "，" + eventConfirmDTO.getEventDesc());// 初报描述，需要拼接
	    			// atUsers 所有用户
	    			eventConfirmDTO.setAtUsers(atUsers);
	    			// 电话通知用户，强提醒
	    			eventConfirmDTO.setNotifyUsers(notifyUsers);
	    			// 短信用户
	    			eventConfirmDTO.setSmsUsers(smsUsers);
	    			if (!CollectionUtils.isEmpty(atUsers)) {
	    				LOGGER.info("自动分发事件-初报:{}:{}", eventId, GsonUtils.beanToJson(eventConfirmDTO));
	    				boolean add = initialReport(eventConfirmDTO);
	    				LOGGER.info("自动分发事件-初报结果:{}:{}", eventId, add);
	    				if (add && !CollectionUtils.isEmpty(notifyUsers)) {
	    					// 异步拉起机器人外呼通知结果状态更新
	    					try {
	    						List<String> jobIds = RobotCallTmpResultContext.obtainJobIds();
	    						robotCallHandlerService.handleRobotCallResponse(AssignJobs.INSTANCE_ID, jobIds, null, eventId);
	    					} finally {
	    						RobotCallTmpResultContext.remove();
	    					}
	    				}
	    			}
        		}
        	}
        }
        if (addEvent > 0) {
            notifyAddWebSoket(emerRescueDTO); // 通知websoket
        }
        return new ResponseVO(addEvent, eventId);
    }

    private void emerPersonMatchToData(List<EmerPersonMatchVO> emerPersonMatchs, List<String> emerRoles, List<EmerUserDTO> emerUsers, List<ProgressUserDTO> atUsers, 
    		List<ProgressUserDTO> notifyUsers, List<ProgressUserDTO> smsUsers) {
    	for (EmerPersonMatchVO tmp : emerPersonMatchs) {
			Integer emerPlanFlag = tmp.getEmerPlanFlag();
			if (emerPlanFlag != null && emerPlanFlag.intValue() == 1) {
				boolean existUser = false;
				List<EmerGroupMatchVO> emerGroups = tmp.getEmerGroups();
				for (EmerGroupMatchVO group : emerGroups) {
					Integer forceRemind = group.getForceRemind();
					Integer smsRemind = group.getSmsRemind();
					
					EmerUserDTO emerUserDTO = new EmerUserDTO();
					emerUserDTO.setEmerGroupId(group.getEmerGroupId());
					emerUserDTO.setEmerRoleId(tmp.getEmerRoleId());
					emerUserDTO.setForceRemind(forceRemind);
					emerUserDTO.setSmsRemind(smsRemind);
					
					List<User> users = group.getUsers();
					List<UserDTO> userDTOs = new ArrayList<>();
					for (User user : users) {
						String userId = user.getUserId();
						String userName = user.getUserName();
						String mobile = user.getMobile();
						
						UserDTO userDTO = new UserDTO();
						userDTO.setUserId(userId);
						userDTO.setUserName(userName);
						userDTO.setMobile(mobile);
						userDTOs.add(userDTO);
						
						ProgressUserDTO atUser = new ProgressUserDTO();
						atUser.setUserId(userId);
						atUser.setUserName(userName);
						atUser.setMobile(mobile);
						existUser = true;
						atUsers.add(atUser);
						if (forceRemind != null && forceRemind.intValue() == 1) {
							ProgressUserDTO notifyUser = new ProgressUserDTO();
							notifyUser.setUserId(userId);
							notifyUser.setUserName(userName);
							notifyUser.setMobile(mobile);
							notifyUsers.add(notifyUser);
						}
						if (smsRemind != null && smsRemind.intValue() == 1) {
							ProgressUserDTO smsUser = new ProgressUserDTO();
							smsUser.setUserId(userId);
							smsUser.setUserName(userName);
							smsUser.setMobile(mobile);
							smsUsers.add(smsUser);
						}
					}
					emerUserDTO.setUsers(userDTOs);
					emerUsers.add(emerUserDTO);
				}
				if (existUser) {
					emerRoles.add(tmp.getEmerRoleId());
				}
			}
		}
    }

    private void emerUserToData(List<String> emerRoles, List<EmerUserDTO> emerUsers, List<ProgressUserDTO> atUsers, 
    		List<ProgressUserDTO> notifyUsers, List<ProgressUserDTO> smsUsers) {
    	emerRoles.clear();
    	atUsers.clear();
    	notifyUsers.clear();
    	smsUsers.clear();
    	for (EmerUserDTO tmp : emerUsers) {
			List<UserDTO> users = tmp.getUsers();
			Integer forceRemind = tmp.getForceRemind();
			Integer smsRemind = tmp.getSmsRemind();
			String emerRoleId = tmp.getEmerRoleId();
				
			for (UserDTO user : users) {
				String userId = user.getUserId();
				String userName = user.getUserName();
				String mobile = user.getMobile();
				
				ProgressUserDTO atUser = new ProgressUserDTO();
				atUser.setUserId(userId);
				atUser.setUserName(userName);
				atUser.setMobile(mobile);
				atUsers.add(atUser);
				if (forceRemind != null && forceRemind.intValue() == 1) {
					ProgressUserDTO notifyUser = new ProgressUserDTO();
					notifyUser.setUserId(userId);
					notifyUser.setUserName(userName);
					notifyUser.setMobile(mobile);
					notifyUsers.add(notifyUser);
				}
				if (smsRemind != null && smsRemind.intValue() == 1) {
					ProgressUserDTO smsUser = new ProgressUserDTO();
					smsUser.setUserId(userId);
					smsUser.setUserName(userName);
					smsUser.setMobile(mobile);
					smsUsers.add(smsUser);
				}
			}
			if (!emerRoles.contains(emerRoleId)) {
				emerRoles.add(emerRoleId);
			}
    	}
    }
    
    
    // App修改事件不拼接事件简述
    @Transactional
    public ResponseVO updateAppEmerRescue(EmerRescueDTO dto) {
    	String eventId = dto.getId();
    	IdStringDTO eventIdDTO = new IdStringDTO(eventId);
        String recordManId = dto.getRecordManId();
        UserSimpleVO loginUser = feignClient.selectByUserId(recordManId);
        
        EventDetailVO detailVO = eventMapper.selectEmerRescueByEventId(eventIdDTO);
        dto.setAppRecordManId(recordManId);
        // 先删除后新增，占用车道
        eventMapper.deleteOccupiedLane(dto);
        if (dto.getOccupiedLanes() != null && dto.getOccupiedLanes().size() > 0) {
            eventMapper.addOccupiedLane(dto);
        }
        // 先删除路损，在新增
        if (dto.getRoadLoss() != null && dto.getRoadLoss().size() > 0) {
            eventMapper.deleteEventRoadLoss(dto);
            eventMapper.addEventRoadLoss(dto);
        }
        // 批量修改附件
        eventMapper.batchUpdateAttachs(dto);
        if (StringUtils.isNotBlank(dto.getMilePost())) {
            pileNo2Lnglat(dto);
        } else {
            dto.setLng(null);
            dto.setLat(null);
        }
        int ret = eventMapper.updateAppEmerRescue(dto);
        updateAppEmerRescueEventLog(detailVO, dto, loginUser, eventId, System.currentTimeMillis() / 1000);
        return new ResponseVO(ret, dto.getId());
    }
    /**
     * @description 小程序修改应急救援的基本信息的事件日志
     * @param detailVO
     * @param dto
     */
    private void updateAppEmerRescueEventLog(EventDetailVO detailVO, EmerRescueDTO dto, UserSimpleVO loginUser, String eventId, Long serverTime) {
    	String beforeChange = "";
    	String afterChange = "";
    	
    	Integer dbRoadNo = detailVO.getRoadNo();
    	Integer roadNo = dto.getRoadNo();
    	String dbDirectionNo = detailVO.getDirectionNo();
    	String directionNo = dto.getDirectionNo();
    	if (dbRoadNo != null && roadNo != null && dbDirectionNo != null && directionNo != null) {
    		if (dbRoadNo.intValue() != roadNo.intValue() || !dbDirectionNo.equals(directionNo)) {
    			beforeChange += "路段方向：" + detailVO.getRoadName() + "/" + detailVO.getDirectionName() + "、、";
    			RoadDirectionVO anRoadDirection = feignClient.selectAnRoadDirection(new RoadDirectionDTO(dto.getRoadNo(), dto.getDirectionNo()));
        		afterChange += "路段方向：" + anRoadDirection.getRoadName() + "/" + anRoadDirection.getDirectionName() + "、、";
    		}
    	}
    	// 事故形态
    	String dbAccidentPattern = detailVO.getAccidentPattern();
    	String accidentPattern = dto.getAccidentPattern();
    	if (!StringUtils.equals(dbAccidentPattern, accidentPattern)) {
    		if (StringUtils.isNoneBlank(dbAccidentPattern)) {
    			beforeChange += "事故形态：" + dbAccidentPattern + "、、";
    		} else {
    			beforeChange += "事故形态：-、、";
    		}
    		if (StringUtils.isNoneBlank(accidentPattern)) {
    			afterChange += "事故形态：" + accidentPattern + "、、";
    		} else {
    			afterChange += "事故形态：-、、";
    		}
    	}

    	List<OccupiedLaneVO> dbOccupiedLanes = detailVO.getOccupiedLanes();
    	List<Integer> occupiedLanes = dto.getOccupiedLanes();
    	int dbOccupiedLaneLength = 0;
    	int occupiedLaneLength = 0;
    	if (!CollectionUtils.isEmpty(dbOccupiedLanes) ) {
    		dbOccupiedLaneLength = dbOccupiedLanes.size();
    	}
    	if (!CollectionUtils.isEmpty(occupiedLanes) ) {
    		occupiedLaneLength = occupiedLanes.size();
    	}
    	boolean change = false;
    	List<Integer> dbLanes = new ArrayList<>();
    	if (dbOccupiedLaneLength > 0) {
    		for (OccupiedLaneVO dbOccupiedLane : dbOccupiedLanes) {
        		Integer occupiedLane = dbOccupiedLane.getOccupiedLane();
        		dbLanes.add(occupiedLane);
    		}
    		Collections.sort(dbLanes);
    	}
    	if (occupiedLaneLength > 0) {
    		Collections.sort(occupiedLanes);
    	}
    	if (dbOccupiedLaneLength != occupiedLaneLength) { // 长度不一样，车道有变更
    		change = true;
    	} else if (dbOccupiedLaneLength > 0) {
    		for (int i = 0; i < dbOccupiedLaneLength; i++) {
    			if (dbLanes.get(i) != occupiedLanes.get(i)) {
    				change = true;
    				break;
    			}
			}
    	}
    	if (change) {
    		beforeChange += "影响车道：";
    		if (dbOccupiedLaneLength > 0) {
    			for (Integer dblane : dbLanes) {
    				beforeChange += this.getDictItemName(70, "事件管理-影响车道", String.valueOf(dblane)) +  "、";
				}
    			beforeChange +=  "、";
    		} else {
    			beforeChange += "-、、";
    		}
    		afterChange += "影响车道：";
    		if (occupiedLaneLength > 0) {
    			for (Integer dblane : occupiedLanes) {
    				afterChange += this.getDictItemName(70, "事件管理-影响车道", String.valueOf(dblane)) +  "、";
    			}
    			afterChange +=  "、";
    		} else {
    			afterChange += "-、、";
    		}
    	}
    	String dbWeather = detailVO.getWeather();
    	String weather = dto.getWeather();
    	if (!StringUtils.equals(dbWeather, weather)) {
    		if (StringUtils.isBlank(dbWeather)) {
    			beforeChange += "天气：-、、";
    		} else {
    			beforeChange += "天气：" + this.getDictItemName(61, "天气代码图标", dbWeather) + "、、";
    		}
    		if (StringUtils.isBlank(weather)) {
    			afterChange += "天气：-、、";
    		} else {
    			afterChange += "天气：" + this.getDictItemName(61, "天气代码图标", weather) + "、、";
    		}
    	}
    	Integer dbDeathMan = detailVO.getDeathMan();
    	Integer deathMan = dto.getDeathMan();
    	if (dbDeathMan != deathMan) {
    		if (dbDeathMan == null) {
    			beforeChange += "死亡：-、、";
    		} else if (dbDeathMan == -1) {
    			beforeChange += "死亡：不详、、";
    		} else if (dbDeathMan == 0) {
    			beforeChange += "死亡：无、、";
    		} else if (dbDeathMan > 0) {
    			beforeChange += "死亡：" + dbDeathMan + "、、";
    		}
    		if (deathMan == null) {
    			afterChange += "死亡：-、、";
    		} else if (deathMan == -1) {
    			afterChange += "死亡：不详、、";
    		} else if (deathMan == 0) {
    			afterChange += "死亡：无、、";
    		} else if (deathMan > 0) {
    			afterChange += "死亡：" + deathMan + "、、";
    		}
    	}
    	Integer dbInjureMan = detailVO.getInjureMan();
    	Integer injureMan = dto.getInjureMan();
    	if (dbInjureMan != injureMan) {
    		if (dbInjureMan == null) {
    			beforeChange += "受伤：-、、";
    		} else if (dbInjureMan == -1) {
    			beforeChange += "受伤：不详、、";
    		} else if (dbInjureMan == 0) {
    			beforeChange += "受伤：无、、";
    		} else if (dbInjureMan > 0) {
    			beforeChange += "受伤：" + dbInjureMan + "、、";
    		}
    		if (injureMan == null) {
    			afterChange += "受伤：-、、";
    		} else if (injureMan == -1) {
    			afterChange += "受伤：不详、、";
    		} else if (injureMan == 0) {
    			afterChange += "受伤：无、、";
    		} else if (injureMan > 0) {
    			afterChange += "受伤：" + injureMan + "、、";
    		}
    	}
    	if (beforeChange.length() > 2) {
    		// 当前登录用户的信息
    		String operator = loginUser.getOrgName() + "-" + loginUser.getUserName();
    		EventLogDTO eventLog = new EventLogDTO();
    		eventLog.setInfoType("基本信息");
    		eventLog.setBeforeChange(beforeChange.substring(0, beforeChange.length()-2));
    		eventLog.setAfterChange(afterChange.substring(0, afterChange.length()-2));
    		eventLog.setEventId(eventId);
    		eventLog.setCreateTime(TimeUtils.getTimeString(TimeUtils.FULL_TIME, serverTime * 1000));
    		eventLog.setOperator(operator);
    		eventLogMapper.add(eventLog);
    	}
    	
    }

    private String joinAppEmerRescueBrief(EmerRescueDTO emerRescueDTO, UserSimpleVO recordManVO) {
        String time = TimeUtils.getTimeString(TimeUtils.DATE_TIME_2, emerRescueDTO.getReportTime() * 1000);
        return time + "接" + recordManVO.getCompanyName() + "-" + recordManVO.getUserName() + "反馈："
                + emerRescueDTO.getBriefDesc();
    }

    public PageInfo<EmerResuceVO> pageEmerRescue(PageDTO pageDTO, EventQueryDTO dto) {
        String userId = dto.getUserId();
        boolean canDistribute = false;
        // 查询role_event的distribute
        int distribute = eventMapper.selectEventDistribute(userId);
        if (distribute > 0) {
            canDistribute = true;
        }
        if (dto.getStartTime() == null) {
			dto.setStartTime(System.currentTimeMillis() / 1000 - day7);// 7天内的救援事件
			dto.setEndTime(System.currentTimeMillis() / 1000 + 3600);
		}
        PageHelper.startPage(pageDTO.getPage(), pageDTO.getLimit());
        List<EmerResuceVO> list = eventMapper.selectEmerRescue(dto);
        if (!CollectionUtils.isEmpty(list)) {
            List<OrganRoadVO> organRoadList = eventMapper.selectOrganRoad();
            List<EmerResuceVO> finishList = new ArrayList<>();
            for (EmerResuceVO vo : list) {
                vo.setOrgName(this.getRoadOrganShortName(organRoadList, vo.getRoadNo(), vo.getSourceId()));
                vo.setCanProcess(canDistribute);
                vo.setCanDistribute(canDistribute);
                if (vo.getDealStatus() == 100) {
                    finishList.add(vo);
                }
            }

            if (!CollectionUtils.isEmpty(finishList)) {
                Map<String, Object> map = new HashMap<>();
                map.put("list", finishList);
                map.put("userId", userId);
                List<EventAnalysisAuthVO> res = eventMapper.selectAnalysisAuth(map);
                for (EventAnalysisAuthVO vo : res) {
                    map.put(vo.getEventId(), vo);
                }
                for (EmerResuceVO vo : list) {
                    vo.setEventAnalysisAuth((EventAnalysisAuthVO) map.get(vo.getId()));
                }
            }
            // 查询事件初报的审核状态
            List<String> eventIds = new ArrayList<>();
            for (EmerResuceVO vo : list) {
            	eventIds.add(vo.getId());
            }
            EventInfoReviewDTO eventInfoReviewDTO = new EventInfoReviewDTO();
            eventInfoReviewDTO.setInfoType(1);
            eventInfoReviewDTO.setEventIds(eventIds);
            List<EventInfoReviewVO> eventInfoReviews = eventMapper.selectEventInfoReview(eventInfoReviewDTO);
            if (!CollectionUtils.isEmpty(eventInfoReviews)) {
            	Map<String, Integer> reviewMap = new HashMap<>();
            	for (EventInfoReviewVO tmp : eventInfoReviews) {
            		reviewMap.put(tmp.getEventId(), tmp.getReviewStatus());
				}
            	for (EmerResuceVO vo : list) {
                	vo.setReviewStatus(reviewMap.get(vo.getId()));
                }
            }
            
        }
        PageInfo<EmerResuceVO> pageInfo = new PageInfo<>(list);
        return pageInfo;
    }

    public String getOrgId(List<OrganRoadVO> list, Integer roadNo) {
        String orgId = "";
        for (OrganRoadVO vo : list) {
            if (vo.getRoadNo().equals(roadNo)) {
                orgId = vo.getOrgId();
                break;
            }
        }
        return orgId;
    }

    public Map<String, Integer> selectSeqNo(EventQueryDTO dto) {
        String id = dto.getId();
        if (StringUtils.isBlank(id)) {
            throw new ArgumentException("事件id不能为空");
        }
        List<EmerResuceVO> list = eventMapper.selectSeqNo(dto);
        Map<String, Integer> retMap = new HashMap<>();
        retMap.put("seqNo", 0);
        if (CollectionUtils.isEmpty(list)) {
            return retMap;
        }
        for (int i = 0; i < list.size(); i++) {
            if (id.equals(list.get(i).getId())) {
                retMap.put("seqNo", i + 1);
                break;
            }
        }
        return retMap;
    }

    /**
     * 查询权限范围内救援的处置中事件
     *
     * @param dto
     * @return
     */
    public List<EmerResuceVO> selectAppEmerRescue(EventQueryDTO dto) {
    	if (dto.getStartTime() == null) {
			dto.setStartTime(System.currentTimeMillis() / 1000 - day7);// 7天内的救援事件
			dto.setEndTime(System.currentTimeMillis() / 1000 + 3600);
		}
        return eventMapper.selectEmerRescue(dto);
    }

    public List<EventTypeVO> treeRescueType() {
        List<EventTypeVO> ret = new ArrayList<EventTypeVO>();
        List<EventTypeVO> list = eventMapper.selectEmerRescueType();
        List<EventTypeVO> parent = new ArrayList<EventTypeVO>();
        for (int i = list.size() - 1; i >= 0; i--) {
            EventTypeVO vo = list.get(i);
            if (StringUtils.isBlank(vo.getPid())) {// 一级菜单
                ret.add(vo);
                parent.add(vo);
                list.remove(i);
            }
        }
        while (list.size() > 0 && parent.size() > 0) {
            parent = eventTypeRecursive(list, parent);
        }

        return ret;
    }

    public List<EventTypeVO> treeEventType(EventTypeDTO dto) {
        List<EventTypeVO> ret = new ArrayList<EventTypeVO>();
        List<EventTypeVO> list = eventMapper.selectEventType(dto);
        List<EventTypeVO> parent = new ArrayList<EventTypeVO>();
        for (int i = list.size() - 1; i >= 0; i--) {
            EventTypeVO vo = list.get(i);
            if (StringUtils.isBlank(vo.getPid())) {// 一级菜单
                ret.add(vo);
                parent.add(vo);
                list.remove(i);
            }
        }
        while (list.size() > 0 && parent.size() > 0) {
            parent = eventTypeRecursive(list, parent);
        }

        return ret;
    }

    public List<BusinessTypeVO> treeBusinessType(BusinessTypeDTO dto) {
        List<BusinessTypeVO> ret = new ArrayList<BusinessTypeVO>();
        List<BusinessTypeVO> list = eventMapper.selectBusinessType(dto);
        List<BusinessTypeVO> parent = new ArrayList<BusinessTypeVO>();
        for (int i = list.size() - 1; i >= 0; i--) {
            BusinessTypeVO vo = list.get(i);
            if (vo.getParentId() != null && vo.getParentId() == 0) {// 一级菜单
                ret.add(vo);
                parent.add(vo);
                list.remove(i);
            }
        }
        while (list.size() > 0 && parent.size() > 0) {
            parent = businessTypeRecursive(list, parent);
        }

        return ret;
    }

    public List<EventTypeVO> eventTypeRecursive(List<EventTypeVO> source, List<EventTypeVO> parent) {
        List<EventTypeVO> nextParent = new ArrayList<EventTypeVO>();
        if (source.size() == 0) {
            return nextParent;
        }
        for (int i = source.size() - 1; i >= 0; i--) {
            EventTypeVO vo = source.get(i);
            for (EventTypeVO targetVO : parent) {
                if (targetVO.getValue().equals(vo.getPid())) {
                    targetVO.getChildren().add(vo);
                    nextParent.add(vo);
                    source.remove(i);
                    break;
                }
            }
        }
        return nextParent;
    }

    public List<BusinessTypeVO> businessTypeRecursive(List<BusinessTypeVO> source, List<BusinessTypeVO> parent) {
        List<BusinessTypeVO> nextParent = new ArrayList<BusinessTypeVO>();
        if (source.size() == 0) {
            return nextParent;
        }
        for (int i = source.size() - 1; i >= 0; i--) {
            BusinessTypeVO vo = source.get(i);
            for (BusinessTypeVO targetVO : parent) {
                if (targetVO.getId().equals(vo.getParentId())) {
                    targetVO.getChildren().add(vo);
                    nextParent.add(vo);
                    source.remove(i);
                    break;
                }
            }
        }
        return nextParent;
    }

    public EventDetailVO selectEmerRescueByEventId(IdStringDTO dto) {
        EventDetailVO eventDetailVO = eventMapper.selectEmerRescueByEventId(dto);
        if (StringUtils.isNotBlank(eventDetailVO.getEmerPlanId())) {
            // 查询该预案的所有应急角色
            dto.setId(eventDetailVO.getEmerPlanId());
            List<EmerRoleVO> emerRoles = eventMapper.selectEmerRoles(dto);
            for (EmerRoleVO r : eventDetailVO.getEmerRoles()) {
                for (EmerRoleVO all : emerRoles) {
                    if (all.getId().equals(r.getId())) {
                        all.setFlag(1);
                        break;
                    }
                }
            }
            eventDetailVO.setEmerRoles(emerRoles);
        }
        if (eventDetailVO != null) {
            List<OrganRoadVO> organRoadList = eventMapper.selectOrganRoad();
            eventDetailVO.setOrgShortName(
                    this.getRoadOrganShortName(organRoadList, eventDetailVO.getRoadNo(), eventDetailVO.getSourceId()));
        }
        return eventDetailVO;
    }

    private String getRoadOrganShortName(List<OrganRoadVO> organRoadList, Integer roadNo, Integer sourceId) {
        String shortName = "";

        if (roadNo != null) {
            String orgId = getOrgId(organRoadList, roadNo);
            shortName = OrganShortNameEnum.getName(orgId);
        } else {
            if (sourceId != null) {
                shortName = SourceIdEnum.getName(sourceId);
            }
        }
        return shortName;
    }

    public EventDetailVO selectEventDetailById(EventQueryDTO dto) {
        EventDetailVO vo = eventMapper.selectEventDetailById(dto);
        if (StringUtils.isNotBlank(vo.getEmerPlanId())) {
            // 查询该预案的所有应急角色
            IdStringDTO idStringDTO = new IdStringDTO();
            idStringDTO.setId(vo.getEmerPlanId());
            List<EmerRoleVO> emerRoles = eventMapper.selectEmerRoles(idStringDTO);
            for (EmerRoleVO r : vo.getEmerRoles()) {
                for (EmerRoleVO all : emerRoles) {
                    if (all.getId().equals(r.getId())) {
                        all.setFlag(1);
                        break;
                    }
                }
            }
            vo.setEmerRoles(emerRoles);
        }
        // 排障已同意，并且事件状态大于4 为不可出车状态
        if (vo.getCanOutSet() != null && vo.getCanOutSet() == 1 && vo.getEventStatus() > 4) {
            vo.setCanOutSet(null);
        }
        return vo;
    }

    /**
     * @描述 拼接接报来源
     */
    public String jointSource(EventDetailVO eventDetailVO) {
        return eventDetailVO.getReportSourceStr();
    }

    /**
     * @描述 拼接事件地点
     */
    public String jointAddress(EventDetailVO eventDetailVO) {
        if (StringUtils.isNotBlank(eventDetailVO.getReportSourceStr())
                && StringUtils.isNotBlank(eventDetailVO.getLocation())) {
            return eventDetailVO.getLocation();
        } else {
            String address = eventDetailVO.getRoadName() + "，" + eventDetailVO.getDirectionName() + "，";
            String milePost = eventDetailVO.getMilePost();
            if (StringUtils.isNotBlank(milePost)) {
                address += eventDetailVO.getMilePost() + "，";
            }
            String eventAddress = eventDetailVO.getEventAddress();
            int source = eventDetailVO.getSource().intValue();
            if (source == 1) {
                if (StringUtils.isNotBlank(eventAddress)) {
                    address += eventAddress + "，";
                }
            } else {
                if (StringUtils.isNotBlank(milePost)) {
                    NearestFacilityTipVO vo = nearestFacilityById(new IdStringDTO(eventDetailVO.getId()));
                    if (vo != null && StringUtils.isNotBlank(vo.getTip())) {
                        address += vo.getTip() + "，";
                    }
                }
            }
            return address.substring(0, address.length() - 1);
        }
    }

    /**
     * @描述 拼接事件初报描述
     */
    public String jointInitReport(EventDetailVO eventDetailVO) {
        String briefDesc = eventDetailVO.getEventDesc();
        if (StringUtils.isNotBlank(briefDesc)) {
            if (briefDesc.endsWith("。")) {
                briefDesc = briefDesc.substring(0, briefDesc.length() - 1);
            }
        } else {
        	briefDesc = eventDetailVO.getBriefDesc();
            if (briefDesc != null && briefDesc.length() > 0) {
                String reportSource = eventDetailVO.getReportSource();
                if (StringUtils.isNotBlank(reportSource)) {
                    briefDesc = "接" + reportSource + "报，" + briefDesc;
                }

                String lastChar = briefDesc.substring(briefDesc.length() - 1);
                if ("。".equals(lastChar) || "，".equals(lastChar) || ".".equals(lastChar) || ",".equals(lastChar)) {
                    briefDesc = briefDesc.substring(0, briefDesc.length() - 1);
                }
                briefDesc += "，";
            } else {
                briefDesc = "";
            }
            Integer deathMan = eventDetailVO.getDeathMan();
            Integer injureMan = eventDetailVO.getInjureMan();
            Integer missMan = eventDetailVO.getMissMan();
            Float incomeLose = eventDetailVO.getIncomeLose();
            List<OccupiedLaneVO> occupiedLanes = eventDetailVO.getOccupiedLanes();
            if (!CollectionUtils.isEmpty(occupiedLanes)) {
                briefDesc += "占用";
                for (OccupiedLaneVO occupiedLaneVO : occupiedLanes) {
                    briefDesc += OccupiedLaneEnum.getLevelStrByValue(occupiedLaneVO.getOccupiedLane()) + "、";
                }
                briefDesc = briefDesc.substring(0, briefDesc.length() - 1) + "，";
            }
            if (deathMan != null) {
                if (deathMan > 0) {
                    briefDesc += ("死亡 " + deathMan + "人，");
                } else if (deathMan == 0) {
                    briefDesc += ("无人死亡，");
                } else if (deathMan == -1) {
                    briefDesc += ("死亡人数不详，");
                } else if (deathMan == -2) {
                    briefDesc += ("有人死亡，");
                }
            }
            if (injureMan != null) {
                if (injureMan > 0) {
                    briefDesc += ("受伤 " + injureMan + "人，");
                } else if (injureMan == 0) {
                    briefDesc += ("无人受伤，");
                } else if (injureMan == -1) {
                    briefDesc += ("受伤人数不详，");
                } else if (injureMan == -2) {
                    briefDesc += ("有人受伤，");
                }
            }
            if (missMan != null) {
                if (missMan > 0) {
                    briefDesc += ("失踪 " + missMan + "人，");
                }
            }
            if (incomeLose != null) {
                if (incomeLose > 0) {
                    briefDesc += ("路产损失 " + incomeLose + " 元，");
                } else if (incomeLose == 0) {
                    briefDesc += ("无路产损失，");
                } else if (incomeLose == -1) {
                    briefDesc += ("路产损失不详，");
                } else if (incomeLose == -2) {
                    briefDesc += ("有路产损失，");
                }
            }
			Float congestionLength = eventDetailVO.getCongestionLength();
			if (congestionLength > 0) {
				briefDesc += ("造成拥堵" + congestionLength + "米，");
			} else if (congestionLength == 0) {
				briefDesc += "暂无拥堵，";
			} else if (congestionLength == -1) {
				briefDesc += "拥堵情况不详，";
			} else if (congestionLength == -2) {
				briefDesc += "有拥堵情况，";
			}
            briefDesc = briefDesc.substring(0, briefDesc.length() - 1);
        }
        return briefDesc;
    }

    private int updateEventDD(EventConfirmDTO dto) {
        EventDDDTO eventDDDTO = new EventDDDTO();
        eventDDDTO.setEventId(dto.getId());
        eventDDDTO.setReportTime(dto.getReportTime());
        eventDDDTO.setReportSource(dto.getReportSource());
        eventDDDTO.setLocation(dto.getLocation());
        eventDDDTO.setEventDesc(dto.getEventDesc());
        eventDDDTO.setUpdateTime(new Date());
        return eventMapper.updateEventDD(eventDDDTO);
    }

    /**
     * @描述 修改事件delStatus为0，事件状态为处置中、责任公司、分发时间
     */
    private int updateEventSatus(EventConfirmDTO dto, EventDetailVO eventDetailVO) {
        Integer roadNo = eventDetailVO.getRoadNo();
        Map<String, Object> map = new HashMap<>();
        map.put("id", dto.getId());
        if (roadNo != null) {
            // 根据路段roadNo匹配责任公司
            OrganizationRoadVO organizationRoadVO = feignClient.selectByRoadNo(new IdIntegerDTO(dto.getRoadNo()));
            if (organizationRoadVO != null) {
                String orgId = organizationRoadVO.getOrgId();
                map.put("orgId", orgId);
                eventDetailVO.setOrgId(orgId);
            }
        }
        map.put("distributeTime", dto.getServerTime());
        return eventMapper.updateDelStatus0(map);
    }

    /**
     * @param m             手机号列表，英文逗号隔开
     * @param eventDetailVO 事件详情对象
     * @param progressDTO   初报进展对象
     * @param atUsers       初报选中的处置人员
     * @描述 初报发送短信、事件WebSocket推送、事件推送到微信卡片
     */
    private void initialReportSmsAndPush(String m, EventDetailVO eventDetailVO, ProgressDTO progressDTO, List<ProgressUserDTO> atUsers) {
        String[] mobileArr = m.split(",");
        SmsTemplateDTO smsTemplateDTO = new SmsTemplateDTO();
        Long reportTime = eventDetailVO.getReportTime();

        String time = TimeUtils.getTimeString(TimeUtils.DATE_TIME_2, reportTime * 1000);
        if (StringUtils.isNotBlank(eventDetailVO.getReportTimeStr())) {
            time = eventDetailVO.getReportTimeStr();
        }
        String source = jointSource(eventDetailVO);
        String address = jointAddress(eventDetailVO);
        String content = jointInitReport(eventDetailVO);
        String eventDesc = eventDetailVO.getEventDesc();
        if (eventDesc != null && eventDesc.length() <= 500) {
        	// 从已生成好的数据库表中获取url
            Map<String, String> urlMap = getWeiXinUrlMap(mobileArr);
            String today = "";
            String seqno = "";
            if (eventDetailVO.getEventTwoType() == 5) { // 突发事件
            	smsTemplateDTO.setTemplateId(stringRedisTemplate.opsForValue().get("its-event:SmsTemplateCode-EMER_INIT_REPORT"));
            	today = TimeUtils.getTimeString(TimeUtils.DATE_7, reportTime * 1000);
            	Long todayStartTime = TimeUtils.toLong(TimeUtils.getTimeString(TimeUtils.DATE, reportTime * 1000) + " 00:00:00", TimeUtils.FULL_TIME);
            	int eventNum = eventMapper.selectTodayEmerSeqno(todayStartTime, reportTime);
            	if (eventNum + 1 < 10) {
            		seqno = "0" + (eventNum + 1);
            	} else {
            		seqno = "" + (eventNum + 1);
            	}
            } else {
            	smsTemplateDTO.setTemplateId(stringRedisTemplate.opsForValue().get("its-event:SmsTemplateCode-INIT_REPORT_4PART"));
            }

            for (String mobile : mobileArr) {
                if (mobile.length() != 11 && !mobile.startsWith("1")) {
                    continue;// 跳过，不发送短信 (手机号格式不对)
                }
                smsTemplateDTO.setMobile(mobile);
                LOGGER.info("发送短信：{}", mobile);
                String urlMsg = GenerateWeiXinUrlUtils.getUrlAndAssembleContent(urlMap, mobile);
                // 组合报送内容
                if (eventDetailVO.getEventTwoType() == 5) { // 突发事件
                	smsTemplateDTO.setContent(smsTemplateDTO.createEmerInitReportContent(today, seqno, time, address, content, source, urlMsg));
                } else {
                	smsTemplateDTO.setContent(smsTemplateDTO.createInitReportContent(time, address, content, source, urlMsg));
                }
                
                boolean sendStatus = AliyunSmsUtils.sendAnSms(smsTemplateDTO);
                String bizId = smsTemplateDTO.getBizId();
                LOGGER.info("发送短信：{}, bizId：{}", mobile, bizId);
                ProgressUserDTO eventPorgressUserDTO = new ProgressUserDTO();
                eventPorgressUserDTO.setEventProgressId(progressDTO.getId());
                eventPorgressUserDTO.setMobile(mobile);
                eventPorgressUserDTO.setBizId(bizId);
                if (sendStatus) {// 发送短信接口调用成功
                    eventPorgressUserDTO.setSmsSendStatus(2);
                } else {
                    // 更新进展用户的短信发送状态为失败(0)
                    eventPorgressUserDTO.setSmsSendStatus(0);
                    eventPorgressUserDTO.setSmsFailReason(smsTemplateDTO.getFailReason());
                }
                eventPorgressUserDTO.setSendTime(TimeUtils.getTimeString());
                int updateStatus = progressMapper.updateSmsSendStatus(eventPorgressUserDTO);
                LOGGER.info("发送短信：{},更新状态：{},入库：{}，进展id：{}", mobile, eventPorgressUserDTO.getSmsSendStatus(), updateStatus,
                        eventPorgressUserDTO.getEventProgressId());
            }
        }

        String eventId = eventDetailVO.getId();
        String briefDesc = eventDetailVO.getBriefDesc();
        String eventTypeName = eventDetailVO.getEventFourTypeName();
        String roadName = eventDetailVO.getRoadName();
        String milePost = StringUtils.trimToEmpty(eventDetailVO.getMilePost());
        String directionName = eventDetailVO.getDirectionName();
        Integer level = eventDetailVO.getLevel();
        String levelName = EmerPlanLevelEnum.getCname(level);
        if (StringUtils.isBlank(eventTypeName)) {
            eventTypeName = eventDetailVO.getEventThreeTypeName();
        }
        LOGGER.info("---事件初报推送消息：" + briefDesc);

        List<String> userIds = new ArrayList<String>();
        boolean checkRecordMan = false;
        for (ProgressUserDTO tmp : atUsers) {
            userIds.add(tmp.getUserId());
            if (tmp.getUserId().equals(eventDetailVO.getRecordManId())) {
                checkRecordMan = true;
            }
        }
        if (eventDetailVO != null && eventDetailVO.getRecordManId() != null) {
            if (!checkRecordMan) {
                userIds.add(eventDetailVO.getRecordManId());
            }
        }

        EventMessageDTO eventMessageDTO = new EventMessageDTO();
        eventMessageDTO.setWebsocketType("addEvent");
        eventMessageDTO.setId(eventId);
        eventMessageDTO.setSource(eventDetailVO.getSource());
        eventMessageDTO.setEventType(eventDetailVO.getEventType());
        eventMessageDTO.setEventTwoType(eventDetailVO.getEventTwoType());
        eventMessageDTO.setEventThreeType(eventDetailVO.getEventThreeType());
        eventMessageDTO.setBriefDesc(briefDesc);
        eventMessageDTO.setDealStatus(eventDetailVO.getDealStatus());
        eventMessageDTO.setUserIds(userIds);
        // 罗城中心，有新的96333事件
        Integer sourceId = eventDetailVO.getSourceId();
        String name = SourceIdEnum.getName(sourceId);
        String orgId = eventDetailVO.getOrgId();
        if (name != null) {
            name = name + "中心，";
            if (sourceId == 1) {
                name = OrganShortNameEnum.getName(orgId) + "中心，";
            }
        } else {
            name = "";
        }
        String voicePrompt = name + roadName + "，" + directionName + milePost + "，有" + eventTypeName;
        if (eventTypeName.endsWith("事件")) {
            eventMessageDTO.setVoicePrompt(voicePrompt);
        } else {
            eventMessageDTO.setVoicePrompt(voicePrompt + "事件");
        }
        eventMessageDTO.setOrgId(orgId);
        itsWebSocketFeignClient.pushEvent(eventMessageDTO);

        // 发送推送到微信消息队列
        WechatEventDTO wechatEventDTO = new WechatEventDTO();
        wechatEventDTO.setEvenId(eventId);
        wechatEventDTO.setBriefDesc(briefDesc);
        wechatEventDTO.setOrgId(eventDetailVO.getOrgId());
        wechatEventDTO.setReportTime(reportTime);
        wechatEventDTO.setAddress(roadName + milePost + directionName);
        wechatEventDTO.setType(WechatMsgDTO.EVENT_INIT_REPORT);
        wechatEventDTO.setEventType(eventTypeName);
        wechatEventDTO.setLevel(levelName);
        wechatProductor.produce(new Gson().toJson(wechatEventDTO));
    }

    @Transactional
    public boolean submitInitialReportReview(EventInfoReviewDTO dto) {
    	List<ProgressUserDTO> infoReviewUsers = dto.getInfoReviewUsers();
    	if (CollectionUtils.isEmpty(infoReviewUsers)) {
    		throw new ArgumentException("请选择初报审核人员");
    	}
    	String frontInitReportDesc = dto.getProgressDesc();
    	String eventId = dto.getId();
    	IdStringDTO eventIdDTO = new IdStringDTO(eventId);
    	EventDetailVO eventDetailVO = eventMapper.selectEmerRescueByEventId(eventIdDTO);
    	// 生成审核数据
    	String today = "";
        String seqno = "";
        Long dbReportTime = eventDetailVO.getReportTime();
        if (eventDetailVO.getEventTwoType() == 5) { // 突发事件
        	today = TimeUtils.getTimeString(TimeUtils.DATE_7, dbReportTime * 1000);
        	Long todayStartTime = TimeUtils.toLong(TimeUtils.getTimeString(TimeUtils.DATE, dbReportTime * 1000) + " 00:00:00", TimeUtils.FULL_TIME);
        	int eventNum = eventMapper.selectTodayEmerSeqno(todayStartTime, dbReportTime);
        	if (eventNum + 1 < 10) {
        		seqno = "0" + (eventNum + 1);
        	} else {
        		seqno = "" + (eventNum + 1);
        	}
        	frontInitReportDesc = frontInitReportDesc.replace("【事件初报】", "【"+today+"日事件"+seqno+"初报】");
        }
        dto.setReviewId(UUID.randomUUID().toString());
        dto.setProgressDesc(frontInitReportDesc);
        dto.setCreateTime(TimeUtils.getTimeString());
        dto.setInfoType(1);
        dto.setReviewStatus(0);
        UserSimpleVO loginUser = feignClient.selectByUserId(dto.getCreateUserId());
        dto.setCreateUserName(loginUser.getUserName());
        dto.setEventNo(eventDetailVO.getEventNo());
        boolean ret = eventMapper.submitInfoReview(dto) > 0;
        if (ret) {
        	// 更新事件处置状态dealStatus=2,delStatus=0
        	eventMapper.updateEventSetPending(eventIdDTO);
        	// 发送消息通知给报送人和所有该信息的审核人
			eventInfoReviewMsg(dto.getReviewId(), dto.getCreateUserId(), dto.getInfoReviewUsers());
        }
    	return ret;
    }

    /**
     * 事件初报
     */
    @Transactional
    public boolean initialReport(EventConfirmDTO dto) {
        String eventId = dto.getId();
        String frontInitReportDesc = dto.getProgressDesc();
        IdStringDTO eventIdDTO = new IdStringDTO(eventId);
        final long serverTime = System.currentTimeMillis() / 1000;
        String serverTimeString = TimeUtils.getTimeString(TimeUtils.FULL_TIME, serverTime * 1000);
        EventDetailVO eventDetailVO = eventMapper.selectEmerRescueByEventId(eventIdDTO);
        // LOGGER.info("事件初报4段信息（数据库）：id:{},时间:{},地点:{},来源:{},描述:{}",
        // eventDetailVO.getId(),eventDetailVO.getReportTimeStr()
        // , eventDetailVO.getLocation(), eventDetailVO.getReportSourceStr(),
        // eventDetailVO.getEventDesc());
        // LOGGER.info("事件初报4段信息（请求）：id:{},时间:{},地点:{},来源:{},描述:{}",
        // dto.getId(),dto.getReportTime()
        // , dto.getLocation(), dto.getReportSource(), dto.getEventDesc());

        if (eventDetailVO == null) {
            throw new ArgumentException("该事件已被删除，请您刷新页面");
        }
        Integer delStatus = eventDetailVO.getDelStatus();
        Integer dealStatus = eventDetailVO.getDealStatus();
        if (dealStatus != null && dealStatus > 2 && delStatus == 0) {
            throw new ArgumentException("该事件已在处置中，请您刷新页面");
        }
        String today = "";
        String seqno = "";
        Long dbReportTime = eventDetailVO.getReportTime();
        if (eventDetailVO.getEventTwoType() == 5) { // 突发事件
        	today = TimeUtils.getTimeString(TimeUtils.DATE_7, dbReportTime * 1000);
        	Long todayStartTime = TimeUtils.toLong(TimeUtils.getTimeString(TimeUtils.DATE, dbReportTime * 1000) + " 00:00:00", TimeUtils.FULL_TIME);
        	int eventNum = eventMapper.selectTodayEmerSeqno(todayStartTime, dbReportTime);
        	if (eventNum + 1 < 10) {
        		seqno = "0" + (eventNum + 1);
        	} else {
        		seqno = "" + (eventNum + 1);
        	}
        	frontInitReportDesc = frontInitReportDesc.replace("【事件初报】", "【"+today+"日事件"+seqno+"初报】");
        }

        // 先将初报的四段内容入库
        if (dto.getReportTime() != null && dto.getEventDesc() != null) {
            int row = updateEventDD(dto);
            LOGGER.info("入库event_dd:{}", row);
        }
        // 设置要发送初报的4段内容
        eventDetailVO.setReportTimeStr(dto.getReportTime());
        eventDetailVO.setLocation(dto.getLocation());
        eventDetailVO.setReportSourceStr(dto.getReportSource());
        eventDetailVO.setEventDesc(dto.getEventDesc());

        dto.setServerTime(serverTime);
        eventDetailVO.setReportSourceStr(dto.getReportSource());// 接报来源用于发送初报短信
        updateEventSatus(dto, eventDetailVO);// 修改事件delStatus为0，事件状态为处置中、责任公司、分发时间

        // 2、新增进展并@所有关联的人，生成初报类型（待确认）
        List<ProgressUserDTO> atUsers = dto.getAtUsers();
        Set<ProgressUserDTO> atUsersSet = new HashSet<ProgressUserDTO>();
        List<ProgressUserDTO> notifyUsers = dto.getNotifyUsers();
        List<ProgressUserDTO> smsUsers = dto.getSmsUsers();
        String smsMobiles = "";

        for (ProgressUserDTO puDTO : atUsers) {
            atUsersSet.add(puDTO);
        }
        for (ProgressUserDTO smsUser : smsUsers) {
            String smsMobile = smsUser.getMobile();
            if (StringUtils.length(smsMobile) == 11 && smsMobile.startsWith("1")
                    && !smsMobiles.contains(smsMobile + ",")) { // 手机号无效，测试发送失败后有返回结果
                smsMobiles += smsMobile + ",";
            }
        }

        List<UserDTO> robotMobileUsers = new ArrayList<>();
        String notifyUserMobiles = "";
        for (ProgressUserDTO notifyUser : notifyUsers) {
            String mobile = notifyUser.getMobile();
            if (StringUtils.isNotBlank(mobile)) {
                if (!notifyUserMobiles.contains(mobile + ",")) {
                    notifyUserMobiles += mobile + ",";
                    UserDTO user = new UserDTO();
                    user.setMobile(mobile);
                    user.setUserName(notifyUser.getUserName());
                    robotMobileUsers.add(user);
                }
            }
        }

        if (robotMobileUsers != null && robotMobileUsers.size() > 0) {
            RobotMobileDTO robotMobileDTO = new RobotMobileDTO();
            robotMobileDTO.setBriefDesc(frontInitReportDesc);
            robotMobileDTO.setId(eventId);
            robotMobileDTO.setSourceId(eventDetailVO.getSourceId());
            robotMobileDTO.setSourceName(stringRedisTemplate.opsForValue().get("its-event:robotCallScene-"+eventDetailVO.getSourceId()));
            robotMobileDTO.setUsers(robotMobileUsers);
            // 外呼智能机器人播报// 语音播报强提醒
            AssignJobs.robotMobile(robotMobileDTO, true);
            // 将标识更新到参数中
            Map<String, String> referenceIdMap = RobotCallTmpResultContext.obtainReferenceIdMap();
            List<ProgressUserDTO> progressUserList = dto.getAtUsers();
            progressUserList.forEach(item -> item.setReferenceId(referenceIdMap.get(item.getMobile())));
            dto.setAtUsers(progressUserList);
        }

        atUsers.clear();
        atUsers.addAll(atUsersSet);

        ProgressDTO progressDTO = toProgress(dto, 1);
        progressDTO.setProgressDesc(frontInitReportDesc);
        boolean add = progressService.add(progressDTO);
        String createUserId = dto.getCreateUserId();
        if (add) {
            if (smsMobiles.length() > 11) {
                smsMobiles = smsMobiles.substring(0, smsMobiles.length() - 1);
            }
            final String m = smsMobiles;
            new Thread(new Runnable() {
                @Override
                public void run() {
                    initialReportSmsAndPush(m, eventDetailVO, progressDTO, atUsers);
                }
            }).start();

        }

        // 查询是否配置有预案审核人员，有，生成“预案启动”卡片
        List<EventEmerUserVO> eventEmerUsers = eventMapper.selectEmerUserById(eventIdDTO);
        List<ProgressUserDTO> emerPlanAuditUsers = new ArrayList<>();
        for (EventEmerUserVO tmpVO : eventEmerUsers) {
            if ("emerPlanAudit".equals(tmpVO.getEmerGroupId())) {
                ProgressUserDTO emerPlanAuditUser = new ProgressUserDTO();
                emerPlanAuditUser.setUserId(tmpVO.getUserId());
                emerPlanAuditUser.setUserName(tmpVO.getUserName());
                emerPlanAuditUser.setMobile(tmpVO.getMobile());
                emerPlanAuditUsers.add(emerPlanAuditUser);
            }
        }
        Integer startPlan = null;
        Integer level = eventDetailVO.getLevel();
        String levelName = EmerPlanLevelEnum.getCname(level);
        if (!CollectionUtils.isEmpty(emerPlanAuditUsers)) {
            // 生成“预案启动”卡片
            String emerPlanId = eventDetailVO.getEmerPlanId();
            if (StringUtils.isNotBlank(emerPlanId)) {
                EmerPlanVO emerPlanVO = eventMapper.selectEmerPlan(new IdStringDTO(emerPlanId));
                String progressDesc = "该事件采用《" + emerPlanVO.getName() + "》，响应等级" + levelName + "，等待领导审核启动。";
                // 新增启动预案卡片
                ProgressDTO startPlanProgressDTO = new ProgressDTO();
                startPlanProgressDTO.setCreateTime(serverTime + 1);
                startPlanProgressDTO.setOccurTime(serverTime + 1);
                startPlanProgressDTO.setProgressDesc(progressDesc);
                startPlanProgressDTO.setAtUsers(emerPlanAuditUsers);
                startPlanProgressDTO.setEventId(eventId);
                startPlanProgressDTO.setCreateUserId(createUserId);
                startPlanProgressDTO.setCardType(20);
                progressService.add(startPlanProgressDTO);
                startPlan = 0;
            }
        }
        // 新增-事件执行预案相关状态
        EventDealStatusDTO eventDealStatusDTO = new EventDealStatusDTO();
        eventDealStatusDTO.setEventId(eventDetailVO.getId());
        eventDealStatusDTO.setPlanResponse(startPlan);
        List<OccupiedLaneVO> occupiedLanes = eventDetailVO.getOccupiedLanes();
        Integer unblock = null;
        if (!CollectionUtils.isEmpty(occupiedLanes)) {
            eventDealStatusDTO.setCmsPublish(0);
            eventDealStatusDTO.setCancelCmsPublish(0);

            Integer facilityType = eventDetailVO.getFacilityTypeNo();// 值为2:涉隧
            int size = occupiedLanes.size();
            for (OccupiedLaneVO occupiedLaneVO : occupiedLanes) {
                Integer occupiedLane = occupiedLaneVO.getOccupiedLane();
                if (occupiedLane != null && occupiedLane == -1) {
                    size--;
                }
                if (occupiedLane != null && occupiedLane == 0) {
                    if (facilityType != null && facilityType == 2) {
                        size--;
                    }
                }
            }

            Integer roadNo = eventDetailVO.getRoadNo();
            String milePost = eventDetailVO.getMilePost();
            int carLane = eventMapper.selectCarLane(roadNo);
            if (StringUtils.isNotBlank(milePost)) {
                String mp = milePost.replace("K", "").replace("k", "").replace("+", "");
                int mpValue = NumberUtils.toInt(mp, -1);
                if (mpValue >= 0) {
                    RoadMilePostDTO roadMilePostDTO = new RoadMilePostDTO(roadNo, mpValue);
                    RoadVO roadVO = feignClient.selectRoadByRoadNoAndMilePost(roadMilePostDTO);
                    if (roadVO != null) {
                        carLane = roadVO.getCarLane();
                    }
                }
            }
            if (carLane > 0) {
                carLane = carLane / 2;
                if (size >= (carLane + 1) || (facilityType != null && facilityType == 2 && size >= carLane)) {// 单向全幅封闭
                    eventDealStatusDTO.setFullClose(1);
                    eventDealStatusDTO.setStartFullClose(serverTime + 2);
                    // 新增留痕进展，该道路目前单向全幅封闭，开始时间：xxxx年xx月xx日 HH:mm:ss。
                    ProgressDTO fullCloseProgressDTO = new ProgressDTO();
                    fullCloseProgressDTO.setCreateTime(serverTime + 2);
                    fullCloseProgressDTO.setOccurTime(serverTime + 2);
                    fullCloseProgressDTO.setProgressDesc("该道路目前单向全幅封闭，开始时间："
                            + TimeUtils.getTimeString(TimeUtils.FULL_TIME_3, (serverTime + 2) * 1000) + "。");
                    fullCloseProgressDTO.setEventId(eventId);
                    fullCloseProgressDTO.setCreateUserId(createUserId);
                    progressService.add(fullCloseProgressDTO);

                    // 新增全幅封闭的历史记录event_deal_full_close
                    EventDealFullCloseDTO fullCloseDTO = new EventDealFullCloseDTO();
                    fullCloseDTO.setEventId(eventId);
                    fullCloseDTO.setStartCloseTime(serverTime + 2);
                    fullCloseDTO.setCreateTime(serverTimeString);
                    eventMapper.addEventDealFullClose(fullCloseDTO);
                    // event_deal_status中的unblock改为0
                    unblock = 0;
                    eventDetailVO.setFullClose(1);// 推送事件到高德的参数使用
                }
            }
        }
        eventDealStatusDTO.setUnblock(unblock);
        eventDealStatusDTO.setInitReportTime(serverTime);
        eventDealStatusDTO.setCreateTime(serverTimeString);
        eventMapper.addEventDealStatus(eventDealStatusDTO);
        final String initReportDesc = frontInitReportDesc;
        if (add) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    // 异步推送事件到高德
                    eventPushToAmap(eventDetailVO, 0, initReportDesc);
                }
            }).start();
        }

        // 判断是否是来自桂享高速的救援工单，桂享高速的救援工单需派单至救援司机端小程序
        if (eventDetailVO.getEventType() == 1 && eventDetailVO.getSource() == EventSourceEnum.SOURCE_4.getSourceValue()) {
            // 异步派单至救援司机端小程序
            LOGGER.info("桂享高速的救援工单，异步派单至救援司机端。eventId={}", eventId);
            new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
                        DDRescuerDTO ddRescuerDTO = new DDRescuerDTO();
                        ddRescuerDTO.setEventId(eventId);
                        ddRescuerDTO.setOrderId(eventDetailVO.getIntegralOrderId());

                        Map<String, String> headParam = new HashMap<>();
                        String content = jfptUserName + ":" + jfptPassword;
                        String encoded = Base64.encodeBase64String(content.getBytes());
                        headParam.put("Rescue-Authorization", "Basic+" + encoded);
                        String res = HttpClientUtils.post(rescuerDomain, headParam, new Gson().toJson(ddRescuerDTO));
                        LOGGER.info("一键救援工单派发结果：{}", res);
                        ResultVO resultVO = new Gson().fromJson(res, ResultVO.class);

                        if (resultVO != null && resultVO.getCode() != null) {
                            // 新增一条进展
                            ProgressDTO distributeProgress = new ProgressDTO();
                            distributeProgress.setCreateTime(serverTime + 3);
                            distributeProgress.setOccurTime(serverTime + 3);
                            distributeProgress.setProgressDesc("救援工单已派发至救援司机端");
                            distributeProgress.setEventId(eventId);
                            distributeProgress.setCreateUserId(createUserId);
                            distributeProgress.setCardType(0);
                            progressService.add(distributeProgress);
                        }

                    } catch (Exception e) {
                        LOGGER.error(e.getMessage(), e);
                    }
                }
            }).start();
        }

        LOGGER.info(" >>> 自动发布情报板(事件分发操作触发):{}", autoReleaseCms);
        if (autoReleaseCms) {
        	autoReleaseCms(eventDetailVO);
        }
        return add;
    }
    private void autoReleaseCms(EventDetailVO eventDetailVO) {
    	// 自动发布情报板（交通诱导策略）
        Integer eventFourType = eventDetailVO.getEventFourType();
        Integer eventThreeType = eventDetailVO.getEventThreeType();
        Integer eventTwoType = eventDetailVO.getEventTwoType();
        Integer eventType = eventDetailVO.getEventType();
        Integer roadNo = eventDetailVO.getRoadNo();
        String milePost = eventDetailVO.getMilePost();
        String lng = eventDetailVO.getLng();
        String lat = eventDetailVO.getLat();
        String directionNo = eventDetailVO.getDirectionNo();
        String eventId = eventDetailVO.getId();
        if (StringUtils.isBlank(milePost) || StringUtils.isBlank(lng) || StringUtils.isBlank(lat)) {
        	LOGGER.info("没有经纬度，不触发交通诱导发布情报板");
        	return;
        }
        EventTypeEnum et = EventTypeEnum.getEnum(eventType, eventTwoType, eventThreeType, eventFourType);
        EventTypeEnum et2 = EventTypeEnum.getEnum(eventType, eventTwoType, eventThreeType, null);
        String content = "";
        int planType = 0;
        List<OccupiedLaneVO> occupiedLanes = eventDetailVO.getOccupiedLanes();
        RoadMilePostDTO roadMilePostDTO = new RoadMilePostDTO();
        roadMilePostDTO.setRoadNo(roadNo);
        roadMilePostDTO.setMpValue(NumberUtils.toInt(milePost.replace("K", "").replace("+", "")));
        RoadVO roadVO = feignClient.selectRoadByRoadNoAndMilePost(roadMilePostDTO);
        int carLane = 4;
        if (roadVO != null) {
        	carLane = roadVO.getCarLane() / 2;
        }
        int size = occupiedLanes.size();
        boolean existAllLane = false;
        String occupiedLaneStr = "";
        if (occupiedLanes != null && size > 0) {
        	for (OccupiedLaneVO occupiedLaneVO : occupiedLanes) {
        		if (occupiedLaneVO.getOccupiedLane() == -1) {
        			size--;
        		}
			}
        	Integer facilityType = eventDetailVO.getFacilityType();
        	if (facilityType != null && facilityType.intValue() == 2) { // 涉隧
        		if (size >= carLane) {
            		existAllLane = true;
            	}
        	} else {
        		if (size >= (carLane + 1)) {
            		existAllLane = true;
            	}
        	}
        	if (!existAllLane) {
        		occupiedLaneStr = "占用";
        		for (OccupiedLaneVO occupiedLaneVO : occupiedLanes) {
        			if (occupiedLaneVO.getOccupiedLane() == 0) {
        				occupiedLaneStr += "应急、";
        			}
        			if (occupiedLaneVO.getOccupiedLane() == 1) {
        				occupiedLaneStr += "1、";
        			}
        			if (occupiedLaneVO.getOccupiedLane() == 2) {
        				occupiedLaneStr += "2、";
        			}
        			if (occupiedLaneVO.getOccupiedLane() == 3) {
        				occupiedLaneStr += "3、";
        			}
        			if (occupiedLaneVO.getOccupiedLane() == 4) {
        				occupiedLaneStr += "4、";
        			}
    			}
        		if (occupiedLaneStr.length() > 2) {
        			occupiedLaneStr = occupiedLaneStr.substring(0, occupiedLaneStr.length() - 1) + "车道";
        		} else {
        			occupiedLaneStr = "";
        		}
        	} else {
        		occupiedLaneStr = "全幅封闭";
        	}
        }
        
        // 根据路段查询几车道，然后匹配是否占用所有车道，是  提示“请绕行”，否 比如提示“占用应急车道、第三车道，请注意行车安全”
        if (et == EventTypeEnum.TRAFFIC_ACCIDENT) {
        	planType = 1;
//        	前方{A}有{B}{C}，请{D}
        	if (existAllLane) {
        		content = "前方有"+this.getDictItemName(124, "交通诱导模板类型", String.valueOf(planType)) +"全幅封闭，请绕行";
        	} else {
        		content = "前方有" + this.getDictItemName(124, "交通诱导模板类型", String.valueOf(planType))  + occupiedLaneStr + "，请注意避让";
        	}
        } else if (et == EventTypeEnum.CAR_RESCUE) {//  || et == EventTypeEnum.CAR_FIRE 车辆自然已停用
        	planType = 4;
//        	前方{A}有{B}{C}，请{D}
        	if (existAllLane) {
        		content = "前方有" + this.getDictItemName(124, "交通诱导模板类型", String.valueOf(planType))  + "全幅封闭，请绕行";
        	} else {
        		content = "前方有" + this.getDictItemName(124, "交通诱导模板类型", String.valueOf(planType))  + occupiedLaneStr + "，请注意避让";
        	}
        } else if (et == EventTypeEnum.EXTREME_WEATHER) {//|| et == EventTypeEnum.INCLEMENT_WEATHER
        	planType = 3;
        	content = "小心驾驶、减速慢行";
        } else if (et == EventTypeEnum.NATURAL_DISASTER_OTHER) {
        	planType = 6;
//        	前方{A}有{B}{C}，请注意行车安全
        	if (existAllLane) {
        		content = "前方有" + EventTypeEnum.NATURAL_DISASTER.getName()  + "全幅封闭，请绕行";
        	} else {
        		content = "前方有" + EventTypeEnum.NATURAL_DISASTER.getName()  + occupiedLaneStr + "，请注意行车安全";
        	}
        } else if (et2 == EventTypeEnum.NATURAL_DISASTER) {
        	planType = 6;
//        	前方{A}有{B}{C}，请注意行车安全
        	if (existAllLane) {
        		content = "前方有" + et.getName()  + "全幅封闭，请绕行";
        	} else {
        		content = "前方有" + et.getName()  + occupiedLaneStr + "，请注意行车安全";
        	}
        } else if (et == EventTypeEnum.SA_SATURATION) {
        	planType = 5;
        	// {"facilityTypeNo":7,"roadNo":141,"directionNo":"0fcce069-318e-43c9-a491-68876c4f55a7","milePost":"K159+000"}
        	 RelatedFacilityDTO relatedFacilityDTO = new RelatedFacilityDTO();
        	 relatedFacilityDTO.setFacilityTypeNo(7);
        	 relatedFacilityDTO.setRoadNo(roadNo);
        	 relatedFacilityDTO.setDirectionNo(directionNo);
        	 relatedFacilityDTO.setMilePost(milePost);
        	 NearestFacilityTipVO nearestRelatedFacility = nearestRelatedFacility(relatedFacilityDTO);
        	 String facilityName = nearestRelatedFacility.getFacilityNameShort();
        	 if (StringUtils.isNoneBlank(facilityName)) {
        		 content = facilityName + "已饱和";
        	 } else {
        		 content = "前方服务区已饱和";
        	 }
        }
        if (planType == 0) {
        	LOGGER.info("事件类型不满足交通诱导发布情报板");
        	return;
        }
        String releaseUserName = "系统";
        String milePostRange = "";
        if (roadVO != null) {
        	milePostRange = roadVO.getPileScope();
        }
		EventCmsDTO eventCmsDTO = new EventCmsDTO(eventFourType, eventThreeType, eventTwoType, eventType,
				roadNo, milePost, lng,  lat, directionNo, eventId,
				milePostRange, releaseUserName, planType, content);
		Map<String, String> headers = ServiceUtils.getInnerLoginHead();
		String result = HttpClientUtils.post(ykDomain + "/its-outfield/cms/autoReleaseEventCms", headers, GsonUtils.beanToJson(eventCmsDTO));
		if ("1".equals(result)) {
			LOGGER.info("交通诱导自动发布情报板成功");
		} else {
			LOGGER.info("交通诱导自动发布情报板失败");
		}
    }

    /**
     * @param stateFlag 0新增，1更新，2删除（高德提供字段）
     * @描述 推送事件到高德（新增，更新，删除）
     */
    public void eventPushToAmap(EventDetailVO eventDetailVO, int stateFlag, String frontInitReportDesc) {
        String milePost = eventDetailVO.getMilePost();
        if (StringUtils.isBlank(milePost)) {
            return;
        }

        Integer eventType = eventDetailVO.getEventType();
        Integer eventTwoType = eventDetailVO.getEventTwoType();
        Integer eventThreeType = eventDetailVO.getEventThreeType();
        Integer eventFourType = eventDetailVO.getEventFourType();
        int type = 0;
        String desc = null;
        if (EventTypeEnum.CAR_RESCUE == EventTypeEnum.getEnum(eventType, eventTwoType, eventThreeType, eventFourType)) {
            type = 103;
            desc = "故障⻋";
        } 
//        else if (EventTypeEnum.MINOR_TRAFFIC_ACCIDENT == EventTypeEnum.getEnum(eventType, eventTwoType,
//                eventThreeType, eventFourType)) {
//            type = 101;
//            desc = "交通事故（⼀般）";
//        } 
        else if (EventTypeEnum.TRAFFIC_ACCIDENT == EventTypeEnum.getEnum(eventType, eventTwoType, eventThreeType,
                eventFourType)) {
            type = 102;
            desc = "交通事故（严重）";
        }
        Integer fullClose = eventDetailVO.getFullClose();
        if (fullClose != null && fullClose == 1) {// 只要是全幅封闭，认为是交通事故导致道路关闭
            type = 102302;
            desc = "交通事故导致道路关闭";
        }
        if (type == 0) {
            return;
        }

        String url = "https://et-api.amap.com/eventpublish/add";
        long now = System.currentTimeMillis() / 1000;
        Map<String, Object> map = new HashMap<>();
        map.put("adcode", "450000");
        map.put("clientKey", amapWebSeq);
        map.put("timestamp", now);
        map.put("sourceId", amapWebClientSourceId);
        map.put("id", eventDetailVO.getEventNo());
        map.put("stateFlag", stateFlag);
        map.put("type", type);
        map.put("locType", 1);
        String roadName = eventDetailVO.getRoadName();
        if (StringUtils.isNoneBlank(roadName)) {
            int index = roadName.indexOf("(");
            if (index > 0) {
                roadName = roadName.substring(0, index);
            }
            map.put("roadName", roadName);
        }
        List<OccupiedLaneVO> occupiedLanes = eventDetailVO.getOccupiedLanes();
        if (!CollectionUtils.isEmpty(occupiedLanes)) {
            String lanes = "";
            for (OccupiedLaneVO occupiedLaneVO : occupiedLanes) {
                Integer occupiedLane = occupiedLaneVO.getOccupiedLane();
                if (occupiedLane == null) {
                    continue;// 脏数据，跳过
                }
                if (occupiedLane == -1) {
                    lanes += "13,";
                } else if (occupiedLane == 0) {
                    lanes += "14,";
                } else if (occupiedLane > 0 && occupiedLane < 8) {
                    lanes += (occupiedLane + ",");
                }
            }
            int lanesLength = lanes.length();
            if (lanesLength > 1) {
                lanes = lanes.substring(0, lanesLength - 1);
                map.put("lanes", lanes);
            }
        }
        String directionName = eventDetailVO.getDirectionName();
        map.put("direction", directionName);
        map.put("locs", "[[\"" + milePost + "\"]]");
        map.put("startDate", TimeUtils.getTimeString(TimeUtils.FULL_TIME, eventDetailVO.getReportTime() * 1000));
        if (frontInitReportDesc == null) {
            frontInitReportDesc = desc;
        }
        map.put("desc", frontInitReportDesc.replace("【事件初报】", ""));
        Long finishTime = eventDetailVO.getFinishTime();
        if (stateFlag == 2 && finishTime != null) {
            map.put("endDate", TimeUtils.getTimeString(TimeUtils.FULL_TIME, finishTime * 1000));
        }
        String content = DictSortUtils.getOrder(map, false);
        LOGGER.info("【高德事件对接】需要hmacSHA256加密的内容：{}", content);
        try {
            String hmacSHA256 = HmacSHA256Utils.hmacSHA256(amapWebClientSecret, content);
            String urlParam = DictSortUtils.getOrderUrlParam(map);
            urlParam = urlParam + "&digest=" + hmacSHA256;
            LOGGER.info("【高德事件对接】hmacSHA256加密后的内容：{}", hmacSHA256);
            String ret = HttpClientUtils.get(url + "?" + urlParam, null, 10000);
            LOGGER.info("【高德事件对接】响应信息：{}", ret);
            if (stateFlag == 0 && StringUtils.isNotBlank(ret)) {
                ResponseVO vo = new Gson().fromJson(ret, ResponseVO.class);
                Integer code = vo.getCode();
                if (code != null && code == 0) {
                    // 推送事件成功，设置push_amap为1
                    int row = eventMapper.updatePushedAmap(new IdStringDTO(eventDetailVO.getId()));
                    LOGGER.info("设置push_map为1：{}", row == 1);
                    pvCounter.labels(desc).inc();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.info("【高德事件对接】接口调用出错：", e.getMessage());
        }
    }

    public Map<String, String> getWeiXinUrlMap(String[] mobileArr) {
        Map<String, String> urlMap = new HashMap<>();
        if (mobileArr == null || mobileArr.length <= 0) {
            return urlMap;
        }
        List<EventUserWeiXinUrlDTO> weiXinUrlList = eventUserWeiXinUrlService
                .queryUrlByMobileList(Arrays.asList(mobileArr));
        weiXinUrlList.forEach(item -> {
            urlMap.put(item.getMobile(), item.getWeiXinUrl());
        });
        return urlMap;
    }

    /**
     * 事件续报
     */
    // @Transactional
    public boolean resubmit(EventConfirmDTO dto) {
        // 查询事件是否存在终报
        ProgressVO progressVO = progressMapper.selectFinalReportByEventId(new IdStringDTO(dto.getId()));
        if (progressVO != null) {
            throw new ArgumentException("该事件已有终报，不能发送续报！");
        }
        // 增加校验
        validParams(dto);

        // 2、新增进展并@所有关联的人，生成初报类型（待确认）
        ProgressDTO progressDTO = toProgress(dto, 5);
        // 续报发送短信
        String mobiles = "";
        List<ProgressUserDTO> atUsers = dto.getAtUsers();
        for (ProgressUserDTO d : atUsers) {
            String mobile = d.getMobile();
            if (StringUtils.length(mobile) == 11 && mobile.startsWith("1")) {
                mobiles = mobiles + mobile + ",";
            }
        }
        // 该事件已有多少条续报
        int count = progressMapper.countResubmitByEventId(new IdStringDTO(dto.getId()));
        if (count > 0) {
            count++;
        }
        String progressDesc = progressDTO.getProgressDesc();
        IdStringDTO eventDTO = new IdStringDTO(dto.getId());
        EventDetailVO eventDetailVO = eventMapper.selectBaseInfoByEventId(eventDTO);
        String today = "";
        String seqno = "";
        Long dbReportTime = eventDetailVO.getReportTime();
        if (eventDetailVO.getEventTwoType() == 5) { // 突发事件
        	today = TimeUtils.getTimeString(TimeUtils.DATE_7, dbReportTime * 1000);
        	Long todayStartTime = TimeUtils.toLong(TimeUtils.getTimeString(TimeUtils.DATE, dbReportTime * 1000) + " 00:00:00", TimeUtils.FULL_TIME);
        	int eventNum = eventMapper.selectTodayEmerSeqno(todayStartTime, dbReportTime);
        	if (eventNum + 1 < 10) {
        		seqno = "0" + (eventNum + 1);
        	} else {
        		seqno = "" + (eventNum + 1);
        	}
        	progressDesc = progressDesc.replace("【事件续报】", "【"+today+"日事件"+seqno+"续报】");
        }
        progressDTO.setProgressDesc(progressDesc);
        boolean ret = progressService.add(progressDTO);
        if (ret) {
            if (mobiles.length() > 11) {
                mobiles = mobiles.substring(0, mobiles.length() - 1);
            }
            if (mobiles.length() > 10) {
	            String[] mobileArr = mobiles.split(",");
	            SmsTemplateDTO smsTemplateDTO = new SmsTemplateDTO();
	            
	            if (eventDetailVO.getEventTwoType() == 5) { // 突发事件
	            	smsTemplateDTO.setTemplateId(stringRedisTemplate.opsForValue().get("its-event:SmsTemplateCode-EMER_RESUBMIT"));
	            } else {
	            	smsTemplateDTO.setTemplateId(stringRedisTemplate.opsForValue().get("its-event:SmsTemplateCode-RESUBMIT"));
	            }
	            // 从已生成好的数据库表中获取url
	            Map<String, String> urlMap = getWeiXinUrlMap(mobileArr);
	            for (String mobile : mobileArr) {
	                smsTemplateDTO.setMobile(mobile);
	                String urlMsg = GenerateWeiXinUrlUtils.getUrlAndAssembleContent( urlMap, mobile);
	                String times = "";
	                if (count > 1) {
	                    times = count + times;
	                }
	                String location = dto.getLocation();
	                String weather= "";
	                if (location.contains("天气情况")) {
	                    String[] locationSplit = location.split("天气情况");
	                    location = locationSplit[0];
	                    weather = locationSplit[locationSplit.length-1];// 防止前端传重复的天气情况文本
//	                    weather = locationSplit[1];
	
	                    if (weather.contains(":")) { // 英文格式
	                        weather = weather.split(":")[1];
	                    }
	                    if (weather.contains("：")) {
	                        weather = weather.split("：")[1]; // 中文格式
	                    }
	                }
	                if (StringUtils.isBlank(weather)) {
	                    weather = "无";
	                }
	                String eventDesc = removeEventDescLastPeriod(dto.getEventDesc()); // 删除掉输入的描述内容的最后一个句号
	                if (eventDesc != null && eventDesc.length() <= 500) {
	                	// 保存续报、终报的内容到event_process_sms_message
	                	EventProcessSmsMessageDTO processSmsMessageDTO = new EventProcessSmsMessageDTO();
	                	processSmsMessageDTO.setEventProcessId(Long.parseLong(progressDTO.getId() + ""));
	                	processSmsMessageDTO.setEventDesc(eventDesc);
	                	location = removeEventDescLastPeriod(location);
	                	processSmsMessageDTO.setLocation(location);
	                	processSmsMessageDTO.setTimes(StringUtils.isNotBlank(times) ? Integer.parseInt(times) : 0);
	                	processSmsMessageDTO.setType(1);
	                	String reportTime = removeEventDescLastPeriod(dto.getReportTime());
	                	processSmsMessageDTO.setReportTime(reportTime);
	                	String reportSource = removeEventDescLastPeriod(dto.getReportSource());
	                	processSmsMessageDTO.setReportSource(reportSource);
	                	weather = removeEventDescLastPeriod(weather);
	                	processSmsMessageDTO.setWeatherDesc(weather);
	                	processSmsMessageDTO.setUrlSuffix(urlMsg);
	                	processSmsMessageDTO.setCreateTime(System.currentTimeMillis() / 1000);
	                	progressSmsMessageMapper.add(processSmsMessageDTO);
	                	// 组装发送短信
	                	if (eventDetailVO.getEventTwoType() == 5) { // 突发事件
	                    	smsTemplateDTO.setContent(smsTemplateDTO.createEmerResubmitReportContent(today, seqno, times, reportTime, location, weather, eventDesc, reportSource, urlMsg));
	                    } else {
	                    	smsTemplateDTO.setContent(smsTemplateDTO.createResubmitReportContent(times, reportTime, location, weather, eventDesc, reportSource, urlMsg));
	                    }
	                	boolean sendStatus = AliyunSmsUtils.sendAnSms(smsTemplateDTO);
	                	String bizId = smsTemplateDTO.getBizId();
	                	ProgressUserDTO eventPorgressUserDTO = new ProgressUserDTO();
	                	eventPorgressUserDTO.setEventProgressId(progressDTO.getId());
	                	eventPorgressUserDTO.setMobile(mobile);
	                	eventPorgressUserDTO.setBizId(bizId);
	                	if (sendStatus) {// 发送短信接口调用成功
	                		eventPorgressUserDTO.setSmsSendStatus(2);
	                	} else {
	                		// 更新进展用户的短信发送状态为失败(0)
	                		eventPorgressUserDTO.setSmsSendStatus(0);
	                		eventPorgressUserDTO.setSmsFailReason(smsTemplateDTO.getFailReason());
	                	}
	                	eventPorgressUserDTO.setSendTime(TimeUtils.getTimeString());
	                	progressMapper.updateSmsSendStatus(eventPorgressUserDTO);
	                }
	            }
            }
        }
        return ret;
    }

    private static String removeEventDescLastPeriod(String eventDesc) {
        if (StringUtils.isBlank(eventDesc)) {
            return eventDesc;
        }
        String lastStr = eventDesc.substring(eventDesc.length() - 1);
        if (lastStr.endsWith("。") || lastStr.endsWith("，") || lastStr.endsWith(".") || lastStr.endsWith(",")) {
            eventDesc = eventDesc.substring(0, eventDesc.length() - 1);
        }
        return eventDesc;
    }

    private void validParams(EventConfirmDTO dto) {
        String eventDesc = dto.getEventDesc();
        if (StringUtils.isBlank(eventDesc)){
            throw new ArgumentException("缺失必要参数：eventDesc");
        }
        String location = dto.getLocation();
        if (StringUtils.isBlank(location)){
            throw new ArgumentException("缺失必要参数：location");
        }
        String reportTime = dto.getReportTime();
        if (StringUtils.isBlank(reportTime)){
            throw new ArgumentException("缺失必要参数：reportTime");
        }
        String reportSource = dto.getReportSource();
        if (StringUtils.isBlank(reportSource)){
            throw new ArgumentException("缺失必要参数：reportSource");
        }
    }

    /**
     * 事件终报
     */
    // @Transactional
    public boolean finalReport(EventConfirmDTO dto) {
        // 查询是否已有终报
    	String eventId = dto.getId();
        IdStringDTO eventDTO = new IdStringDTO(eventId);
        EventDetailVO eventDetailVO = eventMapper.selectBaseInfoByEventId(eventDTO);
        if (eventDetailVO == null) {
            throw new ArgumentException("该事件不存在！");
        }
        // 增加校验
        validParams(dto);
        // 2、新增进展并@所有关联的人，生成初报类型（待审批）
        ProgressDTO progressDTO = toProgress(dto, 10);
        // 终报发送短信
        String mobiles = "";
        List<ProgressUserDTO> atUsers = dto.getAtUsers();
        for (ProgressUserDTO progreesUser : atUsers) {
            String mobile = progreesUser.getMobile();
            if (StringUtils.length(mobile) == 11 && mobile.startsWith("1")) {
                mobiles = mobiles + mobile + ",";
            }
        }
        // 该事件已有多少条终报
        int count = progressMapper.countFinalReportByEventId(new IdStringDTO(dto.getId()));
        if (count > 0) {
            count++;
        }
        String progressDesc = dto.getProgressDesc();
        String today = "";
        String seqno = "";
        Long dbReportTime = eventDetailVO.getReportTime();
        if (eventDetailVO.getEventTwoType() == 5) { // 突发事件
        	today = TimeUtils.getTimeString(TimeUtils.DATE_7, dbReportTime * 1000);
        	Long todayStartTime = TimeUtils.toLong(TimeUtils.getTimeString(TimeUtils.DATE, dbReportTime * 1000) + " 00:00:00", TimeUtils.FULL_TIME);
        	int eventNum = eventMapper.selectTodayEmerSeqno(todayStartTime, dbReportTime);
        	if (eventNum + 1 < 10) {
        		seqno = "0" + (eventNum + 1);
        	} else {
        		seqno = "" + (eventNum + 1);
        	}
        	progressDesc = progressDesc.replace("【事件终报】", "【"+today+"日事件"+seqno+"终报】");
        	progressDTO.setProgressDesc(progressDesc);
        }
        boolean ret = progressService.add(progressDTO);
        if (ret) {
            Long finalReportTime = eventDetailVO.getFinalReportTime();// 终报统计时间
            if (finalReportTime == null) {// 终报时间为空，插入终报统计时间
                dto.setFinalReportTime(System.currentTimeMillis() / 1000);
            }
            eventMapper.updateFinalReport(dto);
            if (mobiles.length() > 11) {
                mobiles = mobiles.substring(0, mobiles.length() - 1);
            }

            if (mobiles.length() > 10) {
            	String[] mobileArr = mobiles.split(",");
            	SmsTemplateDTO smsTemplateDTO = new SmsTemplateDTO();
            	if (eventDetailVO.getEventTwoType() == 5) { // 突发事件
            		smsTemplateDTO.setTemplateId(stringRedisTemplate.opsForValue().get("its-event:SmsTemplateCode-EMER_FINAL_REPORT"));
            	} else {
            		smsTemplateDTO.setTemplateId(stringRedisTemplate.opsForValue().get("its-event:SmsTemplateCode-FINAL_REPORT"));
            	}
            	// 从已生成好的数据库表中获取url
            	Map<String, String> urlMap = getWeiXinUrlMap(mobileArr);
            	for (String mobile : mobileArr) {
            		smsTemplateDTO.setMobile(mobile);
            		String urlMsg = GenerateWeiXinUrlUtils.getUrlAndAssembleContent(urlMap, mobile);
            		String times = "";
            		if (count > 1) {
            			times = count + times;
            		}
            		String location = dto.getLocation();
            		String weather= "";
            		if (location.contains("天气情况")) {
            			String[] locationSplit = location.split("天气情况");
            			location = locationSplit[0];
            			weather = locationSplit[locationSplit.length-1];// 防止前端传重复的天气情况文本
//            			weather = locationSplit[1];
            			
            			if (weather.contains(":")) { // 英文格式
            				weather = weather.split(":")[1];
            			}
            			if (weather.contains("：")) {
            				weather = weather.split("：")[1]; // 中文格式
            			}
            		}
            		if (StringUtils.isBlank(weather)) {
            			weather = "无";
            		}
            		String eventDesc = removeEventDescLastPeriod(dto.getEventDesc()); // 删除掉输入的描述内容的最后一个句号
            		if (eventDesc != null && eventDesc.length() <= 500) {// 超过500，不发送短信
            			// 保存续报、终报的内容到event_process_sms_message
            			EventProcessSmsMessageDTO processSmsMessageDTO = new EventProcessSmsMessageDTO();
            			processSmsMessageDTO.setEventProcessId(Long.parseLong(progressDTO.getId() + ""));
            			processSmsMessageDTO.setEventDesc(eventDesc);
            			location = removeEventDescLastPeriod(location);
            			processSmsMessageDTO.setLocation(location);
            			processSmsMessageDTO.setTimes(StringUtils.isNotBlank(times) ? Integer.parseInt(times) : 0);
            			processSmsMessageDTO.setType(2);
            			String reportTime = removeEventDescLastPeriod(dto.getReportTime());
            			processSmsMessageDTO.setReportTime(reportTime);
            			String reportSource = removeEventDescLastPeriod(dto.getReportSource());
            			processSmsMessageDTO.setReportSource(reportSource);
            			weather = removeEventDescLastPeriod(weather);
            			processSmsMessageDTO.setWeatherDesc(weather);
            			processSmsMessageDTO.setUrlSuffix(urlMsg);
            			processSmsMessageDTO.setCreateTime(System.currentTimeMillis() / 1000);
            			progressSmsMessageMapper.add(processSmsMessageDTO);
            			// 组装发送短信
            			if (eventDetailVO.getEventTwoType() == 5) { // 突发事件
            				smsTemplateDTO.setContent(smsTemplateDTO.createEmerResubmitReportContent(today, seqno, times, reportTime, location, weather, eventDesc, reportSource, urlMsg));
            			} else {
            				smsTemplateDTO.setContent(smsTemplateDTO.createResubmitReportContent(times, reportTime, location, weather, eventDesc, reportSource, urlMsg));
            			}
            			boolean sendStatus = AliyunSmsUtils.sendAnSms(smsTemplateDTO);
            			String bizId = smsTemplateDTO.getBizId();
            			ProgressUserDTO eventPorgressUserDTO = new ProgressUserDTO();
            			eventPorgressUserDTO.setEventProgressId(progressDTO.getId());
            			eventPorgressUserDTO.setMobile(mobile);
            			eventPorgressUserDTO.setBizId(bizId);
            			if (sendStatus) {// 发送短信接口调用成功
            				eventPorgressUserDTO.setSmsSendStatus(2);
            			} else {
            				// 更新进展用户的短信发送状态为失败(0)
            				eventPorgressUserDTO.setSmsSendStatus(0);
            				eventPorgressUserDTO.setSmsFailReason(smsTemplateDTO.getFailReason());
            			}
            			eventPorgressUserDTO.setSendTime(TimeUtils.getTimeString());
            			progressMapper.updateSmsSendStatus(eventPorgressUserDTO);
            		}
            	}
            }
        }
        if (ret) {
        	// 事件待审核的信息置为失效状态4，该事件已发送终报，该续报报送申请已失效
        	EventInfoReviewDTO eventInfoReviewDTO = new EventInfoReviewDTO();
        	eventInfoReviewDTO.setEventId(eventId);
        	eventInfoReviewDTO.setReviewStatus(4);
        	eventInfoReviewDTO.setInfoType(5);
        	eventInfoReviewDTO.setReviewRemark("该事件已发送终报，该续报报送申请已失效");
        	eventMapper.updateEventInfoInvalid(eventInfoReviewDTO);
        }
        return ret;
    }

    private ProgressDTO toProgress(EventConfirmDTO dto, int cardType) {
        ProgressDTO progressDTO = new ProgressDTO();
        Long serverTime = dto.getServerTime();
        if (serverTime == null) {
            serverTime = System.currentTimeMillis() / 1000;
            dto.setServerTime(serverTime);
        }
        progressDTO.setCreateTime(serverTime);
        progressDTO.setOccurTime(serverTime);
        progressDTO.setProgressDesc(dto.getProgressDesc());
        progressDTO.setAtUsers(dto.getAtUsers());
        progressDTO.setEventId(dto.getId());
        progressDTO.setCreateUserId(dto.getCreateUserId());
        progressDTO.setCardType(cardType);
        return progressDTO;
    }

    private void validReportSource(EmerRescueDTO dto) {
        Integer source = dto.getSource();
        int sourceInt = source == null ? 0 : source;
        if (sourceInt != 1) {
            Integer reportSourceKey = dto.getReportSourceKey();
            if (reportSourceKey == null) {
                throw new ArgumentException("接报来源不能为空");
            }
        }
    }

    @Transactional
    public ResponseVO updateEmerRescue(EmerRescueDTO dto) {
        String eventId = dto.getId();
        if (StringUtils.isBlank(eventId)) {
            throw new ArgumentException("事件ID不能为空");
        }
        validReportSource(dto);
        final long serverTime = System.currentTimeMillis() / 1000;
        String serverTimeString = TimeUtils.getTimeString(TimeUtils.FULL_TIME, serverTime * 1000);
        dto.setServerTime(serverTimeString);
        IdStringDTO eventIdDTO = new IdStringDTO(eventId);
        String recordManId = dto.getRecordManId();
        
        EventDetailVO detailVO = eventMapper.selectEmerRescueByEventId(eventIdDTO);
        UserSimpleVO loginUser = feignClient.selectByUserId(dto.getRecordManId());
        updateEmerRescueEventLog(detailVO, dto, loginUser, eventId, serverTime);
        Integer roadNo = detailVO.getRoadNo();// 事件修改前的路段编号
        Integer roadNo2 = dto.getRoadNo();// 前端参数
        String directionNo = detailVO.getDirectionNo();// 事件修改前的方向编号
        final String milePost = dto.getMilePost();// 前端输入的桩号

        int changeEmerPlan = 0;
        if (roadNo != null && roadNo2 != null && roadNo.intValue() != roadNo2.intValue()) {
            // 路段变更，需在进展中体现（路段与方向调整为XXXXX）
            // 查询路段名称和方向名称
            RoadDirectionVO roadDirectionVO = feignClient
                    .selectAnRoadDirection(new RoadDirectionDTO(roadNo2, dto.getDirectionNo()));
            String progressDesc = "路段与方向调整为" + roadDirectionVO.getRoadName() + roadDirectionVO.getDirectionName();
            ProgressDTO progressDTO = new ProgressDTO();
            progressDTO.setCreateTime(serverTime);
            progressDTO.setOccurTime(serverTime);
            progressDTO.setProgressDesc(progressDesc);
            progressDTO.setEventId(eventId);
            progressDTO.setCreateUserId(recordManId);
            progressDTO.setCardType(0);
            progressService.add(progressDTO);
            // 查询路段变更后，新匹配的预案列表是否存在原有事件的预案
            Map<String, Object> map = new HashMap<>();
            map.put("roadNo", roadNo2);
            map.put("emerPlanId", detailVO.getEmerPlanId());
            String emerPlanId = eventMapper.checkEmerplanRoad(map);
            if (emerPlanId == null) {// 事件中的预案没有包含roadNo2
                changeEmerPlan = 1;
            }
        } else if (directionNo != null && !directionNo.equals(dto.getDirectionNo())) {
            // 查询路段名称和方向名称
            RoadDirectionVO roadDirectionVO = feignClient
                    .selectAnRoadDirection(new RoadDirectionDTO(roadNo2, dto.getDirectionNo()));
            String progressDesc = "路段与方向调整为" + roadDirectionVO.getRoadName() + roadDirectionVO.getDirectionName();
            ProgressDTO progressDTO = new ProgressDTO();
            long time = System.currentTimeMillis() / 1000;
            progressDTO.setCreateTime(time);
            progressDTO.setOccurTime(time);
            progressDTO.setProgressDesc(progressDesc);
            progressDTO.setEventId(eventId);
            progressDTO.setCreateUserId(recordManId);
            progressDTO.setCardType(0);
            progressService.add(progressDTO);
        }

        List<EventCarDTO> carOwners = dto.getCarOwners();
        // 清空 该事件的event_car信息
        eventMapper.clearCarOwners(eventIdDTO);
        if (!CollectionUtils.isEmpty(carOwners)) {
            // 批量新增carOwners
            eventMapper.addCarOwners(dto);
        }

        Integer source = dto.getSource();
        source = (source == null ? 0 : source);
        Integer sourceId = detailVO.getSourceId();
        sourceId = (sourceId == null ? 0 : sourceId);
        if (source != 1 || (source == 1 && sourceId == 1)) {
            // 根据路段roadNo匹配责任公司
            if (dto.getRoadNo() != null) {
                if (StringUtils.isNotBlank(milePost)) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("roadNo", dto.getRoadNo());
                    map.put("mile", NumberUtils.toInt(milePost.replace("K", "").replace("+", ""), -1));
                    int rows = eventMapper.validRoadMilePost(map);
                    if (rows == 0) {
                        throw new ArgumentException("桩号不在范围");
                    }
                }

                OrganizationRoadVO organizationRoadVO = feignClient.selectByRoadNo(new IdIntegerDTO(dto.getRoadNo()));
                Integer newSourceId = 0;
                if (organizationRoadVO != null) {
                    newSourceId = organizationRoadVO.getSourceId();
                    if (source != 1) {
                        dto.setOrgId(organizationRoadVO.getOrgId());
                        dto.setSourceId(newSourceId);
                    } else if (sourceId == 1) {
                        if (newSourceId != null && newSourceId == 1) {
                            dto.setOrgId(organizationRoadVO.getOrgId());
                        }
                    }
                }
            }
        }
        // 占用车道，先删后增
        EventDealStatusVO eventDealStatus = eventMapper.selectEventDealStatus(eventIdDTO);
        List<Integer> occupiedLanes = dto.getOccupiedLanes();
        int occupiedLanesSize = 0;
        if (!CollectionUtils.isEmpty(occupiedLanes)) {
            occupiedLanesSize = occupiedLanes.size();
        }
        eventMapper.deleteOccupiedLane(dto);
        if (occupiedLanesSize > 0) {
            eventMapper.addOccupiedLane(dto);
            // event_deal_status cancelCmsPublish=null
            if (eventDealStatus.getCmsPublish() == null) {
                Map<String, Object> map = new HashMap<>();
                map.put("eventId", eventIdDTO.getId());
                map.put("cmsPublish", 0);
                map.put("cancelCmsPublish", null);
                eventMapper.updateCmsPublish(map);
            }
        }
        Integer fullClose = eventDealStatus.getFullClose();
        Integer totalFullClose = eventDealStatus.getTotalFullClose();
        int totalFullCloseTime = totalFullClose == null ? 0 : totalFullClose;
        int carLane = eventMapper.selectCarLane(roadNo2);
        if (StringUtils.isNotBlank(milePost)) {
            String mp = milePost.replace("K", "").replace("k", "").replace("+", "");
            int mpValue = NumberUtils.toInt(mp, -1);
            if (mpValue >= 0) {
                RoadMilePostDTO roadMilePostDTO = new RoadMilePostDTO(roadNo2, mpValue);
                RoadVO roadVO = feignClient.selectRoadByRoadNoAndMilePost(roadMilePostDTO);
                if (roadVO != null) {
                    carLane = roadVO.getCarLane();
                }
            }
        }
        carLane = carLane / 2;
        EventDealStatusDTO eventDealStatusDTO = new EventDealStatusDTO();
        eventDealStatusDTO.setId(eventDealStatus.getId());

        Integer facilityType = dto.getFacilityType();
        if (facilityType == null) {
            facilityType = 0;
        }
        for (Integer occupiedLane : occupiedLanes) {
            if (occupiedLane != null && occupiedLane == -1) {
                occupiedLanesSize--;
            }
            if (occupiedLane != null && occupiedLane == 0) {
                if (facilityType == 2) {
                    occupiedLanesSize--;
                }
            }
        }
        LOGGER.warn("carLane:{}, occupiedLanesSize:{}", carLane, occupiedLanesSize);
        if (fullClose == null || fullClose == 0) {
            if (carLane > 0
                    && (occupiedLanesSize >= (carLane + 1) || (facilityType == 2 && occupiedLanesSize >= carLane))) {// 单向全幅封闭
                eventDealStatusDTO.setFullClose(1);
                eventDealStatusDTO.setStartFullClose(serverTime);
                // 新增留痕进展，该道路目前单向全幅封闭，开始时间：xxxx年xx月xx日 HH:mm:ss。
                ProgressDTO fullCloseProgressDTO = new ProgressDTO();
                fullCloseProgressDTO.setCreateTime(serverTime);
                fullCloseProgressDTO.setOccurTime(serverTime);
                fullCloseProgressDTO.setProgressDesc(
                        "该道路目前单向全幅封闭，开始时间：" + TimeUtils.getTimeString(TimeUtils.FULL_TIME_3, serverTime * 1000) + "。");
                fullCloseProgressDTO.setEventId(eventId);
                fullCloseProgressDTO.setCreateUserId(recordManId);
                progressService.add(fullCloseProgressDTO);
                eventMapper.updateFullClose(eventDealStatusDTO);

                // 新增全幅封闭的历史记录event_deal_full_close
                EventDealFullCloseDTO fullCloseDTO = new EventDealFullCloseDTO();
                fullCloseDTO.setEventId(eventId);
                fullCloseDTO.setStartCloseTime(serverTime);
                fullCloseDTO.setCreateTime(serverTimeString);
                eventMapper.addEventDealFullClose(fullCloseDTO);
                // event_deal_status中的unblock值为null，则改为0
                eventMapper.needUnblock(new IdStringDTO(eventId));
            }
        } else if (fullClose == 1) {// 单向全幅封闭
            boolean firedFullClode = false;
            if (facilityType == 2) {
                if (carLane > 0 && occupiedLanesSize < carLane) {
                    firedFullClode = true;
                }
            } else if (occupiedLanesSize < (carLane + 1)) {
                firedFullClode = true;
            }

            if (firedFullClode) {// 单向全幅封闭解除
                // 该道路单向全幅封闭结束，开始时间：xxxx年xx月xx日 HH:mm:ss，结束时间：xxxx年xx月xx日 HH：mm:ss，持续时间：x小时x分
                Long startFullClose = eventDealStatus.getStartFullClose();
                long thisTime = serverTime - startFullClose;
                String time = "";
                int day = (int) (thisTime / 86400);
                if (day > 0) {
                    time += (day + "天");
                }
                int hour = (int) (thisTime % 86400 / 3600);
                if (hour > 0) {
                    time += (hour + "小时");
                } else {
                    if (day > 0) {
                        time += "0小时";
                    }
                }
                int minute = (int) (thisTime % 3600 / 60);
                int second = (int) thisTime % 60;
                if (second > 30) {
                    time += ((minute + 1) + "分");
                } else {
                    time += (minute + "分");
                }
                ProgressDTO fullCloseProgressDTO = new ProgressDTO();
                fullCloseProgressDTO.setCreateTime(serverTime);
                fullCloseProgressDTO.setOccurTime(serverTime);
                fullCloseProgressDTO.setProgressDesc("该道路目前单向全幅封闭，开始时间："
                        + TimeUtils.getTimeString(TimeUtils.FULL_TIME_3, startFullClose * 1000) + "，结束时间："
                        + TimeUtils.getTimeString(TimeUtils.FULL_TIME_3, serverTime * 1000) + "，持续时间：" + time + "。");
                fullCloseProgressDTO.setEventId(eventId);
                fullCloseProgressDTO.setCreateUserId(recordManId);
                progressService.add(fullCloseProgressDTO);
                eventDealStatusDTO.setFullClose(0);
                eventDealStatusDTO.setStartFullClose(null);
                eventDealStatusDTO.setTotalFullClose(totalFullCloseTime + (int) thisTime);
                eventMapper.updateFullClose(eventDealStatusDTO);

                // 更新全幅封闭的历史记录event_deal_full_close
                EventDealFullCloseDTO fullCloseDTO = new EventDealFullCloseDTO();
                fullCloseDTO.setEventId(eventId);
                fullCloseDTO.setEndCloseTime(serverTime);
                fullCloseDTO.setUpdateTime(serverTimeString);
                eventMapper.updateEventDealFullClose(fullCloseDTO);
            }
        }

        // 批量修改附件
        if (!CollectionUtils.isEmpty(dto.getAttachs())) {
            eventMapper.batchUpdateAttachs(dto);
        }
        // 查询当前填报用户的信息，存在不更新填报人信息
        if (detailVO.getRecordManId() == null && recordManId != null) {
            UserSimpleVO recordManVO = feignClient.selectByUserId(recordManId);
            dto.setRecordMan(recordManVO.getUserName());
            dto.setRecordManTel(recordManVO.getMobile());
        }
        if (StringUtils.isNotBlank(dto.getMilePost())) {
            pileNo2Lnglat(dto);
        } else {
            dto.setLng(null);
            dto.setLat(null);
        }
        // 先删除路损，在新增
        if (dto.getRoadLoss() != null && dto.getRoadLoss().size() > 0) {
            eventMapper.deleteEventRoadLoss(dto);
            eventMapper.addEventRoadLoss(dto);
        }
        // 查询事件是否处理归档激活编辑状态
        Integer activeStatus = eventMapper.checkActiveStatus(eventId);
        if (activeStatus.equals(2)) {
            dto.setActiveStatus(1);// 保存时如果是激活状态更新为1 2:激活 1：未激活
        }
        boolean ret = eventMapper.updateEmerRescue(dto) > 0;
        // 查询救援扩展表，有数据update，无数据insert
        EventDDVO eventDDVO = eventMapper.selectEventDD(eventIdDTO);
        EventDDDTO eventDDDto = new EventDDDTO();
        BeanUtils.copyProperties(dto, eventDDDto);
        eventDDDto.setEventId(eventId);
        eventDDDto.setUpdateTime(new Date());
        if (eventDDVO == null) {
            eventMapper.addEventDD(eventDDDto);
        } else {
            eventDDDto.setReportSource(null); // 更新事件详情时，不更新event_dd内的接报来源（由用户自主填写的记录）
            eventMapper.updateEventDD(eventDDDto);
        }

        if (ret) {
            LOGGER.info("修改事件：{}", new Gson().toJson(dto));
        }
        if (ret) {
        	updateMilePostPushAmap(detailVO, dto);
            return new ResponseVO("修改事件成功", 1, "" + changeEmerPlan);
        }
        return new ResponseVO("修改事件失败", 1, "" + changeEmerPlan);
    }

    /**
     * @description 修改应急救援的基本信息的事件日志
     * @param detailVO
     * @param dto
     */
    private void updateEmerRescueEventLog(EventDetailVO detailVO, EmerRescueDTO dto, UserSimpleVO loginUser, String eventId, Long serverTime) {
    	String beforeChange = "";
    	String afterChange = "";
    	Long dbReportTime = detailVO.getReportTime();
    	Long reportTime = dto.getReportTime();
    	if (dbReportTime != null && reportTime != null && dbReportTime.longValue() != reportTime.longValue()) {
    		beforeChange += "接报时间：" + TimeUtils.getTimeString(TimeUtils.FULL_TIME, dbReportTime * 1000) + "、、";
    		afterChange += "接报时间：" + TimeUtils.getTimeString(TimeUtils.FULL_TIME, reportTime * 1000) + "、、";
    	}
    	
    	Integer dbReportSourceKey = detailVO.getReportSourceKey();
    	String dbReportSource = detailVO.getReportSource();
    	Integer reportSourceKey = dto.getReportSourceKey();
    	String reportSource = dto.getReportSource();
    	if (dbReportSourceKey != null && reportSourceKey != null) {
    		if (dbReportSourceKey == reportSourceKey && dbReportSourceKey == 99 && !dbReportSource.equals(reportSource)) {
    			beforeChange += "接报来源：" + dbReportSource + "、、";
        		afterChange += "接报来源：" + reportSource + "、、";
    		} else if (dbReportSourceKey != reportSourceKey) {
    			beforeChange += "接报来源：" + this.getDictItemName(123, "事件管理-接报来源", String.valueOf(dbReportSourceKey)) + "、、";
        		afterChange += "接报来源：" + this.getDictItemName(123, "事件管理-接报来源", String.valueOf(reportSourceKey)) + "、、";
    		}
    	}
    	
    	Integer dbRoadNo = detailVO.getRoadNo();
    	Integer roadNo = dto.getRoadNo();
    	String dbDirectionNo = detailVO.getDirectionNo();
    	String directionNo = dto.getDirectionNo();
    	if (dbRoadNo != null && roadNo != null && dbDirectionNo != null && directionNo != null) {
    		if (dbRoadNo.intValue() != roadNo.intValue() || !dbDirectionNo.equals(directionNo)) {
    			beforeChange += "路段方向：" + detailVO.getRoadName() + "/" + detailVO.getDirectionName() + "、、";
    			RoadDirectionVO anRoadDirection = feignClient.selectAnRoadDirection(new RoadDirectionDTO(dto.getRoadNo(), dto.getDirectionNo()));
        		afterChange += "路段方向：" + anRoadDirection.getRoadName() + "/" + anRoadDirection.getDirectionName() + "、、";
    		}
    	}
    	
    	List<OccupiedLaneVO> dbOccupiedLanes = detailVO.getOccupiedLanes();
    	List<Integer> occupiedLanes = dto.getOccupiedLanes();
    	int dbOccupiedLaneLength = 0;
    	int occupiedLaneLength = 0;
    	if (!CollectionUtils.isEmpty(dbOccupiedLanes) ) {
    		dbOccupiedLaneLength = dbOccupiedLanes.size();
    	}
    	if (!CollectionUtils.isEmpty(occupiedLanes) ) {
    		occupiedLaneLength = occupiedLanes.size();
    	}
    	boolean change = false;
    	List<Integer> dbLanes = new ArrayList<>();
    	if (dbOccupiedLaneLength > 0) {
    		for (OccupiedLaneVO dbOccupiedLane : dbOccupiedLanes) {
        		Integer occupiedLane = dbOccupiedLane.getOccupiedLane();
        		dbLanes.add(occupiedLane);
    		}
    		Collections.sort(dbLanes);
    	}
    	if (occupiedLaneLength > 0) {
    		Collections.sort(occupiedLanes);
    	}
    	if (dbOccupiedLaneLength != occupiedLaneLength) { // 长度不一样，车道有变更
    		change = true;
    	} else if (dbOccupiedLaneLength > 0) {
    		for (int i = 0; i < dbOccupiedLaneLength; i++) {
    			if (dbLanes.get(i) != occupiedLanes.get(i)) {
    				change = true;
    				break;
    			}
			}
    	}
    	if (change) {
    		beforeChange += "影响车道：";
    		if (dbOccupiedLaneLength > 0) {
    			for (Integer dblane : dbLanes) {
    				beforeChange += this.getDictItemName(70, "事件管理-影响车道", String.valueOf(dblane)) +  "、";
				}
    			beforeChange +=  "、";
    		} else {
    			beforeChange += "-、、";
    		}
    		afterChange += "影响车道：";
    		if (occupiedLaneLength > 0) {
    			for (Integer dblane : occupiedLanes) {
    				afterChange += this.getDictItemName(70, "事件管理-影响车道", String.valueOf(dblane)) +  "、";
    			}
    			afterChange +=  "、";
    		} else {
    			afterChange += "-、、";
    		}
    	}
    	String dbWeather = detailVO.getWeather();
    	String weather = dto.getWeather();
    	if (!StringUtils.equals(dbWeather, weather)) {
    		if (StringUtils.isBlank(dbWeather)) {
    			beforeChange += "天气：-、、";
    		} else {
    			beforeChange += "天气：" + this.getDictItemName(61, "天气代码图标", dbWeather) + "、、";
    		}
    		if (StringUtils.isBlank(weather)) {
    			afterChange += "天气：-、、";
    		} else {
    			afterChange += "天气：" + this.getDictItemName(61, "天气代码图标", weather) + "、、";
    		}
    	}
    	Integer dbDeathMan = detailVO.getDeathMan();
    	Integer deathMan = dto.getDeathMan();
    	if (dbDeathMan != deathMan) {
    		if (dbDeathMan == null) {
    			beforeChange += "死亡：-、、";
    		} else if (dbDeathMan == -1) {
    			beforeChange += "死亡：不详、、";
    		} else if (dbDeathMan == 0) {
    			beforeChange += "死亡：无、、";
    		} else if (dbDeathMan > 0) {
    			beforeChange += "死亡：" + dbDeathMan + "、、";
    		}
    		if (deathMan == null) {
    			afterChange += "死亡：-、、";
    		} else if (deathMan == -1) {
    			afterChange += "死亡：不详、、";
    		} else if (deathMan == 0) {
    			afterChange += "死亡：无、、";
    		} else if (deathMan > 0) {
    			afterChange += "死亡：" + deathMan + "、、";
    		}
    	}
    	Integer dbInjureMan = detailVO.getInjureMan();
    	Integer injureMan = dto.getInjureMan();
    	if (dbInjureMan != injureMan) {
    		if (dbInjureMan == null) {
    			beforeChange += "受伤：-、、";
    		} else if (dbInjureMan == -1) {
    			beforeChange += "受伤：不详、、";
    		} else if (dbInjureMan == 0) {
    			beforeChange += "受伤：无、、";
    		} else if (dbInjureMan > 0) {
    			beforeChange += "受伤：" + dbInjureMan + "、、";
    		}
    		if (injureMan == null) {
    			afterChange += "受伤：-、、";
    		} else if (injureMan == -1) {
    			afterChange += "受伤：不详、、";
    		} else if (injureMan == 0) {
    			afterChange += "受伤：无、、";
    		} else if (injureMan > 0) {
    			afterChange += "受伤：" + injureMan + "、、";
    		}
    	}
    	String dbAccidentCause = detailVO.getAccidentCause();
    	String accidentCause = dto.getAccidentCause();
    	if (!StringUtils.equals(dbAccidentCause, accidentCause)) {
    		if (StringUtils.isBlank(dbAccidentCause)) {
    			beforeChange += "事故原因：-、、";
    		} else {
    			beforeChange += "事故原因：" + this.getDictItemName(100, "应急救援-事故原因", dbAccidentCause) + "、、";
    		}
    		if (StringUtils.isBlank(accidentCause)) {
    			afterChange += "事故原因：-、、";
    		} else {
    			afterChange += "事故原因：" + this.getDictItemName(100, "应急救援-事故原因", accidentCause) + "、、";
    		}
    	} else if ("99".equals(dbAccidentCause)) {
    		beforeChange += "事故原因：" + detailVO.getOtherCause() + "、、";
    		afterChange += "事故原因：" + dto.getOtherCause() + "、、";
    	}
    	if (beforeChange.length() > 2) {
    		// 当前登录用户的信息
    		String operator = loginUser.getOrgName() + "-" + loginUser.getUserName();
    		EventLogDTO eventLog = new EventLogDTO();
    		eventLog.setInfoType("基本信息");
    		eventLog.setBeforeChange(beforeChange.substring(0, beforeChange.length()-2));
    		eventLog.setAfterChange(afterChange.substring(0, afterChange.length()-2));
    		eventLog.setEventId(eventId);
    		eventLog.setCreateTime(TimeUtils.getTimeString(TimeUtils.FULL_TIME, serverTime * 1000));
    		eventLog.setOperator(operator);
    		eventLogMapper.add(eventLog);
    	}
    	
    }

    /**
     * @description 修改应急救援事件，如果调整桩号，需要推送到高德
     */
    private void updateMilePostPushAmap(EventDetailVO detailVO, EmerRescueDTO dto) {
    	// 如果有桩号调整，推送到高德
    	int stateFlag = -1;// 0-新增 1-更新 2-删除 （高德推送中的状态字段）
        Integer pushAmap = detailVO.getPushAmap();
        String milePost = dto.getMilePost();
        if (milePost != null && !milePost.equals(detailVO.getMilePost())) {
        	// event_dd 中的push_amap
        	if (pushAmap == null || pushAmap == 0) {
        		stateFlag = 0;
        	} else {
        		stateFlag = 1;
        	}
        	
        }
        if (stateFlag >= 0) {
        	List<Integer> occupiedLanes = dto.getOccupiedLanes();
        	detailVO.getId();
        	ProgressVO initReport = progressService.selectInitReport(new IdStringDTO(dto.getId()));
        	String initReportDesc = null;
        	if (initReport != null) {
        		initReportDesc = initReport.getProgressDesc();
        	}
        	if (CollectionUtils.isEmpty(occupiedLanes)) {
        		detailVO.setOccupiedLanes(null);
        	} else {
        		List<OccupiedLaneVO> occupiedLaneList = new ArrayList<>();
        		for (Integer occupiedLane : occupiedLanes) {
        			OccupiedLaneVO vo = new OccupiedLaneVO();
        			vo.setOccupiedLane(occupiedLane);
        			occupiedLaneList.add(vo);
        		}
        		detailVO.setOccupiedLanes(occupiedLaneList);
        	}
        	eventPushToAmap(detailVO, stateFlag, initReportDesc);
        }
    }
    
    private ProgressDTO finishProgress(EventFinishDTO dto) {
        // 添加完结进展
        ProgressDTO progressDTO = new ProgressDTO();
        long time = dto.getFinishTime();
        progressDTO.setCreateTime(time);
        progressDTO.setOccurTime(time);
        progressDTO.setProgressDesc("事件已处理完毕。处理结果：" + dto.getDealResult());
        progressDTO.setEventId(dto.getId());
        progressDTO.setCardPass(100);
        progressDTO.setCreateUserId(dto.getFinishUserId());
        return progressDTO;
    }

    @Transactional
    public int finishEmerRescue(EventFinishDTO dto) {
        String eventId = dto.getId();
        IdStringDTO eventIdDTO = new IdStringDTO(eventId);
        EventDetailVO eventDetailVO = eventMapper.selectEmerRescueByEventId(eventIdDTO);
        Integer eventTwoType = eventDetailVO.getEventTwoType();
        Integer eventThreeType = eventDetailVO.getEventThreeType();
        if ((eventTwoType == 6 && eventThreeType == 79) || (eventTwoType == 25 && eventThreeType == 26)) {
        	String weather = dto.getWeather();
        	if (StringUtils.isBlank(weather)) {
        		throw new ArgumentException("天气不能为空");
        	}
        	String accidentCause = dto.getAccidentCause();
        	if (StringUtils.isBlank(accidentCause)) {
				throw new ArgumentException("事故原因不能为空");
			} else if ("999".equals(accidentCause)) {
				String otherCause = dto.getOtherCause();
				if (StringUtils.isBlank(otherCause)) {
					throw new ArgumentException("其他原因不能为空");
				}
			}
        }

        // 校验
        eventValidService.finishEmerRescue(eventDetailVO);

        long serverTime = System.currentTimeMillis() / 1000;
        dto.setFinishTime(serverTime);
        Long finalReportTime = eventDetailVO.getFinalReportTime();
        if (finalReportTime == null) { // 终报时间为空，插入终报统计时间
            dto.setFinalReportTime(serverTime);
        }
        // 第一步: 添加完结进展
        ProgressDTO finishProgressDTO = finishProgress(dto);
        progressService.add(finishProgressDTO);

        Map<String, Object> map = new HashMap<>();
        map.put("eventId", eventId);
        map.put("cardType", 1);
        ProgressVO initReport = progressService.selectInitReport(eventIdDTO);
        dto.setConfirmTime(initReport.getUpdateTime());// 确认时间
        dto.setDistributeTime(initReport.getOccurTime());// 分发时间

        // 第二步: 更新事件主表的字段
        // 改变事件处理状态为100，并且修改finishUserId，结束事件时间，确认时间和分发时间
        int ret = eventMapper.finishEmerRescue(dto);
        Integer source = eventDetailVO.getSource();
        if (source != null && source == 1) {// source=1为96333事件
            String eventNo = eventDetailVO.getEventNo();
            LOGGER.info("提交96333开关:{}，工单号：{}", eventRpc96333Finish, eventNo);
            if (eventNo.startsWith("DD-")) {
                LOGGER.info("不需要提交到96333系统，工单号：{}", eventNo);
            } else if (ret > 0 && "1".equals(eventRpc96333Finish)) {
                if ("new".equals(event96333Mode)) {
                    dealResultSubmitTo96333(eventDetailVO, dto);
                    // 新增一条需要同步状态的记录event_96333_sync
                    map.put("eventNo", eventNo);
                    int row = eventMapper.selectEvent96333ResultCode(eventIdDTO);
                    if (row == 0) {
                        eventMapper.addEvent96333ResultCode(map);
                    }
                } else {
                    oldDealResultSubmitTo96333(eventDetailVO, dto);
                }
            }
        }

        if (ret > 0) {
        	// 事件待审核的信息置为失效状态4，该事件已发送终报，该续报报送申请已失效
        	EventInfoReviewDTO eventInfoReviewDTO = new EventInfoReviewDTO();
        	eventInfoReviewDTO.setEventId(eventId);
        	eventInfoReviewDTO.setReviewStatus(4);
        	eventInfoReviewDTO.setReviewRemark("事件已完结：该事件已完结，该信息报送申请已失效");
        	eventMapper.updateEventInfoInvalid(eventInfoReviewDTO);

            Integer pushAmap = eventDetailVO.getPushAmap();
            if (pushAmap != null && pushAmap == 1) {
                eventDetailVO.setFinishTime(serverTime);

                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        // 删除高德事件
                        eventPushToAmap(eventDetailVO, 2, null);
                    }
                }).start();
            }
            // 异步处理：事件归档后自动发送短信给该事件相关的处置人（OA内，外单位除外）
            sendSmsToEventUser(eventId, eventDetailVO, dto.getFinishUserId());
        }
        return ret;
    }

    private void sendSmsToEventUser(String eventId, EventDetailVO eventDetailVO, String finishUserId) {
        // 校验参数
        if (StringUtils.isEmpty(eventId)) {
            LOGGER.error("The event id is empty when async send sms message,event id: {}", eventId);
            return;
        }
        // 查询该事件的应急预案是否处于已审核状态，如果是，则需要发送短信提醒
        int count = eventMapper.countEventEmerApproveStatus(eventId);
        if (count <= 0) {
            LOGGER.error("The event not approve OK,should not send sms message,event id: {}", eventId);
            return;
        }
        // 基于eventId查询OA内系统内，排除一路多方的相关人员信息（手机号）
        List<EventEmerUserVO> emerUserList = eventMapper.selectEmerUserByIdAndExcludeOrgName(eventId,
                EventConstant.EMER_USER_ORG_EXCLUDE);
        if (CollectionUtils.isEmpty(emerUserList)) {
            LOGGER.error("The event has not emer user when async send sms message,event id: {}", eventId);
            return;
        }
        // 异步线程发起短信发送
        new Thread(() -> asyncSendSms(eventId, eventDetailVO, finishUserId, emerUserList)).start();
    }

    private void asyncSendSms(String eventId, EventDetailVO eventDetailVO, String finishUserId, List<EventEmerUserVO> emerUserList) {
        // 封装进展数据
        ProgressDTO progressDTO = new ProgressDTO();
        progressDTO.setEventId(eventId);
        progressDTO.setCreateUserId(finishUserId);
        long time = System.currentTimeMillis() / 1000;
        progressDTO.setCreateTime(time);
        progressDTO.setOccurTime(time);
        progressDTO.setProgressDesc("事件归档后自动发送短信给该事件相关的处置人");
        progressDTO.setIsUse(0); // 默认不展示到界面
        List<ProgressUserDTO> progressUserDTOList = new ArrayList<>();
        progressDTO.setAtUsers(progressUserDTOList);

        // 发送sms消息
        String milePost = eventDetailVO.getMilePost();
        String location = eventDetailVO.getRoadName() + (milePost != null ? milePost : "");
        String orderNo = eventDetailVO.getEventNo();
        emerUserList.forEach(item -> {
            // 是否手机号校验
            String mobile = item.getMobile();
            if (StringUtils.isEmpty(mobile) || StringUtils.length(mobile) != 11 || !mobile.startsWith("1")) {
                LOGGER.warn("The event mobile is empty or error: {}", new Gson().toJson(item));
                return;
            }
            SmsTemplateDTO smsTemplateDTO = new SmsTemplateDTO();
            smsTemplateDTO.setTemplateId(stringRedisTemplate.opsForValue().get("its-event:SmsTemplateCode-FINISH_EMER_EVENT_REMIND"));
            smsTemplateDTO.setMobile(mobile);
            smsTemplateDTO.setContent(smsTemplateDTO.createFinishEmerEventContent(location, orderNo));
            boolean sendAnSms = AliyunSmsUtils.sendAnSms(smsTemplateDTO);
            if (!sendAnSms) {
                LOGGER.error("The finish event remind send sms message failed: {}", new Gson().toJson(smsTemplateDTO));
            }
            // 将数据记录到集合，便于批量插入
            ProgressUserDTO progressUserDTO = new ProgressUserDTO();
            progressUserDTO.setUserId(item.getUserId());
            progressUserDTO.setUserName(item.getUserName());
            progressUserDTO.setMobile(item.getMobile());
            progressUserDTO.setBizId(smsTemplateDTO.getBizId());
            progressUserDTO.setEventProgressId(progressDTO.getId());
            progressUserDTO.setSmsSendStatus(smsTemplateDTO.getRet());
            progressUserDTO.setSmsFailReason(smsTemplateDTO.getFailReason());

            progressUserDTOList.add(progressUserDTO);
        });
        // 插入到进展表
        int add = progressMapper.add(progressDTO);
        int insert = progressMapper.batchAddProgressUser(progressDTO);
        LOGGER.info("Insert progress is success? process: {},process user: {} when async send sms message", add,
                insert);
    }

    private void oldDealResultSubmitTo96333(EventDetailVO eventVO, EventFinishDTO dto) {
        // 处理96333事件，发送一个指令到command交换器（8.134）
        CommandDTO commandDTO = new CommandDTO();
        commandDTO.setSourceId("" + eventVO.getSourceId());
        commandDTO.setUuid(UUID.randomUUID().toString());
        commandDTO.setType("Event96333");
        // 事件编号eventNo，96333中的id(busiId)，处理结果dealResult
        Event96333SubmitDTO submitDTO = new Event96333SubmitDTO();
        submitDTO.setBusiId(eventVO.getBusiId());
        submitDTO.setDealResult(dto.getDealResult());
        submitDTO.setEventNo(eventVO.getEventNo());
        commandDTO.setParams(new Gson().toJson(submitDTO));
        itsMsFeignClient.produce(commandDTO);
    }

    /**
     * @描述 对接千方后的实现
     */
    private void dealResultSubmitTo96333(EventDetailVO eventVO, EventFinishDTO dto) {
        // 处理96333事件，发送一个指令到command交换器（8.134）
        CommandDTO commandDTO = new CommandDTO();
        commandDTO.setSourceId("" + SourceIdEnum.XFZ_99.getIndex());// 新发展来源
        commandDTO.setUuid(UUID.randomUUID().toString());
        commandDTO.setType(SystemConstants.EVENT_96333_FINISH_TYPE);
        // 事件编号eventNo，96333中的id(busiId)，处理结果dealResult
        Event96333SubmitDTO submitDTO = new Event96333SubmitDTO();
        submitDTO.setDealResult(dto.getDealResult());
        submitDTO.setEventNo(eventVO.getEventNo());
        submitDTO.setServiceType(ServiceTypeEnum.DD.getValue());// 96333的业务编号（救援）
        submitDTO.setSourceId(eventVO.getSourceId());
        submitDTO.setSubmitAccount(dto.getFinishAccount());
        commandDTO.setParams(new Gson().toJson(submitDTO));
        itsMsFeignClient.produce(commandDTO);
    }

    @Transactional
    public int emerUser(EventEmerUserDTO dto) {
        List<EmerUserDTO> emerUsers = dto.getEmerUsers();
        validEmerUser(emerUsers);
        // 更新事件中的emer_plan_id，该字段已在第一步中插入
//        eventMapper.updateEmerPlanId(dto);
        // 删除event_emer_user，event_emer_role
        eventMapper.deleteEmerUser(dto);

        for (EmerUserDTO emerUserDTO : emerUsers) {
        	Integer forceRemind = emerUserDTO.getForceRemind();
            Integer smsRemind = emerUserDTO.getSmsRemind();
            if (forceRemind == null) {
            	forceRemind = 0;
            }
            if (smsRemind == null) {
            	smsRemind = 0;
            }
            if (forceRemind != 0 && forceRemind != 1) {
            	throw new ArgumentException("Force remind 参数异常");
            }
            if (smsRemind != 0 && smsRemind != 1) {
                throw new ArgumentException("短信提醒smsRemind 参数异常");
            }
            List<UserDTO> users = emerUserDTO.getUsers();
            for (UserDTO user : users) {
                user.setForceRemind(forceRemind);
                user.setSmsRemind(smsRemind);
            }
        }

        return eventMapper.emerUser(dto);
    }

    @Transactional
    public boolean applyRemoveObstacles(RemoveObstaclesDTO dto) {
        // 更新事件中的car_order为1，排障派单情况
        dto.setCarOrder(1);
        eventMapper.updateCarOrder(dto);
        // event_emer_user新增
        // 生成新的进展（请求拖车救援）
        ProgressDTO progressDTO = new ProgressDTO();
        long time = System.currentTimeMillis() / 1000;
        progressDTO.setCreateTime(time);
        progressDTO.setOccurTime(time);
        progressDTO.setProgressDesc("请求拖车救援。");
        progressDTO.setEventId(dto.getId());
        progressDTO.setAtUsers(dto.getAtUsers());
        progressDTO.setCreateUserId(dto.getCreateUserId());
        progressDTO.setCardType(15);
        return progressService.add(progressDTO);
    }

    // SELECT eeu.user_id,eeu.emer_group_id,eg.group_name,er.role_name FROM
    // event_emer_user eeu,emer_group eg,emer_role
    // er WHERE eeu.emer_group_id=eg.id AND eg.role_id=er.id AND er.`value`=2
    public RemoveObstaclesUserVO selectRmObstaclesById(IdStringDTO dto) {
        return eventMapper.selectRmObstaclesById(dto);
    }

    public int operateAuthorityById(IdStringDTO dto, Object userId) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", dto.getId());
        map.put("userId", userId);
        return eventMapper.operateAuthorityById(map);
    }

    public int viewAuthorityById(EventAuthorityDTO dto) {
        return eventMapper.viewAuthorityById(dto);
    }

    final static String EVENT_URL = "its-event/event/ossCallback";

    public Map<String, String> ossUpload(AttachDTO dto) {
        Map<String, String> map = OssUtils.getSignature(ossConfig, dto, EVENT_URL);
        return map;
    }

    public AttachDTO ossCallback(HttpServletRequest request) throws ServletException, IOException {
        AttachDTO dto = OssUtils.callback(request.getInputStream(),
                NumberUtils.toInt(request.getHeader("content-length")));
        // 保存附件表记录
        eventMapper.addAttach(dto);
        return dto;
    }

    public boolean ossDelete(AttachDTO dto) {
        // 删除附件记录
        boolean ret = eventMapper.deleteAttach(dto) > 0;
        List<String> keys = new ArrayList<String>();
        keys.add(dto.getDiskFileName());
        OssUtils.batchDelete(ossConfig, keys);
        return ret;
    }

    public List<AttachDTO> ossUrl(List<AttachDTO> dtos) {
        return OssUtils.getUrl(ossConfig, dtos);
    }

    public int updateEventByOtherStatus(Map<String, Object> map) {
        // 更新事件激活编辑和回访状态
        return eventMapper.updateEventByOtherStatus(map);
    }

    public List<EventEmerUserVO> selectEmerUserById(IdStringDTO dto) {
        return eventMapper.selectEmerUserById(dto);
    }

    public List<GroupEventEmerUserVO> selectGroupEmerUserById(IdStringDTO dto) {
        List<GroupEventEmerUserVO> groupEmerUsers = eventMapper.selectGroupEmerUserById(dto);
        if (!CollectionUtils.isEmpty(groupEmerUsers)) {
            GroupEventEmerUserVO groupEmerUser = groupEmerUsers.get(0);
            if (StringUtils.isBlank(groupEmerUser.getEmerRoleId())) {
                groupEmerUsers.remove(0);
                groupEmerUsers.add(groupEmerUser);
            }
        }
        return groupEmerUsers;
    }

    private void validEmerUser(List<EmerUserDTO> emerUsers) {
        if (emerUsers == null || emerUsers.size() == 0) {
            throw new ArgumentException("应急角色下的用户不能为空");
        } else {
            for (EmerUserDTO emerUserDTO : emerUsers) {
                if (emerUserDTO.getUsers() == null || emerUserDTO.getUsers().size() == 0) {
                    throw new ArgumentException("应急角色下的用户不能为空");
                }
            }
        }
    }

    private boolean existEmerUser(List<EmerUserDTO> emerUsers, String userId) {
        for (EmerUserDTO vo : emerUsers) {
            for (UserDTO d : vo.getUsers()) {
                if (userId.equals(d.getUserId())) {
                    return true;
                }
            }
        }
        return false;
    }

    private EventEmerUserVO existEventEmerUser(List<EventEmerUserVO> eventEmerUsers, String userId) {
        for (EventEmerUserVO vo : eventEmerUsers) {
            if (userId.equals(vo.getUserId())) {
                return vo;
            }
        }
        return null;
    }

    private void pushEventProgressToRemoveUser(List<EventEmerUserVO> removeUsers, final String eventId,
                                               final Integer initReportProgressId) {
        List<String> removeUserIds = new ArrayList<String>();
        for (EventEmerUserVO removeUser : removeUsers) {
            removeUserIds.add(removeUser.getUserId());
        }
        if (!CollectionUtils.isEmpty(removeUserIds)) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    EventProgressMessageDTO eventProgressMessageDTO = new EventProgressMessageDTO();
                    eventProgressMessageDTO.setWebsocketType("addEventProgress");
                    eventProgressMessageDTO.setEventId(eventId);
                    eventProgressMessageDTO.setId(initReportProgressId);
                    eventProgressMessageDTO.setUserIds(removeUserIds);
                    // 推送WebSocket消息=》移除的处置人员
                    itsWebSocketFeignClient.pushEventProgress(eventProgressMessageDTO);
                }
            }).start();
        }
    }

    /**
     * @描述 处置变更：变更人员
     */
    @Transactional
    public int changeEmerUser(EventEmerUserDTO dto, String userId) {
    	// 短信提醒发送（smsRemind）
        List<EmerUserDTO> emerUsers = dto.getEmerUsers();/** 前端传入的参数 **/
        // 1、校验参数
        validEmerUser(emerUsers);

        long serverTime = System.currentTimeMillis() / 1000;
        UserSimpleVO loginUser = feignClient.selectByUserId(userId);
        final String eventId = dto.getId();
        IdStringDTO eventIdDTO = new IdStringDTO(eventId);
        List<EventEmerUserVO> dbEventEmerUsers = eventMapper.selectEmerUserById(eventIdDTO);
        
        StringBuffer changedProgressDesc = new StringBuffer("");

        ProgressVO initReportProgressVO = progressService.selectInitReport(eventIdDTO);// 初报进展
        Integer initReportProgressId = initReportProgressVO.getId();
        IdIntegerDTO initReportIdDTO = new IdIntegerDTO(initReportProgressId);
        final List<ProgressUserVO> initReportProgressUsers = progressMapper.selectProgressUser(initReportIdDTO);
        

        // 2、计算出移除的用户，拼接进展留痕描述
        List<EventEmerUserVO> removeUsers = new ArrayList<>();// 移除的用户
        List<String> removeUserIds = new ArrayList<>();
        for (EventEmerUserVO dbEventEmerUser : dbEventEmerUsers) {
            boolean exits = existEmerUser(emerUsers, dbEventEmerUser.getUserId());
            if (!exits) {
            	 if(!removeUserIds.contains(dbEventEmerUser.getUserId())) {
                 	removeUserIds.add(dbEventEmerUser.getUserId());
                 	removeUsers.add(dbEventEmerUser);
                 	changedProgressDesc.append(dbEventEmerUser.getUserName() + "，");
                 }
            }
        }
        if (changedProgressDesc.length() > 0) {
            changedProgressDesc.insert(0, "处置人员移除：");
        }
        // 3、推送事件进展给移除的用户
        pushEventProgressToRemoveUser(removeUsers, eventId, initReportProgressId);

        // 4、计算出新增的用户，拼接进展留痕描述
        List<UserDTO> addEmerUsers = new ArrayList<>();
        List<UserDTO> addSmsUsers = new ArrayList<>();
        String addUserMobiles = "";// 新增的处置人员的手机号，用英文逗号,隔开；例如18176270000,15994360000
        List<String> addUserIds = new ArrayList<>();
        boolean excuteOnce = true;
        for (EmerUserDTO vo : emerUsers) {
            for (UserDTO d : vo.getUsers()) {
            	EventEmerUserVO existEventEmerUser = existEventEmerUser(dbEventEmerUsers, d.getUserId());
                boolean exist = existEventEmerUser == null ? false : true;
                if (!exist) {
                    String tmpMobile = d.getMobile();
                    if (tmpMobile != null && tmpMobile.length() > 0 && addUserMobiles.indexOf(tmpMobile + ",") == -1) {
                    	addUserMobiles = addUserMobiles + tmpMobile + ",";
                        addEmerUsers.add(d);
                        if(vo.getSmsRemind() == 1) {
                        	addSmsUsers.add(d);
                        }
                    }
                    if (excuteOnce) {
                        changedProgressDesc.append("处置人员新增：");
                        excuteOnce = false;
                    }
                    if(!addUserIds.contains(d.getUserId())) {
                    	addUserIds.add(d.getUserId());
                    	changedProgressDesc.append(d.getUserName() + "，");
                     }
                } else {
                	if(existEventEmerUser.getSmsRemind() == 0 && vo.getSmsRemind() == 1) {
                		addSmsUsers.add(d);
                	}
                }
                d.setForceRemind(vo.getForceRemind());
                d.setSmsRemind(vo.getSmsRemind());
            }
        }

        if (changedProgressDesc.length() > 0) {
            changedProgressDesc.replace(changedProgressDesc.length() - 1, changedProgressDesc.length(), "。");
        }

        for (EmerUserDTO emerUser : emerUsers) {
            List<UserDTO> users = emerUser.getUsers();
            for (UserDTO u : users) {
                for (EventEmerUserVO eventEmerUser : dbEventEmerUsers) {
                    if (eventEmerUser.getUserId().equals(u.getUserId())) {
                        u.setForceRemind(emerUser.getForceRemind());
                        break;
                    }
                }
            }
        }
        boolean change = false;
        if (StringUtils.isNotBlank(changedProgressDesc.toString())) {
            // 处置人员有变化，事件重新关联应急人员
            change = true;
            emerUserEventLog(dbEventEmerUsers, emerUsers, loginUser, eventId, serverTime, false);
        } else {
            eventMapper.deleteEmerUser(dto);
            eventMapper.emerUser(dto);
            // 增加检测无人员变更，但是存在变更电话标识，forceRemind 0->1，需要拨号处理
            List<UserDTO> resultList = robotCallByForceRemind(emerUsers, initReportProgressUsers);
            EventDetailVO eventDetailVO = eventMapper.selectEmerRescueByEventId(eventIdDTO);
            RobotCallToChangeUsers(eventIdDTO.getId(), initReportProgressVO, resultList, eventDetailVO.getSourceId());
            return 1;// 无人员变化直接返回
        }

        // 查询进展用户（确认过初报的）
        // SELECT * FROM event_progress_user WHERE event_progress_id=167 AND card_pass=1
        List<ProgressUserVO> confirmUsers = progressService.selectConfirmIRUser(initReportIdDTO);// 查询确认初报的用户

        ProgressDTO addProgressDTO = new ProgressDTO();// 人员变化留痕进展
        addProgressDTO.setCreateTime(serverTime);
        addProgressDTO.setOccurTime(serverTime);
        addProgressDTO.setProgressDesc(changedProgressDesc.toString());
        addProgressDTO.setId(initReportProgressId);

        // 删除了哪些用户removeUsers
        List<ProgressUserDTO> removeProgressUsers = new ArrayList<>();
        for (EventEmerUserVO removeUser : removeUsers) {
            String mobile = removeUser.getMobile();
            if (StringUtils.isBlank(mobile)) {
                continue;
            }
            for (ProgressUserVO progressUserVO : initReportProgressUsers) {
                String dbMobile = progressUserVO.getMobile();
                if (mobile.equals(dbMobile)) {
                    ProgressUserDTO removeProgressUser = new ProgressUserDTO();
                    removeProgressUser.setEventProgressId(progressUserVO.getEventProgressId());
                    removeProgressUser.setUserId(progressUserVO.getUserId());
                    removeProgressUser.setUserName(progressUserVO.getUserName());
                    removeProgressUser.setMobile(mobile);
                    removeProgressUser.setBizId(progressUserVO.getBizId());
                    removeProgressUser.setSmsFailReason(progressUserVO.getSmsFailReason());
                    removeProgressUser.setSmsSendStatus(progressUserVO.getSmsSendStatus());
                    Timestamp sendTime = progressUserVO.getSendTime();
                    if (sendTime != null) {
                        removeProgressUser
                                .setSendTime(TimeUtils.getTimeString(TimeUtils.FULL_TIME, sendTime.getTime()));
                    } else {
                        removeProgressUser.setSendTime(TimeUtils.getTimeString());
                    }
                    removeProgressUser.setJobId(progressUserVO.getJobId());
                    removeProgressUser.setReferenceId(progressUserVO.getReferenceId());
                    removeProgressUser.setRobotCallStatus(progressUserVO.getRobotCallStatus());
                    removeProgressUser.setRobotFailReason(progressUserVO.getRobotFailReason());
                    Timestamp robotCallTime = progressUserVO.getRobotCallTime();
                    if (robotCallTime != null) {
                        removeProgressUser.setRobotCallTime(
                                TimeUtils.getTimeString(TimeUtils.FULL_TIME, robotCallTime.getTime()));
                    } else {
                        removeProgressUser.setRobotCallTime(TimeUtils.getTimeString());
                    }
                    
                    break;
                }
            }
        }

        List<ProgressUserVO> delStatusProgressUsers = progressMapper.selectDelProgressUser(initReportIdDTO);
        // 删除event_emer_user，event_progress_user，event_emer_role
        if (change) {
            progressService.deletePlanUser(addProgressDTO);
        }
        eventMapper.deleteEmerUser(dto);
        eventMapper.emerUser(dto);
        if (!CollectionUtils.isEmpty(removeProgressUsers)) {
            Map<String, Object> map = new HashMap<>();
            map.put("removeProgressUsers", removeProgressUsers);
            eventMapper.addRemoveProgressUser(map);// 添加删除的进展用户
        }

        ProgressDTO progressDTO = new ProgressDTO();
        List<ProgressUserDTO> atUsers = progressDTO.getAtUsers();
        String atUserIds = new String(";");
        for (EmerUserDTO tmp : emerUsers) {// emerUsers：前端传入的处置人员列表
            for (UserDTO userDTO : tmp.getUsers()) {
                String atUserId = userDTO.getUserId();
                if (StringUtils.isBlank(atUserId)) {
                    continue;
                }
                if (atUserIds.contains(";" + atUserId + ";")) {
                    continue;
                } else {
                    atUserIds += atUserId + ";";
                    ProgressUserDTO atUser = new ProgressUserDTO();
                    atUser.setEventProgressId(initReportProgressId);
                    atUser.setUserId(atUserId);
                    atUser.setUserName(userDTO.getUserName());
                    atUser.setMobile(userDTO.getMobile());
                    if (confirmUsers != null) {
                        for (ProgressUserVO tmpVO : confirmUsers) {
                            if (tmpVO.getUserId().equals(atUser.getUserId())) {
                                atUser.setCardPass(1);
                                break;
                            }
                        }
                    }
                    atUsers.add(atUser);
                }
            }
        }
        progressDTO.setId(initReportProgressId);

        if (change) {
            for (ProgressUserDTO atUser : progressDTO.getAtUsers()) {
                String mobile = atUser.getMobile();
                if (StringUtils.isNotBlank(mobile)) {
                    for (ProgressUserVO progressUserVO : initReportProgressUsers) {
                        String dbMobile = progressUserVO.getMobile();
                        if (mobile.equals(dbMobile)) {
                            atUser.setBizId(progressUserVO.getBizId());
                            atUser.setSmsFailReason(progressUserVO.getSmsFailReason());
                            atUser.setSmsSendStatus(progressUserVO.getSmsSendStatus());
                            Timestamp sendTime = progressUserVO.getSendTime();
                            if (sendTime != null) {
                                atUser.setSendTime(TimeUtils.getTimeString(TimeUtils.FULL_TIME, sendTime.getTime()));
                            } else {
                                atUser.setSendTime(TimeUtils.getTimeString());
                            }
                            atUser.setJobId(progressUserVO.getJobId());
                            atUser.setReferenceId(progressUserVO.getReferenceId());
                            atUser.setRobotCallStatus(progressUserVO.getRobotCallStatus());
                            atUser.setRobotFailReason(progressUserVO.getRobotFailReason());
                            Timestamp robotCallTime = progressUserVO.getRobotCallTime();
                            if (robotCallTime != null) {
                                atUser.setRobotCallTime(
                                        TimeUtils.getTimeString(TimeUtils.FULL_TIME, robotCallTime.getTime()));
                            } else {
                                atUser.setRobotCallTime(TimeUtils.getTimeString());
                            }
                            break;
                        }
                    }
                }
            }
            // event_progress_user的del_status是已删除状态，前端又没传这个用户的，需要保留数据
            for (ProgressUserVO progressUserVO : delStatusProgressUsers) {
                boolean exist = existEmerUser(emerUsers, progressUserVO.getUserId());
                if (!exist) {
                    ProgressUserDTO progressUserDTO = new ProgressUserDTO();
                    progressUserDTO.setUserId(progressUserVO.getUserId());
                    progressUserDTO.setUserName(progressUserVO.getUserName());
                    progressUserDTO.setMobile(progressUserVO.getMobile());
                    progressUserDTO.setCardPass(progressUserVO.getCardPass());
                    progressUserDTO.setSmsSendStatus(progressUserVO.getSmsSendStatus());
                    progressUserDTO.setSmsFailReason(progressUserVO.getSmsFailReason());
                    progressUserDTO.setBizId(progressUserVO.getBizId());
                    progressUserDTO.setJobId(progressUserVO.getJobId());
                    progressUserDTO.setReferenceId(progressUserVO.getReferenceId());
                    progressUserDTO.setRobotCallStatus(progressUserVO.getRobotCallStatus());
                    Timestamp robotCallTime = progressUserVO.getRobotCallTime();
                    if (robotCallTime != null) {
                        progressUserDTO.setRobotCallTime(
                                TimeUtils.getTimeString(TimeUtils.FULL_TIME, robotCallTime.getTime()));
                    } else {
                        progressUserDTO.setRobotCallTime(TimeUtils.getTimeString());
                    }
                    progressUserDTO.setDelStatus(1);
                    Timestamp sendTime = progressUserVO.getSendTime();
                    if (sendTime != null) {
                        progressUserDTO.setSendTime(TimeUtils.getTimeString(TimeUtils.FULL_TIME, sendTime.getTime()));
                    }
                    progressDTO.getAtUsers().add(progressUserDTO);
                }
            }
            progressService.addAtInitReportUser(progressDTO);
            // 处置人员有变化，事件重新关联应急人员
            addProgressDTO.setEventId(eventId);
            addProgressDTO.setCreateUserId(userId);
            addProgressDTO.setAtUsers(atUsers);
            progressService.add(addProgressDTO);

            // 发送初报短信通知到新增的处置人员
            EventDetailVO eventDetailVO = eventMapper.selectEmerRescueByEventId(eventIdDTO);
            String initReportProgressDesc = initReportProgressVO.getProgressDesc();
            String initReportTime = initReportProgressDesc.substring(initReportProgressDesc.indexOf("】") + 1,
                    initReportProgressDesc.indexOf("，"));
            if (StringUtils.isNotBlank(eventDetailVO.getReportTimeStr())) {
                initReportTime = eventDetailVO.getReportTimeStr();
            }
            String source = jointSource(eventDetailVO);
            String address = jointAddress(eventDetailVO);
            String content = jointInitReport(eventDetailVO);
            // UserSimpleVO loginUser = feignClient.selectByUserId(userId);
            // content += "——报送人：" + loginUser.getUserName() + "，";
            SmsTemplateDTO smsTemplateDTO = new SmsTemplateDTO();
            String today = "";
            String seqno = "";
            Long reportTime = eventDetailVO.getReportTime();
            if (eventDetailVO.getEventTwoType() == 5) { // 突发事件
            	smsTemplateDTO.setTemplateId(stringRedisTemplate.opsForValue().get("its-event:SmsTemplateCode-EMER_INIT_REPORT"));
            	today = TimeUtils.getTimeString(TimeUtils.DATE_7, reportTime * 1000);
            	Long todayStartTime = TimeUtils.toLong(TimeUtils.getTimeString(TimeUtils.DATE, reportTime * 1000) + " 00:00:00", TimeUtils.FULL_TIME);
            	int eventNum = eventMapper.selectTodayEmerSeqno(todayStartTime, reportTime);
            	if (eventNum + 1 < 10) {
            		seqno = "0" + (eventNum + 1);
            	} else {
            		seqno = "" + (eventNum + 1);
            	}
            } else {
            	smsTemplateDTO.setTemplateId(stringRedisTemplate.opsForValue().get("its-event:SmsTemplateCode-INIT_REPORT_4PART"));
            }
            for (UserDTO u : addSmsUsers) {
                ProgressUserDTO eventPorgressUserDTO = new ProgressUserDTO();
                boolean exits = false;
                for (ProgressUserVO tmp : initReportProgressUsers) {
                    if (tmp.getUserId().equals(u.getUserId())) {
                        if (StringUtils.isNotBlank(tmp.getMobile()) && StringUtils.isNotBlank(tmp.getBizId())) {
                            exits = true;
                        }
                        break;
                    }
                }
                String smsMobile = u.getMobile();
                if (!exits && StringUtils.length(smsMobile) == 11 && smsMobile.startsWith("1")) {
                    smsTemplateDTO.setMobile(smsMobile);
                    // 从已生成好的数据库表中获取url
                    Map<String, String> urlMap = getWeiXinUrlMap(new String[]{smsMobile});
                    String urlMsg = GenerateWeiXinUrlUtils.getUrlAndAssembleContent( urlMap, smsMobile);
                    // 组合报送内容
                    if (eventDetailVO.getEventTwoType() == 5) { // 突发事件
                    	smsTemplateDTO.setContent(smsTemplateDTO.createEmerInitReportContent(today, seqno, TimeUtils.getTimeString(TimeUtils.DATE_TIME_2, reportTime * 1000), address, content, source, urlMsg));
                    } else {
                    	smsTemplateDTO.setContent(smsTemplateDTO.createInitReportContent(TimeUtils.getTimeString(TimeUtils.DATE_TIME_2, reportTime * 1000), address, content, source, urlMsg));
                    }
                    boolean sendStatus = AliyunSmsUtils.sendAnSms(smsTemplateDTO);// 发送短信给新增的处置人员
                    String bizId = smsTemplateDTO.getBizId();
                    // 更新进展用户的短信发送状态为失败(0)
                    eventPorgressUserDTO.setEventProgressId(initReportProgressId);
                    eventPorgressUserDTO.setMobile(smsMobile);
                    eventPorgressUserDTO.setSmsSendStatus(sendStatus ? 2 : 0);
                    eventPorgressUserDTO.setSmsFailReason(sendStatus ? null : smsTemplateDTO.getFailReason());
                    eventPorgressUserDTO.setBizId(bizId);
                    eventPorgressUserDTO.setSendTime(TimeUtils.getTimeString());
                    eventPorgressUserDTO.setUserId(u.getUserId());
                    progressMapper.updateSmsSendStatus(eventPorgressUserDTO);
                }

            }
            // 将初报未拨打电话的人员，在变更人员处置时，被勾选电话标识的，也需要发起外呼;如果是上次操作已外呼过的，直接过滤掉
            List<UserDTO> resultList = robotCallByForceRemind(emerUsers, initReportProgressUsers);
            addEmerUsers.addAll(resultList);// resultList针对仅仅是变更了标识的人员，addEmerUsers针对的是新增的人员
            List<UserDTO> additionalList = filterCallList(initReportProgressUsers, addEmerUsers);
            if (CollectionUtils.isEmpty(additionalList)) {
                LOGGER.info("The robot call list is empty,should not to call.");
                return 1;
            }
            // 过滤需要打电话的
            List<UserDTO> callList = additionalList.stream().filter(item -> item.getForceRemind() == 1)
                    .collect(Collectors.toList());
            // 针对新增加的人员名单，增加智能外呼能力
            RobotCallToChangeUsers(eventId, initReportProgressVO, callList, eventDetailVO.getSourceId());
        }

        return 1;
    }


    /**
     * @描述 小程序自动分发前的预案匹配用户，第一次新增事件应急用户
     */
    public int addEmerUser(EventEmerUserDTO dto) {
    	// 短信提醒发送（smsRemind）
        List<EmerUserDTO> emerUsers = dto.getEmerUsers();/** 传入的参数 **/
        // 校验参数
        validEmerUser(emerUsers);
        return eventMapper.emerUser(dto);
    }

    /**
     * @描述 处置变更：小程序邀请人员
     */
    @Transactional
    public int addEmerUserAndSms(EventEmerUserDTO dto, String userId) {
    	// 短信提醒发送（smsRemind）
    	List<EmerUserDTO> emerUsers = dto.getEmerUsers();/** 前端传入的参数 **/
    	// 1、校验参数
    	validEmerUser(emerUsers);
    	
    	long serverTime = System.currentTimeMillis() / 1000;
    	UserSimpleVO loginUser = feignClient.selectByUserId(userId);
    	final String eventId = dto.getId();
    	IdStringDTO eventIdDTO = new IdStringDTO(eventId);
    	List<EventEmerUserVO> dbEventEmerUsers = eventMapper.selectEmerUserById(eventIdDTO);
    	
    	StringBuffer changedProgressDesc = new StringBuffer("");
    	
    	ProgressVO initReportProgressVO = progressService.selectInitReport(eventIdDTO);// 初报进展
    	Integer initReportProgressId = initReportProgressVO.getId();
    	IdIntegerDTO initReportIdDTO = new IdIntegerDTO(initReportProgressId);
    	final List<ProgressUserVO> initReportProgressUsers = progressMapper.selectProgressUser(initReportIdDTO);
    	
    	
    	// 2、计算出新增的用户，拼接进展留痕描述
    	List<UserDTO> addEmerUsers = new ArrayList<>();
    	List<UserDTO> addSmsUsers = new ArrayList<>();
    	String addUserMobiles = "";// 新增的处置人员的手机号，用英文逗号,隔开；例如18176270000,15994360000
    	List<String> addUserIds = new ArrayList<>();
    	boolean excuteOnce = true;
    	for (EmerUserDTO vo : emerUsers) {
    		for (UserDTO d : vo.getUsers()) {
    			EventEmerUserVO existEventEmerUser = existEventEmerUser(dbEventEmerUsers, d.getUserId());
    			boolean exist = existEventEmerUser == null ? false : true;
    			if (!exist) {
    				String tmpMobile = d.getMobile();
    				if (tmpMobile != null && tmpMobile.length() > 0 && addUserMobiles.indexOf(tmpMobile + ",") == -1) {
    					addUserMobiles = addUserMobiles + tmpMobile + ",";
    					addEmerUsers.add(d);
    					if(vo.getSmsRemind() == 1) {
    						addSmsUsers.add(d);
    					}
    				}
    				if (excuteOnce) {
    					changedProgressDesc.append("处置人员新增：");
    					excuteOnce = false;
    				}
    				if(!addUserIds.contains(d.getUserId())) {
    					addUserIds.add(d.getUserId());
    					changedProgressDesc.append(d.getUserName() + "，");
    				}
    			} else {
    				if(existEventEmerUser.getSmsRemind() == 0 && vo.getSmsRemind() == 1) {
    					addSmsUsers.add(d);
    				}
    			}
    			d.setForceRemind(vo.getForceRemind());
    			d.setSmsRemind(vo.getSmsRemind());
    		}
    	}
    	
    	if (changedProgressDesc.length() > 0) {
    		changedProgressDesc.replace(changedProgressDesc.length() - 1, changedProgressDesc.length(), "。");
    	}
    	
    	boolean change = false;
    	if (StringUtils.isNotBlank(changedProgressDesc.toString())) {
    		// 处置人员有变化，事件重新关联应急人员
    		change = true;
    		emerUserEventLog(dbEventEmerUsers, emerUsers, loginUser, eventId, serverTime, true);
    	} else {
    		return 1;// 无人员变化直接返回
    	}
    	
    	// 查询进展用户（确认过初报的）
    	List<ProgressUserVO> confirmUsers = progressService.selectConfirmIRUser(initReportIdDTO);// 查询确认初报的用户
    	
    	ProgressDTO addProgressDTO = new ProgressDTO();// 人员变化留痕进展
    	addProgressDTO.setCreateTime(serverTime);
    	addProgressDTO.setOccurTime(serverTime);
    	addProgressDTO.setProgressDesc(changedProgressDesc.toString());
    	addProgressDTO.setId(initReportProgressId);
    	
    	eventMapper.emerUser(dto);
    	
    	ProgressDTO progressDTO = new ProgressDTO();
    	List<ProgressUserDTO> atUsers = progressDTO.getAtUsers();
    	String atUserIds = new String(";");
    	for (EmerUserDTO tmp : emerUsers) {// emerUsers：前端传入的处置人员列表
    		for (UserDTO userDTO : tmp.getUsers()) {
    			String atUserId = userDTO.getUserId();
    			if (StringUtils.isBlank(atUserId)) {
    				continue;
    			}
    			if (atUserIds.contains(";" + atUserId + ";")) {
    				continue;
    			} else {
    				atUserIds += atUserId + ";";
    				ProgressUserDTO atUser = new ProgressUserDTO();
    				atUser.setEventProgressId(initReportProgressId);
    				atUser.setUserId(atUserId);
    				atUser.setUserName(userDTO.getUserName());
    				atUser.setMobile(userDTO.getMobile());
    				if (confirmUsers != null) {
    					for (ProgressUserVO tmpVO : confirmUsers) {
    						if (tmpVO.getUserId().equals(atUser.getUserId())) {
    							atUser.setCardPass(1);
    							break;
    						}
    					}
    				}
    				atUsers.add(atUser);
    			}
    		}
    	}
    	progressDTO.setId(initReportProgressId);
    	
    	if (change) {
    		for (ProgressUserDTO atUser : progressDTO.getAtUsers()) {
    			String mobile = atUser.getMobile();
    			if (StringUtils.isNotBlank(mobile)) {
    				for (ProgressUserVO progressUserVO : initReportProgressUsers) {
    					String dbMobile = progressUserVO.getMobile();
    					if (mobile.equals(dbMobile)) {
    						atUser.setBizId(progressUserVO.getBizId());
    						atUser.setSmsFailReason(progressUserVO.getSmsFailReason());
    						atUser.setSmsSendStatus(progressUserVO.getSmsSendStatus());
    						Timestamp sendTime = progressUserVO.getSendTime();
    						if (sendTime != null) {
    							atUser.setSendTime(TimeUtils.getTimeString(TimeUtils.FULL_TIME, sendTime.getTime()));
    						} else {
    							atUser.setSendTime(TimeUtils.getTimeString());
    						}
    						atUser.setJobId(progressUserVO.getJobId());
    						atUser.setReferenceId(progressUserVO.getReferenceId());
    						atUser.setRobotCallStatus(progressUserVO.getRobotCallStatus());
    						atUser.setRobotFailReason(progressUserVO.getRobotFailReason());
    						Timestamp robotCallTime = progressUserVO.getRobotCallTime();
    						if (robotCallTime != null) {
    							atUser.setRobotCallTime(TimeUtils.getTimeString(TimeUtils.FULL_TIME, robotCallTime.getTime()));
    						} else {
    							atUser.setRobotCallTime(TimeUtils.getTimeString());
    						}
    						break;
    					}
    				}
    			}
    		}
    		progressService.addAtInitReportUser(progressDTO);
    		// 处置人员有变化，事件重新关联应急人员
    		addProgressDTO.setEventId(eventId);
    		addProgressDTO.setCreateUserId(userId);
    		addProgressDTO.setAtUsers(atUsers);
    		progressService.add(addProgressDTO);
    		
    		// 发送初报短信通知到新增的处置人员
    		EventDetailVO eventDetailVO = eventMapper.selectEmerRescueByEventId(eventIdDTO);
    		String initReportProgressDesc = initReportProgressVO.getProgressDesc();
    		String initReportTime = initReportProgressDesc.substring(initReportProgressDesc.indexOf("】") + 1, initReportProgressDesc.indexOf("，"));
    		if (StringUtils.isNotBlank(eventDetailVO.getReportTimeStr())) {
    			initReportTime = eventDetailVO.getReportTimeStr();
    		}
    		String source = jointSource(eventDetailVO);
    		String address = jointAddress(eventDetailVO);
    		String content = jointInitReport(eventDetailVO);
    		SmsTemplateDTO smsTemplateDTO = new SmsTemplateDTO();
    		String today = "";
    		String seqno = "";
    		Long reportTime = eventDetailVO.getReportTime();
    		if (eventDetailVO.getEventTwoType() == 5) { // 突发事件
    			smsTemplateDTO.setTemplateId(stringRedisTemplate.opsForValue().get("its-event:SmsTemplateCode-EMER_INIT_REPORT"));
    			today = TimeUtils.getTimeString(TimeUtils.DATE_7, reportTime * 1000);
    			Long todayStartTime = TimeUtils.toLong(TimeUtils.getTimeString(TimeUtils.DATE, reportTime * 1000) + " 00:00:00", TimeUtils.FULL_TIME);
    			int eventNum = eventMapper.selectTodayEmerSeqno(todayStartTime, reportTime);
    			if (eventNum + 1 < 10) {
    				seqno = "0" + (eventNum + 1);
    			} else {
    				seqno = "" + (eventNum + 1);
    			}
    		} else {
    			smsTemplateDTO.setTemplateId(stringRedisTemplate.opsForValue().get("its-event:SmsTemplateCode-INIT_REPORT_4PART"));
    		}
    		for (UserDTO u : addSmsUsers) {
    			ProgressUserDTO eventPorgressUserDTO = new ProgressUserDTO();
    			boolean exits = false;
    			for (ProgressUserVO tmp : initReportProgressUsers) {
    				if (tmp.getUserId().equals(u.getUserId())) {
    					if (StringUtils.isNotBlank(tmp.getMobile()) && StringUtils.isNotBlank(tmp.getBizId())) {
    						exits = true;
    					}
    					break;
    				}
    			}
    			String smsMobile = u.getMobile();
    			if (!exits && StringUtils.length(smsMobile) == 11 && smsMobile.startsWith("1")) {
    				smsTemplateDTO.setMobile(smsMobile);
    				// 从已生成好的数据库表中获取url
    				Map<String, String> urlMap = getWeiXinUrlMap(new String[]{smsMobile});
    				String urlMsg = GenerateWeiXinUrlUtils.getUrlAndAssembleContent( urlMap, smsMobile);
    				// 组合报送内容
    				if (eventDetailVO.getEventTwoType() == 5) { // 突发事件
    					smsTemplateDTO.setContent(smsTemplateDTO.createEmerInitReportContent(today, seqno, TimeUtils.getTimeString(TimeUtils.DATE_TIME_2, reportTime * 1000), address, content, source, urlMsg));
    				} else {
    					smsTemplateDTO.setContent(smsTemplateDTO.createInitReportContent(TimeUtils.getTimeString(TimeUtils.DATE_TIME_2, reportTime * 1000), address, content, source, urlMsg));
    				}
    				boolean sendStatus = AliyunSmsUtils.sendAnSms(smsTemplateDTO);// 发送短信给新增的处置人员
    				String bizId = smsTemplateDTO.getBizId();
    				// 更新进展用户的短信发送状态为失败(0)
    				eventPorgressUserDTO.setEventProgressId(initReportProgressId);
    				eventPorgressUserDTO.setMobile(smsMobile);
    				eventPorgressUserDTO.setSmsSendStatus(sendStatus ? 2 : 0);
    				eventPorgressUserDTO.setSmsFailReason(sendStatus ? null : smsTemplateDTO.getFailReason());
    				eventPorgressUserDTO.setBizId(bizId);
    				eventPorgressUserDTO.setSendTime(TimeUtils.getTimeString());
    				eventPorgressUserDTO.setUserId(u.getUserId());
    				progressMapper.updateSmsSendStatus(eventPorgressUserDTO);
    			}
    			
    		}
    	}
    	
    	return 1;
    }

    /**
     * @description 变更人员的事件日志
     * @param dbEventEmerUsers
     * @param emerUsers
     * @param loginUser
     * @param eventId
     * @param serverTime
     */
	private void emerUserEventLog(List<EventEmerUserVO> dbEventEmerUsers, List<EmerUserDTO> emerUsers, UserSimpleVO loginUser, String eventId, Long serverTime, boolean isAdd) {
        String beforeChange = "";
        for (int i = 0; i < dbEventEmerUsers.size(); i++) {
        	EventEmerUserVO eventEmerUser = dbEventEmerUsers.get(i);
        	if (StringUtils.isBlank(eventEmerUser.getMobile())) {
        		beforeChange += eventEmerUser.getUserName();
        	} else {
        		beforeChange += eventEmerUser.getUserName() + "（" + eventEmerUser.getMobile() + "）";
        	}
        	if (i < dbEventEmerUsers.size() - 1) {
        		beforeChange += "、、";
        	}
		}
        String afterChange = "";
        for (int i = 0; i < emerUsers.size(); i++) {
        	EmerUserDTO emerUser = emerUsers.get(i);
        	List<UserDTO> users = emerUser.getUsers();
        	if (CollectionUtils.isEmpty(users)) {
        		continue;
        	}
        	for (UserDTO user : users) {
        		if (StringUtils.isBlank(user.getMobile())) {
        			afterChange += user.getUserName() + "、、";
            	} else {
            		afterChange += user.getUserName() + "（" + user.getMobile() + "）、、";
            	}
			}
        }
        if (isAdd) {
        	afterChange += beforeChange;
        }
        // 当前登录用户的信息
    	String operator = loginUser.getOrgName() + "-" + loginUser.getUserName();
    	EventLogDTO eventLog = new EventLogDTO();
    	eventLog.setInfoType("处置信息");
    	eventLog.setBeforeChange(beforeChange);
    	eventLog.setAfterChange(afterChange.substring(0, afterChange.length()-2));
    	eventLog.setEventId(eventId);
    	eventLog.setCreateTime(TimeUtils.getTimeString(TimeUtils.FULL_TIME, serverTime * 1000));
    	eventLog.setOperator(operator);
    	eventLogMapper.add(eventLog);
	} 

    private static List<UserDTO> filterCallList(List<ProgressUserVO> initReportProgressUsers,
                                                List<UserDTO> addEmerUsers) {
        List<UserDTO> additionalList = new ArrayList<>();
        for (UserDTO addUser : addEmerUsers) {
            boolean exist = false;
            for (ProgressUserVO progressUser : initReportProgressUsers) {
                String progressUserUserId = progressUser.getUserId();
                String progressUserMobile = progressUser.getMobile();
                if (StringUtils.isEmpty(progressUserUserId) || StringUtils.isEmpty(progressUserMobile)) {
                    continue; // 忽略无user或者无电话的人员
                }
                // 已发短信未拨打过的
                if (progressUserUserId.equals(addUser.getUserId()) && progressUserMobile.equals(addUser.getMobile())) {
                    if (progressUser.getReferenceId() == null) {
                        additionalList.add(addUser);
                    }
                    exist = true;
                }
            }
            // 找不到对应记录，说明属于新增的，即未发短信+未打电话的
            if (!exist) {
                additionalList.add(addUser);
            }
        }
        return additionalList;
    }

    private List<UserDTO> robotCallByForceRemind(List<EmerUserDTO> emerUsers,
                                                 List<ProgressUserVO> initReportProgressUsers) {
        List<UserDTO> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(emerUsers)) {
            return resultList;
        }
        for (EmerUserDTO emerUser : emerUsers) {
            Integer forceRemind = emerUser.getForceRemind();
            if (forceRemind == null || forceRemind != 1) {
                continue;
            }
            // 标记需要拨打电话且之前未拨打过电话的
            List<UserDTO> users = emerUser.getUsers();
            for (UserDTO user : users) {
                // 如果无任何电话记录的，全部拨打
                if (CollectionUtils.isEmpty(initReportProgressUsers)) {
                    resultList.add(user);
                    continue;
                }
                for (ProgressUserVO progressUser : initReportProgressUsers) {
                    String progressUserUserId = progressUser.getUserId();
                    String progressUserMobile = progressUser.getMobile();
                    if (StringUtils.isEmpty(progressUserUserId) || StringUtils.isEmpty(progressUserMobile)) {
                        continue; // 忽略无user或者无电话的人员
                    }
                    // 未拨打过的
                    if (progressUserUserId.equals(user.getUserId()) && progressUserMobile.equals(user.getMobile())
                            && progressUser.getReferenceId() == null) {
                        resultList.add(user);
                    }
                }
            }
        }
        return resultList;
    }

    private void RobotCallToChangeUsers(String eventId, ProgressVO initReportProgressVO, List<UserDTO> callList,
                                        Integer sourceId) {
        if (StringUtils.isEmpty(eventId) || CollectionUtils.isEmpty(callList)) {
            LOGGER.warn("The event is not exist or the add user is none.");
            return;
        }
        // 取消报送人播报
        // String desc = initReportProgressVO.getProgressDesc() + "——报送人：" +
        // initPortUser.getUserName() + "，";
        LOGGER.info("报送内容：{}", initReportProgressVO.getProgressDesc());
        RobotMobileDTO mobileDTO = new RobotMobileDTO();
        mobileDTO.setBriefDesc(initReportProgressVO.getProgressDesc());
        mobileDTO.setId(eventId);
        mobileDTO.setSourceId(sourceId);
        mobileDTO.setSourceName(stringRedisTemplate.opsForValue().get("its-event:robotCallScene-"+sourceId));
        mobileDTO.setUsers(callList);
        AssignJobs.robotMobile(mobileDTO, true);
        List<ProgressUserDTO> progressUserDTOList = new ArrayList<>();
        callList.forEach(addUser -> {
            if (addUser.getForceRemind() == 1) {
                ProgressUserDTO puDto = new ProgressUserDTO();
                puDto.setMobile(addUser.getMobile());
                puDto.setUserId(addUser.getUserId());
                progressUserDTOList.add(puDto);
            }
        });
        updateUserPhoneReferenceId(progressUserDTOList);
        progressUserDTOList.forEach(userDTO -> {
            ProgressUserVO progressUserVO = new ProgressUserVO();
            progressUserVO.setEventProgressId(initReportProgressVO.getId());
            progressUserVO.setUserId(userDTO.getUserId());
            progressUserVO.setReferenceId(userDTO.getReferenceId());
            progressUserVO.setMobile(userDTO.getMobile());
            progressMapper.updateByIdAndUserAndMobile(progressUserVO);
        });
    }

    private List<ProgressUserDTO> updateUserPhoneReferenceId(List<ProgressUserDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            LOGGER.error("The phone reference list is empty.");
            return list;
        }
        // 将标识更新到参数中
        Map<String, String> referenceIdMap = RobotCallTmpResultContext.obtainReferenceIdMap();
        list.forEach(item -> item.setReferenceId(referenceIdMap.get(item.getMobile())));
        return list;
    }

    public EventCountVO countEvent(EventCountDTO eventCountDTO) {
        EventCountVO vo = eventMapper.countEvent(eventCountDTO);
        if (vo != null) {
            vo.setTotalAll(vo.getFinish() + vo.getInProgress() + vo.getUnSure());
        }
        return vo;
    }

    public EventCountByOrgVO countEventByOrg(EventCountDTO eventCountDTO) {
        EventCountByOrgVO vo = eventMapper.countEventByOrg(eventCountDTO);
        if (vo != null) {
            vo.setOther(vo.getTotalAll() - vo.getConsult() - vo.getTraffic() - vo.getRescue());
        }
        return vo;
    }

    public Map<String, Object> countEventTypeByOrg(EventCountDTO eventCountDTO) {

        return eventMapper.countEventTypeByOrg(eventCountDTO);
    }

    public Map<String, Object> countEventRoadByOrg(EventCountDTO eventCountDTO) {
        return eventMapper.countEventRoadByOrg(eventCountDTO);
    }

    @Deprecated
    /** 通过账号密码爬取96333工单方法的方式，新增96333工单 **/
    public boolean add96333Rescue(Event96333DTO dto) {
        String eventNo = dto.getEventNo();
        Integer newSourceId = dto.getSourceId();
        if (newSourceId == null) {
            LOGGER.error("96333的sourceId不能为空");
            return false;
        }
        EventDetailVO eventVO = eventMapper.selectBaseInfoByEventNo(new IdStringDTO(eventNo));
        if (eventVO != null) {
            Integer sourceId = eventVO.getSourceId();
            if (sourceId == null || newSourceId != sourceId) {
                // 删除云控数据库中的工单；
                Map<String, Object> map = new HashMap<>();
                map.put("eventNo", "del-" + eventNo);
                map.put("id", eventVO.getId());
                eventMapper.deleteAndUpdateEventNo(map);
            } else {
                // 该事件已存在，更新数据库（dealStatus和dealResult）
                return eventMapper.update96333EmerRescue(dto) > 0;
            }
        }

        if (dto.getId() == null) {
            dto.setId(UUID.randomUUID().toString());
        }
        if (newSourceId >= 1) {
            dto.setOrgId(OrgSourceIdEnum.getName(newSourceId));
            dto.setDealOrgName(OrgSourceIdEnum.getShortName(newSourceId));
        }

        // 上报时间=工单生成时间
        String yyyyMMddHHmmss = eventNo.replace("DD", "").replace("TS", "").replace("JY", "");
        long reportTime = TimeUtils.toLong(yyyyMMddHHmmss, "yyyyMMddHHmmss");
        dto.setReportTime(reportTime);
        dto.setOrderCreateTime(reportTime);
        // 校验milePost桩号
        String milePost = dto.getMilePost();
        dto.setGgjMilePost(milePost);
        if (StringUtils.isNotBlank(milePost)) {
            milePost = milePost.replace("K", "").replace("k", "");
            if (milePost.contains("+")) {
                String tmpMilePost = milePost.replace("+", "");
                if (NumberUtils.toInt(tmpMilePost, -1) == -1) {
                    dto.setMilePost(null);
                } else {
                    dto.setMilePost("K" + milePost);
                }
            } else {
                if (NumberUtils.toInt(milePost, -1) == -1) {
                    dto.setMilePost(null);
                } else {
                    dto.setMilePost("K" + milePost);
                }
            }
        }

        boolean ret = eventMapper.add96333Rescue(dto) > 0;
        if (ret) {
            // 电话播报（云外呼智能机器人）
            String timeString = TimeUtils.getTimeString(TimeUtils.DATE_TIME_6, reportTime * 1000);
            String eventType96333 = "";
            if (eventNo.startsWith("DD")) {
                eventType96333 = "救援";
            } else if (eventNo.startsWith("TS")) {
                eventType96333 = "投诉";
            } else if (eventNo.startsWith("JY")) {
                eventType96333 = "建议";
            }
            // 罗城中心，有新的96333事件
            Integer sourceId = dto.getSourceId();
            if (StringUtils.isNotBlank(event96333XfzMobile) && sourceId != null
                    && (sourceId == 2 || sourceId == 3 || sourceId == 4 || sourceId == 5 || sourceId == 6)) {// 根据需求要求，临时电话只通知大化、罗城、灵山、来宾的工单
                String name = SourceIdEnum.getShortName(sourceId);
                String content = timeString + "，" + name + "有新的" + eventType96333 + "工单需要处理。";
                RobotMobileDTO robotMobileDTO = new RobotMobileDTO();
                robotMobileDTO.setId(dto.getId());
                robotMobileDTO.setSourceId(sourceId);
                robotMobileDTO.setSourceName(stringRedisTemplate.opsForValue().get("its-event:robotCallScene-"+sourceId));
                List<UserDTO> robotMobileUsers = new ArrayList<>();
                String[] mobiles = event96333XfzMobile.split(",");
                for (String mobile : mobiles) {
                    UserDTO userDTO = new UserDTO();
                    userDTO.setMobile(mobile);
                    userDTO.setUserId(mobile);
                    userDTO.setUserName(mobile);
                    robotMobileUsers.add(userDTO);
                }
                robotMobileDTO.setUsers(robotMobileUsers);
                robotMobileDTO.setBriefDesc(content);

                // 外呼智能机器人播报// 语音播报强提醒
                AssignJobs.robotMobile(robotMobileDTO, false);
            }

            new Thread(new Runnable() {
                @Override
                public void run() {
                    EventMessageDTO eventMessageDTO = new EventMessageDTO();
                    eventMessageDTO.setWebsocketType("addEvent");
                    eventMessageDTO.setId(dto.getId());
                    eventMessageDTO.setSource(1);
                    eventMessageDTO.setEventType(dto.getEventType());
                    eventMessageDTO.setEventTwoType(dto.getEventTwoType());
                    eventMessageDTO.setEventThreeType(dto.getEventThreeType());
                    eventMessageDTO.setBriefDesc(dto.getBriefDesc());
                    eventMessageDTO.setDealStatus(dto.getDealStatus());
                    String name = SourceIdEnum.getName(sourceId);
                    if (name != null) {
                        name = name + "中心，";
                    } else {
                        name = "";
                    }
                    eventMessageDTO.setVoicePrompt(name + "有新的96333事件");
                    eventMessageDTO.setOrgId(dto.getOrgId());
                    itsWebSocketFeignClient.push96333Event(eventMessageDTO);
                }
            }).start();
        }
        return ret;
    }

    /**
     * 与千方对接工单的方式，新增96333工单
     **/
    public boolean addEvent96333Order(Event96333NewOrderDTO event96333NewOrderDTO) {
        LOGGER.info(new Gson().toJson(event96333NewOrderDTO));
        Integer newSourceId = event96333NewOrderDTO.getSourceId();
        if (newSourceId == null || newSourceId == 0) {
        	LOGGER.error("96333的sourceId不能为空");
            return false;
        } 
        long serverTime = System.currentTimeMillis();
        event96333NewOrderDTO.setComplaintType(event96333NewOrderDTO.getReasonAble());
        String eventNo = event96333NewOrderDTO.getRecordId();
        EventDetailVO eventVO = eventMapper.selectBaseInfoByEventNo(new IdStringDTO(eventNo));
        if (eventVO != null) {
            Integer sourceId = eventVO.getSourceId();
            if (sourceId == null || newSourceId != sourceId) {
                // 删除云控数据库中的工单；
                Map<String, Object> map = new HashMap<>();
                map.put("eventNo", "del-" + eventNo + serverTime);
                map.put("id", eventVO.getId());
                eventMapper.deleteAndUpdateEventNo(map);
            } else {
                // 该事件已存在，更新数据库（dealResult）
                event96333NewOrderDTO.setId(eventVO.getId());
                return eventMapper.updateEvent96333Order(event96333NewOrderDTO) > 0;
            }
        }

        Event96333DTO dto = new Event96333DTO();
        String eventId = dto.getId();
        if (eventId == null) {
            eventId = UUID.randomUUID().toString();
            dto.setId(eventId);
        }
        dto.setSource(1);
        dto.setEventNo(eventNo);
        String serviceType = event96333NewOrderDTO.getServiceType();
        String caseType = event96333NewOrderDTO.getCaseType();
        if (ServiceTypeEnum.DD.getValue().equals(serviceType)) {// 救援
            dto.setEventType(1);
            dto.setEventTwoType(6);
            dto.setEventThreeType(11);
        } else if (ServiceTypeEnum.TS.getValue().equals(serviceType)) {// 投诉
            dto.setEventType(2);
            dto.setEventTwoType(13);
            dto.setEventThreeType(15);
            if ("0202".equals(caseType)) {// 举报
                dto.setEventTwoType(14);
                dto.setEventThreeType(16);
            }
        } else if (ServiceTypeEnum.JY.getValue().equals(serviceType)) {// 建议
            dto.setEventType(3);
            dto.setEventTwoType(21);
            dto.setEventThreeType(22);
            if ("0301".equals(caseType)) {// 意见
                dto.setEventTwoType(19);
                dto.setEventThreeType(20);
            }
        } else if (ServiceTypeEnum.ZX.getValue().equals(serviceType)) {// 咨询
            dto.setEventType(4);
            dto.setEventTwoType(17);
            dto.setEventThreeType(18);
        }
        dto.setCarType(NumberUtils.toInt(event96333NewOrderDTO.getCarType()));
        dto.setCarPlate(event96333NewOrderDTO.getCarno());
        dto.setDealStatus(2);
        dto.setEventStatus(1);
        dto.setBriefDesc(event96333NewOrderDTO.getContent());
        dto.setSourceId(newSourceId);
        if (newSourceId >= 1) {
            dto.setOrgId(OrgSourceIdEnum.getName(newSourceId));
            dto.setDealOrgId(OrgSourceIdEnum.getName(newSourceId));
            dto.setDealOrgName(OrgSourceIdEnum.getShortName(newSourceId));
        }

        dto.setComplaintType(event96333NewOrderDTO.getComplaintType());
        dto.setComplaintTarget(event96333NewOrderDTO.getTopic());
        // 上报时间=工单生成时间
        String yyyyMMddHHmmss = eventNo.replace("DD", "").replace("TS", "").replace("JY", "").replace("ZX", "");
        if (yyyyMMddHHmmss.length() >= 14) {
        	yyyyMMddHHmmss = yyyyMMddHHmmss.substring(0, 14);
        } else {
        	yyyyMMddHHmmss = TimeUtils.getTimeString("yyyyMMddHHmmss");
        }
        long reportTime = TimeUtils.toLong(yyyyMMddHHmmss, "yyyyMMddHHmmss");
        dto.setReportTime(reportTime);
        dto.setOrderCreateTime(reportTime);
        dto.setGgjRoadNo(event96333NewOrderDTO.getRoadSection());
        // 校验milePost桩号
        String milePost = event96333NewOrderDTO.getPilecode();
        dto.setGgjMilePost(milePost);
        dto.setEventAddress(event96333NewOrderDTO.getEventlocation());
        dto.setReportMan(event96333NewOrderDTO.getNameOut());
        dto.setReportManTel(event96333NewOrderDTO.getPhoneOut());
        if (StringUtils.isNotBlank(milePost)) {
            milePost = milePost.replace("K", "").replace("k", "");
            if (milePost.contains("+")) {
                String tmpMilePost = milePost.replace("+", "");
                if (NumberUtils.toInt(tmpMilePost, -1) == -1) {
                    dto.setMilePost(null);
                } else {
                    dto.setMilePost("K" + milePost);
                }
            } else {
                if (NumberUtils.toInt(milePost, -1) == -1) {
                    dto.setMilePost(null);
                } else {
                    dto.setMilePost("K" + milePost);
                }
            }
        }
        dto.setCreateTime(serverTime / 1000);
        dto.setBusiId(event96333NewOrderDTO.getBusiId());
        dto.setDealResult(event96333NewOrderDTO.getReplayContent());

        String busiFieldName = event96333NewOrderDTO.getBusiFieldName();
        String businessType = busiFieldName2BusinessType(serviceType, busiFieldName);
        dto.setBusinessType(businessType);

        boolean ret = eventMapper.add96333Rescue(dto) > 0;

        if (ret) {
            // 咨询工单类型，需要新增咨询扩展表数据
            Integer eventType = dto.getEventType();
            if (eventType != null && eventType == 4) {
                EventZxDTO eventZxDTO = new EventZxDTO();
                eventZxDTO.setZxId(UUID.randomUUID().toString());
                eventZxDTO.setOrg1Id(dto.getOrgId());
                eventZxDTO.setId(eventId);
                eventMapper.addEventZx(eventZxDTO);
            }
            
            Integer sourceId = dto.getSourceId();

            // 电话播报（云外呼智能机器人）
            String timeString = TimeUtils.getTimeString(TimeUtils.DATE_TIME_6, reportTime * 1000);
            new96333EventRobotMobile(eventId, eventNo, timeString, sourceId);

            new Thread(new Runnable() {
                @Override
                public void run() {
                    EventMessageDTO eventMessageDTO = new EventMessageDTO();
                    eventMessageDTO.setWebsocketType("addEvent");
                    eventMessageDTO.setId(dto.getId());
                    eventMessageDTO.setSource(1);
                    eventMessageDTO.setEventType(dto.getEventType());
                    eventMessageDTO.setEventTwoType(dto.getEventTwoType());
                    eventMessageDTO.setEventThreeType(dto.getEventThreeType());
                    eventMessageDTO.setBriefDesc(dto.getBriefDesc());
                    eventMessageDTO.setDealStatus(dto.getDealStatus());
                    String name = SourceIdEnum.getShortName(sourceId);
                    if (name == null) {
                        name = "";
                    }
                    eventMessageDTO.setVoicePrompt(name + "有新的96333事件");
                    eventMessageDTO.setOrgId(dto.getOrgId());
                    itsWebSocketFeignClient.push96333Event(eventMessageDTO);
                }
            }).start();
        }

        return ret;
    }

    private void new96333EventRobotMobile(String eventId, String eventNo, String timeString, Integer sourceId) {
    	if (StringUtils.isBlank(event96333XfzMobile) && StringUtils.isBlank(event96333YhMobile)) {
       	 LOGGER.warn("通话强提醒需要配置新发展客服电话或沿海客服电话，eventNo为{}", eventNo);
       	 return;
        }
    	 String eventType96333 = "";
         if (eventNo.startsWith("DD")) {
             eventType96333 = "救援";
         } else if (eventNo.startsWith("TS")) {
             eventType96333 = "投诉";
         } else if (eventNo.startsWith("JY")) {
             eventType96333 = "建议";
         } else if (eventNo.startsWith("ZX")) {
             eventType96333 = "咨询";
         }
         if (sourceId == null) {
        	 LOGGER.warn("新的96333事件数据有问题，sourceId为null,eventNo为{}", eventNo);
        	 return;
         }
         if ("咨询".equals(eventType96333)) {
        	 LOGGER.info("新的96333咨询事件不需要电话通知{}", eventNo);
        	 return;
         }
         String name = SourceIdEnum.getShortName(sourceId);
         if (name == null) {
        	 LOGGER.info("新的96333事件sourceId有问题：{}, sourceId{}", eventNo);
        	 return ;
         }
         String content = timeString + "，" + name + "有新的" + eventType96333 + "工单需要处理。";
         RobotMobileDTO robotMobileDTO = new RobotMobileDTO();
         robotMobileDTO.setId(eventId);
         robotMobileDTO.setSourceId(sourceId);
         robotMobileDTO.setSourceName(stringRedisTemplate.opsForValue().get("its-event:robotCallScene-"+sourceId));
         List<UserDTO> robotMobileUsers = new ArrayList<>();
         String event96333Mobile = event96333XfzMobile;
         if (sourceId.intValue() == 7 || sourceId.intValue() == 8 || sourceId.intValue() == 9) {
        	 event96333Mobile = event96333YhMobile;
         }
         String[] mobiles = event96333Mobile.split(",");
         // demo：罗城中心，有新的救援工单需要处理。
         for (String mobile : mobiles) {
             UserDTO userDTO = new UserDTO();
             userDTO.setMobile(mobile);
             userDTO.setUserId(mobile);
             userDTO.setUserName(mobile);
             robotMobileUsers.add(userDTO);
         }
         robotMobileDTO.setUsers(robotMobileUsers);
         robotMobileDTO.setBriefDesc(content);
         // 外呼智能机器人播报// 语音播报强提醒
         AssignJobs.robotMobile(robotMobileDTO, false);
//         if (sourceId == 2 || sourceId == 3 || sourceId == 4 || sourceId == 5 || sourceId == 6) {// 根据需求要求，新发展的电话强提醒只通知罗城、大化、昭平、灵山、来宾的工单
//         }
    }
    

    private String busiFieldName2BusinessType(String serviceType, String busiFieldName) {
        if (StringUtils.isNotBlank(busiFieldName)) {
        	busiFieldName = busiFieldName.replaceAll("->", "-＞").replaceAll("-\\\\u003e", "-＞");
            if (ServiceTypeEnum.DD.getValue().equals(serviceType)) {
                if (busiFieldName.contains("-＞路障清理")) {
                    if (busiFieldName.contains("-＞路障清理-＞故障车辆")) {
                        return "2001";
                    } else if (busiFieldName.contains("-＞路障清理-＞非机动车辆")) {
                        return "2005";
                    } else if (busiFieldName.contains("-＞路障清理-＞行人")) {
                        return "2006";
                    } else {
                        return "2004";// "-＞路障清理-＞其他"
                    }
                } else if (busiFieldName.contains("-＞交通运输事故")) {
                    return "2002";
                }
            } else {
                if (busiFieldName.contains("-＞收费-＞")) {
                    return "1001";
                } else if (busiFieldName.contains("-＞工程建设-＞")) {
                    return "1002";
                } else if (busiFieldName.contains("-＞路网服务-＞")) {
                    return "1003";
                } else if (busiFieldName.contains("-＞规划立项-＞")) {
                    return "1004";
                } else if (busiFieldName.contains("-＞养护保通-＞")) {
                    return "1005";
                } else if (busiFieldName.contains("-＞路政执法-＞")) {
                    return "1006";
                } else if (busiFieldName.contains("-＞其他-＞")) {
                    return "1007";
                }
            }
        }
        return null;
    }

    public void notifyAddWebSoket(EmerRescueDTO dto) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                EventMessageDTO eventMessageDTO = new EventMessageDTO();
                eventMessageDTO.setWebsocketType("addEvent");
                eventMessageDTO.setId(dto.getId());
                eventMessageDTO.setSource(dto.getSource());
                eventMessageDTO.setEventType(dto.getEventType());
                eventMessageDTO.setEventTwoType(dto.getEventTwoType());
                eventMessageDTO.setEventThreeType(dto.getEventThreeType());
                eventMessageDTO.setBriefDesc(dto.getBriefDesc());
                eventMessageDTO.setOrgId(dto.getOrgId());
                eventMessageDTO.setDealStatus(dto.getDealStatus());

                // 罗城中心，有新的96333事件
                Integer sourceId = dto.getSourceId();
                String name = SourceIdEnum.getShortName(sourceId);
                if (name != null) {
                    name = name + "，";
                    if (sourceId == 1) {
                        String orgId = dto.getOrgId();
                        name = name + OrganShortNameEnum.getName(orgId) + "，";
                    }
                } else {
                    name = "";
                }
                eventMessageDTO.setVoicePrompt(name + "有小程序上报的事件");
                itsWebSocketFeignClient.push96333Event(eventMessageDTO);
            }
        }).start();
    }

    public List<EventTypeVO> tsJyType() {
        return eventMapper.selectJyType();
    }

    public PageInfo<TsJyVO> pageTsJy(PageDTO pageDTO, EventQueryDTO dto) {
        UserSimpleVO loginUser = feignClient.selectByUserId(dto.getUserId());
        dto.setCompanyId(loginUser.getCompanyId());
        PageHelper.startPage(pageDTO.getPage(), pageDTO.getLimit());
        List<TsJyVO> list = eventMapper.selectTsJy(dto);
        PageInfo<TsJyVO> pageInfo = new PageInfo<>(list);
        List<TsJyVO> ret = pageInfo.getList();
        if (!CollectionUtils.isEmpty(ret)) {
            long serverTime = System.currentTimeMillis() / 1000;
            for (TsJyVO tsJyVO : ret) {
                tsJyVO.setReportSource(EventReportSourceEnum.getSourceStrByValue(tsJyVO.getReportSourceKey()));
                tsJyVO.setServerTime(serverTime);
                Integer step = tsJyVO.getStep() == null ? 1 : tsJyVO.getStep();
                if (step == 1 || step == 3) {
                    tsJyVO.setDealOrgName("客服中心");
                } else { //step2
                    if (tsJyVO.getUser2Id() != null) {
                        UserSimpleVO user2 = feignClient.selectByUserId(tsJyVO.getUser2Id());
                        String org2company = user2.getCompanyName();
                        if (org2company != null) {
                            tsJyVO.setDealOrgName(org2company + '-' + tsJyVO.getDealOrgName()); //业务部门调查：公司-组织
                        }
                    }
                }
            }
        }

        return pageInfo;
    }

    public TsJyDetailVO selectTsJyByEventId(IdStringDTO dto) {
        TsJyDetailVO vo = eventMapper.selectTsJyByEventId(dto);
        if (vo.getUser1Id() != null) {
            UserSimpleVO user1 = feignClient.selectByUserId(vo.getUser1Id());
            vo.setUser1Name(user1.getUserName());
            vo.setOrg1Name(user1.getOrgName());
            vo.setCompany1Name(user1.getCompanyName());
        }
        if (vo.getUser2Id() != null) {
            UserSimpleVO user2 = feignClient.selectByUserId(vo.getUser2Id());
            vo.setUser2Name(user2.getUserName());
            vo.setOrg2Name(user2.getOrgName());
            vo.setCompany2Name(user2.getCompanyName());
        }
        if (vo.getUser3Id() != null) {
            UserSimpleVO user3 = feignClient.selectByUserId(vo.getUser3Id());
            vo.setUser3Name(user3.getUserName());
            vo.setOrg3Name(user3.getOrgName());
            vo.setCompany3Name(user3.getCompanyName());
        }
        if (vo.getUser4Id() != null) {
            UserSimpleVO user4 = feignClient.selectByUserId(vo.getUser4Id());
            vo.setUser4Name(user4.getUserName());
            vo.setOrg4Name(user4.getOrgName());
            vo.setCompany4Name(user4.getCompanyName());
        }
        vo.setCompanyName(vo.getOrgName());

        String reportManTel = vo.getReportManTel();
        if (reportManTel != null) {
            vo.setReportManTel(reportManTel.trim());
        }

        return vo;
    }

    @Transactional
    public ResponseVO dealTsStepOne(EventTsDTO dto) {
        EventAuthorityDTO aDTO = new EventAuthorityDTO();
        aDTO.setUserId(dto.getUser1Id());
        aDTO.setId(dto.getId());
        aDTO.setRoleIds(dto.getRoleIds());

        int viewAuthorityById = eventMapper.viewAuthorityById(aDTO);

        TsJyDetailVO vo = eventMapper.selectTsJyByEventId(new IdStringDTO(dto.getId()));
        UserSimpleVO user1 = feignClient.selectByUserId(dto.getUser1Id());

        String loginUserCompanyId = user1.getCompanyId();
        if (viewAuthorityById == 0) {
            throw new ArgumentException("您没有该事件权限");
        }

        // 判断该事件是否是意见建议、是否含有表扬
        boolean isPraise = false;
        {
            EventDetailVO eventDetailVO = eventMapper.selectBaseInfoByEventId(new IdStringDTO(dto.getId()));
            // 20，22为意见建议
            if (eventDetailVO != null
                    && (eventDetailVO.getEventThreeType() == 20 || eventDetailVO.getEventThreeType() == 22)) {
                if (eventDetailVO.getBriefDesc().contains("表扬")) {
                    isPraise = true;
                }
            }
        }

        // 保存event_ts的kf_advice, user1_id, user2_id, step 和
        // event的step,deal_org_id,deal_org_name
        // 根据user2_id查询部门
        UserSimpleVO user2 = feignClient.selectByUserId(dto.getUser2Id());
        dto.setDealOrgId(user2.getOrgId());
        dto.setDealOrgName(user2.getOrgName());
        dto.setCompany2Id(user2.getCompanyId());
        if (isPraise) {
            dto.setIsPraise(1);
        }
        if (StringUtils.isBlank(dto.getEtId())) {
            String etId = UUID.randomUUID().toString();
            dto.setEtId(etId);
            dto.setStep(2);
            if (dto.getUser1Id() != null) {
                dto.setCompany1Id(loginUserCompanyId);
            }
            // 新增event_ts
            eventMapper.addEventTs(dto);
        } else {
            if (vo.getStep() > 2) {
                throw new ArgumentException("业务部门调查已结束，不能修改客服处置阶段。");
            }
            dto.setStep(null);
            dto.setOrg2Name(vo.getDealOrgName());
            // 更新event_ts
            eventMapper.updateEventTs(dto);
        }
        int ret = eventMapper.updateTsStep(dto);// event的step,deal_org_id,deal_org_name,complaint_type
        return new ResponseVO(ret);
    }

    @Transactional
    public ResponseVO dealTsStepTwo(EventTsDTO dto) {
        EventAuthorityDTO aDTO = new EventAuthorityDTO();
        aDTO.setUserId(dto.getUser3Id());
        aDTO.setId(dto.getId());
        aDTO.setRoleIds(dto.getRoleIds());

        int viewAuthorityById = eventMapper.viewAuthorityById(aDTO);
        TsJyDetailVO vo = eventMapper.selectTsJyByEventId(new IdStringDTO(dto.getId()));
        if (viewAuthorityById == 0) {
            throw new ArgumentException("您没有该事件权限");
        }
        // user1_id
        // 根据user1_id查询部门
        UserSimpleVO user1 = feignClient.selectByUserId(vo.getUser1Id());
        dto.setDealOrgId(user1.getOrgId());
        dto.setDealOrgName(user1.getOrgName());
        dto.setOrg2Name(vo.getDealOrgName());
        if (vo.getStep() != null && vo.getStep() < 3) {
            dto.setStep(3);
        }
        // 更新event_ts
        eventMapper.updateEventTs(dto);
        int ret = eventMapper.updateTsStep(dto);// event的step,deal_org_id,deal_org_name,complaint_type
        return new ResponseVO(ret);
    }

    @Transactional
    public ResponseVO dealTsStepThree(EventTsDTO dto) {
        EventAuthorityDTO aDTO = new EventAuthorityDTO();
        aDTO.setUserId(dto.getUser4Id());
        aDTO.setId(dto.getId());
        aDTO.setRoleIds(dto.getRoleIds());

        int viewAuthorityById = eventMapper.viewAuthorityById(aDTO);
        if (viewAuthorityById == 0) {
            throw new ArgumentException("您没有该事件权限");
        }
        // TsJyDetailVO vo = eventMapper.selectTsJyByEventId(new
        // IdStringDTO(dto.getId()));
        // user1_id
        // 根据user1_id查询部门
        // UserSimpleVO user1 = roadFeignClient.selectByUserId(vo.getUser1Id());
        // dto.setDealOrgId(user1.getOrgId());
        // dto.setDealOrgName(user1.getOrgName());
        // 更新event_ts
        Integer satisfaction = dto.getSatisfaction();
        if (satisfaction != null) {
            if (satisfaction != 5 && satisfaction != 10 && satisfaction != 15) {
                throw new ArgumentException("请求body参数校验不通过:满意度数据有问题");
            }
        }
        Integer revisit = dto.getRevisit();
        if (revisit != null) {
            if (revisit != 0 && revisit != 1) {
                throw new ArgumentException("请求body参数校验不通过:用户回访数据有问题");
            }
        }
        int ret = eventMapper.updateEventTs(dto);
        eventMapper.updateTsStep(dto);// event的complaint_type
        return new ResponseVO(ret);
    }

    @Transactional
    public ResponseVO finishTsJy(EventTsDTO dto) {
        String eventId = dto.getId();
        TsJyDetailVO vo = eventMapper.selectTsJyByEventId(new IdStringDTO(eventId));

        // 校验
        eventValidService.finishTsJy(dto, vo);
        String eventNo = vo.getEventNo();
        dto.setFinishTime(System.currentTimeMillis() / 1000);

        // 第一步，更新事件主表相关字段
        int ret = eventMapper.finishTsJy(dto);

        if (vo.getSource() != null && vo.getSource() == 1) {// 96333事件
            LOGGER.info("提交96333开关:{},eventNo:{}", eventRpc96333Finish, vo.getEventNo());
            if (ret > 0 && "1".equals(eventRpc96333Finish)) {
                CommandDTO commandDTO = new CommandDTO();
                commandDTO.setUuid(UUID.randomUUID().toString());
                Event96333SubmitDTO submitDTO = new Event96333SubmitDTO();
                submitDTO.setDealResult(dto.getDealResult());
                submitDTO.setEventNo(eventNo);
                submitDTO.setComplaintType(dto.getComplaintType());
                if ("new".equals(event96333Mode)) {// 对接千方后的实现
                    commandDTO.setSourceId("" + SourceIdEnum.XFZ_99.getIndex());// 新发展来源
                    commandDTO.setType(SystemConstants.EVENT_96333_FINISH_TYPE);
                    if (eventNo.startsWith(ServiceTypeEnum.TS.getName())) {
                        submitDTO.setServiceType(ServiceTypeEnum.TS.getValue());
                    } else if (eventNo.startsWith(ServiceTypeEnum.JY.getName())) {
                        submitDTO.setServiceType(ServiceTypeEnum.JY.getValue());
                    }
                    submitDTO.setSourceId(vo.getSourceId());
                    submitDTO.setSubmitAccount(dto.getFinishAccount());
                    commandDTO.setParams(new Gson().toJson(submitDTO));
                    // 第二步，提交结果到阿里云xfzCommand交换器
                    itsMsFeignClient.produce(commandDTO);
                    // 第三步，新增一条需要同步状态的记录event_96333_sync
                    Map<String, Object> map = new HashMap<>();
                    map.put("eventId", eventId);
                    map.put("eventNo", eventNo);
                    eventMapper.addEvent96333ResultCode(map);
                } else {
                    // 第二步，提交结果到阿里云command交换器
                    commandDTO.setSourceId("" + vo.getSourceId());
                    commandDTO.setType("Event96333");
                    submitDTO.setBusiId(vo.getBusiId());
                    commandDTO.setParams(new Gson().toJson(submitDTO));
                    itsMsFeignClient.produce(commandDTO);
                }

            }
        }
        if (ret > 0) {// 最后一步，WebSocket消息推送
            new Thread(new Runnable() {
                @Override
                public void run() {
                    EventProgressMessageDTO eventProgressMessageDTO = new EventProgressMessageDTO();
                    eventProgressMessageDTO.setWebsocketType("finishTsJy");
                    eventProgressMessageDTO.setEventId(eventId);
                    List<String> userIds = new ArrayList<>();
                    userIds.add(vo.getUser1Id());
                    userIds.add(vo.getUser2Id());
                    userIds.add(vo.getUser3Id());
                    userIds.add(dto.getUser4Id());
                    eventProgressMessageDTO.setUserIds(userIds);
                    itsWebSocketFeignClient.pushEventProgress(eventProgressMessageDTO);
                }
            }).start();
        }
        return new ResponseVO(ret > 0);
    }

    @Transactional
    public ResponseVO updateTsJyRevisit(EventTsDTO dto) {
        int ret = eventMapper.updateTsJyRevisit(dto);
        return new ResponseVO(ret > 0);
    }

    public Object todayAvgDealTime() {
        int avgDealTime = eventMapper.todayAvgDealTime();
        return new CountVO(avgDealTime);
    }

    public List<EventTodayTypeVO> countEventTodayType() {
        List<EventTodayTypeVO> vos = new ArrayList<>();
        List<EventTodayTypeVO> todayTypeVOS = eventMapper.countEventTodayType();
        if (todayTypeVOS != null && todayTypeVOS.size() > 0) {
            for (int i = 1; i <= 5; i++) {
                boolean status = false;
                for (EventTodayTypeVO vo : todayTypeVOS) {
                    if (vo.getSourceId() != null && vo.getSourceId().equals(i)) {
                        vos.add(new EventTodayTypeVO(i, vo.getRescueTotal(), vo.getPassgerTotal(), vo.getLsaacTotal(),
                                vo.getAccidentTotal()));
                        status = true;
                        break;
                    }
                }
                if (!status) {
                    vos.add(new EventTodayTypeVO(i, 0, 0, 0, 0));
                }
            }

        }
        return vos;
    }

    public Map<String, Integer> countYanHaiEvent() {
        return eventMapper.countYanHaiEvent();
    }

    public List<EventTypeTop5MapVO> countTypeTop5() {
        Map<String, String> listType = getTop5Type();
        // 返回的统计top5结果塞进map里
        List<EventTypeTop5VO> top5VOList = eventMapper.countTypeTop5(listType);
        List<EventTypeTop5MapVO> countVo = new ArrayList<>();
        if (top5VOList != null && top5VOList.size() > 0) {
            for (int i = 1; i <= 5; i++) {
                boolean status = false;
                for (EventTypeTop5VO vo : top5VOList) {
                    if (vo.getSourceId().equals(i)) {
                        status = true;
                        Map<String, Integer> map = new HashMap<>();
                        map.put(listType.get("type1"), vo.getType1());
                        map.put(listType.get("type2"), vo.getType2());
                        map.put(listType.get("type3"), vo.getType3());
                        map.put(listType.get("type4"), vo.getType4());
                        map.put(listType.get("type5"), vo.getType5());
                        EventTypeTop5MapVO mapVO = new EventTypeTop5MapVO(vo.getSourceId(), map);
                        countVo.add(mapVO);
                    }
                }
                if (!status) {
                    Map<String, Integer> map = new HashMap<>();
                    map.put(listType.get("type1"), 0);
                    map.put(listType.get("type2"), 0);
                    map.put(listType.get("type3"), 0);
                    map.put(listType.get("type4"), 0);
                    map.put(listType.get("type5"), 0);
                    EventTypeTop5MapVO mapVO = new EventTypeTop5MapVO(i, map);
                    countVo.add(mapVO);
                }
            }
        } else {
            for (int i = 1; i <= 5; i++) {
                Map<String, Integer> map = new HashMap<>();
                map.put(listType.get("type1"), 0);
                map.put(listType.get("type2"), 0);
                map.put(listType.get("type3"), 0);
                map.put(listType.get("type4"), 0);
                map.put(listType.get("type5"), 0);
                EventTypeTop5MapVO mapVO = new EventTypeTop5MapVO(i, map);
                countVo.add(mapVO);
            }
        }

        return countVo;
    }

    public List<EventHeatVO> selectEventHeat() {
        return eventMapper.selectEventHeat(getTop5Type());
    }

    // 当天事件/投诉总数+上月同比
    public Map<String, Object> countEventTotal(Map<String, Object> todayMap, Map<String, Object> lastMothDayMap) {
        Integer totalEvents = eventMapper.countEventTotal(todayMap);
        Integer lastMothEvents = eventMapper.countEventTotal(lastMothDayMap);
        Map<String, Object> countMap = new HashMap<>();
        countMap.put("eventCount", totalEvents);
        Object index = "";
        if (lastMothEvents == 0) {
            index = 0;
        } else if (totalEvents - lastMothEvents == 0) {
            index = 0;
        } else {
            index = ((totalEvents - lastMothEvents) / (double) lastMothEvents) * 100;
        }
        countMap.put("index", index);
        return countMap;
    }

    public Map<String, String> getTop5Type() {
        Map<String, String> listType = new HashMap<>();
        listType.put("type1", "车辆救援");
        listType.put("type2", "轻微交通事故");
        listType.put("type3", "事故灾难");
        listType.put("type4", "道路遗洒");
        listType.put("type5", "行人事件");
        List<String> top5 = eventMapper.selectTypeTop5();
        if (top5 != null && top5.size() > 0) {
            if (top5.size() == 5) {
                // listType.putAll(top5);
            } else {
                for (String s : listType.keySet()) {
                    if (top5.size() < 5) {
                        if (!top5.contains(listType.get(s))) {
                            top5.add(listType.get(s));
                        }
                    } else {
                        break;
                    }
                }
            }
            listType.clear();
            for (int i = 0; i < top5.size(); i++) {
                listType.put("type" + (i + 1), top5.get(i));
            }
        }
        return listType;
    }

    public NearestFacilityTipVO nearestFacilityById(IdStringDTO dto) {
        EventDetailVO eventDetailVO = eventMapper.selectEmerRescueByEventId(dto);
        if (eventDetailVO != null) {
            Integer roadNo = eventDetailVO.getRoadNo();
            String milePost = eventDetailVO.getMilePost();
            String directionName = eventDetailVO.getDirectionName();
            if (roadNo != null && StringUtils.isNotBlank(milePost)) {
                Integer mpValue = NumberUtils.toInt(milePost.replace("K", "").replace("+", ""));
                Map<String, Object> map = new HashMap<>();
                map.put("roadNo", roadNo);
                map.put("mpValue", mpValue);
                NearestFacilityTipVO nearestFacilityTipVO = eventMapper.nearestFacilityById(map);
                if (nearestFacilityTipVO != null) {
                	int mDistance = nearestFacilityTipVO.getDistance();
                    int kmDistance = mDistance / 1000;
                    String facilityName = nearestFacilityTipVO.getFacilityName();
                    facilityName = facilityName.replace("（", "(").replace("）", ")").replace("(上行)", "").replace("(下行)",
                            "");
                    String direction = "";
                    Integer facilityMpValue = nearestFacilityTipVO.getMpValue();
                    if (directionName != null && directionName.contains("上行")) {
                        if (facilityMpValue > mpValue) {
                            direction = "（前方）";
                        } else if (facilityMpValue < mpValue) {
                            direction = "（后方）";
                        }
                    } else if (directionName != null && directionName.contains("下行")) {
                        if (facilityMpValue > mpValue) {
                            direction = "（后方）";
                        } else if (facilityMpValue < mpValue) {
                            direction = "（前方）";
                        }
                    }
                    if (mDistance == 0) {
                    	nearestFacilityTipVO.setTip(facilityName + "内");
                    } else if (kmDistance == 0) {
                        nearestFacilityTipVO.setTip("距离" + direction + facilityName + "1公里内");// （后方）
                    } else {
                        nearestFacilityTipVO.setTip("距离" + direction + facilityName + kmDistance + "公里");
                    }
                    return nearestFacilityTipVO;
                }
            }

        }
        return new NearestFacilityTipVO();
    }

    public String getDictItemName(Integer parentId, Integer typeId, String typeName, String value) {
        String dictItemName = "";// 字典数据项名称
        DictItemDTO dto = new DictItemDTO();
        dto.setTypeId(typeId);
        dto.setTypeName(typeName);
        dto.setParentId(parentId);
        List<DictItemVO> itemList = feignClient.selectDictItem(dto);
        if (itemList.size() > 0) {
            for (DictItemVO dictItemVO : itemList) {
                if (dictItemVO.getValue().equals(value)) {
                    dictItemName = dictItemVO.getName();
                    break;
                }
            }
        }
        return dictItemName;
    }

    public String getDictItemName(Integer typeId, String typeName, String value) {
        String dictItemName = "";// 字典数据项名称
        if ("999".equals(value)) {
            return "";
        }
        DictItemDTO dto = new DictItemDTO();
        dto.setTypeId(typeId);
        dto.setTypeName(typeName);
        List<DictItemVO> itemList = feignClient.selectDictItem(dto);
        if (itemList.size() > 0) {
            for (DictItemVO dictItemVO : itemList) {
                if (dictItemVO.getValue().equals(value)) {
                    dictItemName = dictItemVO.getName();
                    break;
                }
            }
        }
        return dictItemName;
    }

    public void exportEmerRescueDetail(IdStringDTO idStringDTO, HttpServletResponse response) {
        EventDetailVO eventDetailVO = eventMapper.selectEmerRescueByEventId(idStringDTO);
        String templateName = "emer_rescue_detail.ftl";
        String eventNo = eventDetailVO.getEventNo();// 事件编号
        String exportFileName = "工单" + eventNo;
        Map<String, Object> dataMap = new HashMap<String, Object>();
        // 数据处理
        Integer sourceId = eventDetailVO.getSourceId();
        if (sourceId != null && sourceId == 1) {
            eventDetailVO.setTitle("沿海高速分公司");
        } else {
            eventDetailVO.setTitle("新发展集团");
        }
        Integer eventTwoType = eventDetailVO.getEventTwoType();
        if (eventTwoType != null && eventTwoType == 5) {
            eventDetailVO.setTitle(eventDetailVO.getTitle() + "突发事件");
        } else {
            eventDetailVO.setTitle(eventDetailVO.getTitle() + "轻微事件");
        }
        String eventFourTypeName = eventDetailVO.getEventFourTypeName();
        if (StringUtils.isNotBlank(eventFourTypeName)) {
            eventDetailVO.setEventThreeTypeName(eventFourTypeName);
        }
        // 增加天气、事故原因、事故类型、路损内容、路况类型、所属区域
        String weather = eventDetailVO.getWeather();
        if (StringUtils.isNotBlank(weather)) {
            eventDetailVO.setWeather(this.getDictItemName(61, "天气代码图标", weather));
        } else {
            eventDetailVO.setWeather("");
        }
        // 业务类型
        Integer eventType = eventDetailVO.getEventType();// 业务分类
        String businessType = eventDetailVO.getBusinessType();
        String businessTypeName = "";
        if (StringUtils.isNotBlank(businessType)) {
            if ((eventType != null && eventType == 1) || eventNo.indexOf("DD") > -1) {
                businessTypeName = this.getDictItemName(-1, null, "应急救援事件-业务分类", businessType);
            } else {
                businessTypeName = this.getDictItemName(126, "投诉建议-信息咨询-业务分类", businessType);
            }
        }
        eventDetailVO.setBusinessType(businessTypeName);
        // 路况类型
        String facilityName = eventDetailVO.getFacilityName();
        if (StringUtils.isNotBlank(facilityName)) {
            String typeName = EventFacilityEnums.getEventFacilityType(eventDetailVO.getFacilityTypeNo());
            if (StringUtils.isNotBlank(typeName)) {
                eventDetailVO.setFacilityName(typeName + "/" + facilityName);
            } else {
                eventDetailVO.setFacilityName(facilityName);
            }
        } else {
            eventDetailVO.setFacilityName("");
        }

        // 所属区域
        String address = eventDetailVO.getAddress();
        eventDetailVO.setAddress(StringUtils.isBlank(address) ? "" : address);

        // 事故类型
        String accidentType = eventDetailVO.getAccidentType();// 应急事故类型
        String otherTypeCause = eventDetailVO.getOtherTypeCause();// 应急事故类型其他原因
        String otherTypeCause_set = StringUtils.isNotBlank(otherTypeCause) ? "其他（" + otherTypeCause + "）" : "";
        if (StringUtils.isNotBlank(accidentType)) {
            if (!"999".equals(accidentType)) {
                otherTypeCause_set = this.getDictItemName(99, "应急救援-事故类型", accidentType);
            }
        }
        Integer dangeFlag = eventDetailVO.getDangeFlag();// 涉及危化品车 （默认0-未涉及,1-涉及；默认 0)
        if (dangeFlag == 1) {
            eventDetailVO.setOtherTypeCause(otherTypeCause_set + "（涉及危化品车）");
        } else {
            eventDetailVO.setOtherTypeCause(otherTypeCause_set);
        }

        // 事故原因
        String accidentCause = eventDetailVO.getAccidentCause();
        String otherCause = eventDetailVO.getOtherCause();// 其他事故原因
        String otherCause_set = StringUtils.isNotBlank(otherCause) ? "其他（" + otherCause + "）" : "";
        if (StringUtils.isNotBlank(accidentCause)) {
            if ("999".equals(accidentCause)) {
                eventDetailVO.setOtherCause(otherCause_set);
            } else {
                eventDetailVO.setOtherCause(this.getDictItemName(100, "应急救援-事故原因", accidentCause));
            }
        } else {
            eventDetailVO.setOtherCause(otherCause_set);
        }
        // 路损内容
        String roadLossCause = eventDetailVO.getRoadLossCause();
        List<EventRoadLossVO> roadLoss = eventDetailVO.getRoadLoss();
        if (roadLoss.size() > 0) {
            DictItemDTO dto = new DictItemDTO();
            dto.setTypeId(101);
            dto.setTypeName("应急救援-路损内容");
            List<DictItemVO> itemList = feignClient.selectDictItem(dto);
            String roadLossNames = "";
            for (EventRoadLossVO roadLossVO : roadLoss) {
                String typeCode = roadLossVO.getTypeCode();
                String value = roadLossVO.getValue();
                if ("999".equals(typeCode)) {
                    roadLossNames += "其他（" + roadLossCause + "）,";
                } else {
                    String dictItemName = "";
                    for (DictItemVO dictItemVO : itemList) {
                        if (dictItemVO.getValue().equals(typeCode)) {
                            dictItemName = dictItemVO.getName();
                            break;
                        }
                    }
                    if (StringUtils.isNotBlank(dictItemName)) {
                        String itemName = dictItemName.replace("（", value).replace("）", "");
                        roadLossNames += itemName + ",";
                    }
                }
            }
            eventDetailVO.setRoadLossCause(roadLossNames.substring(0, roadLossNames.length() - 1));
        } else {
            eventDetailVO.setRoadLossCause("");
        }
        eventDetailVO
                .setReportTimeStr(TimeUtils.getTimeString(TimeUtils.FULL_TIME_3, eventDetailVO.getReportTime() * 1000));
        eventDetailVO.setLevelName(
                eventDetailVO.getLevel() == null ? "" : EmerPlanLevelEnum.getCname(eventDetailVO.getLevel()));
        eventDetailVO.setSourceName(EventSourceEnum.getSourceStrByValue(eventDetailVO.getSource()));
        // 新增导出接报来源
        Integer reportSourceKey = eventDetailVO.getReportSourceKey();
        if (reportSourceKey != null) {
            eventDetailVO
                    .setReportSourceKeyName(this.getDictItemName(123, "事件管理-接报来源", String.valueOf(reportSourceKey)));
        } else {
            eventDetailVO.setReportSourceKeyName("");
        }
        if (eventDetailVO.getDistributeTime() != null) {
            eventDetailVO.setDistributeTimeStr(
                    TimeUtils.getTimeString(TimeUtils.FULL_TIME_3, eventDetailVO.getDistributeTime() * 1000));
        }
        if (eventDetailVO.getCarType() != null) {
            eventDetailVO.setCarTypeName(EventCarTypeEnum.getName(eventDetailVO.getCarType()));
        }
        if (eventDetailVO.getCarMan() != null) {
            eventDetailVO.setCarManStr(eventDetailVO.getCarMan() + "人");
        }
        Integer deathMan = eventDetailVO.getDeathMan();
        if (deathMan != null && deathMan >= 0) {
            eventDetailVO.setDeathManStr(deathMan + "人");
        } else if (deathMan != null && deathMan == -1) {
            eventDetailVO.setDeathManStr("不详");
        }
        Integer injureMan = eventDetailVO.getInjureMan();
        if (injureMan != null && injureMan >= 0) {
            eventDetailVO.setInjureManStr(injureMan + "人");
        } else if (injureMan != null && injureMan == -1) {
            eventDetailVO.setInjureManStr("不详");
        }
        Integer missMan = eventDetailVO.getMissMan();
        if (missMan != null && missMan >= 0) {
            eventDetailVO.setMissManStr(missMan + "人");
        } else if (missMan != null && missMan == -1) {
            eventDetailVO.setMissManStr("不详");
        }
        Float congestionLength = eventDetailVO.getCongestionLength();
        if (congestionLength != null && congestionLength >= 0) {
            eventDetailVO.setCongestionLengthStr("拥堵" + congestionLength + "米");
        }
        Float incomeLose = eventDetailVO.getIncomeLose();
        if (incomeLose != null) {
            if (incomeLose == 0) {
                eventDetailVO.setIncomeLoseStr("无");
            } else if (incomeLose == -1) {
                eventDetailVO.setIncomeLoseStr("不详");
            } else if (incomeLose == -2) {
                eventDetailVO.setIncomeLoseStr("有");
            } else if (incomeLose > 0) {
                eventDetailVO.setIncomeLoseStr(incomeLose + "元");
            }
        }
        List<OccupiedLaneVO> occupiedLanes = eventDetailVO.getOccupiedLanes();
        eventDetailVO.setLaneTunnel("0");
        eventDetailVO.setLane0("0");
        eventDetailVO.setLane1("0");
        eventDetailVO.setLane2("0");
        eventDetailVO.setLane3("0");
        eventDetailVO.setLane4("0");
        if (!CollectionUtils.isEmpty(occupiedLanes)) {
            for (OccupiedLaneVO occupiedLaneVO : occupiedLanes) {
                Integer occupiedLane = occupiedLaneVO.getOccupiedLane();
                if (occupiedLane == null) {
                    continue;
                }
                if (occupiedLane == 0) {
                    eventDetailVO.setLane0("1");
                } else if (occupiedLane == 1) {
                    eventDetailVO.setLane1("1");
                } else if (occupiedLane == 2) {
                    eventDetailVO.setLane2("1");
                } else if (occupiedLane == 3) {
                    eventDetailVO.setLane3("1");
                } else if (occupiedLane == 4) {
                    eventDetailVO.setLane4("1");
                } else if (occupiedLane == -1) {
                    eventDetailVO.setLaneTunnel("1");
                }
            }
        }

        ProgressDTO progressDTO = new ProgressDTO();
        progressDTO.setEventId(idStringDTO.getId());
        List<GroupProgressVO> progressList = progressMapper.selectListWithGroup(progressDTO);
        if (!CollectionUtils.isEmpty(progressList)) {
            int resubmitCount = 0;
            int finalReportCount = 0;

            int confirmCount = 0;// 确认 （只显示一次确认序号）
            int startCount = 0;// 出发（只显示一次出发序号）
            int arriveCount = 0;// 到达（只显示一次到达序号）
            int leaveCount = 0;// 离开（只显示一次离开序号）
            int endCount = 0;// 完结（只显示一次完结序号）

            int step = 0;
            int stepCount = 0;
            List<ProgressExportVO> progressExportList = new ArrayList<>();
            for (int i = progressList.size() - 1; i >= 0; i--) {
                GroupProgressVO groupProgressVO = progressList.get(i);
                ProgressExportVO progressExportVO = new ProgressExportVO();
                String progressDesc = groupProgressVO.getProgressDesc();
                if (StringUtils.isBlank(progressDesc)) {
                    progressExportVO.setProgressDesc("");
                } else {
                    progressExportVO.setProgressDesc(progressDesc);
                }
                int cardType = groupProgressVO.getCardType() == null ? 0 : groupProgressVO.getCardType().intValue();
                int cardPass = groupProgressVO.getCardPass() == null ? 0 : groupProgressVO.getCardPass().intValue();
                progressExportVO.setCardType(cardType);
                progressExportVO.setStep(0);
                String progressDesc2 = progressExportVO.getProgressDesc();
                if (cardType == 5) {
                    resubmitCount++;
                    if (resubmitCount > 1) {
                        progressExportVO
                                .setProgressDesc(progressDesc2.replace("【事件续报】", "【事件续报" + resubmitCount + "】"));
                    }
                } else if (cardType == 10) {
                    finalReportCount++;
                    if (finalReportCount > 1) {
                        progressExportVO
                                .setProgressDesc(progressDesc2.replace("【事件终报】", "【事件终报" + finalReportCount + "】"));
                    }
                } else if (cardType == 1) {
                    if (stepCount < 1) {
                        stepCount = 1;
                        step++;
                        progressExportVO.setStep(step);
                    }
                    List<ProgressUserVO> atUsers = groupProgressVO.getAtUsers();
                    if (!CollectionUtils.isEmpty(atUsers)) {
                        for (ProgressUserVO user : atUsers) {
                            progressExportVO.setProgressDesc(progressDesc2 + "@" + user.getUserName());
                        }
                    }
                } else if (cardType == 0 && cardPass == 5) {
                    if (confirmCount == 0) {
                        confirmCount = 1;
                        stepCount = 2;
                        step++;
                        progressExportVO.setStep(step);
                    }
                } else if (cardType == 0 && cardPass == 15) {
                    if (startCount == 0) {
                        startCount = 1;
                        stepCount = 3;
                        step++;
                        progressExportVO.setStep(step);
                    }
                } else if (cardType == 0 && cardPass == 20) {
                    if (arriveCount == 0) {
                        arriveCount = 1;
                        stepCount = 4;
                        step++;
                        progressExportVO.setStep(step);
                    }
                } else if (cardType == 0 && cardPass == 30) {
                    if (leaveCount == 0) {
                        leaveCount = 1;
                        stepCount = 5;
                        step++;
                        progressExportVO.setStep(step);
                    }
                } else if (cardType == 0 && cardPass == 100) {
                    if (endCount == 0) {
                        endCount = 1;
                        stepCount = 6;
                        step++;
                        progressExportVO.setStep(step);
                    }
                }
                progressExportVO.setCardPass(cardPass);
                progressExportVO.setCompanyName(groupProgressVO.getCompanyName());
                progressExportVO.setOrgName(groupProgressVO.getOrgName());
                progressExportVO.setCreateUserName(groupProgressVO.getCreateUserName());

                Long createTime = groupProgressVO.getCreateTime();
                if (createTime != null) {
                    progressExportVO.setCreateTime(TimeUtils.getTimeString(TimeUtils.DATE_TIME_1, createTime * 1000));
                }

                List<ProgressAttachVO> attachs = groupProgressVO.getAttachs();
                LOGGER.info("attachs:", attachs.size());
                if (!CollectionUtils.isEmpty(attachs)) {
                    List<AttachDTO> attachDtos = new ArrayList<>();
                    for (ProgressAttachVO tmp : attachs) {
                        String contentType = tmp.getContentType();
                        if (contentType != null && contentType.startsWith("image")) {
                            AttachDTO attachDTO = new AttachDTO();
                            attachDTO.setFileName(tmp.getFileName());
                            attachDTO.setDiskFileName(tmp.getDiskFileName());
                            attachDtos.add(attachDTO);
                        }
                    }

                    List<AttachDTO> ossUrls = ossUrl(attachDtos);
                    for (AttachDTO attachDTO : ossUrls) {
                        LOGGER.info("url:{}", attachDTO.getUrl());
                        attachDTO.setDigest(Base64Utils.imageUrlToBase64(attachDTO.getUrl()));
                    }
                    progressExportVO.setOssUrls(ossUrls);
                }

                progressExportList.add(progressExportVO);
            }
            eventDetailVO.setProgressList(progressExportList);
        } else {
            eventDetailVO.setProgressList(null);
        }
        Integer facilityType = eventDetailVO.getFacilityTypeNo();
        if (facilityType == null) {
            eventDetailVO.setFacilityType(0);
        }

        //增加事件完成处置项
        List<ProcessSceneExportDTO> processSceneExportDTOS = eventMapper.selectProcessScene(idStringDTO);
        LOGGER.info("process scene attachs:", processSceneExportDTOS.size());
        if (!CollectionUtils.isEmpty(processSceneExportDTOS)) {
            List<ProcessSceneExportVO> processSceneExportList = new ArrayList<>();
            Map<String, List<ProcessSceneExportDTO>> processSceneMap = processSceneExportDTOS.stream().collect(Collectors.groupingBy(ProcessSceneExportDTO::getSceneName));
            processSceneMap.forEach((k, v) -> {
                ProcessSceneExportVO vo = new ProcessSceneExportVO();
                List<AttachDTO> attachList = new ArrayList<>();
                for (ProcessSceneExportDTO dto : v) {
                    AttachDTO attachDTO = new AttachDTO();
                    attachDTO.setFileName(dto.getFileName());
                    attachDTO.setDiskFileName(dto.getDiskFileName());
                    attachList.add(attachDTO);
                }
                List<AttachDTO> SceneOssUrls = ossUrl(attachList);
                for (AttachDTO attachDTO : SceneOssUrls) {
                    LOGGER.info("SceneUrl:{}", attachDTO.getUrl());
                    attachDTO.setDigest(Base64Utils.imageUrlToBase64(attachDTO.getUrl()));
                }
                vo.setSceneName(k);
                vo.setOssUrls(SceneOssUrls);
                processSceneExportList.add(vo);
            });
            eventDetailVO.setProcessSceneList(processSceneExportList);
        } else {
            eventDetailVO.setProcessSceneList(null);
        }

        dataMap.put("eventDetailVO", eventDetailVO);
        ExportUtils.downloadWord(templatePath, templateName, fileExportPath, exportFileName, dataMap, response);
    }

    @Transactional
    public int addZx(EventZxDTO eventZxDTO) {
        eventZxDTO.setZxId(UUID.randomUUID().toString());
        long time = System.currentTimeMillis();
        eventZxDTO.setCreateTime(time / 1000);
        // 查询当前填报用户的信息
        UserSimpleVO recordManVO = feignClient.selectByUserId(eventZxDTO.getRecordManId());
        eventZxDTO.setRecordMan(recordManVO.getUserName());
        eventZxDTO.setRecordManTel(recordManVO.getMobile());
        eventZxDTO.setEventStatus(1);
        // 查询责任用户的信息
        UserSimpleVO user1VO = feignClient.selectByUserId(eventZxDTO.getUser1Id());
        eventZxDTO.setUser1Name(user1VO.getUserName());

        eventZxDTO.setOrgId(eventZxDTO.getOrg1Id());
        int rows = eventMapper.addEventZx(eventZxDTO);
        String yyMMddHHmmssSSS = TimeUtils.getTimeString(TimeUtils.DATETIME, time);
        String eventNo = "ZX-" + yyMMddHHmmssSSS;
        eventZxDTO.setEventNo(eventNo);
        if (rows == 1) {
            rows = eventMapper.addZx(eventZxDTO);
        }
        return rows;
    }

    @Transactional
    public boolean updateZx(EventZxDTO eventZxDTO) {
        long time = System.currentTimeMillis() / 1000;
        eventZxDTO.setUpdateTime(time);
        // 查询责任用户的信息
        UserSimpleVO user1VO = feignClient.selectByUserId(eventZxDTO.getUser1Id());
        eventZxDTO.setUser1Name(user1VO.getUserName());

        int rows = eventMapper.updateZx(eventZxDTO);
        rows = eventMapper.updateEventZx(eventZxDTO);
        return rows > 0;
    }

    @Transactional
    public boolean finishZx(EventZxDTO eventZxDTO) {
        UserSimpleVO loginUser = feignClient.selectByUserId(eventZxDTO.getFinishUserId());
        long time = System.currentTimeMillis() / 1000;
        eventZxDTO.setFinishTime(time);
        eventZxDTO.setUpdateTime(time);
        eventZxDTO.setFinishUserName(loginUser.getUserName());
        // 查询责任用户的信息
        UserSimpleVO user1VO = feignClient.selectByUserId(eventZxDTO.getUser1Id());
        eventZxDTO.setUser1Name(user1VO.getUserName());

        int rows = eventMapper.finishZx(eventZxDTO);
        rows = eventMapper.updateEventZx(eventZxDTO);

        String eventId = eventZxDTO.getId();

        IdStringDTO eventIdDTO = new IdStringDTO(eventId);
        EventDetailVO eventVO = eventMapper.selectEmerRescueByEventId(eventIdDTO);
        Integer source = eventVO.getSource();
        if (source != null && source == 1) {// source=1为96333事件
            String eventNo = eventVO.getEventNo();
            LOGGER.info("提交96333开关:{}，工单号：{}", eventRpc96333Finish, eventNo);
            if (eventNo.startsWith("ZX-")) {
                LOGGER.info("不需要提交到96333系统，工单号：{}", eventNo);
            } else if (rows > 0 && "1".equals(eventRpc96333Finish)) {
                // 处理96333事件，发送一个指令到command交换器（8.134）
                CommandDTO commandDTO = new CommandDTO();
                commandDTO.setSourceId("" + SourceIdEnum.XFZ_99.getIndex());// 新发展来源
                commandDTO.setUuid(UUID.randomUUID().toString());
                commandDTO.setType(SystemConstants.EVENT_96333_FINISH_TYPE);
                // 事件编号eventNo，96333中的id(busiId)，处理结果dealResult
                Event96333SubmitDTO submitDTO = new Event96333SubmitDTO();
                submitDTO.setDealResult(eventZxDTO.getDealResult());
                submitDTO.setEventNo(eventVO.getEventNo());
                submitDTO.setServiceType(ServiceTypeEnum.ZX.getValue());// 96333的业务编号（救援）
                submitDTO.setSourceId(eventVO.getSourceId());
                submitDTO.setSubmitAccount(eventZxDTO.getFinishAccount());
                commandDTO.setParams(new Gson().toJson(submitDTO));
                itsMsFeignClient.produce(commandDTO);

                // 新增一条需要同步状态的记录event_96333_sync
                Map<String, Object> map = new HashMap<>();
                map.put("eventId", eventId);
                map.put("eventNo", eventNo);
                int row = eventMapper.selectEvent96333ResultCode(eventIdDTO);
                if (row == 0) {
                    eventMapper.addEvent96333ResultCode(map);
                }
            }
        }

        return rows > 0;
    }

    public PageInfo<EventZxVO> pageZx(PageDTO pageDTO, EventQueryDTO dto) {
        UserSimpleVO loginUser = feignClient.selectByUserId(dto.getUserId());
        dto.setCompanyId(loginUser.getCompanyId());
        PageHelper.startPage(pageDTO.getPage(), pageDTO.getLimit());
        List<String> dealOrg = new ArrayList<>();
        List<String> organizations = dto.getOrganizations();
        if (!CollectionUtils.isEmpty(organizations)) {
            for (String orgId : organizations) {
                if ("dfbd6525-b051-4719-b356-cd4e5454212".equals(orgId)) {// 广西北部湾投资集团有限公司，忽略
                    continue;
                }
                if ("dfbd6525-b051-4719-b356-cd4e545408f7".equals(orgId)) {// 新发展交通集团有限公司
                    dealOrg.add("6dc5e3e4-160c-4e3f-b42b-8c9f8d28aeb5");// 新发展集团本部
                    dealOrg.add(OrgSourceIdEnum.getName(2));
                    dealOrg.add(OrgSourceIdEnum.getName(3));
                    dealOrg.add(OrgSourceIdEnum.getName(4));
                    dealOrg.add(OrgSourceIdEnum.getName(5));
                    dealOrg.add(OrgSourceIdEnum.getName(6));
                    dealOrg.add(OrgSourceIdEnum.getName(7));
                    dealOrg.add(OrgSourceIdEnum.getName(8));
                    dealOrg.add(OrgSourceIdEnum.getName(9));
                } else if ("5f587a1c-189d-417b-910f-c09f0a18763f".equals(orgId)) {// 沿海公司
                    dealOrg.add(YanHaiOrgEnum.getName(1));
                    dealOrg.add(YanHaiOrgEnum.getName(2));
                    dealOrg.add(YanHaiOrgEnum.getName(3));
                    dealOrg.add(YanHaiOrgEnum.getName(4));
                    dealOrg.add(YanHaiOrgEnum.getName(5));
                } else {
                    dealOrg.add(orgId);
                }
            }
        }
        dto.setOrganizations(dealOrg);
        List<EventZxVO> list = eventMapper.selectZx(dto);
        for (EventZxVO eventZxVO : list) {
            Integer source = eventZxVO.getSource();
            source = (source == null) ? 0 : source;
            Integer reportSourceKey = eventZxVO.getReportSourceKey();
            reportSourceKey = (reportSourceKey == null) ? 0 : reportSourceKey;
            if (source == 1 || reportSourceKey == 1) {
                eventZxVO.setReportSource("96333");
            } else {
                eventZxVO.setReportSource("本地");
            }
        }
        PageInfo<EventZxVO> pageInfo = new PageInfo<>(list);
        return pageInfo;
    }

    public EventZxDetailVO selectZxDetail(IdStringDTO dto) {
        EventZxDetailVO vo = eventMapper.selectZxDetail(dto);
        String user1Id = vo.getUser1Id();
        if (user1Id != null) {
            UserSimpleVO user1VO = feignClient.selectByUserId(user1Id);
            if (user1VO != null) {
                vo.setOrg1Name(user1VO.getCompanyName());
            }
        }
        return vo;
    }

    public List<EventContructionVO> selectWithContruction(String userId) {
        Map<String, Object> map = new HashMap<>();
        map.put("userId", userId);
        List<EventContructionVO> list = eventMapper.selectWithContruction(map);
        long nowTime = System.currentTimeMillis() / 1000;
        for (EventContructionVO vo : list) {
            if (vo.getReportType() == 2) {
                if (vo.getStartTime() > nowTime) {
                    vo.setDealStatus(0);
                } else if (vo.getEndTime() < nowTime) {
                    vo.setDealStatus(2);
                } else {
                    vo.setDealStatus(1);
                }
            }
        }
        return list;
    }

    @Transactional
    public int abnormalFinish(EventFinishDTO dto) {
        // 判断是否有出车单，有的话需要完成后才能完结事件
        IdStringDTO idStringDTO = new IdStringDTO(dto.getId());
        EventDetailVO eventVO = eventMapper.selectEmerRescueByEventId(idStringDTO);
        if (eventVO.getDealStatus() != null && eventVO.getDealStatus() == 100) {
            throw new ArgumentException("该事件已完结，请勿重复点击。");
        }
        // 添加完结进展
        ProgressDTO progressDTO = new ProgressDTO();
        long time = System.currentTimeMillis() / 1000;
        progressDTO.setCreateTime(time);
        progressDTO.setOccurTime(time);
        UserSimpleVO loginUserVO = feignClient.selectByUserId(dto.getFinishUserId());
        String progressDesc = loginUserVO.getUserName() + TimeUtils.getTimeString(TimeUtils.DATE_TIME_4, time * 1000)
                + "完结该事件，完结原因：" + dto.getDealResult();

        progressDTO.setProgressDesc(progressDesc);
        progressDTO.setEventId(dto.getId());
        progressDTO.setCardPass(100);
        progressDTO.setCreateUserId(dto.getFinishUserId());
        progressService.add(progressDTO);
        // 改变事件处理状态为100，并且修改finishUserId，结束事件时间
        dto.setFinishTime(time);
        return eventMapper.finishEmerRescue(dto);
    }

    public List<OrganizationVO> orgRoadByUser(String userId, List<OrganizationVO> organizationTree, String[] roles) {
        List<String> leafs = new ArrayList<>();
        recursiveOrganizationTree(organizationTree, leafs);

        Map<String, Object> map = new HashMap<>();
        map.put("roles", Arrays.asList(roles));

        UserSimpleVO loginUser = feignClient.selectByUserId(userId);
        String companyId = loginUser.getCompanyId();
        if (leafs.contains(companyId)) {
            LOGGER.info("#################包含companyId########################{}", companyId);
            map.put("companyId", companyId);
        } else {
            map.put("companyId", null);
        }
        // 通过eventOrgList查询关联的路段
        List<OrgRoadThreeLevelVO> eventOrgList = eventMapper.selectEventOrgList(map);
        List<String> removeLeafs = new ArrayList<>();

        for (String leaf : leafs) {
            boolean remove = true;
            for (OrgRoadThreeLevelVO vo : eventOrgList) {
                if (vo.getOrgId().equals(leaf)) {
                    remove = false;
                    break;
                }
            }
            if (remove) {
                removeLeafs.add(leaf);
            }
            remove = true;
        }
        recursiveDelOrganizationLeaf(organizationTree, removeLeafs);
        recursiveDelOrganizationParent(organizationTree, leafs);
        if (!CollectionUtils.isEmpty(eventOrgList)) {
            recursiveOrganizationSetParentRoad(organizationTree, eventOrgList);
        }
        if (organizationTree.size() > 0) {
			for (int i = organizationTree.size() - 1; i >= 0; i--) {
				if (CollectionUtils.isEmpty(organizationTree.get(i).getChildren())) {
					organizationTree.remove(i);
				}
			}
		}

        return organizationTree;
    }

    void recursiveOrganizationTree(List<OrganizationVO> source, List<String> leafs) {
        for (OrganizationVO org : source) {
            if (CollectionUtils.isEmpty(org.getChildren())) {
                leafs.add(org.getOrgId());
            } else {
                recursiveOrganizationTree(org.getChildren(), leafs);
            }
        }
    }

    void recursiveDelOrganizationLeaf(List<OrganizationVO> source, List<String> removeLeafs) {
        for (int i = source.size() - 1; i >= 0; i--) {
            OrganizationVO org = source.get(i);
            if (CollectionUtils.isEmpty(org.getChildren())) {
                for (String s : removeLeafs) {
                    if (org.getOrgId().equals(s)) {
                        source.remove(i);
                    }
                }
            } else {
                recursiveDelOrganizationLeaf(org.getChildren(), removeLeafs);
            }
        }
    }

    void recursiveDelOrganizationParent(List<OrganizationVO> source, List<String> leafs) {
        for (int i = source.size() - 1; i >= 0; i--) {
            OrganizationVO org = source.get(i);
            if (CollectionUtils.isEmpty(org.getChildren())) {
                boolean exit = true;
                for (String leaf : leafs) {
                    if (org.getOrgId().equals(leaf)) {
                        exit = false;
                    }
                }
                if (exit) {
                    source.remove(i);
                }
                exit = true;
            } else {
                recursiveDelOrganizationParent(org.getChildren(), leafs);
            }
        }
    }

    void recursiveOrganizationSetParentRoad(List<OrganizationVO> source, List<OrgRoadThreeLevelVO> eventOrgList) {
        for (OrganizationVO org : source) {
            if (CollectionUtils.isEmpty(org.getChildren())) {
                for (OrgRoadThreeLevelVO orgRoad : eventOrgList) {
                    if (org.getOrgId().equals(orgRoad.getOrgId())) {
                        org.setParentRoads(orgRoad.getChildren());
                    }
                }
            } else {
                recursiveOrganizationSetParentRoad(org.getChildren(), eventOrgList);
            }
        }
    }

    public void exportTsJyWord(IdStringDTO dto, HttpServletResponse response) {
        TsJyDetailVO vo = eventMapper.selectTsJyByEventId(dto);
        if (vo.getUser1Id() != null) {
            UserSimpleVO user1 = feignClient.selectByUserId(vo.getUser1Id());
            vo.setUser1Name(user1.getUserName());
            vo.setOrg1Name(user1.getOrgName());
            vo.setCompany1Name(user1.getCompanyName());
        } else {
            vo.setUser1Name("");
            vo.setOrg1Name("");
            vo.setCompany1Name("");
        }
        if (vo.getUser2Id() != null) {
            UserSimpleVO user2 = feignClient.selectByUserId(vo.getUser2Id());
            vo.setUser2Name(user2.getUserName());
            vo.setOrg2Name(user2.getOrgName());
            vo.setCompany2Name(user2.getCompanyName());
        } else {
            vo.setUser2Name("");
            vo.setOrg2Name("");
            vo.setCompany2Name("");
        }
        if (vo.getUser3Id() != null) {
            UserSimpleVO user3 = feignClient.selectByUserId(vo.getUser3Id());
            vo.setUser3Name(user3.getUserName());
            vo.setOrg3Name(user3.getOrgName());
            vo.setCompany3Name(user3.getCompanyName());
        } else {
            vo.setUser3Name("");
            vo.setOrg3Name("");
            vo.setCompany3Name("");
        }
        if (vo.getUser4Id() != null) {
            UserSimpleVO user4 = feignClient.selectByUserId(vo.getUser4Id());
            vo.setUser4Name(user4.getUserName());
            vo.setOrg4Name(user4.getOrgName());
            vo.setCompany4Name(user4.getCompanyName());
        } else {
            vo.setUser4Name("");
            vo.setOrg4Name("");
            vo.setCompany4Name("");
        }

        if (vo.getStep() != null && vo.getStep() > 1) {
            vo.setOrgName(vo.getOrg2Name());
            vo.setCompanyName(vo.getCompany2Name());
        } else {
            vo.setOrgName("");
            vo.setCompanyName(vo.getDealOrgName());
        }
        String templateName = "ts_detail.ftl";
        String exportFileName = "工单" + vo.getEventNo();
        Map<String, Object> dataMap = new HashMap<String, Object>();
        // 数据处理
        vo.setReportTimeStr(TimeUtils.getTimeString(TimeUtils.FULL_TIME_3, vo.getReportTime() * 1000));
        vo.setSourceName(EventSourceEnum.getSourceStrByValue(vo.getSource()));
        if (vo.getComplaintType() == null || vo.getComplaintType() == 0) {
            vo.setComplaintTypeStr("无理");
        } else {
            vo.setComplaintTypeStr("有理");
        }
        if (vo.getEventThreeType() == null) {
            vo.setEventThreeTypeStr("");
        } else if (vo.getEventThreeType() == 15) {
            vo.setEventThreeTypeStr("投诉");
        } else if (vo.getEventThreeType() == 16) {
            vo.setEventThreeTypeStr("举报");
        } else if (vo.getEventThreeType() == 20) {
            vo.setEventThreeTypeStr("意见");
        } else if (vo.getEventThreeType() == 22) {
            vo.setEventThreeTypeStr("建议");
        }
        exportFileName = vo.getEventThreeTypeStr() + exportFileName;
        if (vo.getSatisfaction() == null) {
            vo.setSatisfaction(0);
        }
        if (vo.getRevisit() == null) {
            vo.setRevisit(0);
        }
        dataMap.put("eventDetailVO", vo);
        ExportUtils.downloadWord(templatePath, templateName, fileExportPath, exportFileName, dataMap, response);
    }

    public void exportZxWord(IdStringDTO dto, HttpServletResponse response) {
        EventZxDetailVO vo = eventMapper.selectZxDetail(dto);
        if (vo.getUser1Id() != null) {
            UserSimpleVO user1 = feignClient.selectByUserId(vo.getUser1Id());
            vo.setUser1Name(user1.getUserName());
            vo.setOrg1Name(user1.getOrgName());
            vo.setCompany1Name(user1.getCompanyName());
        } else {
            vo.setUser1Name("");
            vo.setOrg1Name("");
            vo.setCompany1Name("");
        }
        String templateName = "zx_detail.ftl";
        String exportFileName = "咨询工单" + vo.getEventNo();
        Map<String, Object> dataMap = new HashMap<String, Object>();
        // 数据处理
        vo.setReportTimeStr(TimeUtils.getTimeString(TimeUtils.FULL_TIME_3, vo.getReportTime() * 1000));
        Integer source = vo.getSource();
        String sourceStrByValue = EventSourceEnum.getSourceStrByValue(source);
        String otherSource = vo.getOtherSource();
        if (source == null) {
            vo.setSourceName("");
        } else if (source == 99) {
            if (StringUtils.isBlank(otherSource)) {
                vo.setSourceName(sourceStrByValue);
            } else {
                vo.setSourceName(sourceStrByValue + "-" + otherSource);
            }
        } else {
            vo.setSourceName(sourceStrByValue);
        }
        if (vo.getSatisfaction() == null) {
            vo.setSatisfaction(0);
        }
        if (vo.getRevisit() == null) {
            vo.setRevisit(0);
        }
        if (vo.getDealResult() == null) {
            vo.setDealResult("");
        }
        if (vo.getFeedback() == null) {
            vo.setFeedback("");
        }
        if (vo.getFinishTime() == null) {
            vo.setFinishTimeStr("");
        } else {
            vo.setFinishTimeStr(TimeUtils.getTimeString(TimeUtils.FULL_TIME_3, vo.getFinishTime() * 1000));
        }
        dataMap.put("eventDetailVO", vo);
        ExportUtils.downloadWord(templatePath, templateName, fileExportPath, exportFileName, dataMap, response);
    }

    public EventDealStatusVO selectEventDealStatus(IdStringDTO dto) {
        EventDealStatusVO eventDealStatus = eventMapper.selectEventDealStatus(dto);
        if (eventDealStatus == null) {
            return null;
        }
        EventDetailVO eventDetailVO = eventMapper.selectEmerRescueByEventId(dto);
        eventDealStatus.setDealStatus(eventDetailVO.getDealStatus());
        eventDealStatus.setFinishTime(eventDetailVO.getFinishTime());
        String emerPlanLevelId = eventDetailVO.getEmerPlanLevelId();
        Integer level = eventDetailVO.getLevel();
        Map<String, Object> map = new HashMap<>();
        map.put("emerPlanLevelId", emerPlanLevelId);
        map.put("planLevel", level);
        EmerPlanVO emerPlanVO = eventMapper.selectEmerplanAuditTime(map);
        if (emerPlanVO != null) {
            eventDealStatus.setPlanAuditTime(emerPlanVO.getAuditTime());
        }
        Integer sceneUpload = eventDealStatus.getSceneUpload();
        if (sceneUpload == null || sceneUpload == 0) {
            if (level != null && level <= 8 && level > 0) {// 事件预案等级必须为I/II/III/IV级
                eventDealStatus.setSceneUpload(0);
            } else {
                eventDealStatus.setSceneUpload(null);
            }
        } else if (sceneUpload == 1) {
            List<ProgressSceneVO> scenes = progressMapper.selectScene(dto);
            if (scenes == null) {
                eventDealStatus.setSceneUpload(0);
            } else {
                eventDealStatus.setSceneUpload(0);
                for (ProgressSceneVO progressSceneVO : scenes) {
                    if (!CollectionUtils.isEmpty(progressSceneVO.getAttachs())) {
                        eventDealStatus.setSceneUpload(1);
                        break;
                    }
                }
            }
        }
        Integer cmsPublish = eventDealStatus.getCmsPublish();
        if (cmsPublish == null || cmsPublish == 0) {
            eventDealStatus.setCancelCmsPublish(null);
        } else if (cmsPublish == 1) {
            Integer cancelCmsPublish = eventDealStatus.getCancelCmsPublish();
            if (cancelCmsPublish == null) {
                eventDealStatus.setCancelCmsPublish(0);
            }
        }

        if (emerPlanVO != null) {
        	eventDealStatus.setResponseTime(emerPlanVO.getResponseTime());
        	eventDealStatus.setPlanName(emerPlanVO.getName());
        }
        eventDealStatus.setPlanLevelName(EmerPlanLevelEnum.getCname(level));
        // 查询初报相关的进展用户短信发送情况
        int failCount = progressMapper.selectInitReportFailSms(dto);
        eventDealStatus.setInitReportSms(failCount > 0 ? 0 : 1);
        // 查询事件归档评价分析状态
        Long countdownTime = eventMapper.selectEevnetFileAnalyseTime(dto);
        if (countdownTime == null || countdownTime == 2000000000) {
            eventDealStatus.setCountdownTime(null);
        } else {
            eventDealStatus.setCountdownTime(countdownTime);
        }

        // 4级及以上应急救援事件，处置评价完成状态
        if (eventDetailVO.getEventType() == 1 && level <= 8) {
            // 判断是否在预案中配置启动突发事件应急处置
            // 查询emer接口
            ResponseVO responseVO = itsEmerFeignClient.appraisePermission(dto);
            if (responseVO != null && responseVO.getCode() == 1) {
                // 在预案中配置启动突发事件应急处置，判断是否存在评价表是否已完成
                EventEvlRecordDTO queryDTO = new EventEvlRecordDTO();
                queryDTO.setEventId(dto.getId());
                EventEvlRecordVO recordVO = eventMapper.getEvlRecord(queryDTO);
                if (recordVO != null) {
                    // 评价表已存在，判断评价表状态
                    if (EventEvaStateConstants.STATE_STAGING == recordVO.getState()) {
                        // 评价表状态为暂存，有问题
                        eventDealStatus.setEvlProblem(SystemConstants.EVL_PROBLEM);
                    } else {
                        // 评价表为其他状态，无问题
                        eventDealStatus.setEvlProblem(SystemConstants.EVL_NO_PROBLEM);
                    }
                } else {
                    // 评价表不存在，有问题
                    eventDealStatus.setEvlProblem(SystemConstants.EVL_PROBLEM);
                }
            } else {
                // 未在预案中配置启动突发事件应急处置，无问题
                eventDealStatus.setEvlProblem(SystemConstants.EVL_NO_PROBLEM);
            }
        } else {
            // 事件不是4级应急救援，无问题
            eventDealStatus.setEvlProblem(SystemConstants.EVL_NO_PROBLEM);
        }
        //SELECT * FROM event_progress WHERE card_type=20 AND card_valid=1 AND event_id='08ddd10d-7a89-46d7-be3f-0bff9caa8579' ORDER BY occur_time DESC LIMIT 1;
        ProgressVO vo = progressMapper.selectStartPlanByEventId(dto);
        if (vo != null) {
        	eventDealStatus.setStartPlanTime(vo.getOccurTime());
        }
        return eventDealStatus;
    }

    @Transactional
    public boolean cancelPlan(IdStringDTO dto, String userId) {
        long serverTime = System.currentTimeMillis() / 1000;
        EventDetailVO eventDetailVO = eventMapper.selectEmerRescueByEventId(dto);
        // 查询是否配置有预案审核人员，有，生成“预案启动”卡片
        List<EventEmerUserVO> eventEmerUsers = eventMapper.selectEmerUserById(dto);
        List<ProgressUserDTO> emerPlanAuditUsers = new ArrayList<>();
        for (EventEmerUserVO tmpVO : eventEmerUsers) {
            if ("emerPlanAudit".equals(tmpVO.getEmerGroupId())) {
                ProgressUserDTO emerPlanAuditUser = new ProgressUserDTO();
                emerPlanAuditUser.setUserId(tmpVO.getUserId());
                emerPlanAuditUser.setUserName(tmpVO.getUserName());
                emerPlanAuditUser.setMobile(tmpVO.getMobile());
                emerPlanAuditUsers.add(emerPlanAuditUser);
            }
        }

        if (CollectionUtils.isEmpty(emerPlanAuditUsers)) {
        	String emerPlanLevelId = eventDetailVO.getEmerPlanLevelId();
        	EmerPlanLevelAuditVO emerplanLevelAuditVO = eventMapper.selectAudit(new IdStringDTO(emerPlanLevelId));
        	if (emerplanLevelAuditVO != null && !CollectionUtils.isEmpty(emerplanLevelAuditVO.getUsers())) {
        		List<BasicUserVO> users = emerplanLevelAuditVO.getUsers();
    			String orgId = eventDetailVO.getOrgId();
    			for (BasicUserVO tmpVO : users) {
    				ProgressUserDTO emerPlanAuditUser = new ProgressUserDTO();
    				emerPlanAuditUser.setUserId(tmpVO.getUserId());
    				emerPlanAuditUser.setUserName(tmpVO.getUserName());
    				emerPlanAuditUser.setMobile(tmpVO.getMobile());
    				if (orgId.equals(tmpVO.getCompanyId())) {
    					emerPlanAuditUsers.add(emerPlanAuditUser);
    				}
    			}
        	}
        }

        if (!CollectionUtils.isEmpty(emerPlanAuditUsers)) {
            // 生成“预案启动”卡片
            String emerPlanId = eventDetailVO.getEmerPlanId();
            if (StringUtils.isNotBlank(emerPlanId)) {
                EmerPlanVO emerPlanVO = eventMapper.selectEmerPlan(new IdStringDTO(emerPlanId));
                String progressDesc = "该事件采用《" + emerPlanVO.getName() + "》，响应等级"
                        + EmerPlanLevelEnum.getCname(eventDetailVO.getLevel()) + "，等待领导审核解除。";
                // 新增启动预案卡片
                ProgressDTO startPlanProgressDTO = new ProgressDTO();
                startPlanProgressDTO.setCreateTime(serverTime);
                startPlanProgressDTO.setOccurTime(serverTime);
                startPlanProgressDTO.setProgressDesc(progressDesc);
                startPlanProgressDTO.setEventId(dto.getId());
                startPlanProgressDTO.setCreateUserId(userId);
                startPlanProgressDTO.setCardType(25);
                startPlanProgressDTO.setAtUsers(emerPlanAuditUsers);
                progressService.add(startPlanProgressDTO);
            }
        }
        // cancel_plan=2
        EventDealStatusDTO eventDealStatusDTO = new EventDealStatusDTO();
        eventDealStatusDTO.setEventId(eventDetailVO.getId());
        eventDealStatusDTO.setCancelPlan(2);
        eventMapper.updateCancelPlan(eventDealStatusDTO);

        return true;
    }

    public boolean updateCmsPublish(IdStringDTO dto, String userId) {
        long serverTime = System.currentTimeMillis() / 1000;
        ProgressDTO cmsPublishProgressDTO = new ProgressDTO();
        cmsPublishProgressDTO.setCreateTime(serverTime);
        cmsPublishProgressDTO.setOccurTime(serverTime);
        cmsPublishProgressDTO.setProgressDesc("存在占道情况，已发布情报板。");
        cmsPublishProgressDTO.setEventId(dto.getId());
        cmsPublishProgressDTO.setCreateUserId(userId);
        cmsPublishProgressDTO.setCardType(0);
        progressService.add(cmsPublishProgressDTO);
        Map<String, Object> map = new HashMap<>();
        map.put("eventId", dto.getId());
        map.put("cmsPublish", 1);
        map.put("cancelCmsPublish", 0);
        return eventMapper.updateCmsPublish(map) > 0;
    }

    public boolean updateCancelCmsPublish(IdStringDTO dto, String userId) {
        long serverTime = System.currentTimeMillis() / 1000;
        ProgressDTO cmsPublishProgressDTO = new ProgressDTO();
        cmsPublishProgressDTO.setCreateTime(serverTime);
        cmsPublishProgressDTO.setOccurTime(serverTime);
        cmsPublishProgressDTO.setProgressDesc("已撤销事件相关情报板发布。");
        cmsPublishProgressDTO.setEventId(dto.getId());
        cmsPublishProgressDTO.setCreateUserId(userId);
        cmsPublishProgressDTO.setCardType(0);
        progressService.add(cmsPublishProgressDTO);
        return eventMapper.updateCancelCmsPublish(dto) > 0;
    }

    public WorkingTableCountVO countWorkingTable(String userId) {
        return eventMapper.countWorkingTable(userId);
    }

    public PageInfo<EmerProgressVO> pageMyNoAuditEvent(PageDTO pageDTO, EventQueryDTO dto) {
    	Long startTime = dto.getStartTime();
    	if (startTime == null) {
    		dto.setStartTime(TimeUtils.getBackTimeLong(7));
    	}
        PageHelper.startPage(pageDTO.getPage(), pageDTO.getLimit());
        List<EmerProgressVO> list = eventMapper.selectMyNoAuditEvent(dto);
        PageInfo<EmerProgressVO> pageInfo = new PageInfo<>(list);
        if (!CollectionUtils.isEmpty(list)) {
            Map<String, Object> map = new HashMap<>();
            map.put("list", list);
            List<EventAuditResponseTimeVO> res = eventMapper.selectAuditResponseTimeList(map);
            for (EventAuditResponseTimeVO vo : res) {
                map.put(vo.getEventId(), vo);
            }
            for (EmerProgressVO vo : list) {
                vo.setAuditResponseTime((EventAuditResponseTimeVO) map.get(vo.getId()));
            }
        }
        return pageInfo;
    }

    public PageInfo<EmerProgressVO> pageMyAuditDealingEvent(PageDTO pageDTO, EventQueryDTO dto) {
    	Long startTime = dto.getStartTime();
    	if (startTime == null) {
    		dto.setStartTime(TimeUtils.getBackTimeLong(7));
    	}
        PageHelper.startPage(pageDTO.getPage(), pageDTO.getLimit());
        List<EmerProgressVO> list = eventMapper.selectMyAuditDealingEvent(dto);
        PageInfo<EmerProgressVO> pageInfo = new PageInfo<>(list);
        if (!CollectionUtils.isEmpty(list)) {
            Map<String, Object> map = new HashMap<>();
            map.put("list", list);
            List<EventAuditResponseTimeVO> res = eventMapper.selectAuditResponseTimeList(map);
            for (EventAuditResponseTimeVO vo : res) {
                map.put(vo.getEventId(), vo);
            }
            for (EmerProgressVO vo : list) {
                vo.setAuditResponseTime((EventAuditResponseTimeVO) map.get(vo.getId()));
            }
        }
        return pageInfo;
    }

    public PageInfo<EmerProgressVO> pageMyNoDealEvent(PageDTO pageDTO, EventQueryDTO dto) {
    	if (dto.getStartTime() == null) {
			dto.setStartTime(System.currentTimeMillis() / 1000 - day7);// 7天内的进展
			dto.setEndTime(System.currentTimeMillis() / 1000 + 3600);
		}
        PageHelper.startPage(pageDTO.getPage(), pageDTO.getLimit());
        List<EmerProgressVO> list = eventMapper.selectMyNoDealEvent(dto);
        PageInfo<EmerProgressVO> pageInfo = new PageInfo<>(list);
        if (!CollectionUtils.isEmpty(list)) {
            // 根据所属路段，增加路段中心简称
            List<OrganRoadVO> organRoadList = eventMapper.selectOrganRoad();
            for (EmerProgressVO vo : list) {
                vo.setOrgName(this.getRoadOrganShortName(organRoadList, vo.getRoadNo(), vo.getSourceId()));
            }
            Map<String, Object> map = new HashMap<>();
            map.put("list", list);
            List<EventAuditResponseTimeVO> res = eventMapper.selectAuditResponseTimeList(map);
            for (EventAuditResponseTimeVO vo : res) {
                map.put(vo.getEventId(), vo);
            }
            for (EmerProgressVO vo : list) {
                vo.setAuditResponseTime((EventAuditResponseTimeVO) map.get(vo.getId()));
            }
        }
        return pageInfo;
    }

    public PageInfo<EmerProgressVO> pageMyNoAnalysisEvent(PageDTO pageDTO, EventQueryDTO dto) {
        PageHelper.startPage(pageDTO.getPage(), pageDTO.getLimit());
        List<EmerProgressVO> list = eventMapper.selectMyNoAnalysisEvent(dto);
        PageInfo<EmerProgressVO> pageInfo = new PageInfo<>(list);
        if (!CollectionUtils.isEmpty(list)) {
            for (EmerProgressVO vo : list) {
                Long analysisTime = vo.getAnalysisTime();
                if (analysisTime == null) {
                    analysisTime = 0L;
                }
                analysisTime = analysisTime * 86400;
                vo.setAnalysisTime(analysisTime);
            }
        }
        return pageInfo;
    }

    public ResponseVO selectAnalysisAuthById(IdStringDTO dto, String userId) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", dto.getId());
        map.put("userId", userId);
        int rows = eventMapper.selectAnalysisAuthById(map);
        return new ResponseVO(rows > 0);
    }

    public GaodeAddrVO getAddress(RoadPileNoDTO dto) {
        // 参数校验
        if (dto.getRoadNo() == null || StringUtils.isBlank(dto.getDirectionNo())
                || StringUtils.isBlank(dto.getMilePost())) {
            throw new ArgumentException("请选择路段、方向和桩号");
        }
        // 获取经纬度
        RoadPileNoVO lngLatVO = feignClient.pileno2Lnglat(dto);
        if (lngLatVO == null || StringUtils.isBlank(lngLatVO.getLng()) || StringUtils.isBlank(lngLatVO.getLat())) {
            throw new ArgumentException("无法获取经纬度，请检查路段、桩号");
        }
        // 根据经纬度获取行政区域地址
        GaodeAddrVO lngLatToAddr = GaodeMapUtils.lngLatToAddr(lngLatVO.getLng(), lngLatVO.getLat());

        return lngLatToAddr;
    }

    public List<EventDealFullCloseVO> selectEventDealFullClose(IdStringDTO dto) {
        return eventMapper.selectEventDealFullClose(dto);
    }

    public NearestFacilityTipVO nearestRelatedFacility(RelatedFacilityDTO dto) {
        String milePost = dto.getMilePost().replace("K", "").replace("k", "").replace("+", "");
        dto.setMpValue(NumberUtils.toInt(milePost));
        NearestFacilityTipVO nearestRelatedFacility = new NearestFacilityTipVO();
        if (dto.getFacilityTypeNo() == 2) {
            List<FacilityTunnelDTO> facilityTunnelDTOS = eventMapper.selectFacilityTunnel(dto);
            Map<String, Integer> map = new HashMap<>();
            facilityTunnelDTOS.forEach(item -> {
                if (item.getEndMilePost() != null && item.getStartMilePost() != null) {
                    String startMilePost = item.getStartMilePost().replace("K", "").replace("k", "").replace("+", "");
                    String endMilePost = item.getEndMilePost().replace("K", "").replace("k", "").replace("+", "");
                    Integer startDistance = Math.abs(Integer.parseInt(startMilePost) - dto.getMpValue());
                    Integer endDistance = Math.abs(Integer.parseInt(endMilePost) - dto.getMpValue());
                    Integer distance = Math.min(startDistance, endDistance);
                    map.put(item.getFacilityNo(), distance);
                }
            });
            if (map.size() != 0) {
                List<Map.Entry<String, Integer>> list = new ArrayList(map.entrySet());
                Collections.sort(list, (o1, o2) -> (o1.getValue() - o2.getValue()));
                nearestRelatedFacility.setFacilityNo(list.get(0).getKey());
                nearestRelatedFacility.setDistance(list.get(0).getValue());
            }
        } else {
            nearestRelatedFacility = eventMapper.nearestRelatedFacility(dto);
        }
        return nearestRelatedFacility == null ? new NearestFacilityTipVO() : nearestRelatedFacility;
    }

    /**
     * 保存/暂存 应急救援事件的处置评估表
     *
     * @param evaVO  评估内容
     * @param userId 提交者userid
     * @return 保存/暂存结果
     */
    public ResponseVO saveEventEvaluation(EventEvaluationVO evaVO, String userId) {
        LOGGER.info("用户{}保存处置评估表，事件id{}", userId, evaVO.getEventId());
        ResponseVO result = new ResponseVO(false);

        if (StringUtils.isEmpty(userId)) {
            result.setMessage("用户信息错误");
            return result;
        }

        // 获取该用户的orgId
        UserSimpleVO userSimpleVO = feignClient.selectByUserId(userId);
        if (null == userSimpleVO) {
            LOGGER.warn("[saveEventEvaluation]用户{}不存在！", userId);
            result.setMessage("用户信息错误");
            return result;
        }

        if (null == evaVO.getState()) {
            result.setMessage("入参状态错误");
            return result;
        }

        // 判断操作类型
        switch (evaVO.getState()) {
            case EventEvaStateConstants.STATE_STAGING:
                // 暂存
                return stageEventEvaluation(evaVO, userSimpleVO);
            case EventEvaStateConstants.STATE_SAVED:
                // 保存
                evaVO.setState(EventEvaStateConstants.STATE_FINISHED);
                return stageEventEvaluation(evaVO, userSimpleVO);
            default:
                LOGGER.info("state状态为:{}，不做处理", evaVO.getState());
                result.setMessage("入参状态错误");
                return result;
        }
    }

    /**
     * 暂存应急救援事件的处置评估表
     *
     * @param evaVO        评估内容
     * @param userSimpleVO 提交的用户信息
     * @return 暂存结果
     */
    private ResponseVO stageEventEvaluation(EventEvaluationVO evaVO, UserSimpleVO userSimpleVO) {
        ResponseVO result = new ResponseVO(false);
        if (StringUtils.isEmpty(evaVO.getEventId()) || null == evaVO.getId()) {
            LOGGER.warn("保存时无eventId或recordId，保存失败");
            result.setMessage("缺少eventId或recordId");
            return result;
        }
        // 判断数据库中的记录状态，如果是暂存则可以编辑
        EventEvlRecordDTO queryDTO = new EventEvlRecordDTO();
        queryDTO.setEventId(evaVO.getEventId());
        EventEvlRecordVO queryVO = eventMapper.getEvlRecord(queryDTO);
        if (queryVO != null) {
            if (EventEvaStateConstants.STATE_STAGING != queryVO.getState()) {
                LOGGER.info("事件{}的评价表在库中状态为{}，禁止修改", evaVO.getEventId(), queryVO.getState());
                result.setMessage("该评价表已处于已提交状态，无法再次提交");
                return result;
            }
            // 数据库中状态为暂存，可以修改
        } else {
            // 数据库中无此记录，后续的update操作也将会失败，直接返回
            result.setMessage("数据操作失败");
            return result;
        }

        Date now = new Date();

        // 拼接record表数据
        EventEvlRecordVO recordVO = new EventEvlRecordVO();
        recordVO.setId(evaVO.getId());
        recordVO.setEventId(evaVO.getEventId());
        recordVO.setState(evaVO.getState());
        recordVO.setTotalScore(evaVO.getTotalScore());
        recordVO.setUpdateTime(now);
        // 更新record表
        int updateRecordResult = eventMapper.updateEventEvlRecord(recordVO);
        if (updateRecordResult <= 0) {
            result.setMessage("保存失败");
            return result;
        }

        List<EventEvaluationItemVO> itemVOS = evaVO.getEvl();
        if (itemVOS == null || itemVOS.size() == 0) {
            // evl数组为空，说明前端没有改动的行，直接返回修改成功
            return new ResponseVO(true);
        }

        // 拼接item表数据
        List<EventEvlItemVO> itemData = new ArrayList<>();
        for (int i = 0; i < evaVO.getEvl().size(); i++) {
            String projectName = itemVOS.get(i).getProjectName();
            Float percent = itemVOS.get(i).getPercent();
            List<EventEvaluationContentVO> contentVOS = itemVOS.get(i).getProject();

            for (int j = 0; j < contentVOS.size(); j++) {
                EventEvlItemVO itemVO = new EventEvlItemVO();
                itemVO.setId(contentVOS.get(j).getId());
                itemVO.setPercent(percent);
                itemVO.setEvlContent(contentVOS.get(j).getContent());
                itemVO.setProjectName(projectName);
                itemVO.setSort(contentVOS.get(j).getSort());
                itemVO.setScore(contentVOS.get(j).getScore());
                itemVO.setExperience(contentVOS.get(j).getExperience());
                itemVO.setProblem(contentVOS.get(j).getProblem());
                itemVO.setImprovement(contentVOS.get(j).getImprovement());
                itemVO.setEvlOrgName(userSimpleVO.getCompanyName() + "-" + userSimpleVO.getOrgName());
                itemVO.setEvlUserName(userSimpleVO.getUserName());
                itemVO.setRemarks(contentVOS.get(j).getRemarks());
                itemVO.setUpdateTime(now);
                itemData.add(itemVO);
            }
        }

        // 批量更新item表
        if (itemData.size() > 0) {
            eventMapper.updateEventEvlItem(itemData);
        }

        return new ResponseVO(true);
    }

    /**
     * 查询应急救援事件的处置评估表
     *
     * @param dto 查询条件，eventId
     * @return 处置评估表数据
     */
    public EventEvaluationVO getEventEvaluationData(EventEvlRecordDTO dto) {
        EventEvaluationVO result = null;
        String title = "";

        UserSimpleVO userSimpleVO = feignClient.selectByUserId(dto.getUserId());
        if (null == userSimpleVO) {
            LOGGER.warn("[getEventEvaluationData]用户{}不存在！", dto.getUserId());
            return new EventEvaluationVO();
        }
        dto.setOrgName(userSimpleVO.getOrgName());
        dto.setUserName(userSimpleVO.getUserName());

        // 获取事件的公司名称
        EventQueryDTO tempDTO = new EventQueryDTO();
        tempDTO.setId(dto.getEventId());
        EventDetailVO edVO = eventMapper.selectEventDetailById(tempDTO);
        title = "广西新发展交通集团有限公司突发事件应急处置评价表";


        // 判断数据库中是否有数据
        EventEvlRecordVO recordVO = eventMapper.getEvlRecord(dto);
        if (null == recordVO) {
            // 数据库中还没有记录，需先生成
            result = genEvlRecordByTemplate(dto);
            result.setTitle(title);
            return result;
        }
        List<EventEvlItemVO> itemVOList = eventMapper.getEvlItem(dto);

        result = assembleEventEvl(recordVO, itemVOList);
        result.setTitle(title);
        return result;
    }

    /**
     * 根据模板生成应急救援事件评估记录
     *
     * @param dto 处置评估表数据
     */
    private EventEvaluationVO genEvlRecordByTemplate(EventEvlRecordDTO dto) {
        Date now = new Date();
        // 获取模板数据
        List<EventEvlItemVO> templateItemList = eventMapper.getTemplate();

        // 组装初始化的评估记录
        EventEvlRecordVO recordVO = new EventEvlRecordVO();
        recordVO.setEventId(dto.getEventId());
        recordVO.setState(EventEvaStateConstants.STATE_STAGING);
        recordVO.setTotalScore(0F);
        recordVO.setCreateTime(now);
        recordVO.setUpdateTime(now);
        int recordResult = eventMapper.addEventEvlRecord(recordVO);

        // 组装初始化的评估项
        for (EventEvlItemVO itemVO : templateItemList) {
            itemVO.setScore(0);
            itemVO.setExperience("");
            itemVO.setProblem("");
            itemVO.setImprovement("");
            itemVO.setEvlOrgName("");
            itemVO.setEvlUserName("");
            itemVO.setRemarks("");
            itemVO.setCreateTime(now);
            itemVO.setUpdateTime(null);
        }
        int itemResult = eventMapper.addEventEvlItem(templateItemList);

        // 组装关联表
        List<EventEvlRecordItemDTO> recordItemDTOList = new ArrayList<>();
        for (int i = 0; i < templateItemList.size(); i++) {
            EventEvlRecordItemDTO recordItemDTO = new EventEvlRecordItemDTO();
            recordItemDTO.setRecordId(recordVO.getId());
            recordItemDTO.setItemId(templateItemList.get(i).getId());
            recordItemDTOList.add(recordItemDTO);
        }
        int recordItemResult = eventMapper.addEventEvlRecordItem(recordItemDTOList);

        return assembleEventEvl(recordVO, templateItemList);
    }

    /**
     * 根据record和item数据拼接应急救援事件的处置评估表
     *
     * @param recordVO   event_evl_record表数据
     * @param itemVOList event_evl_item表数据
     * @return 处置评估表数据
     */
    private EventEvaluationVO assembleEventEvl(EventEvlRecordVO recordVO, List<EventEvlItemVO> itemVOList) {
        EventEvaluationVO result = new EventEvaluationVO();

        List<EventEvaluationContentVO> contentVOList = new ArrayList<>();

        List<EventEvaluationItemVO> evl = new ArrayList<>();
        EventEvaluationItemVO data = new EventEvaluationItemVO();
        for (int i = 0; i < itemVOList.size(); i++) {
            data.setPercent(itemVOList.get(i).getPercent());
            data.setProjectName(itemVOList.get(i).getProjectName());
            EventEvaluationContentVO contentVO = new EventEvaluationContentVO();
            contentVO.setId(itemVOList.get(i).getId());
            contentVO.setContent(itemVOList.get(i).getEvlContent());
            contentVO.setExperience(itemVOList.get(i).getExperience());
            contentVO.setProblem(itemVOList.get(i).getProblem());
            contentVO.setImprovement(itemVOList.get(i).getImprovement());
            contentVO.setEvlOrg(itemVOList.get(i).getEvlOrgName());
            contentVO.setEvlUser(itemVOList.get(i).getEvlUserName());
            if (itemVOList.get(i).getUpdateTime() != null) {
                contentVO.setEvlTime(TimeUtils.formatDate(itemVOList.get(i).getUpdateTime(), TimeUtils.FULL_TIME));
            } else {
                contentVO.setEvlTime("");
            }
            contentVO.setRemarks(itemVOList.get(i).getRemarks());
            contentVO.setScore(itemVOList.get(i).getScore());
            contentVO.setSort(itemVOList.get(i).getSort());
            contentVOList.add(contentVO);

            if (StringUtils.isNotEmpty(itemVOList.get(i).getProjectName())) {
                if (i + 1 >= itemVOList.size()
                        || !itemVOList.get(i).getProjectName().equals(itemVOList.get(i + 1).getProjectName())) {
                    // 下一条记录更换project或结束
                    data.setProject(contentVOList);
                    evl.add(data);
                    data = new EventEvaluationItemVO();
                    contentVOList = new ArrayList<>();
                }
            }
        }

        result.setId(recordVO.getId());
        result.setState(recordVO.getState());
        result.setEventId(recordVO.getEventId());
        result.setTotalScore(recordVO.getTotalScore());
        result.setEvl(evl);

        return result;
    }

    public Integer selectEventSource(IdStringDTO idStringDTO) {
        return eventMapper.selectEventSource(idStringDTO);
    }

    public ResponseVO updateEvent96333ResultCode(Event96333OrderSuccessCodeDTO dto) {
        // 96333工单提交处理结果成功码同步event_96333_sync
        Map<String, Object> map = new HashMap<>();
        map.put("eventNo", dto.getEventNo());
        map.put("success", dto.getSuccess());
        map.put("createTime", TimeUtils.getTimeString());
        int row = eventMapper.updateEvent96333ResultCode(map);
        return new ResponseVO(row);
    }

    public ResponseVO updateEvent96333VisitCode(Event96333OrderSuccessCodeDTO dto) {
        // 96333工单提交处理结果成功码同步event_96333_sync
        Map<String, Object> map = new HashMap<>();
        map.put("eventNo", dto.getEventNo());
        map.put("success", dto.getSuccess());
        map.put("createTime", TimeUtils.getTimeString());
        int row = eventMapper.updateEvent96333VisitCode(map);
        return new ResponseVO(row);
    }
    
    public ResponseVO cancel96333Order(Event96333NewOrderDTO dto) {
    	EventDetailVO eventDetailVO = eventMapper.selectBaseInfoByEventNo(new IdStringDTO(dto.getRecordId()));
    	if (eventDetailVO == null) {
    		return new ResponseVO(true);
    	}
    	// 删除本地工单
    	boolean ret = eventMapper.delete(new IdStringDTO(eventDetailVO.getId())) > 0;
    	return new ResponseVO(ret);
    }

    public ResponseVO update96333OrderDealOrg(Event96333NewOrderDTO dto) {
    	IdStringDTO idStringDTO = new IdStringDTO(dto.getRecordId());
        EventDetailVO eventDetailVO = eventMapper.selectBaseInfoByEventNo(idStringDTO);
        if (eventDetailVO == null) {
            return new ResponseVO(true);
        }
        dto.setId(eventDetailVO.getId());
        String orgId = OrgSourceIdEnum.getName(dto.getSourceId());
        dto.setSourceDept(orgId);
        String orgShortName = OrgSourceIdEnum.getShortName(dto.getSourceId());
        dto.setSourceArea(orgShortName);
        // 更新本地工单处理部门和sourceId
        boolean ret = eventMapper.update96333OrderDealOrg(dto) > 0;
        if (ret) {
        	Integer eventType = eventDetailVO.getEventType();
        	if (eventType != null && eventType == 4) {// 咨询工单
        		eventMapper.updateEventZxDealOrg(dto);
        	}
        }

        return new ResponseVO(ret);
    }

    public RoadPileNoVO lnglatToMilePost(LngLatDTO dto) {
        RoadPileNoVO lnglatToMilePost = eventMapper.lnglatToMilePost(dto);
        if (lnglatToMilePost != null) {
            Integer roadNo = lnglatToMilePost.getRoadNo();
            String milePost = lnglatToMilePost.getMilePost();
            if (roadNo != null && milePost != null) {
                int mpValue = NumberUtils.toInt(milePost.replace("K", "").replace("+", ""));
                // 匹配救援电话
                Map<String, Object> map = new HashMap<>();
                map.put("roadNo", roadNo);
                map.put("mpValue", mpValue);
                List<RoadRescuePhoneVO> list = eventMapper.selectRoadRescuePhone(map);
                lnglatToMilePost.setRoadRescuePhones(list);
            }
        }
        return lnglatToMilePost;
    }

    @Transactional
    public boolean addDDForGzh(DDForGzhDTO dto) {
        dto.setId(UUID.randomUUID().toString());
        Long createTime = System.currentTimeMillis();
        String yyMMddHHmmssSSS = TimeUtils.getTimeString(TimeUtils.DATETIME, createTime);
        String eventNo = "DD-" + yyMMddHHmmssSSS;
        dto.setEventNo(eventNo);
//        有故障车辆/交通事故事件，需要拖车救援/需要医疗救助/有路损或污染
        Integer gzhEventType = dto.getGzhEventType();
        dto.setEventType(1);
        dto.setEventTwoType(6);
        StringBuffer detail = new StringBuffer();
        if (gzhEventType == 1) {// 交通事故
            dto.setEventThreeType(79);// 轻微交通事故
            detail.append("有交通事故事件");
        } else {// 故障车辆
            dto.setEventThreeType(11);// 车辆救援
            detail.append("有故障车辆事件");
        }
        createTime = createTime / 1000;
        dto.setReportTime(createTime);
        dto.setLevel(10);
        dto.setSource(EventSourceEnum.SOURCE_3.getSourceValue());// 公众号
        dto.setEventStatus(1);
        dto.setDealStatus(2);
        dto.setCreateTime(createTime);
        // 根据路段roadNo匹配责任公司
        if (dto.getRoadNo() != null) {
            OrganizationRoadVO organizationRoadVO = feignClient.selectByRoadNo(new IdIntegerDTO(dto.getRoadNo()));
            if (organizationRoadVO != null) {
                dto.setOrgId(organizationRoadVO.getOrgId());
                dto.setSourceId(organizationRoadVO.getSourceId());
            }
        }
        dto.setDelStatus(0);
        dto.setReportSourceKey(7);
        List<Integer> rescueOption = dto.getRescueOption();
        if (!CollectionUtils.isEmpty(rescueOption)) {
            for (Integer item : rescueOption) {
                if (item == 1) {
                    detail.append("，需要拖车救援");
                } else if (item == 2) {
                    detail.append("，需要医疗救助");
                } else if (item == 3) {
                    detail.append("，有路损或污染");
                }
            }
        }
        dto.setBriefDesc(detail.toString());
        List<Integer> occupiedLanes = dto.getOccupiedLanes();
        if (!CollectionUtils.isEmpty(occupiedLanes)) {
            EmerRescueDTO emerRescueDTO = new EmerRescueDTO();
            emerRescueDTO.setId(dto.getId());
            emerRescueDTO.setOccupiedLanes(occupiedLanes);
            eventMapper.addOccupiedLane(emerRescueDTO);
        }
        List<Integer> attachs = dto.getAttachs();
        if (!CollectionUtils.isEmpty(attachs)) {
            EmerRescueDTO emerRescueDTO = new EmerRescueDTO();
            emerRescueDTO.setId(dto.getId());
            emerRescueDTO.setAttachs(attachs);
            eventMapper.batchUpdateAttachs(emerRescueDTO);
        }
        int ret = eventMapper.addDDForGzh(dto);
        ret = eventMapper.addEventUserGzh(dto);

        if (ret > 0) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    EventMessageDTO eventMessageDTO = new EventMessageDTO();
                    eventMessageDTO.setWebsocketType("addEvent");
                    eventMessageDTO.setId(dto.getId());
                    eventMessageDTO.setSource(dto.getSource());
                    eventMessageDTO.setEventType(dto.getEventType());
                    eventMessageDTO.setEventTwoType(dto.getEventTwoType());
                    eventMessageDTO.setEventThreeType(dto.getEventThreeType());
                    eventMessageDTO.setBriefDesc(dto.getBriefDesc());
                    eventMessageDTO.setOrgId(dto.getOrgId());
                    eventMessageDTO.setDealStatus(dto.getDealStatus());

                    // 罗城中心，有公众号上报的事件
                    Integer sourceId = dto.getSourceId();
                    String name = SourceIdEnum.getName(sourceId);
                    if (name != null) {
                        name = name + "中心，";
                        if (sourceId == 1) {
                            String orgId = dto.getOrgId();
                            name = name + OrganShortNameEnum.getName(orgId) + "中心，";
                        }
                    } else {
                        name = "";
                    }
                    eventMessageDTO.setVoicePrompt(name + "有公众号上报的事件");
                    itsWebSocketFeignClient.push96333Event(eventMessageDTO);
                }
            }).start();
        }

        return ret > 0;
    }

    /**
     * 北软桂享高速一键救援业务，新增救援工单（大部分沿用公众号新增一键救援的逻辑）
     *
     * @param ddIntegralDTO 救援信息
     * @return eventId
     */
    public String addDDIntegral(@RequestBody DDIntegralDTO ddIntegralDTO) {
        LOGGER.info("addIntegralDTO={}", new Gson().toJson(ddIntegralDTO));
        DDForGzhDTO dto = new DDForGzhDTO();

        String eventId = UUID.randomUUID().toString();
        dto.setId(eventId);
        long createTime = System.currentTimeMillis();
        String yyMMddHHmmssSSS = TimeUtils.getTimeString(TimeUtils.DATETIME, createTime);
        String eventNo = "DD-" + yyMMddHHmmssSSS;
        dto.setEventNo(eventNo);
//        有故障车辆/交通事故事件，需要拖车救援/需要医疗救助/有路损或污染
        Integer eventType = ddIntegralDTO.getEventType();
        dto.setEventType(1);
        dto.setEventTwoType(6);
        StringBuilder detail = new StringBuilder();
        detail.append("接桂享高速报，");
        detail.append(ddIntegralDTO.getRoadName() + "，桩号：");
        detail.append(ddIntegralDTO.getMilePost() + "，方向：");
        detail.append(ddIntegralDTO.getDirection());

        if (eventType == 1) {// 交通事故
            dto.setEventThreeType(79);// 轻微交通事故
            detail.append("，有交通事故事件");
        } else {// 故障车辆
            dto.setEventThreeType(11);// 车辆救援
            detail.append("，有故障车辆事件");
        }
        detail.append("，上报人：" + ddIntegralDTO.getReporter());
        detail.append("，车牌号：" + ddIntegralDTO.getPlateNumber());
        detail.append("，车主电话：" + ddIntegralDTO.getPhoneNum());

        createTime = createTime / 1000;
        dto.setReportTime(createTime);
        dto.setLevel(10);
        dto.setSource(EventSourceEnum.SOURCE_4.getSourceValue());// 桂享高速
        dto.setEventStatus(1);
        dto.setDealStatus(2);
        dto.setCreateTime(createTime);
        // 根据路段roadNo匹配责任公司
        if (ddIntegralDTO.getRoadNo() != null) {
            dto.setRoadNo(ddIntegralDTO.getRoadNo());
            OrganizationRoadVO organizationRoadVO = feignClient.selectByRoadNo(new IdIntegerDTO(ddIntegralDTO.getRoadNo()));
            if (organizationRoadVO != null) {
                dto.setOrgId(organizationRoadVO.getOrgId());
                dto.setSourceId(organizationRoadVO.getSourceId());
            }
        }
        dto.setDelStatus(0);
        dto.setReportSourceKey(9); // 接报来源:桂享高速
        dto.setMilePost(ddIntegralDTO.getMilePost());
        dto.setReportMan(ddIntegralDTO.getReporter());
        dto.setReportManTel(ddIntegralDTO.getPhoneNum());
        dto.setLat(ddIntegralDTO.getLat());
        dto.setLng(ddIntegralDTO.getLng());
        dto.setCarPlate(ddIntegralDTO.getPlateNumber());
        dto.setBriefDesc(detail.toString());

        // 获取上下行
        List<DirectionVO> directionVOS = eventMapper.selectDirectionByRoad(ddIntegralDTO.getRoadNo());
        if (directionVOS != null && directionVOS.size() > 0) {
            String roadLine = "direction"; // 判断传入的参数是上下行
            if (ddIntegralDTO.getDirection().contains("上行")) {
                roadLine = "上行";
            } else if (ddIntegralDTO.getDirection().contains("下行")) {
                roadLine = "下行";
            }

            for (DirectionVO directionVO : directionVOS) {
                if (directionVO.getDirectionName().contains(roadLine)) {
                    dto.setDirectionNo(directionVO.getDirectionNo());
                    break;
                }
            }
        }


        List<Integer> occupiedLanes = ddIntegralDTO.getOccupiedLane();
        if (!CollectionUtils.isEmpty(occupiedLanes)) {
            EmerRescueDTO emerRescueDTO = new EmerRescueDTO();
            emerRescueDTO.setId(eventId);
            emerRescueDTO.setOccupiedLanes(occupiedLanes);
            eventMapper.addOccupiedLane(emerRescueDTO);
        }
//		List<Integer> attachs = dto.getAttachs();
//		if (!CollectionUtils.isEmpty(attachs)) {
//			EmerRescueDTO emerRescueDTO = new EmerRescueDTO();
//			emerRescueDTO.setId(dto.getId());
//			emerRescueDTO.setAttachs(attachs);
//			eventMapper.batchUpdateAttachs(emerRescueDTO);
//		}
        // 保存至event表
        int retEvent = eventMapper.addDDForGzh(dto);

        // 保存至event_dd表
        EventDDDTO eventDDDto = new EventDDDTO();
        eventDDDto.setEventId(eventId);
        eventDDDto.setAccidentCarNum(1);
        eventDDDto.setIntegralOrderId(ddIntegralDTO.getOrderId());
        int retDD = eventMapper.addEventDD(eventDDDto);

        if (retEvent > 0 && retDD > 0) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    EventMessageDTO eventMessageDTO = new EventMessageDTO();
                    eventMessageDTO.setWebsocketType("addEvent");
                    eventMessageDTO.setId(dto.getId());
                    eventMessageDTO.setSource(dto.getSource());
                    eventMessageDTO.setEventType(dto.getEventType());
                    eventMessageDTO.setEventTwoType(dto.getEventTwoType());
                    eventMessageDTO.setEventThreeType(dto.getEventThreeType());
                    eventMessageDTO.setBriefDesc(dto.getBriefDesc());
                    eventMessageDTO.setOrgId(dto.getOrgId());
                    eventMessageDTO.setDealStatus(dto.getDealStatus());

                    // 罗城中心，有桂享高速上报的事件
                    Integer sourceId = dto.getSourceId();
                    String name = SourceIdEnum.getShortName(sourceId);
                    if (name != null) {
                        name = name + "，";
                        if (sourceId == 1) {
                            String orgId = dto.getOrgId();
                            name = name + OrganShortNameEnum.getName(orgId) + "，";
                        }
                    } else {
                        name = "";
                    }
                    eventMessageDTO.setVoicePrompt(name + "有桂享高速上报的事件");
                    itsWebSocketFeignClient.push96333Event(eventMessageDTO);
                }
            }).start();
        }

        return eventId;
    }

    /**
     * 修改桂享高速一键救援拖车进度状态
     *
     * @param ddRescuerInfoDTO
     * @return
     */
    public Integer updateDDIntegral(DDRescuerInfoDTO ddRescuerInfoDTO) {
        LOGGER.info("updateDDIntegral={}", new Gson().toJson(ddRescuerInfoDTO));
        // 更新event_dd表
        EventDDDTO eventDDDTO = new EventDDDTO();
        eventDDDTO.setEventId(ddRescuerInfoDTO.getEventId());
        eventDDDTO.setRescueStatus(ddRescuerInfoDTO.getStatus());
        int retDD = eventMapper.updateEventDD(eventDDDTO);
        if (retDD <= 0) {
            LOGGER.info("updateDDIntegral retDD={}", retDD);
            return 0;
        }

        EventDetailVO eventDetailVO = selectBaseInfoByEventId(new IdStringDTO(ddRescuerInfoDTO.getEventId()));

        String progressDesc = "救援拖车待接单";
        if (ddRescuerInfoDTO.getStatus() == 1) {
            progressDesc = "救援拖车已接单";
        } else if (ddRescuerInfoDTO.getStatus() == 2) {
            progressDesc = "救援拖车已结束";
        } else if (ddRescuerInfoDTO.getStatus() == 3) {
           //修改初报信息
            String occurTimeStr = ddRescuerInfoDTO.getOccurTime()+"000";
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String sd = sdf.format(Long.valueOf(occurTimeStr)); // 时间戳转换成时间
            String appendReport="  "+sd+"【最新消息】来自【桂享高速】：拖车订单取消。";
            String newBriefDesc = eventDetailVO.getBriefDesc()+appendReport;
            EmerRescueDTO dto =new EmerRescueDTO();
            dto.setId(ddRescuerInfoDTO.getEventId());
            dto.setBriefDesc(newBriefDesc);
          return  eventMapper.updateInitialReportBrief(dto);

        }

        // 新增一条新进展
        long now = System.currentTimeMillis() / 1000;
        ProgressDTO distributeProgress = new ProgressDTO();
        distributeProgress.setCreateTime(now);
        distributeProgress.setOccurTime(now);
        distributeProgress.setProgressDesc(progressDesc);
        distributeProgress.setEventId(ddRescuerInfoDTO.getEventId());
        distributeProgress.setCreateUserId(eventDetailVO.getRecordManId());
        distributeProgress.setCardType(0);
        progressService.add(distributeProgress);

        return 1;
    }

    /**
     * 桂享高速一键救援拖车进展消息新增
     *
     * @param ddRescuerInfoDTO
     * @return
     */
    public Integer addDDProgress(DDRescuerInfoDTO ddRescuerInfoDTO) {
        LOGGER.info("addDDProgress={}", new Gson().toJson(ddRescuerInfoDTO));
        String eventId =ddRescuerInfoDTO.getEventId();
        EventDetailVO eventDetailVO = selectBaseInfoByEventId(new IdStringDTO(eventId));
        //判断是否处于处置中的状态
        if(eventDetailVO.getDealStatus()!=3) {
            LOGGER.info("事件不在处置中,EventId={}",eventId);
            return 0;
        }
        String progressDesc = ddRescuerInfoDTO.getProgress(); //获取桂享高速传过来的进展消息
        // 新增一条新进展
        long now = System.currentTimeMillis() / 1000;
        ProgressDTO distributeProgress = new ProgressDTO();
        distributeProgress.setCreateTime(now);
      //  distributeProgress.setOccurTime(now);
        distributeProgress.setOccurTime(ddRescuerInfoDTO.getOccurTime()); //进展发生时间为一键救援推送过来的时间
        distributeProgress.setProgressDesc(progressDesc);
        distributeProgress.setEventId(eventId);
        distributeProgress.setCreateUserId(eventDetailVO.getRecordManId());
        distributeProgress.setCardType(0);
       if(!progressService.add(distributeProgress)) //进展添加失败
            return 0;
      return 1;
    }

    /**
     * 桂享高速一键救援拖车进展图片新增
     *
     * @param ddRescuerInfoDTO
     * @return
     */
    @Transactional
    public Integer addDDProgressImg(DDRescuerInfoDTO ddRescuerInfoDTO) {
      //  LOGGER.info("addDDProgressImg={}", new Gson().toJson(ddRescuerInfoDTO));
        String eventId = ddRescuerInfoDTO.getEventId();
        EventDetailVO eventDetailVO = selectBaseInfoByEventId(new IdStringDTO(eventId));
        int deal_status = eventDetailVO.getDealStatus();
        if ( deal_status!= 3 && deal_status != 2) { //进展图片推送
            LOGGER.info("事件不在待处置或处置中状态,图片推送失败,EventId={}", eventId);
            return 0;
        }
        List<String> images = ddRescuerInfoDTO.getImgbase64Str();
        List<String> filenames = ddRescuerInfoDTO.getImgFileName();
        List<Integer> attachs = new ArrayList<>();
        for (int i = 0; i < images.size(); i++) { //上传每个图片
            //1.获取桂享高速传过来的base64编码的图片解码为byte[]字节流
            byte[] imgbyte = java.util.Base64.getDecoder().decode(images.get(i));
            //2.上传到阿里云oss
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");// 把文件按照日期进行分类
            Date date = new Date();
            String dir = format.format(date); // 用户上传文件时指定的前缀，按月存储。
            String filePath = dir + '/' + UUID.randomUUID().toString(); //随机生成oss文件名
            AttachDTO attachDTO = OssUtils.remoteFileUpload(ossConfig, filePath, imgbyte, 30000);
            if (attachDTO == null) {
                LOGGER.info("图片上传失败," + "eventId:" + ddRescuerInfoDTO.getEventId() + "filename" + ddRescuerInfoDTO.getImgFileName());
                return 0;
            }
            //3.图片附件绑定事件
            //(1)附件添加到progress_attach表
            attachDTO.setFileName(filenames.get(i)); //桂享高速传来的文件名
            attachDTO.setDiskFileName(attachDTO.getDiskFileName()); //oss中的文件名
            //   attachDTO.setDigest(attachDTO.getDigest());
            attachDTO.setFileSize(attachDTO.getFileSize());
            //   attachDTO.setDiskDirectory(attachDTO.getDiskDirectory());
            attachDTO.setContentType("image/jpeg"); //附件类型为图片
            attachDTO.setCreateTime(System.currentTimeMillis() / 1000);
            //添加附件到数据库表
            if(deal_status==3) {
                progressMapper.addAttach(attachDTO);
            }
            else {
                eventMapper.addAttach(attachDTO);
            }
            if (attachDTO.getId() == null) {
                LOGGER.info("附件添加失败,attachDTO={}", new Gson().toJson(attachDTO));
                return 0;
            }
            attachs.add(attachDTO.getId());//获取附件id
        }
        LOGGER.info("附件批量添加成功,attachs={}", new Gson().toJson(attachs));

        if (deal_status == 2) { //待处置
            EmerRescueDTO dto = new EmerRescueDTO();
            dto.setId(eventId);
            dto.setAttachs(attachs);
            int result = eventMapper.updateAttachs(dto); //更新事件附件为attachs
            if (result == 0) {
                LOGGER.info("附件绑定事件失败,attachDTO={}", new Gson().toJson(attachs));
                return 0;
            }
            LOGGER.info("附件绑定事件成功,attachDTO={}", new Gson().toJson(attachs));
            return 1;
        } else  { //处置中
            //(2)新增进展
            ProgressDTO progressDTO = new ProgressDTO(); //构建进展信息
            progressDTO.setAttachs(attachs);
            progressDTO.setEventId(ddRescuerInfoDTO.getEventId());
            long now = System.currentTimeMillis() / 1000;
            progressDTO.setCreateTime(now);
          //  progressDTO.setOccurTime(now);
            progressDTO.setOccurTime(ddRescuerInfoDTO.getOccurTime()); //一键救援推送的发生时间
            progressDTO.setProgressDesc(ddRescuerInfoDTO.getProgress());
            progressDTO.setCreateUserId(eventDetailVO.getRecordManId());
            boolean addresult = progressService.add(progressDTO);
            if (addresult) {
                LOGGER.info("进展添加成功,progessDTO={}", new Gson().toJson(progressDTO));
                return 1;
            }
            LOGGER.info("进展添加失败,progessDTO={}", new Gson().toJson(progressDTO));
            return 0;
        }
    }

    /**
     * 桂享高速一键救援拖车取消订单
     *
     * @param ddRescuerInfoDTO
     * @return
     */
    public Integer cancelDDIntegral(DDRescuerInfoDTO ddRescuerInfoDTO) {
        String eventId = ddRescuerInfoDTO.getEventId();
        EventDetailVO eventDetailVO = selectBaseInfoByEventId(new IdStringDTO(eventId));
        int deal_status = eventDetailVO.getDealStatus();
        if ( deal_status!= 3 && deal_status != 2) { //处置中或者待处置
            LOGGER.info("事件不在待处置或处置中状态,取消失败,EventId={}", eventId);
            return 0;
        }

        if(deal_status ==3) //已派单，取消订单通过新增进展的方式提示
        {
            String progress = "一键救援拖车订单已取消";
            ddRescuerInfoDTO.setProgress(progress);
            int addProresult = addDDProgress(ddRescuerInfoDTO);
            if (addProresult == 0) //进展添加失败
                return addProresult;
        }
        return updateDDIntegral(ddRescuerInfoDTO); //更新event_dd对应的订单状态status
    }

    /**
     * @描述 查询公众号个人提交的救援记录
     */
    public List<DDForGzhVO> selectOwnDDForGzh(IdStringDTO dto, String openid) {
        String id = dto.getId();
        if (StringUtils.isNotBlank(openid) && openid.equals(id)) {
            List<DDForGzhVO> ret = eventMapper.selectOwnDDForGzh(dto);
            for (DDForGzhVO ddForGzhVO : ret) {
                Integer eventThreeType = ddForGzhVO.getEventThreeType();
                if (eventThreeType != null && eventThreeType == 79) {
                    ddForGzhVO.setGzhEventType(1);
                } else {
                    ddForGzhVO.setGzhEventType(2);
                }
            }
            return ret;
        }
        throw new ArgumentException("参数不正确，请检查openid");
    }

    public List<OngoingDDForGzhVO> selectOngoingDDForGzh() {
        return eventMapper.selectOngoingDDForGzh();
    }

    public EventDetailVO selectBaseInfoByEventId(IdStringDTO idStringDTO) {
        return eventMapper.selectBaseInfoByEventId(idStringDTO);
    }

    public List<String> selectOrgIdByRoles(String roles) {
        Map<String, Object> map = new HashMap<>();
        map.put("roleIds", Arrays.asList(roles.split(";")));
        return eventMapper.selectOrgIdByRoles(map);
    }

    public Object statAvgDealTimeGroupOrgId(String role, EventAnalysisDTO eventAnalysisDTO) {
        long time20230401 = TimeUtils.toLong("2023-04-01 00:00:00", TimeUtils.FULL_TIME);// 特殊处理，业主要求
        Long startTime = eventAnalysisDTO.getStartTime();
        if (startTime == null || startTime < time20230401) {
            eventAnalysisDTO.setStartTime(time20230401);
        }

        List<EventStatDealTimeVO> list = eventMapper.statAvgDealTimeGroupOrgId(eventAnalysisDTO);
        int eventTotal = 0;
        Long totalDealTime = 0L;
        if (CollectionUtils.isEmpty(list)) {
            Map<String, Object> ret = new HashMap<>();
            ret.put("avgDealTime", 0);
            ret.put("orgDealTime", new ArrayList<>());
            return ret;
        }
        for (int i = (list.size() - 1); i >= 0; i--) {
            if (list.get(i).getDealTime() == null) {
                list.remove(i);
            }
        }
        if (CollectionUtils.isEmpty(list)) {
            Map<String, Object> ret = new HashMap<>();
            ret.put("avgDealTime", 0);
            ret.put("orgDealTime", new ArrayList<>());
            return ret;
        }
        for (EventStatDealTimeVO vo : list) {
            if (vo.getDealTime() == null) {
                continue;
            }
            Integer total = vo.getTotal();
            if (total != null) {
                eventTotal += total;
            }
            totalDealTime += vo.getDealTime();
            Long dealTime = vo.getDealTime();
            dealTime = dealTime / total;
            vo.setDealTime(dealTime);
            String orgName = OrganShortNameEnum.getName(vo.getOrgId());
            vo.setOrgName(orgName);
        }
        Long avgDealTime = totalDealTime / eventTotal;

        List<String> orgIds = selectOrgIdByRoles(role);
        List<EventStatDealTimeVO> filteredList = new ArrayList<>();
        for (EventStatDealTimeVO tmp : list) {
            if (orgIds.contains(tmp.getOrgId())) {
                filteredList.add(tmp);
            }
        }

        Map<String, Object> ret = new HashMap<>();
        ret.put("avgDealTime", avgDealTime);
        ret.put("orgDealTime", filteredList);
        return ret;
    }

    public List<ChargePolicyVO> selectChargePolicyForGzh() {
        return eventMapper.selectChargePolicyForGzh();
    }

    public List<PublicInfoVO> selectPublicInfoForGzh() {
        return eventMapper.selectPublicInfoForGzh();
    }

    @Transactional
    public boolean nostatDealReason(NostatDealReasonDTO dto) {
        boolean ret = eventMapper.nostatDealReason(dto) > 0;
        if (ret) {
            // 进展留痕，申请该事件不计入处置效率考核，原因：nostatDealReason
            ProgressDTO progressDTO = new ProgressDTO();
            progressDTO.setProgressDesc("申请该事件不计入处置效率考核，原因：" + dto.getNostatDealReason());
            progressDTO.setEventId(dto.getId());
            progressDTO.setOccurTime(System.currentTimeMillis() / 1000);
            progressDTO.setTheme(1);
            progressDTO.setCreateUserId(dto.getUserId());
            progressService.add(progressDTO);
        }
        return ret;
    }

    public EventNostatDealReasonVO selectNostatDealReason(IdStringDTO dto) {
        EventNostatDealReasonVO ret = new EventNostatDealReasonVO();
//        boolean checkPass = eventMapper.checkStatDealEventType(dto) > 0;
//        if (!checkPass) {
//            ret.setStatDealTime(1);
//            ret.setEventTimeoutButton(1);
//            ret.setTip("该事件类型不计入考核");
//            return ret;
//        }
        EventTimeoutAuditVO eventTimeoutAuditVO = eventFileMapper.selectEventTimeoutAuditByEventId(dto);
        ret = eventMapper.selectNostatDealReason(dto);
        if (ret == null) {
            ret = new EventNostatDealReasonVO();
            ret.setStatDealTime(0);
        }
        if (ret != null && ret.getStatDealTime() == 1) {// 不计入考核
            ret.setEventTimeoutButton(1);
            ret.setTip("该事件申诉通过，处置时长不计入考核");
            return ret;
        }
        if (eventTimeoutAuditVO == null) {
            ret.setEventTimeoutButton(0);
            ret.setStatDealTime(0);
            ret.setTip("该事件处置时长计入考核");
            return ret;
        }
        ret.setEventTimeoutButton(1);
        Integer auditStatus = eventTimeoutAuditVO.getAuditStatus();
        if (auditStatus == null) {
            auditStatus = 0;
        }
        if (auditStatus == 0) {
            ret.setStatDealTime(0);// 待审核
            ret.setTip("该事件正在等待申诉");
        } else if (auditStatus == 1) {
            ret.setStatDealTime(1);// 审核通过
            ret.setTip("该事件申诉通过，处置时长不计入考核");
        } else if (auditStatus == 2) {
            ret.setStatDealTime(0);// 审核不通过
            ret.setTip("该事件申诉不通过，处置时长计入考核");
        }
        return ret;
    }

    public String publishToAmap(AmapEventPublishDTO dto) {
        String url = "https://et-api.amap.com/eventpublish/add";
        long now = System.currentTimeMillis() / 1000;
        Map<String, Object> map = new HashMap<>();
        map.put("adcode", dto.getAdcode());
        map.put("clientKey", dto.getClientKey());
        map.put("timestamp", now);
        dto.setTimestamp(now + "");
        map.put("sourceId", dto.getSourceId());
        map.put("id", dto.getId());
        map.put("stateFlag", dto.getStateFlag());
        map.put("type", dto.getType());
        map.put("locType", dto.getLocType());
        map.put("locs", dto.getLocs());
        map.put("startDate", dto.getStartDate());
        map.put("desc", dto.getDesc());
        String content = DictSortUtils.getOrder(map, false);
        System.out.println(content);
        try {
            String hmacSHA256 = HmacSHA256Utils.hmacSHA256("f6961ed2893af87907f0363757ff9388", content);
            String urlParam = DictSortUtils.getOrderUrlParam(map);
            urlParam = urlParam + "&digest=" + hmacSHA256;
            System.out.println(urlParam);
            dto.setDigest(hmacSHA256);
            String ret = HttpClientUtils.get(url + "?" + urlParam, null, 10000);
            return ret;
        } catch (Exception e) {
            e.printStackTrace();
            return e.getMessage();
        }
    }

    public List<OrganizationVO> orgRoadDirection(String userId, List<OrganizationVO> organizationTree, String[] roles) {
        List<String> leafs = new ArrayList<>();
        recursiveOrganizationTree(organizationTree, leafs);
        List<String> roleList = Arrays.asList(roles);
        Map<String, Object> map = new HashMap<>();
        map.put("roles", roleList);
        UserSimpleVO loginUser = feignClient.selectByUserId(userId);
        String companyId = loginUser.getCompanyId();
        if (leafs.contains(companyId)) {
            LOGGER.info("#################包含companyId########################{}", companyId);
            map.put("companyId", companyId);
        } else {
            map.put("companyId", null);
        }
        List<RoadVO> roads = eventMapper.selectEventRoad(roleList);
        List<Integer> roadNos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(roads)) {
        	for (RoadVO roadVO : roads) {
        		roadNos.add(roadVO.getRoadNo());
			}
        }
        map.put("roadNos", roadNos);
        
        // 通过eventOrgList查询关联的路段
        List<OrgRoadListVO> eventOrgList = eventMapper.orgRoadDirectionList(map);
        List<String> removeLeafs = new ArrayList<>();

        for (String leaf : leafs) {
            boolean remove = true;
            for (OrgRoadListVO vo : eventOrgList) {
                if (vo.getOrgId().equals(leaf)) {
                    remove = false;
                    break;
                }
            }
            if (remove) {
                removeLeafs.add(leaf);
            }
            remove = true;
        }
        recursiveDelOrganizationLeaf(organizationTree, removeLeafs);
        recursiveDelOrganizationParent(organizationTree, leafs);
        if (!CollectionUtils.isEmpty(eventOrgList)) {
            orgRoadDirectionList(organizationTree, eventOrgList);
        }

        // 取后三层树形
        // 1.取最外层的orgID和children集合
        List<String> rootOrgIds = new ArrayList<>();
        Map<String, List<OrganizationVO>> oneTwoLevelOrg = new HashMap<>();
        
//        String orgId = null;
//        List<OrganizationVO> children = new ArrayList<>();
        for (OrganizationVO vo : organizationTree) {
            if (vo.getPid() == null && !CollectionUtils.isEmpty(vo.getChildren())) {
            	String orgId = vo.getOrgId();
                rootOrgIds.add(orgId);
                oneTwoLevelOrg.put(orgId, vo.getChildren());
//                children = vo.getChildren();
            }
        }
        // 2.判断pid是否和最外层的orgID一致，是的话，则取出该层的children，添加到集合
        List<OrganizationVO> ret = new ArrayList<>();
        
        for (String orgId : oneTwoLevelOrg.keySet()) {
        	List<OrganizationVO> orgRoadDirection = new ArrayList<>();
        	for (OrganizationVO vo : oneTwoLevelOrg.get(orgId)) {
                if (vo.getPid().equals(orgId)) {
                    if (!CollectionUtils.isEmpty(vo.getChildren())) {
                        orgRoadDirection.addAll(vo.getChildren());
                    }
                }
            }
        	if (CollectionUtils.isEmpty(orgRoadDirection)) {
            	orgRoadDirection.addAll(oneTwoLevelOrg.get(orgId));
            }
        	ret.addAll(orgRoadDirection);
        }
        
        
//        for (OrganizationVO vo : children) {
//            if (vo.getPid().equals(orgId)) {
//                if (!CollectionUtils.isEmpty(vo.getChildren())) {
//                    orgRoadDirection.addAll(vo.getChildren());
//                }
//            }
//        }
//        if (CollectionUtils.isEmpty(orgRoadDirection)) {
//        	orgRoadDirection.addAll(children);
//        }

        return ret;
    }

    void orgRoadDirectionList(List<OrganizationVO> source, List<OrgRoadListVO> eventOrgList) {
        for (OrganizationVO org : source) {
            if (CollectionUtils.isEmpty(org.getChildren())) {
                for (OrgRoadListVO orgRoad : eventOrgList) {
                    if (org.getOrgId().equals(orgRoad.getOrgId())) {
                        org.setRoads(orgRoad.getChildren());
                    }
                }
            } else {
                orgRoadDirectionList(org.getChildren(), eventOrgList);
            }
        }
    }

    /**
     * @param userId
     * @param organizationTree
     * @param roles
     * @return
     * @描述 组织机构+路段+设施 6级菜单
     */
    public List<OrganizationVO> orgRoadFacilityByUser(String userId, List<OrganizationVO> organizationTree,
                                                      String[] roles) {
        List<String> leafs = new ArrayList<>();
        recursiveOrganizationTree(organizationTree, leafs);
        Map<String, Object> map = new HashMap<>();
        map.put("roles", Arrays.asList(roles));
        //根据用户设置，去掉用户组织机构所属路段
        /*
         * UserSimpleVO loginUser = feignClient.selectByUserId(userId); String companyId
         * = loginUser.getCompanyId(); if (leafs.contains(companyId)) {
         * LOGGER.info("#################包含companyId########################{}",
         * companyId); map.put("companyId", companyId); } else { map.put("companyId",
         * null); }
         */
        // 通过eventOrgList查询关联的路段
        List<OrgRoadListVO> orgRoadFacilityList = eventMapper.orgRoadFacilityList(map);
        List<String> removeLeafs = new ArrayList<>();

        for (String leaf : leafs) {
            boolean remove = true;
            for (OrgRoadListVO vo : orgRoadFacilityList) {
                if (vo.getOrgId().equals(leaf)) {
                    remove = false;
                    break;
                }
            }
            if (remove) {
                removeLeafs.add(leaf);
            }
            remove = true;
        }
        recursiveDelOrganizationLeaf(organizationTree, removeLeafs);
        recursiveDelOrganizationParent(organizationTree, leafs);
        if (!CollectionUtils.isEmpty(orgRoadFacilityList)) {
            orgRoadFacilityList(organizationTree, orgRoadFacilityList);
        }
        return organizationTree;
    }

    private void orgRoadFacilityList(List<OrganizationVO> source, List<OrgRoadListVO> eventOrgList) {
        for (OrganizationVO org : source) {
            if (CollectionUtils.isEmpty(org.getChildren())) {
                for (OrgRoadListVO orgRoad : eventOrgList) {
                    if (org.getOrgId().equals(orgRoad.getOrgId())) {
                        org.setRoads(orgRoad.getChildren());
                    }
                }
            } else {
                orgRoadFacilityList(org.getChildren(), eventOrgList);
            }
        }
    }

    /**
     * @描述 2小时内处置率
     */
    public Object dealWithin2Hour(String role, EventAnalysisDTO eventAnalysisDTO) {
    	eventAnalysisDTO.setTimeSecond(2 * 3600L);
    	return dealWithinXsecond(role, eventAnalysisDTO);
    }


    /**
     * @描述 X秒内处置率
     */
    public Object dealWithinXsecond(String role, EventAnalysisDTO eventAnalysisDTO) {
        long time20230401 = TimeUtils.toLong("2023-04-01 00:00:00", TimeUtils.FULL_TIME);// 特殊处理，业主要求
        Long startTime = eventAnalysisDTO.getStartTime();
        if (startTime == null || startTime < time20230401) {
            eventAnalysisDTO.setStartTime(time20230401);
        }
        List<EventStatDealTimeVO> list = eventMapper.statAvgDealTimeGroupOrgId(eventAnalysisDTO);
        eventAnalysisDTO.setLtTime(eventAnalysisDTO.getTimeSecond());
        if (CollectionUtils.isEmpty(list)) {
            Map<String, Object> ret = new HashMap<>();
            ret.put("orgDealTime", new ArrayList<>());
            return ret;
        }
        for (int i = (list.size() - 1); i >= 0; i--) {
            if (list.get(i).getDealTime() == null) {
                list.remove(i);
            }
        }
        if (CollectionUtils.isEmpty(list)) {
            Map<String, Object> ret = new HashMap<>();
            ret.put("orgDealTime", new ArrayList<>());
            return ret;
        }
        for (EventStatDealTimeVO vo : list) {
            if (vo.getDealTime() == null) {
                continue;
            }
            String orgName = OrganShortNameEnum.getName(vo.getOrgId());
            vo.setOrgName(orgName);
        }

        List<String> orgIds = selectOrgIdByRoles(role);
        List<EventStatDealTimeVO> filteredList = new ArrayList<>();
        for (EventStatDealTimeVO tmp : list) {
            if (orgIds.contains(tmp.getOrgId())) {
                filteredList.add(tmp);
            }
        }
        List<EventStatDealTimeVO> within2HourList = eventMapper.statAvgDealTimeGroupOrgId(eventAnalysisDTO);
        for (EventStatDealTimeVO eventStatDealTimeVO : filteredList) {
            eventStatDealTimeVO.setTotal2Hour(0);
            if (!CollectionUtils.isEmpty(within2HourList)) {
                for (EventStatDealTimeVO eventStatDealTimeVO2 : within2HourList) {
                    if (eventStatDealTimeVO.getOrgId().equals(eventStatDealTimeVO2.getOrgId())) {
                        eventStatDealTimeVO.setTotal2Hour(eventStatDealTimeVO2.getTotal());
                        break;
                    }
                }
            }
        }

        Map<String, Object> ret = new HashMap<>();
        ret.put("orgDealTime", filteredList);
        return ret;
    }

    /**
     * @描述 X秒内处置率
     */
    public Object confirmWithinXsecond(String role, EventAnalysisDTO eventAnalysisDTO) {
        long time20230401 = TimeUtils.toLong("2023-04-01 00:00:00", TimeUtils.FULL_TIME);// 特殊处理，业主要求
        Long startTime = eventAnalysisDTO.getStartTime();
        if (startTime == null || startTime < time20230401) {
            eventAnalysisDTO.setStartTime(time20230401);
        }
        List<EventStatDealTimeVO> list = eventMapper.confirmWithinXsecond(eventAnalysisDTO);
        if (CollectionUtils.isEmpty(list)) {
            Map<String, Object> ret = new HashMap<>();
            ret.put("orgDealTime", new ArrayList<>());
            return ret;
        }
        for (int i = (list.size() - 1); i >= 0; i--) {
            if (list.get(i).getDealTime() == null) {
                list.remove(i);
            }
        }
        if (CollectionUtils.isEmpty(list)) {
            Map<String, Object> ret = new HashMap<>();
            ret.put("orgDealTime", new ArrayList<>());
            return ret;
        }
        for (EventStatDealTimeVO vo : list) {
            if (vo.getDealTime() == null) {
                continue;
            }
            String orgName = OrganShortNameEnum.getName(vo.getOrgId());
            vo.setOrgName(orgName);
        }

        List<String> orgIds = selectOrgIdByRoles(role);
        List<EventStatDealTimeVO> filteredList = new ArrayList<>();
        for (EventStatDealTimeVO tmp : list) {
            if (orgIds.contains(tmp.getOrgId())) {
                filteredList.add(tmp);
            }
        }

        eventAnalysisDTO.setReachFlag(1);
        List<EventStatDealTimeVO> reachList = eventMapper.confirmWithinXsecond(eventAnalysisDTO);
        for (EventStatDealTimeVO eventStatDealTimeVO : filteredList) {
            eventStatDealTimeVO.setTotalReach(0);
            if (!CollectionUtils.isEmpty(reachList)) {
                for (EventStatDealTimeVO reachVO : reachList) {
                    if (eventStatDealTimeVO.getOrgId().equals(reachVO.getOrgId())) {
                        eventStatDealTimeVO.setTotalReach(reachVO.getTotal());
                        break;
                    }
                }
            }
        }
        eventAnalysisDTO.setReachTime(eventAnalysisDTO.getTimeSecond());
        List<EventStatDealTimeVO> reachWithin1HourList = eventMapper.confirmWithinXsecond(eventAnalysisDTO);
        for (EventStatDealTimeVO eventStatDealTimeVO : filteredList) {
            eventStatDealTimeVO.setTotalReach1Hour(0);
            if (!CollectionUtils.isEmpty(reachWithin1HourList)) {
                for (EventStatDealTimeVO reachWithin1HourVO : reachWithin1HourList) {
                    if (eventStatDealTimeVO.getOrgId().equals(reachWithin1HourVO.getOrgId())) {
                        eventStatDealTimeVO.setTotalReach1Hour(reachWithin1HourVO.getTotal());
                        break;
                    }
                }
            }
        }

        Map<String, Object> ret = new HashMap<>();
        ret.put("orgDealTime", filteredList);
        return ret;
    }

    /**
     * @描述 1小时内到达率
     */
    public Object reachWithin1Hour(String role, EventAnalysisDTO eventAnalysisDTO) {
    	eventAnalysisDTO.setTimeSecond(3600L);
        return reachWithinXsecond(role, eventAnalysisDTO);
    }
    
    public Object reachWithinXsecond(String role, EventAnalysisDTO eventAnalysisDTO) {
        long time20230401 = TimeUtils.toLong("2023-04-01 00:00:00", TimeUtils.FULL_TIME);// 特殊处理，业主要求
        Long startTime = eventAnalysisDTO.getStartTime();
        if (startTime == null || startTime < time20230401) {
            eventAnalysisDTO.setStartTime(time20230401);
        }
        List<EventStatDealTimeVO> list = eventMapper.statAvgDealTimeGroupOrgId(eventAnalysisDTO);
        if (CollectionUtils.isEmpty(list)) {
            Map<String, Object> ret = new HashMap<>();
            ret.put("orgDealTime", new ArrayList<>());
            return ret;
        }
        for (int i = (list.size() - 1); i >= 0; i--) {
            if (list.get(i).getDealTime() == null) {
                list.remove(i);
            }
        }
        if (CollectionUtils.isEmpty(list)) {
            Map<String, Object> ret = new HashMap<>();
            ret.put("orgDealTime", new ArrayList<>());
            return ret;
        }
        for (EventStatDealTimeVO vo : list) {
            if (vo.getDealTime() == null) {
                continue;
            }
            String orgName = OrganShortNameEnum.getName(vo.getOrgId());
            vo.setOrgName(orgName);
        }

        List<String> orgIds = selectOrgIdByRoles(role);
        List<EventStatDealTimeVO> filteredList = new ArrayList<>();
        for (EventStatDealTimeVO tmp : list) {
            if (orgIds.contains(tmp.getOrgId())) {
                filteredList.add(tmp);
            }
        }

        eventAnalysisDTO.setReachFlag(1);
        List<EventStatDealTimeVO> reachList = eventMapper.statAvgDealTimeGroupOrgId(eventAnalysisDTO);
        for (EventStatDealTimeVO eventStatDealTimeVO : filteredList) {
            eventStatDealTimeVO.setTotalReach(0);
            if (!CollectionUtils.isEmpty(reachList)) {
                for (EventStatDealTimeVO reachVO : reachList) {
                    if (eventStatDealTimeVO.getOrgId().equals(reachVO.getOrgId())) {
                        eventStatDealTimeVO.setTotalReach(reachVO.getTotal());
                        break;
                    }
                }
            }
        }
        eventAnalysisDTO.setReachTime(eventAnalysisDTO.getTimeSecond());
        List<EventStatDealTimeVO> reachWithin1HourList = eventMapper.statAvgDealTimeGroupOrgId(eventAnalysisDTO);
        for (EventStatDealTimeVO eventStatDealTimeVO : filteredList) {
            eventStatDealTimeVO.setTotalReach1Hour(0);
            if (!CollectionUtils.isEmpty(reachWithin1HourList)) {
                for (EventStatDealTimeVO reachWithin1HourVO : reachWithin1HourList) {
                    if (eventStatDealTimeVO.getOrgId().equals(reachWithin1HourVO.getOrgId())) {
                        eventStatDealTimeVO.setTotalReach1Hour(reachWithin1HourVO.getTotal());
                        break;
                    }
                }
            }
        }

        Map<String, Object> ret = new HashMap<>();
        ret.put("orgDealTime", filteredList);
        return ret;
    }

    public Object unblockWithinXsecond(String role, EventAnalysisDTO eventAnalysisDTO) {
        long time20230401 = TimeUtils.toLong("2023-04-01 00:00:00", TimeUtils.FULL_TIME);// 特殊处理，业主要求
        Long startTime = eventAnalysisDTO.getStartTime();
        if (startTime == null || startTime < time20230401) {
            eventAnalysisDTO.setStartTime(time20230401);
        }
        List<EventStatDealTimeVO> list = eventMapper.unblockWithinXsecond(eventAnalysisDTO);
        if (CollectionUtils.isEmpty(list)) {
            Map<String, Object> ret = new HashMap<>();
            ret.put("orgDealTime", new ArrayList<>());
            return ret;
        }
        for (int i = (list.size() - 1); i >= 0; i--) {
            if (list.get(i).getDealTime() == null) {
                list.remove(i);
            }
        }
        if (CollectionUtils.isEmpty(list)) {
            Map<String, Object> ret = new HashMap<>();
            ret.put("orgDealTime", new ArrayList<>());
            return ret;
        }
        for (EventStatDealTimeVO vo : list) {
            if (vo.getDealTime() == null) {
                continue;
            }
            String orgName = OrganShortNameEnum.getName(vo.getOrgId());
            vo.setOrgName(orgName);
        }

        List<String> orgIds = selectOrgIdByRoles(role);
        List<EventStatDealTimeVO> filteredList = new ArrayList<>();
        for (EventStatDealTimeVO tmp : list) {
            if (orgIds.contains(tmp.getOrgId())) {
                filteredList.add(tmp);
            }
        }

        eventAnalysisDTO.setReachFlag(1);
        List<EventStatDealTimeVO> reachList = eventMapper.unblockWithinXsecond(eventAnalysisDTO);
        for (EventStatDealTimeVO eventStatDealTimeVO : filteredList) {
            eventStatDealTimeVO.setTotalReach(0);
            if (!CollectionUtils.isEmpty(reachList)) {
                for (EventStatDealTimeVO reachVO : reachList) {
                    if (eventStatDealTimeVO.getOrgId().equals(reachVO.getOrgId())) {
                        eventStatDealTimeVO.setTotalReach(reachVO.getTotal());
                        break;
                    }
                }
            }
        }
        eventAnalysisDTO.setReachTime(eventAnalysisDTO.getTimeSecond());
        List<EventStatDealTimeVO> reachWithin1HourList = eventMapper.unblockWithinXsecond(eventAnalysisDTO);
        for (EventStatDealTimeVO eventStatDealTimeVO : filteredList) {
            eventStatDealTimeVO.setTotalReach1Hour(0);
            if (!CollectionUtils.isEmpty(reachWithin1HourList)) {
                for (EventStatDealTimeVO reachWithin1HourVO : reachWithin1HourList) {
                    if (eventStatDealTimeVO.getOrgId().equals(reachWithin1HourVO.getOrgId())) {
                        eventStatDealTimeVO.setTotalReach1Hour(reachWithin1HourVO.getTotal());
                        break;
                    }
                }
            }
        }

        Map<String, Object> ret = new HashMap<>();
        ret.put("orgDealTime", filteredList);
        return ret;
    }

    public boolean eventMatchLock(VideoPostionDTO dto) {
        EventDDDTO eventDDDto = new EventDDDTO();
        eventDDDto.setEventId(dto.getEventId());
        eventDDDto.setCameraId(dto.getDeviceId());
        eventDDDto.setUpdateTime(new Date());
        return eventMapper.updateEventDD(eventDDDto) > 0;
    }

    public List<EventAttachVO> selectEventTimeoutAuditAttachs(IdStringDTO dto) {
        return eventMapper.selectEventTimeoutAuditAttachs(dto);
    }

    public int addTsJy(EventTsDTO eventTsDTO) {
        eventTsDTO.setEtId(UUID.randomUUID().toString());
        long time = System.currentTimeMillis();
        eventTsDTO.setCreateTime(time / 1000);
        eventTsDTO.setSource(0);
        // 查询当前填报用户的信息
        UserSimpleVO recordManVO = feignClient.selectByUserId(eventTsDTO.getRecordManId());
        eventTsDTO.setRecordMan(recordManVO.getUserName());
        eventTsDTO.setRecordManTel(recordManVO.getMobile());

        String yyMMddHHmmssSSS = TimeUtils.getTimeString(TimeUtils.DATETIME, time);
        String eventNo = "TS-" + yyMMddHHmmssSSS;
        Integer eventThreeType = eventTsDTO.getEventThreeType();
        if (eventThreeType != null) {
            if (eventThreeType < 20) {
                eventNo = "TS-" + eventNo.split("-")[1];
            } else {
                eventNo = "JY-" + eventNo.split("-")[1];
            }
        }
        eventTsDTO.setEventNo(eventNo);
        eventTsDTO.setSourceId(OrgSourceIdEnum.getIndex(eventTsDTO.getOrgId()));
        return eventMapper.addTsJy(eventTsDTO);
    }

    public int updateTsJy(EventTsDTO eventTsDTO) {
        TsJyDetailVO vo = eventMapper.selectTsJyByEventId(new IdStringDTO(eventTsDTO.getId()));
        if (vo == null) {
            throw new ArgumentException("该事件不存在！");
        }
        String eventNo = vo.getEventNo();
        Integer eventThreeType = eventTsDTO.getEventThreeType();
        if (eventThreeType != null) {
            if (eventThreeType < 20) {
                eventNo = "TS-" + eventNo.split("-")[1];
            } else {
                eventNo = "JY-" + eventNo.split("-")[1];
            }
        }
        eventTsDTO.setEventNo(eventNo);
        eventTsDTO.setSourceId(OrgSourceIdEnum.getIndex(eventTsDTO.getOrgId()));
        return eventMapper.updateTsJy(eventTsDTO);
    }

    /**
     * 投诉建议列表导出
     *
     * @param dto      页面查询条件
     * @param response http response
     */
    public void exportTsJy(EventQueryDTO dto, HttpServletResponse response) {
        if (dto.getUserId() != null) {
            UserSimpleVO loginUser = feignClient.selectByUserId(dto.getUserId());
            dto.setCompanyId(loginUser.getCompanyId());
        }

        // 1.先查询所有事件类型，放到map中作为缓存
        LOGGER.info("[exportExcel]开始进行事件类型查询");
        List<EventTypeRescueVO> eventTypeList = eventTypeMapper.selectAllList();
        Map<Integer, String> eventTypeMap = new HashMap<>();
        for (EventTypeRescueVO eventTypeRescueVO : eventTypeList) {
            eventTypeMap.put(Integer.parseInt(eventTypeRescueVO.getId()), eventTypeRescueVO.getName());
        }
        LOGGER.info("[exportExcel]事件类型查询完成，并写入内存");
        List<TsJyVO> selectList = eventMapper.selectTsJy(dto);
        if (!selectList.isEmpty()) {
            long serverTime = System.currentTimeMillis() / 1000;
            for (TsJyVO tsJyVO : selectList) {
                tsJyVO.setServerTime(serverTime);
                Integer step = tsJyVO.getStep() == null ? 1 : tsJyVO.getStep();
                if (step == 1 || step == 3) {
                    tsJyVO.setDealOrgName("客服中心");
                } else { //step2
                    if (tsJyVO.getUser2Id() != null) {
                        UserSimpleVO user2 = feignClient.selectByUserId(tsJyVO.getUser2Id());
                        String org2company = user2.getCompanyName();
                        if (org2company != null) {
                            tsJyVO.setDealOrgName(org2company + '-' + tsJyVO.getDealOrgName()); //业务部门调查：公司-组织
                        }
                    }
                }
            }
        }
        int size = selectList.size();
        LOGGER.info("查询出的投诉建议数量：" + size);
        if (size > 0) {
            // 构造Excel的基础文件结构
            String[] titles = {"序号", "工单号", "事件类型", "信息来源", "接报来源", "接报时间", "类型",
                    "相关内容", "处理结果", "责任公司", "流转部门", "责任人", "处理人", "处置阶段", "业务分类"};
            String fileName;
            String title;
            if (null == dto.getStartTime() && null == dto.getEndTime()) {
                // 查询条件不包含时间，修改文件名及表头的名称
                fileName = "投诉建议列表（处置中）";
                title = fileName;
            } else {
                // 查询条件包含时间
                fileName = TimeUtils.getTimeString(DATE, dto.getStartTime() * 1000) + "-"
                        + TimeUtils.getTimeString(DATE, dto.getEndTime() * 1000) + "投诉建议列表（处置中）";
                title = fileName;
            }

            String filePath = fileExportPath + File.separator + fileName + "##" + UUID.randomUUID().toString()
                    + "%%.xls";
            ExcelUtils tool = new ExcelUtils();
            int cellWidth[] = {8, 22, 14, 14, 14, 20, 10, 50, 15, 16, 23, 15, 15, 15, 20, 20};
            tool.init(filePath, "sheet1");
            tool.writeTitle(title, 0, 0, 14, 0);
            tool.writeHeadStr(titles, cellWidth, 1);

            // 10.填充Excel文件中的每一行数据
            LOGGER.info("开始填充每一行数据");
            List<String> dataList = new ArrayList<String>();
            for (int i = 0; i < size; i++) {
                if (i == fileExportMaxCount) {
                    break;
                }

                TsJyVO tsJyVO = selectList.get(i);
                dataList.clear();
                Integer sourceValue = selectList.get(i).getSource();
                String eventSource = (sourceValue == null)
                        ? org.apache.commons.lang.StringUtils.EMPTY
                        : EventSourceEnum.getSourceStrByValue(sourceValue);
                long reportTime = null == selectList.get(i).getReportTime() ? 0 : selectList.get(i).getReportTime(); // 防null处理
                String reportTimeStr = reportTime == 0 ? org.apache.commons.lang.StringUtils.EMPTY
                        : TimeUtils.getTimeString(DATETIME, reportTime * 1000);
                String type = Event96333Service.getComplaintType(tsJyVO.getComplaintType());
                if (org.apache.commons.lang.StringUtils.isEmpty(type))
                    type = org.apache.commons.lang.StringUtils.EMPTY;
                String stepStr, userName;
                TsJyDetailVO tsJyDetailVO = selectTsJyByEventId(new IdStringDTO(selectList.get(i).getId()));
                switch (tsJyVO.getStep()) {
                    case 1:
                        stepStr = "尚未开始";
                        userName = tsJyDetailVO.getUser1Name();
                        break;
                    case 2:
                        stepStr = "客服处置";
                        userName = tsJyDetailVO.getUser2Name();
                        break;
                    case 3:
                        stepStr = "业务部门调查";
                        userName = tsJyDetailVO.getUser3Name();
                        break;
                    default:
                        stepStr = "";
                        userName = "";
                }
                dataList.add(String.valueOf(i + 1)); // 序号
                dataList.add(tsJyVO.getEventNo()); // 工单号
                dataList.add(eventTypeMap.get(tsJyVO.getEventThreeType())); // 事件类型
                dataList.add(eventSource); // 信息来源
                dataList.add(EventReportSourceEnum.getSourceStrByValue(tsJyVO.getReportSourceKey())); // 接报来源
                dataList.add(reportTimeStr); // 接报时间
                dataList.add(type); // 类型
                dataList.add(tsJyVO.getBriefDesc()); // 相关内容
                dataList.add(""); // 处理结果留空
                dataList.add(tsJyVO.getOrgName());//责任公司
                dataList.add(tsJyVO.getDealOrgName());//流转部门 公司-部门
                dataList.add(userName);//责任人
                dataList.add(tsJyVO.getRecordMan());//处理人
                dataList.add(stepStr); // 处置阶段
                dataList.add(tsJyDetailVO.getBusinessTypeName());//业务分类

                Map<Integer, Integer> leftAlignMap = new HashMap<>();
                leftAlignMap.put(6, 6); // 相关内容左对齐
                tool.writeContentRow(dataList, 2 + i, leftAlignMap, null); // 写入一行数据
            }
            tool.writeBook(false); // 写入整个Excel表
            LOGGER.info("投诉建议列表写入完成");
            tool.downloadFile(filePath, response);
            ExcelUtils.deleteFile(filePath);
        }
    }

    public void exportZx(EventQueryDTO eventQueryDTO, HttpServletResponse response) {
        UserSimpleVO loginUser = feignClient.selectByUserId(eventQueryDTO.getUserId());
        eventQueryDTO.setCompanyId(loginUser.getCompanyId());
        List<String> dealOrg = new ArrayList<>();
        List<String> organizations = eventQueryDTO.getOrganizations();
        if (!CollectionUtils.isEmpty(organizations)) {
            for (String orgId : organizations) {
                if ("dfbd6525-b051-4719-b356-cd4e5454212".equals(orgId)) {// 广西北部湾投资集团有限公司，忽略
                    continue;
                }
                if ("dfbd6525-b051-4719-b356-cd4e545408f7".equals(orgId)) {// 新发展交通集团有限公司
                    dealOrg.add("6dc5e3e4-160c-4e3f-b42b-8c9f8d28aeb5");// 新发展集团本部
                    dealOrg.add(OrgSourceIdEnum.getName(2));
                    dealOrg.add(OrgSourceIdEnum.getName(3));
                    dealOrg.add(OrgSourceIdEnum.getName(4));
                    dealOrg.add(OrgSourceIdEnum.getName(5));
                    dealOrg.add(OrgSourceIdEnum.getName(6));
                    dealOrg.add(OrgSourceIdEnum.getName(7));
                    dealOrg.add(OrgSourceIdEnum.getName(8));
                    dealOrg.add(OrgSourceIdEnum.getName(9));
                } else if ("5f587a1c-189d-417b-910f-c09f0a18763f".equals(orgId)) {// 沿海公司
                    dealOrg.add(YanHaiOrgEnum.getName(1));
                    dealOrg.add(YanHaiOrgEnum.getName(2));
                    dealOrg.add(YanHaiOrgEnum.getName(3));
                    dealOrg.add(YanHaiOrgEnum.getName(4));
                    dealOrg.add(YanHaiOrgEnum.getName(5));
                } else {
                    dealOrg.add(orgId);
                }
            }
        }
        eventQueryDTO.setOrganizations(dealOrg);
        List<EventZxVO> selectList = eventMapper.selectZx(eventQueryDTO);
        for (EventZxVO eventZxVO : selectList) {
            Integer source = eventZxVO.getSource();
            source = (source == null) ? 0 : source;
            Integer reportSourceKey = eventZxVO.getReportSourceKey();
            reportSourceKey = (reportSourceKey == null) ? 0 : reportSourceKey;
            if (source == 1 || reportSourceKey == 1) {
                eventZxVO.setReportSource("96333");
            } else {
                eventZxVO.setReportSource("本地");
            }
        }
        LOGGER.info("查询出的信息咨询数量：" + selectList.size());
        List<EventZxVO> exportData = eventMapper.selectZx(eventQueryDTO);
        String[] titles = {"序号", "工单号", "事件分类", "信息来源", "接报来源", "接报时间", "责任单位",
                "责任人", "咨询内容", "办理结果", "处理人", "处理时间", "业务分类"};

        String fileName;
        String title;
        if (null == eventQueryDTO.getStartTime() && null == eventQueryDTO.getEndTime()) {
            // 查询条件不包含时间，修改文件名及表头的名称
            fileName = "信息咨询详情列表（处置中）";
        } else {
            // 查询条件包含时间
            fileName = TimeUtils.getTimeString(DATE, eventQueryDTO.getStartTime() * 1000) + "-"
                    + TimeUtils.getTimeString(DATE, eventQueryDTO.getEndTime() * 1000) + "信息咨询详情列表（处置中）";
        }
        title = fileName;
        String filePath = fileExportPath + File.separator + fileName + "##" + UUID.randomUUID().toString() + "%%.xls";
        ExcelUtils tool = new ExcelUtils();
        int cellWidth[] = {9, 21, 15, 15, 15, 25, 45, 13, 50, 15, 15, 15, 15};
        tool.init(filePath, "sheet1");
        tool.writeTitle(title, 0, 0, 12, 0);
        tool.writeHeadStr(titles, cellWidth, 1);
        List<String> rowData = new ArrayList<String>();

        int size = exportData.size();
        if (size == 0) {
            return;
        }
        for (int i = 0; i < size; i++) {
            EventZxVO eventVO = exportData.get(i);
            rowData.clear();

            rowData.add(i + 1 + ""); // 序号
            rowData.add(eventVO.getEventNo()); // 工单号
            rowData.add("信息咨询");// 事件分类
            rowData.add(EventSourceEnum.getSourceStrByValue(eventVO.getSource())); // 事件来源
            rowData.add(EventReportSourceEnum.getSourceStrByValue(eventVO.getReportSourceKey())); // 接报来源
            rowData.add(TimeUtils.getTimeString(TimeUtils.FULL_TIME, eventVO.getReportTime() * 1000));// 接报时间
            //String companyWithOrg = eventVO.getCompanyName() + "-" + eventVO.getOrgName();
            //避免出现null-null或xxx-null的情况，作以下改写
            String companyWithOrg = "";
            if (eventVO.getCompanyName() != null && !eventVO.getCompanyName().equals("")) {
                companyWithOrg += eventVO.getCompanyName();
            }

            if (eventVO.getOrgName() != null && !eventVO.getOrgName().equals("")) {
                if (!companyWithOrg.isEmpty()) {
                    companyWithOrg += "-";
                }
                companyWithOrg += eventVO.getOrgName();
            }
            rowData.add(eventVO.getCompanyName());// 责任单位，公司-部门
            rowData.add(eventVO.getUserName());// 责任人
            rowData.add(eventVO.getBriefDesc()); // 咨询内容
            rowData.add(""); // 办理结果留空
            rowData.add(""); // 处理人留空
            rowData.add(""); // 处理结果留空
            rowData.add(eventVO.getBusinessTypeName());// 业务分类
            tool.writeContentRow(rowData, 2 + i); // 写入一行数据
        }

        tool.writeBook(false); // 写入整个Excel表
        LOGGER.info("完成信息咨询excel写入");
        tool.downloadFile(filePath, response);
        ExcelUtils.deleteFile(filePath);
    }

    public List<OrgListVO> orgByUser(Map<String, Object> map){
        List<OrgListVO> list = eventMapper.orgByUser(map);
        //只要运营公司
        List<String> orgEnumValueList = new ArrayList<>();
        for (OrgSourceIdEnum orgEnum : OrgSourceIdEnum.values()) {
            orgEnumValueList.add(orgEnum.getName());
        }
        List<OrgListVO> result = list.stream().filter(a -> orgEnumValueList.contains(a.getOrgId())).collect(Collectors.toList());
        for (OrgListVO vo: result){
            if (vo.getOrgId().equals(OrgSourceIdEnum.SOURCE_ID_1.getName())){
                result.remove(vo);
            }
            vo.setOrgName(getShortOrgName(vo.getOrgName()));
        }
        return result;
    }

    private String getShortOrgName(String originName) {
        if (org.apache.commons.lang.StringUtils.isEmpty(originName)) {
            return originName;
        }
        originName = originName.replaceFirst("高速公路运营分公司", org.apache.commons.lang.StringUtils.EMPTY);
        originName = originName.replaceFirst("分公司", org.apache.commons.lang.StringUtils.EMPTY);
        originName = originName.replaceFirst("高速公路运营管理中心", org.apache.commons.lang.StringUtils.EMPTY);
        originName = originName.replaceFirst("运营管理中心", org.apache.commons.lang.StringUtils.EMPTY);
        originName = originName.replaceFirst("中心", org.apache.commons.lang.StringUtils.EMPTY);
        return originName;
    }

    public List<String> query96333OrderList(List<String> eventNos) {
    	Map<String, Object> map = new HashMap<>();
    	map.put("eventNos", eventNos);
    	return eventMapper.query96333OrderList(map);
    }

	public Integer location(UserLocationDTO dto) {
		int flag = 0;
		// 查询未完结（事件处理状态<100）且有经纬度的该处置人员的事件距离
		List<EventLocationVO> list = eventMapper.selectLocation(dto);
		if(!CollectionUtils.isEmpty(list)) {
			for(EventLocationVO vo : list) {
				if(vo.getDistance()*1000 < locationMile) {
					//判断是否已到达打卡（进展已到达20、已离开30均未打卡）
					List<ProgressVO> progressList = progressMapper.selectIsLocation(vo);
					if(CollectionUtils.isEmpty(progressList)) {
						//打卡
						EventClockStatuDTO eventClockStatuDTO = new EventClockStatuDTO();
						eventClockStatuDTO.setId(vo.getEventId());
						eventClockStatuDTO.setUserId(dto.getUserId());
						eventClockStatuDTO.setLat(dto.getLat());
						eventClockStatuDTO.setLng(dto.getLng());
						eventClockStatuDTO.setEventStatus(6);//到达打卡
						String progressDesc;
						try {
							// 根据经纬度获取行政区域地址
							GaodeAddrVO lngLatToAddr = GaodeMapUtils.lngLatToAddr(dto.getLng(), dto.getLat());
							progressDesc = "已到达，地点："+lngLatToAddr.getRegeocode().getFormatted_address().replace("广西壮族自治区", "");
						} catch (Exception e) {
							progressDesc = "";
						}
						eventClockStatuDTO.setProgressDesc(progressDesc);
						clockEventStatus(eventClockStatuDTO);
						flag = 1;
					}
				}
			}
		}
		return flag;
	}

	public List<UserLocationVO> personLocationByEventId(IdStringDTO dto) {
		// 根据event_id在event_emer_user表查处置人员，关联user表查人员最新定位信息
		return eventMapper.personLocationByEventId(dto);
	}

	private void infoReview(String eventId) {
		EmerDutyTodayDTO emerDutyTodayDTO = new EmerDutyTodayDTO();
		emerDutyTodayDTO.setId(eventId);
		emerDutyTodayDTO.setDutyTime(TimeUtils.getTimeString(TimeUtils.DATE));
		List<TodayEmerDutyVO> todayEmerDutys = itsEmerFeignClient.emerDutySelectTodayByEventId(emerDutyTodayDTO);
		for (TodayEmerDutyVO todayEmerDutyVO : todayEmerDutys) {
			String groupName = todayEmerDutyVO.getGroupName();
			if (groupName.contains("信息报送") || (groupName.contains("值班") && groupName.contains("中层")) || (groupName.contains("应急") && groupName.contains("中层"))) {
				
			}
			
			
		}		
		
	}

	public PageInfo<EventInfoReviewVO> pageEventInfoReview(PageDTO pageDTO, EventInfoReviewDTO dto) {
        PageHelper.startPage(pageDTO.getPage(), pageDTO.getLimit());
        List<EventInfoReviewVO> list = eventMapper.selectEventInfoReview(dto);
        if (!CollectionUtils.isEmpty(list)) {
        	List<String> reviewIds = new ArrayList<>();
        	for (EventInfoReviewVO eventInfoReviewVO : list) {
        		reviewIds.add(eventInfoReviewVO.getId());
        	}
        	List<EventInfoReviewVO> eventInfoReviewUser = eventMapper.selectEventInfoReviewUser(reviewIds);
        	Map<String, List<ProgressUserDTO>> reviewUserMap = new HashMap<>();
        	for (EventInfoReviewVO vo : eventInfoReviewUser) {
        		reviewUserMap.put(vo.getId(), vo.getInfoReviewUsers());	
			}
        	for (EventInfoReviewVO eventInfoReviewVO : list) {
        		eventInfoReviewVO.setInfoReviewUsers(reviewUserMap.get(eventInfoReviewVO.getId()));
        	}
        }
        PageInfo<EventInfoReviewVO> pageInfo = new PageInfo<>(list);
        return pageInfo;
    }

	@Transactional
	public boolean submitInfoReview(EventConfirmDTO dto, Integer infoType) {
		List<ProgressUserDTO> infoReviewUsers = dto.getInfoReviewUsers();
    	if (CollectionUtils.isEmpty(infoReviewUsers)) {
    		throw new ArgumentException("请选择审核人员");
    	}
    	String frontInitReportDesc = dto.getProgressDesc();
    	String eventId = dto.getId();
    	IdStringDTO eventIdDTO = new IdStringDTO(eventId);
    	EventDetailVO eventDetailVO = eventMapper.selectEmerRescueByEventId(eventIdDTO);
    	// 生成审核数据
    	String today = "";
        String seqno = "";
        Long dbReportTime = eventDetailVO.getReportTime();
        if (eventDetailVO.getEventTwoType() == 5) { // 突发事件
        	today = TimeUtils.getTimeString(TimeUtils.DATE_7, dbReportTime * 1000);
        	Long todayStartTime = TimeUtils.toLong(TimeUtils.getTimeString(TimeUtils.DATE, dbReportTime * 1000) + " 00:00:00", TimeUtils.FULL_TIME);
        	int eventNum = eventMapper.selectTodayEmerSeqno(todayStartTime, dbReportTime);
        	if (eventNum + 1 < 10) {
        		seqno = "0" + (eventNum + 1);
        	} else {
        		seqno = "" + (eventNum + 1);
        	}
        	if (infoType == 5) {
        		frontInitReportDesc = frontInitReportDesc.replace("【事件续报】", "【"+today+"日事件"+seqno+"续报】");
        	} else if (infoType == 10) {
        		frontInitReportDesc = frontInitReportDesc.replace("【事件终报】", "【"+today+"日事件"+seqno+"终报】");
        	}
        }
        EventInfoReviewDTO reviewDTO = new EventInfoReviewDTO();
        String reviewId = UUID.randomUUID().toString();
        String nowTime = TimeUtils.getTimeString();
        reviewDTO.setReviewId(reviewId);
        reviewDTO.setProgressDesc(frontInitReportDesc);
        reviewDTO.setCreateTime(nowTime);
        reviewDTO.setInfoType(infoType);
        reviewDTO.setReviewStatus(0);
        reviewDTO.setId(eventId);
        reviewDTO.setEventId(eventId);
        reviewDTO.setCreateUserId(dto.getCreateUserId());
        UserSimpleVO loginUser = feignClient.selectByUserId(dto.getCreateUserId());
        reviewDTO.setCreateUserName(loginUser.getUserName());
        reviewDTO.setEventNo(eventDetailVO.getEventNo());
        reviewDTO.setEventDesc(dto.getEventDesc());
        reviewDTO.setReportTime(dto.getReportTime());
        reviewDTO.setReportSource(dto.getReportSource());
        reviewDTO.setLocation(dto.getLocation());
        reviewDTO.setInfoReviewUsers(dto.getInfoReviewUsers());
        boolean ret = eventMapper.submitInfoReview(reviewDTO) > 0;
        if (ret) {
        	EventReportDraftDTO draftDTO = new EventReportDraftDTO();
        	draftDTO.setReviewId(reviewId);
        	draftDTO.setAtUser(dto.getAtUsers());
        	draftDTO.setProgressDesc(frontInitReportDesc);
        	draftDTO.setEventDesc(dto.getEventDesc());
        	draftDTO.setLocation(dto.getLocation());
        	draftDTO.setReportSource(dto.getReportSource());
        	draftDTO.setReportTime(dto.getReportTime());
        	draftDTO.setCreateTime(nowTime);
        	eventMapper.addEventReportDraft(draftDTO);
        	eventInfoReviewMsg(reviewId, dto.getCreateUserId(), dto.getInfoReviewUsers());
        }
    	return ret;
	}

	@Transactional
	public boolean reviewEventInfo(EventInfoReviewDTO dto) {
		Integer reviewStatus = dto.getReviewStatus();
		if (reviewStatus == 4) {
			return false;
		}
		// 更新审核信息表
		String loginUserId = dto.getCreateUserId();
		UserSimpleVO loginUser = feignClient.selectByUserId(loginUserId);
		dto.setCreateUserName(loginUser.getUserName());
		dto.setCreateTime(TimeUtils.getTimeString());
		boolean ret = eventMapper.updateEventInfoReview(dto) > 0;
		if (ret) {
			EventInfoReviewVO vo = eventMapper.selectEventInfoReviewByReviewId(dto);
			if (reviewStatus == 1) {// 审核通过
				String eventId = vo.getEventId();
				Integer infoType = vo.getInfoType();
				EventConfirmDTO eventConfirmDTO = eventMapper.selectEventReportDraftByReviewId(dto);
				if (eventConfirmDTO == null) {
					eventConfirmDTO = new EventConfirmDTO();
					eventConfirmDTO.setId(eventId);
					eventConfirmDTO.setProgressDesc(vo.getProgressDesc());
					eventConfirmDTO.setCreateUserId(vo.getCreateUserId());
					eventConfirmDTO.setLocation(vo.getLocation());
					eventConfirmDTO.setEventDesc(vo.getEventDesc());
					eventConfirmDTO.setReportSource(vo.getReportSource());
					eventConfirmDTO.setReportTime(vo.getReportTime());
				}
				if (infoType == 1) {//初报
					EventConfirmDTO param = new EventConfirmDTO();
					param.setId(eventId);
					param.setProgressDesc(vo.getProgressDesc());
					List<ProgressUserDTO> atUsers = new ArrayList<>();
					List<ProgressUserDTO> notifyUsers = new ArrayList<>();
					List<ProgressUserDTO> smsUsers = new ArrayList<>();
					List<EventEmerUserVO> eventEmerUsers = eventMapper.selectEmerUserById(new IdStringDTO(eventId));
					for (EventEmerUserVO eventEmerUser : eventEmerUsers) {
						ProgressUserDTO user = new ProgressUserDTO();
						user.setUserId(eventEmerUser.getUserId());
						user.setUserName(eventEmerUser.getUserName());
						user.setMobile(eventEmerUser.getMobile());
						atUsers.add(user);
						Integer forceRemind = eventEmerUser.getForceRemind();
						if (forceRemind != null && forceRemind.intValue() == 1) {
							notifyUsers.add(user);
						}
						Integer smsRemind = eventEmerUser.getSmsRemind();
						if (smsRemind != null && smsRemind.intValue() == 1) {
							smsUsers.add(user);
						}
					}
					param.setLocation(vo.getLocation());
					param.setEventDesc(vo.getEventDesc());
					param.setReportSource(vo.getReportSource());
					param.setReportTime(vo.getReportTime());
					param.setCreateUserId(vo.getCreateUserId());
					param.setAtUsers(atUsers);
					param.setNotifyUsers(notifyUsers);
					param.setSmsUsers(smsUsers);
					boolean add = initialReport(param);
					if (add && !CollectionUtils.isEmpty(notifyUsers)) {
    					// 异步拉起机器人外呼通知结果状态更新
    					try {
    						List<String> jobIds = RobotCallTmpResultContext.obtainJobIds();
    						robotCallHandlerService.handleRobotCallResponse(AssignJobs.INSTANCE_ID, jobIds, null, eventId);
    					} finally {
    						RobotCallTmpResultContext.remove();
    					}
    				}
				} else if (infoType == 5) {//续报
					resubmit(eventConfirmDTO);
				} else if (infoType == 10) {//终报
					finalReport(eventConfirmDTO);
				}
			}
			// 发送消息通知给报送人和所有该信息的审核人
			eventInfoReviewMsg(vo.getId(), vo.getCreateUserId(), null);
		}
		return ret;
	}

	private void eventInfoReviewMsg(String reviewId, String createUserId, List<ProgressUserDTO> infoReviewUsers) {
		List<String> reviewIds = new ArrayList<>();
		reviewIds.add(reviewId);
		List<String> userIds = new ArrayList<>();
		if (infoReviewUsers == null) {
			List<EventInfoReviewVO> eventInfoReviewUser = eventMapper.selectEventInfoReviewUser(reviewIds);
			// 消息推送
			userIds.add(createUserId);
			for (EventInfoReviewVO user : eventInfoReviewUser) {
				infoReviewUsers = user.getInfoReviewUsers();
				for (ProgressUserDTO progressUser : infoReviewUsers) {
					userIds.add(progressUser.getUserId());
				}
			}
		} else {
			for (ProgressUserDTO progressUser : infoReviewUsers) {
				userIds.add(progressUser.getUserId());
			}
		}
		MessageDTO messageDTO = new MessageDTO();
		messageDTO.setUserIds(userIds);
		EventMessageDTO eventMessageDTO = new EventMessageDTO();
        eventMessageDTO.setWebsocketType("reviewEventInfo");
        messageDTO.setMessage(eventMessageDTO);
		itsWebSocketFeignClient.pushSomeUser(messageDTO);
	}

	public int statLoginUserEventInfoReview(String userId) {
		int rows = eventMapper.statLoginUserEventInfoReview(userId);
		return rows;
	}

	@Transactional
	public boolean updateEventInfoReview(EventInfoReviewDTO dto) {
		EventInfoReviewVO eventInfoReviewVO = eventMapper.selectEventInfoReviewByReviewId(dto);
		if (eventInfoReviewVO == null || !dto.getCreateUserId().equals(eventInfoReviewVO.getCreateUserId())) {
			return false;
		}
		boolean ret = eventMapper.updateEventInfoReviewDesc(dto) > 0;
		Integer infoType = eventInfoReviewVO.getInfoType();
		if (infoType != null && infoType != 1) {
			eventMapper.updateEventReportDraft(dto);
		}
		return ret;
	}

	public EventInfoReviewStatVO statEventInfoReviewByEventId(EventInfoReviewDTO dto) {
		dto.setReviewStatus(0);
		int reviewingNum = eventMapper.statEventInfoReviewByEventId(dto);
		dto.setReviewStatus(2);
		int reviewFailNum = eventMapper.statEventInfoReviewByEventId(dto);
		EventInfoReviewStatVO vo = new EventInfoReviewStatVO();
		vo.setReviewingNum(reviewingNum);
		vo.setReviewFailNum(reviewFailNum);
		return vo;
	}

	@Transactional
	public boolean confirmUnblock(EventDealStatusDTO dto, String userId) {
		Integer unblock = dto.getUnblock();
		String eventId = dto.getEventId();
		int rows = 0;
		long serverTime = System.currentTimeMillis() / 1000;
		dto.setUnblockTime(serverTime);
		if (unblock == 0) {
			rows = eventMapper.unblockFail(new IdStringDTO(eventId));
			if (rows == 0) {
				return false;
			}
			ProgressDTO progressDTO = new ProgressDTO();
			progressDTO.setProgressDesc("单向全幅封闭未解除，请尽快处置");
			progressDTO.setEventId(eventId);
			progressDTO.setOccurTime(serverTime);
			progressDTO.setCreateTime(serverTime);
			progressDTO.setTheme(1);
			progressDTO.setCreateUserId(userId);
			progressDTO.setCardType(0);
			progressService.add(progressDTO);
		} else if (unblock == 2) {
			rows = eventMapper.cleanUnblock(dto);
		}
		return rows > 0;
	}
}
