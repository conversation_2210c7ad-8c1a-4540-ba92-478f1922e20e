package com.bt.itsevent.domain.vo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: QinShiheng
 * @Date: 2022年12月07日17:02
 * @Description:
 **/
public class EventAnalysisVO implements Serializable {
    private Integer total;
    List<EventTypeStaticsVO> eventTypeStaticsVOS; // 事件类型分析
    List<EventDataStaticsVO> eventDataStaticsVOS; // 事件数据分析
    List<EventTrendVO> eventTrendVOS; // 事件变化趋势分析
    List<AccidentAnalysisVO> accidentAnalysisVOS; // 路段事故发生情况
    List<AccidentPercentVO> accidentPercentVOS; // 事故率
    List<FrequentAccidentRoadVO> frequentAccidentRoadVOS; // 事故多发地
    List<AccidentCauseVO> accidentCauseVOS; //事故原因

    private List<AccidentTimeAnalysisVO> timeList = new ArrayList<>(); // 事故多发时段统计
    private List<AccidentWeatherAnalysisVO> weatherList = new ArrayList<>(); // 事故多发天气类型统计

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public List<EventTypeStaticsVO> getEventTypeStaticsVOS() {
        return eventTypeStaticsVOS;
    }

    public void setEventTypeStaticsVOS(List<EventTypeStaticsVO> eventTypeStaticsVOS) {
        this.eventTypeStaticsVOS = eventTypeStaticsVOS;
    }

    public List<EventDataStaticsVO> getEventDataStaticsVOS() {
        return eventDataStaticsVOS;
    }

    public void setEventDataStaticsVOS(List<EventDataStaticsVO> eventDataStaticsVOS) {
        this.eventDataStaticsVOS = eventDataStaticsVOS;
    }

    public List<EventTrendVO> getEventTrendVOS() {
        return eventTrendVOS;
    }

    public void setEventTrendVOS(List<EventTrendVO> eventTrendVOS) {
        this.eventTrendVOS = eventTrendVOS;
    }

    public List<AccidentAnalysisVO> getAccidentAnalysisVOS() {
        return accidentAnalysisVOS;
    }

    public void setAccidentAnalysisVOS(List<AccidentAnalysisVO> accidentAnalysisVOS) {
        this.accidentAnalysisVOS = accidentAnalysisVOS;
    }

    public List<AccidentPercentVO> getAccidentPercentVOS() {
        return accidentPercentVOS;
    }

    public void setAccidentPercentVOS(List<AccidentPercentVO> accidentPercentVOS) {
        this.accidentPercentVOS = accidentPercentVOS;
    }

    public List<FrequentAccidentRoadVO> getFrequentAccidentRoadVOS() {
        return frequentAccidentRoadVOS;
    }

    public void setFrequentAccidentRoadVOS(List<FrequentAccidentRoadVO> frequentAccidentRoadVOS) {
        this.frequentAccidentRoadVOS = frequentAccidentRoadVOS;
    }

    public List<AccidentTimeAnalysisVO> getTimeList() {
        return timeList;
    }

    public void setTimeList(List<AccidentTimeAnalysisVO> timeList) {
        this.timeList = timeList;
    }

    public List<AccidentWeatherAnalysisVO> getWeatherList() {
        return weatherList;
    }

    public void setWeatherList(List<AccidentWeatherAnalysisVO> weatherList) {
        this.weatherList = weatherList;
    }

    public List<AccidentCauseVO> getAccidentCauseVOS() {
        return accidentCauseVOS;
    }

    public void setAccidentCauseVOS(List<AccidentCauseVO> accidentCauseVOS) {
        this.accidentCauseVOS = accidentCauseVOS;
    }
}
